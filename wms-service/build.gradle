dependencies {
    implementation project(':wms-interface')

    implementation('com.nsy.base:nsy-base-paper')
    implementation("com.nsy.api.etl${snapshotGroupEnd}:etl-interface:+") {
        exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
        exclude group: 'io.springfox'
    }
    implementation('com.nsy.transfer:api-transfer-models:+')

    implementation 'io.github.openfeign:feign-httpclient:11.9'
    implementation("com.nsy.api.pms${snapshotGroupEnd}:pms-interface:+")
    implementation("com.nsy.api.activiti${snapshotGroupEnd}:activiti-interface:+")
    implementation 'javax.inject:javax.inject:1'
    implementation 'javax.validation:validation-api:2.0.1.Final'
    implementation 'org.apache.commons:commons-lang3:3.6'
    implementation 'com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.8.10'
    implementation 'org.flywaydb:flyway-core:7.9.1'
    implementation 'joda-time:joda-time:2.9.9'
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.0.0'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation "org.springframework.boot:spring-boot-devtools"
    implementation('org.springframework.boot:spring-boot-starter-data-redis') {
        exclude group: 'io.lettuce', module: 'lettuce-core'
    }
    implementation 'redis.clients:jedis'
    implementation 'com.aliyun:aliyun-java-sdk-sts:2.1.6'
    implementation 'com.aliyun:aliyun-java-sdk-core:2.1.7'
    implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    implementation 'io.jsonwebtoken:jjwt-impl:0.11.2'
    implementation 'io.jsonwebtoken:jjwt-jackson:0.11.2'
    implementation 'com.alibaba:fastjson:1.2.68'
    implementation 'org.freemarker:freemarker:2.3.28'
    implementation 'com.google.zxing:core:3.5.0'
    implementation 'com.google.zxing:javase:3.5.0'
    implementation 'com.itextpdf:itextpdf:5.5.9'
    implementation 'com.itextpdf.tool:xmlworker:5.4.0'
    implementation 'org.xhtmlrenderer:core-renderer:R8'
    implementation 'org.apache.pdfbox:fontbox:2.0.24'
    implementation 'org.apache.pdfbox:pdfbox:2.0.24'

    // @Valid标签需要此校验器
    implementation 'org.hibernate:hibernate-validator:7.0.1.Final'

    implementation('io.springfox:springfox-swagger2:2.8.0')
    implementation('io.springfox:springfox-swagger-ui:2.8.0')

    //elastic job
    implementation('com.google.guava:guava:20.0')
    implementation('com.dangdang:elastic-job-lite-core:2.1.5')
    implementation('com.dangdang:elastic-job-lite-spring:2.1.5')
    implementation('org.apache.curator:curator-framework:2.10.0')
    implementation('org.apache.curator:curator-recipes:2.10.0')
    implementation('org.apache.kafka:kafka-clients:2.8.0')
    implementation('org.apache.kafka:kafka-streams:2.8.0')
    implementation('org.springframework.kafka:spring-kafka:2.7.1')

    //mybatis-plus
    implementation('com.baomidou:mybatis-plus-boot-starter:3.4.3')

    //bootstrap引导配置生效
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'
    implementation('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    implementation('com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel')
    implementation('com.alibaba.csp:sentinel-datasource-nacos')
    // lock support
    implementation('org.springframework.boot:spring-boot-starter-aop')
    implementation('org.redisson:redisson:3.16.0')

    implementation('com.thoughtworks.xstream:xstream:********')

    implementation('org.springframework.boot:spring-boot-starter-websocket')


    implementation('cn.hutool:hutool-all')

    runtimeOnly('mysql:mysql-connector-java:8.0.33')

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.integration:spring-integration-test'
    testImplementation 'com.h2database:h2:1.4.196'

    //数据权限
    implementation('com.nsy.permission:permission-starter:1.2.5')

    implementation 'javax.mail:javax.mail-api:1.6.2'
    implementation 'com.sun.mail:javax.mail:1.6.2'

    implementation('org.springframework.boot:spring-boot-starter-amqp')

    implementation('org.apache.poi:poi-ooxml:4.1.2')



}
