package com.nsy.wms.business.service.stockout;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.stockout.CustomsDeclarePurchaseOrderStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclarePurchaseOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclarePurchaseOrderResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareDocumentItemBo;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareCustomerOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclarePurchaseOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclarePurchaseOrderItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclarePurchaseOrderMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class StockoutCustomsDeclarePurchaseOrderService extends ServiceImpl<StockoutCustomsDeclarePurchaseOrderMapper, StockoutCustomsDeclarePurchaseOrderEntity> {


    @Resource
    StockoutCustomsDeclarePurchaseOrderItemService stockoutCustomsDeclarePurchaseOrderItemService;
    @Resource
    StockoutCustomsDeclareCustomerOrderService declareCustomerOrderService;

    @Resource
    StockoutCustomsDeclareContractService declareContractService;

    /**
     * 分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutCustomsDeclarePurchaseOrderResponse> pageList(StockoutCustomsDeclarePurchaseOrderPageRequest request) {

        Page<StockoutCustomsDeclarePurchaseOrderResponse> pageResult = this.baseMapper.findPage(new Page<>(request.getPageIndex(), request.getPageSize(), false), request);
        pageResult.getRecords().forEach(record -> record.setStatus(CustomsDeclarePurchaseOrderStatusEnum.of(record.getStatus())));
        PageResponse<StockoutCustomsDeclarePurchaseOrderResponse> response = new PageResponse<>();
        response.setContent(pageResult.getRecords());
        response.setTotalCount(this.baseMapper.countPage(request));
        return response;
    }

    /**
     * 明细
     *
     * @param declarePurchaseOrderId
     * @return
     */
    public StockoutCustomsDeclarePurchaseOrderResponse detail(Integer declarePurchaseOrderId) {
        StockoutCustomsDeclarePurchaseOrderEntity entity = getById(declarePurchaseOrderId);
        if (ObjectUtil.isNull(entity))
            throw new BusinessServiceException("采购单不存在");
        return buildResponse(entity);
    }

    private StockoutCustomsDeclarePurchaseOrderResponse buildResponse(StockoutCustomsDeclarePurchaseOrderEntity entity) {
        StockoutCustomsDeclarePurchaseOrderResponse response = BeanUtil.toBean(entity, StockoutCustomsDeclarePurchaseOrderResponse.class);
        response.setStatus(CustomsDeclarePurchaseOrderStatusEnum.of(response.getStatus()));
        List<StockoutCustomsDeclarePurchaseOrderItemEntity> itemList = stockoutCustomsDeclarePurchaseOrderItemService.listByDeclarePurchaseOrderId(response.getId());
        int totalQty = itemList.stream().mapToInt(StockoutCustomsDeclarePurchaseOrderItemEntity::getPendingQty).sum();
        response.setQty(totalQty);

        StockoutCustomsDeclareCustomerOrderEntity customerOrder = declareCustomerOrderService.findByOrderNo(entity.getOrderNo());
        if (ObjectUtil.isNotNull(customerOrder))
            response.setDeclareCustomerOrderId(customerOrder.getId());

        StockoutCustomsDeclareContractEntity declareContract = declareContractService.getByDeclareContractNo(response.getDeclareContractNo());
        if (ObjectUtil.isNotNull(declareContract))
            response.setDeclareContractId(declareContract.getDeclareContractId());
        return response;
    }


    /**
     * 新增
     *
     * @return
     */
    @Transactional
    public StockoutCustomsDeclarePurchaseOrderEntity add(String fbaShipmentId, String declareContractNo, List<StockoutCustomsDeclareDocumentItemBo> boList, SupplierDto supplierDto, StockoutCustomsDeclareFormEntity form) {
        if (CollectionUtil.isEmpty(boList)) {
            throw new BusinessServiceException(String.format("报关单据明细为空 %s %s ", form.getProtocolNo(), form.getgNo()));
        }

        StockoutCustomsDeclarePurchaseOrderEntity entity = findByFbaShipmentIdAndContractNo(fbaShipmentId, declareContractNo);
        if (ObjectUtil.isNull(entity)) {
            entity = new StockoutCustomsDeclarePurchaseOrderEntity();
            Date createDate = fetchCreateDate(form.getStockoutDate());
            String date = DateUtil.format(createDate, "yyyyMMdd");
            String code = String.format("%010d", countByDate(createDate) + 1);
            entity.setPurchaseOrderNo("PO" + date + code);

            entity.setOrderNo(fbaShipmentId);
            entity.setSpaceId(1);
            entity.setSpaceName("新时颖主仓库");
            entity.setDeclareContractNo(declareContractNo);
            entity.setSupplierId(supplierDto.getSupplierId());
            entity.setSupplierName(supplierDto.getSupplierName());
            entity.setPurchaseUserId(supplierDto.getContactPurchaserEmpId());
            entity.setPurchaseUserName(supplierDto.getContactPurchaserEmpCode());
            entity.setPurchaseUserRealName(supplierDto.getContactPurchaserEmpName());
            entity.setStatus(CustomsDeclarePurchaseOrderStatusEnum.FINISHED.name());
            entity.setCreateBy(entity.getPurchaseUserRealName());
            entity.setCreateDate(createDate);
            save(entity);
        }


        //新增明细
        StockoutCustomsDeclarePurchaseOrderEntity finalEntity = entity;
        boList.forEach(bo -> stockoutCustomsDeclarePurchaseOrderItemService.add(finalEntity, bo));

        return entity;
    }

    /**
     * 查找今天创建的列表
     *
     * @return
     */
    private Integer countByDate(Date date) {
        String today = DateUtil.format(date, "yyyy-MM-dd");
        String begin = today + " 00:00:00";
        String end = today + " 23:59:59";
        return count(new LambdaQueryWrapper<StockoutCustomsDeclarePurchaseOrderEntity>()
                .ge(StockoutCustomsDeclarePurchaseOrderEntity::getCreateDate, begin)
                .le(StockoutCustomsDeclarePurchaseOrderEntity::getCreateDate, end));
    }

    /**
     * 通过采购单号查找
     *
     * @param purchaseOrderNo
     * @return
     */
    public StockoutCustomsDeclarePurchaseOrderEntity findByPurchaseOrderNo(String purchaseOrderNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclarePurchaseOrderEntity>()
                .eq(StockoutCustomsDeclarePurchaseOrderEntity::getPurchaseOrderNo, purchaseOrderNo)
                .last("limit 1"));
    }

    /**
     * 通过采购单号查找
     *
     * @param fbaShipmentId
     * @param declareContractNo
     * @return
     */
    public StockoutCustomsDeclarePurchaseOrderEntity findByFbaShipmentIdAndContractNo(String fbaShipmentId, String declareContractNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclarePurchaseOrderEntity>()
                .eq(StockoutCustomsDeclarePurchaseOrderEntity::getOrderNo, fbaShipmentId)
                .eq(StockoutCustomsDeclarePurchaseOrderEntity::getDeclareContractNo, declareContractNo)
                .last("limit 1"));
    }

    /**
     * 通过采购单号查找
     *
     * @param declareContractNo
     * @return
     */
    public List<StockoutCustomsDeclarePurchaseOrderEntity> findByContractNo(String declareContractNo) {
        return list(new LambdaQueryWrapper<StockoutCustomsDeclarePurchaseOrderEntity>()
                .eq(StockoutCustomsDeclarePurchaseOrderEntity::getDeclareContractNo, declareContractNo));
    }

    /**
     * 通过 报关合同号删除
     *
     * @param contractNo
     * @return
     */
    public List<String> removeByContractNo(String contractNo) {
        List<StockoutCustomsDeclarePurchaseOrderEntity> purchaseOrderList = findByContractNo(contractNo);
        return purchaseOrderList.stream().map(purchaseOrder -> {
            //删除明细
            stockoutCustomsDeclarePurchaseOrderItemService.removeByDeclarePurchaseOrderId(purchaseOrder.getId());
            //删除主表
            removeById(purchaseOrder.getId());
            return purchaseOrder.getPurchaseOrderNo();
        }).collect(Collectors.toList());
    }

    /**
     * 创建时间 25 day before
     *
     * @param stockoutDate
     * @return
     */
    private Date fetchCreateDate(Date stockoutDate) {
        // 计算26天前的日期
        Date past = DateUtil.offsetDay(stockoutDate, -25);

        // 随机生成9点到19点之间的一个时间
        int hour = RandomUtil.randomInt(9, 19); // 包含9和19
        int minute = RandomUtil.randomInt(0, 60);
        int second = RandomUtil.randomInt(0, 60);
        // 生成具体时间
        return DateUtil.parse(DateUtil.format(past, "yyyy-MM-dd") + " " + String.format("%02d:%02d:%02d", hour, minute, second));
    }

}
