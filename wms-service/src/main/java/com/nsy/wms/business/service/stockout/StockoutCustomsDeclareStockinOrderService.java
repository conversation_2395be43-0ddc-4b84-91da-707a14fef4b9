package com.nsy.wms.business.service.stockout;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.stockout.CustomsDeclareStockinOrderStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareStockinOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareStockinOrderResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareDocumentItemBo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclarePurchaseOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareStockinOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareStockinOrderItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareStockinOrderMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class StockoutCustomsDeclareStockinOrderService extends ServiceImpl<StockoutCustomsDeclareStockinOrderMapper, StockoutCustomsDeclareStockinOrderEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareStockinOrderService.class);

    @Resource
    StockoutCustomsDeclareStockinOrderItemService stockoutCustomsDeclareStockinOrderItemService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockoutCustomsDeclarePurchaseOrderService stockoutCustomsDeclarePurchaseOrderService;

    /**
     * 分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutCustomsDeclareStockinOrderResponse> pageList(StockoutCustomsDeclareStockinOrderPageRequest request) {
        Page<StockoutCustomsDeclareStockinOrderResponse> pageResult = this.baseMapper.findPage(new Page<>(request.getPageIndex(), request.getPageSize(), false), request);
        pageResult.getRecords().forEach(record -> record.setStatus(CustomsDeclareStockinOrderStatusEnum.of(record.getStatus())));
        PageResponse<StockoutCustomsDeclareStockinOrderResponse> response = new PageResponse<>();
        response.setContent(pageResult.getRecords());
        response.setTotalCount(this.baseMapper.countPage(request));
        return response;
    }

    /**
     * 明细
     *
     * @param declareStockinOrderId
     * @return
     */
    public StockoutCustomsDeclareStockinOrderResponse detail(Integer declareStockinOrderId) {
        StockoutCustomsDeclareStockinOrderEntity entity = getById(declareStockinOrderId);
        if (ObjectUtil.isNull(entity))
            throw new BusinessServiceException("入库单不存在");

        return buildResponse(entity);
    }

    public StockoutCustomsDeclareStockinOrderResponse buildResponse(StockoutCustomsDeclareStockinOrderEntity entity) {
        StockoutCustomsDeclareStockinOrderResponse response = BeanUtil.toBean(entity, StockoutCustomsDeclareStockinOrderResponse.class);
        response.setStatus(CustomsDeclareStockinOrderStatusEnum.of(response.getStatus()));
        List<StockoutCustomsDeclareStockinOrderItemEntity> itemList = stockoutCustomsDeclareStockinOrderItemService.listByDeclareStockinOrderId(response.getId());
        int purchaseQty = itemList.stream().mapToInt(StockoutCustomsDeclareStockinOrderItemEntity::getPurchaseQty).sum();
        int receivedQty = itemList.stream().mapToInt(StockoutCustomsDeclareStockinOrderItemEntity::getReceivedQty).sum();
        int shelvedQty = itemList.stream().mapToInt(StockoutCustomsDeclareStockinOrderItemEntity::getShelvedQty).sum();
        response.setPurchaseQty(purchaseQty);
        response.setReceivedQty(receivedQty);
        response.setShelvedQty(shelvedQty);

        StockoutCustomsDeclarePurchaseOrderEntity purchaseOrderEntity = stockoutCustomsDeclarePurchaseOrderService.findByPurchaseOrderNo(entity.getPurchaseOrderNo());
        if (ObjectUtil.isNotNull(purchaseOrderEntity)) {
            response.setDeclarePurchaseOrderId(purchaseOrderEntity.getId());
        }
        return response;
    }


    /**
     * 新增
     *
     * @param purchaseOrderEntity
     * @param boList
     * @param form
     * @return
     */
    @Transactional
    public StockoutCustomsDeclareStockinOrderEntity add(StockoutCustomsDeclarePurchaseOrderEntity purchaseOrderEntity, List<StockoutCustomsDeclareDocumentItemBo> boList, StockoutCustomsDeclareFormEntity form) {
        if (CollectionUtil.isEmpty(boList)) {
            throw new BusinessServiceException(String.format("报关单据明细为空 %s %s ", form.getProtocolNo(), form.getgNo()));
        }

        StockoutCustomsDeclareStockinOrderEntity entity = findByPurchaseOrderNo(purchaseOrderEntity.getPurchaseOrderNo());
        if (ObjectUtil.isNull(entity)) {
            entity = BeanUtil.toBean(purchaseOrderEntity, StockoutCustomsDeclareStockinOrderEntity.class);
            entity.setStatus(CustomsDeclareStockinOrderStatusEnum.FINISHED.name());
            Date createDate = form.getStockinDate();
            String date = DateUtil.format(createDate, "yyyyMMdd").substring(2);
            String code = String.format("%06d", countByDate(createDate) + 1);
            entity.setStockinOrderNo("CGRK" + date + code);
            entity.setCreateBy(loginInfoService.getName());
            entity.setCreateDate(createDate);
            entity.setReceivedDate(entity.getCreateDate());
            entity.setShelvedDate(fetchShelvedDate(createDate));
            save(entity);
        }

        StockoutCustomsDeclareStockinOrderEntity finalEntity = entity;
        boList.forEach(bo -> stockoutCustomsDeclareStockinOrderItemService.add(finalEntity, bo));

        return entity;
    }

    /**
     * 查找今天创建的列表
     *
     * @return
     */
    private Integer countByDate(Date date) {
        String today = DateUtil.format(date, "yyyy-MM-dd");
        String begin = today + " 00:00:00";
        String end = today + " 23:59:59";
        return count(new LambdaQueryWrapper<StockoutCustomsDeclareStockinOrderEntity>()
                .ge(StockoutCustomsDeclareStockinOrderEntity::getCreateDate, begin)
                .le(StockoutCustomsDeclareStockinOrderEntity::getCreateDate, end));
    }


    /**
     * 通过 报关单据明细id删除
     *
     * @param purchaseOrderNo
     * @return
     */
    public void removeByPurchaseOrderNo(String purchaseOrderNo) {
        StockoutCustomsDeclareStockinOrderEntity declareStockinOrderEntity = findByPurchaseOrderNo(purchaseOrderNo);
        if (ObjectUtil.isNull(declareStockinOrderEntity)) {
            LOGGER.error("入库单不存在 {} ", purchaseOrderNo);
            return;
        }

        //删除主表
        removeById(declareStockinOrderEntity.getId());
        //删除明细
        stockoutCustomsDeclareStockinOrderItemService.removeByDeclareStockinOrderId(declareStockinOrderEntity.getId());

    }


    private Date fetchShelvedDate(Date receivedDate) {
        Date past = DateUtil.offsetDay(receivedDate, 1);

        // 随机生成9点到19点之间的一个时间
        int hour = RandomUtil.randomInt(9, 19); // 包含9和19
        int minute = RandomUtil.randomInt(0, 60);
        int second = RandomUtil.randomInt(0, 60);
        // 生成具体时间
        return DateUtil.parse(DateUtil.format(past, "yyyy-MM-dd") + " " + String.format("%02d:%02d:%02d", hour, minute, second));
    }

    /**
     * 通过采购单号查找
     *
     * @param purchaseOrderNo
     * @return
     */
    private StockoutCustomsDeclareStockinOrderEntity findByPurchaseOrderNo(String purchaseOrderNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclareStockinOrderEntity>()
                .eq(StockoutCustomsDeclareStockinOrderEntity::getPurchaseOrderNo, purchaseOrderNo)
                .last("limit 1"));
    }

}
