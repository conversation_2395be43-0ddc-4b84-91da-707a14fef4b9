package com.nsy.wms.business.service.stockout;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentAggregatedItemResult;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormSystemMarkEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormMatchSupplierRequest;
import com.nsy.api.wms.response.common.CommonResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareManualMatchResultBo;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierTaxInvoicedItemInvoiceRequest;
import com.nsy.wms.business.manage.scm.response.SupplierTaxInvoicedItemInvoiceDto;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsClearRequest;
import com.nsy.wms.business.service.bd.BdCompanyService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdCompanyEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareDocumentItemMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 关单匹配
 */
@Service
public class StockoutCustomsDeclareFormMatchService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareFormMatchService.class);

    @Autowired
    StockoutCustomsDeclareFormItemService formItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutCustomsDeclareDocumentItemMapper documentItemMapper;
    @Autowired
    StockoutCustomsDeclareDocumentService declareDocumentService;
    @Autowired
    StockoutCustomsDeclareFormService formService;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    StockoutCustomsDeclareFormLogService logService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    BdCompanyService companyService;
    @Resource
    SupplierApiService supplierApiService;
    @Resource
    StockoutCustomsDeclareDocumentAggregatedItemService declareDocumentAggregatedItemService;


    /**
     * 批量匹配供应商
     * <p>
     * 1.查找所有待处理的关单 而且含税金额大于0
     * 2.到scm去自动匹配供应商，数量 = 总数量 - 人工拆分数量
     * 3.匹配成功
     */
    @Transactional
    public void batchMatchSupplier() {
        //1.查找所有待处理的关单 而且含税金额大于0
        List<StockoutCustomsDeclareFormEntity> formList = formService.list(new LambdaQueryWrapper<StockoutCustomsDeclareFormEntity>()
                .eq(StockoutCustomsDeclareFormEntity::getStatus, StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name())
                .gt(StockoutCustomsDeclareFormEntity::getTaxInclusivePrice, 0));

        autoMatchSupplier(formList);
    }

    @Transactional
    public void batchMatchSupplier(List<Integer> formIdList) {
        List<StockoutCustomsDeclareFormEntity> formList = formService.listByIds(formIdList);
        formList.forEach(form -> {
            if (!StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(form.getStatus()))
                throw new BusinessServiceException(String.format("关单 %s  项 %s 非待处理", form.getDeclareDocumentNo(), form.getgNo()));
            if (BigDecimal.ZERO.compareTo(form.getTaxInclusivePrice()) > 0)
                throw new BusinessServiceException(String.format("关单 %s  项 %s 含税金额应大于0", form.getDeclareDocumentNo(), form.getgNo()));
        });

        autoMatchSupplier(formList);
    }

    private void autoMatchSupplier(List<StockoutCustomsDeclareFormEntity> formList) {
        //自动匹配供应商需考虑公司抬头优先级
        Map<Integer, BdCompanyEntity> companyMap = companyService.getEnableList().stream().collect(Collectors.toMap(BdCompanyEntity::getCompanyId, Function.identity()));
        //2.到scm去自动匹配供应商，数量 = 总数量 - 人工拆分数量
        List<SupplierTaxInvoicedItemInvoiceRequest> requestList = formList.stream()
                .filter(form -> !Objects.isNull(form.getCompanyId()) && !Objects.isNull(companyMap.get(form.getCompanyId())))
                .sorted(Comparator.comparingInt(o -> companyMap.get(o.getCompanyId()).getMatchPriority()))
                .map(form -> {
                    try {
                        return buildMatchSupplierRequest(form);
                    } catch (Exception e) {
                        LOGGER.error(e.getMessage(), e);
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());
        CollectionUtil.split(requestList, 50).forEach(splitList -> {
            List<SupplierTaxInvoicedItemInvoiceDto> responseList = scmApiService.invoiceBatch(splitList);
            // 3.匹配成功
            responseList.forEach(response -> {
                try {
                    SpringUtil.getBean(StockoutCustomsDeclareFormMatchService.class)
                            .matchSupplierComplete(response, StockoutCustomsDeclareFormService.MatchTypeResultEnum.AUTO);
                } catch (Exception e) {
                    LOGGER.error(e.getMessage(), e);
                    if (Objects.isNull(response.getBusinessId()))
                        return;
                    StockoutCustomsDeclareFormEntity formEntity = formService.getById(response.getBusinessId());
                    if (Objects.isNull(formEntity))
                        return;
                    logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.MATCH_SUPPLIER, String.format("失败：%s", e.getMessage()));
                }
            });
        });
    }

    public CommonResponse matchSupplierManualWithResp(StockoutCustomsDeclareFormMatchSupplierRequest supplierRequest) {
        CommonResponse response = new CommonResponse();
        response.setResult(2);
        List<StockoutCustomsDeclareManualMatchResultBo> resultBoList = SpringUtil.getBean(StockoutCustomsDeclareFormMatchService.class)
                .matchSupplierManual(supplierRequest);
        //部分失败
        if (supplierRequest.getDeclareFormIdList().size() != resultBoList.size()) {
            response.setResult(1);
        }
        //全失败
        if (supplierRequest.getDeclareFormIdList().size() == resultBoList.size()) {
            response.setResult(0);
        }
        response.setMessage(resultBoList.stream().map(StockoutCustomsDeclareManualMatchResultBo::getErrMsg).collect(Collectors.joining(";")));
        return response;
    }

    /**
     * 手动匹配供应商
     * <p>
     * 1. 找出关单，判断关单是否【待处理】,或者【已处理】且重新选择供应商
     * 2. 到scm去自动匹配供应商，数量 = 总数量 - 人工拆分数量
     * 3. 匹配成功   0  -- 清空供应商
     *
     * @param supplierRequest
     */
    @Transactional
    public List<StockoutCustomsDeclareManualMatchResultBo> matchSupplierManual(StockoutCustomsDeclareFormMatchSupplierRequest supplierRequest) {
        if (Objects.isNull(supplierRequest.getSupplierId()))
            throw new BusinessServiceException("供应商不能为空");
        List<StockoutCustomsDeclareManualMatchResultBo> resultList = new ArrayList<>();
        List<SupplierTaxInvoicedItemInvoiceRequest> requestList = supplierRequest.getDeclareFormIdList().stream().map(formId -> {
            StockoutCustomsDeclareFormEntity form = formService.findById(formId);
            try {
                return buildMatchSupplierRequest(form, supplierRequest.getSupplierId());
            } catch (Exception e) {
                LOGGER.error(String.format("buildMatchSupplierRequest：%s", e.getMessage()), e);
                resultList.add(new StockoutCustomsDeclareManualMatchResultBo(formId, e.getMessage()));
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(requestList)) {
            return resultList;
        }

        CollectionUtil.split(requestList, 50).forEach(splitRequestList -> {
            List<SupplierTaxInvoicedItemInvoiceDto> responseList = scmApiService.invoiceOne(requestList);
            // 3.匹配成功
            responseList.forEach(responseItem -> {
                try {
                    SpringUtil.getBean(StockoutCustomsDeclareFormMatchService.class)
                            .matchSupplierComplete(responseItem, StockoutCustomsDeclareFormService.MatchTypeResultEnum.MANUAL);
                } catch (Exception e) {
                    LOGGER.error(String.format("matchSupplierComplete：%s", e.getMessage()), e);
                    resultList.add(new StockoutCustomsDeclareManualMatchResultBo(responseItem.getBusinessId(), e.getMessage()));
                }
            });
        });

        return resultList;
    }


    /**
     * * 3.匹配到的，
     * * a. 回填供应商，进项明细
     * * b. 关单变成已处理
     * * c. 发送supplier同步关单与供应商匹配数据
     *
     * @param response
     * @param matchType
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void matchSupplierComplete(SupplierTaxInvoicedItemInvoiceDto response, StockoutCustomsDeclareFormService.MatchTypeResultEnum matchType) {
        if (Objects.isNull(response.getBusinessId())) {
            throw new BusinessServiceException(String.format("businessId为空： %s ", JSONUtil.toJsonStr(response)));
        }
        StockoutCustomsDeclareFormEntity formEntity = formService.getById(response.getBusinessId());
        if (Objects.isNull(formEntity)) {
            throw new BusinessServiceException(String.format("找不到关单： %s ", response.getBusinessId()));
        }
        if (!response.isSuccess()) {
            throw new BusinessServiceException(String.format(" %s 匹配失败 %s ： %s ", matchType.getDesc(), formEntity.getDeclareFormId(), response.getErrMsg()));
        }

        Integer supplierId = formEntity.getSupplierId();

        // 供应商置空
        if (0 == response.getSupplierId()) {
            //清除关单，回退待处理
            clearSupplier(formEntity);
        } else {
            //a. 回填供应商，进项明细
            //b. 关单变成待审核
            matchSupplierSucceed(formEntity, response, matchType);
        }

        //c. 发送supplier清空供应商数据（审核完会重新推送）
        if (ObjectUtil.isNotNull(supplierId)) {
            StockoutCustomsDeclareFormWmsClearRequest request = new StockoutCustomsDeclareFormWmsClearRequest();
            request.setSupplierId(supplierId);
            request.setFormId(formEntity.getDeclareFormId());
            request.setOperator(loginInfoService.getName());
            supplierApiService.customsDeclareFormClear(request);
        }
    }

    /**
     * 清除关单，回退待处理
     *
     * @param formEntity
     */
    private void clearSupplier(StockoutCustomsDeclareFormEntity formEntity) {
        // 关单变成待处理 清空数据
        formService.update(new LambdaUpdateWrapper<StockoutCustomsDeclareFormEntity>()
                .set(StockoutCustomsDeclareFormEntity::getSupplierId, null)
                .set(StockoutCustomsDeclareFormEntity::getSupplierName, null)
                .set(StockoutCustomsDeclareFormEntity::getStockinDate, null)
                .set(StockoutCustomsDeclareFormEntity::getStatus, StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name())
                .set(StockoutCustomsDeclareFormEntity::getShouldChooseOtherSupplier, Boolean.FALSE)
                .set(StockoutCustomsDeclareFormEntity::getSystemMark, null)
                .set(StockoutCustomsDeclareFormEntity::getMatchDate, null)
                .set(StockoutCustomsDeclareFormEntity::getDeliveryDate, null)
                .eq(StockoutCustomsDeclareFormEntity::getDeclareFormId, formEntity.getDeclareFormId()));
        //删除明细
        formItemService.update(new LambdaUpdateWrapper<StockoutCustomsDeclareFormItemEntity>()
                .set(StockoutCustomsDeclareFormItemEntity::getIsDeleted, Boolean.TRUE)
                .eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, formEntity.getDeclareFormId()));

        logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.MATCH_SUPPLIER, "供应商清除");
    }

    /**
     * 匹配供应商成功
     *
     * @param formEntity
     * @param response
     * @param matchType
     */
    private void matchSupplierSucceed(StockoutCustomsDeclareFormEntity formEntity, SupplierTaxInvoicedItemInvoiceDto response, StockoutCustomsDeclareFormService.MatchTypeResultEnum matchType) {
        formEntity.setSupplierId(response.getSupplierId());
        formEntity.setSupplierName(response.getSupplierName());
        formEntity.setStatus(StockoutCustomsDeclareFormStatusEnum.WAIT_AUDIT.name());
        formEntity.setShouldChooseOtherSupplier(Boolean.FALSE);
        formEntity.setMatchDate(new Date());
        if (StockoutCustomsDeclareFormService.MatchTypeResultEnum.AUTO.name().equals(matchType.name())) {
            formEntity.setSystemMark(StockoutCustomsDeclareFormSystemMarkEnum.SYSTEM.name());
        } else {
            formEntity.setSystemMark(StockoutCustomsDeclareFormSystemMarkEnum.MANUAL.name());
        }

        formService.updateById(formEntity);

        //删除明细
        List<StockoutCustomsDeclareFormItemEntity> oldFormItemList = formItemService.itemList(formEntity.getDeclareFormId());
        if (!oldFormItemList.isEmpty()) {
            formItemService.update(new LambdaUpdateWrapper<StockoutCustomsDeclareFormItemEntity>()
                    .set(StockoutCustomsDeclareFormItemEntity::getIsDeleted, Boolean.TRUE)
                    .eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, formEntity.getDeclareFormId()));

            logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.MATCH_SUPPLIER, "重新匹配供应商，清除进项明细");
        }


        List<StockoutCustomsDeclareFormItemEntity> itemList = response.getItem().stream().map(item -> {
            StockoutCustomsDeclareFormItemEntity itemEntity = new StockoutCustomsDeclareFormItemEntity();
            BeanUtilsEx.copyProperties(item, itemEntity);
            itemEntity.setIsDeleted(Boolean.FALSE);
            itemEntity.setIsManual(Boolean.FALSE);
            itemEntity.setTaxItemId(item.getInvoicedItem());
            itemEntity.setDeclareFormId(response.getBusinessId());
            return itemEntity;
        }).collect(Collectors.toList());
        formItemService.saveBatch(itemList);

        logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.MATCH_SUPPLIER, String.format("%s供应商 【%s】", matchType.getDesc(), response.getSupplierName()));
    }


    /**
     * 构建请求对象
     *
     * @param form
     * @return
     */
    private SupplierTaxInvoicedItemInvoiceRequest buildMatchSupplierRequest(StockoutCustomsDeclareFormEntity form) {
        List<StockoutCustomsDeclareFormItemEntity> formItemList = formItemService.itemListManual(form.getDeclareFormId());
        BigDecimal manualPrice = formItemList.stream()
                .map(StockoutCustomsDeclareFormItemEntity::getTaxInclusivePrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        SupplierTaxInvoicedItemInvoiceRequest request = new SupplierTaxInvoicedItemInvoiceRequest();
        BeanUtilsEx.copyProperties(form, request);
        request.setLimitDate(DateUtil.offsetDay(form.getStockoutDate(), -40));
        request.setBusinessId(form.getDeclareFormId());
        request.setInputQtyCount(form.getgQty() - form.getTotalManualInputQty());
        request.setOperatorName(loginInfoService.getName());
        request.setCategoryId(form.getWmsCategoryId());
        request.setTaxInclusivePrice(request.getTaxInclusivePrice().subtract(manualPrice));

        if (StrUtil.isEmpty(form.getgNo()))
            throw new BusinessServiceException(String.format("关单 %s 项号为空", form.getDeclareFormId()));
        if (!NumberUtil.isNumber(form.getgNo()))
            throw new BusinessServiceException(String.format("关单 %s 项号不为数字", form.getDeclareFormId()));
        int gNo = Integer.parseInt(form.getgNo());
        if (gNo <= 0)
            throw new BusinessServiceException(String.format("关单 %s 项号必须大于0", form.getDeclareFormId()));
        StockoutCustomsDeclareDocumentEntity document = declareDocumentService.getByDeclareDocumentNo(form.getProtocolNo());
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList = declareDocumentAggregatedItemService.buildResultList(document);
        if (gNo > aggregatedItemResultList.size())
            throw new BusinessServiceException(String.format("关单 %s 项号必须小于单据明细数量 %s", form.getDeclareFormId(), aggregatedItemResultList.size()));
        Map<Integer, StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultMap = aggregatedItemResultList.stream().collect(Collectors.toMap(StockoutCustomsDeclareDocumentAggregatedItemResult::getgNo, Function.identity()));
        StockoutCustomsDeclareDocumentAggregatedItemResult stockoutCustomsDeclareDocumentAggregatedItemResult = aggregatedItemResultMap.get(gNo);
        request.setSpinType(stockoutCustomsDeclareDocumentAggregatedItemResult.getSpinType());
        request.setFabricType(stockoutCustomsDeclareDocumentAggregatedItemResult.getFabricType().replaceAll("[\\d%]", ""));
        return request;
    }

    private SupplierTaxInvoicedItemInvoiceRequest buildMatchSupplierRequest(StockoutCustomsDeclareFormEntity form, Integer supplierId) {
        //【待处理】的关单，供应商不能清空
        if (supplierId == 0 && StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(form.getStatus())) {
            throw new BusinessServiceException(String.format("%s %s ，【待处理】的关单，供应商不能清空", form.getProtocolNo(), form.getgNo()));
        }
        if (supplierId.equals(form.getSupplierId())) {
            throw new BusinessServiceException(String.format("%s %s ，供应商没有改变", form.getProtocolNo(), form.getgNo()));
        }
        //1. 找出关单，判断关单是否【待处理】,或者【已处理】且重新选择供应商
        if (!StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(form.getStatus())
                && !StockoutCustomsDeclareFormStatusEnum.DEALT.name().equals(form.getStatus())
                && !StockoutCustomsDeclareFormStatusEnum.WAIT_AUDIT.name().equals(form.getStatus())) {
            throw new BusinessServiceException(String.format("%s %s ，当前关单非【待处理】【待审核】【已处理】", form.getProtocolNo(), form.getgNo()));
        }
        //2.到scm去自动匹配供应商，数量 = 总数量 - 人工拆分数量
        SupplierTaxInvoicedItemInvoiceRequest request = buildMatchSupplierRequest(form);
        request.setSupplierId(supplierId);
        return request;
    }
}

