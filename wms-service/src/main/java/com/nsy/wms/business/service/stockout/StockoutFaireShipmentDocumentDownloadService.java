package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stockout.FaireShipmentDocumentExport;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutFaireShipmentStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentSearchRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentExportList;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.repository.entity.stockout.StockoutFaireShipmentEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutFaireShipmentMapper;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StockoutFaireShipmentDocumentDownloadService implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutFaireShipmentDocumentDownloadService.class);

    @Autowired
    StockoutFaireShipmentService faireShipmentService;
    @Autowired
    StockoutFaireShipmentMapper faireShipmentMapper;
    @Autowired
    StockoutReceiverInfoService receiverInfoService;
    @Autowired
    StockoutFaireShipmentItemService faireShipmentItemService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_FAIRE_SHIPMENT_DOCUMENT;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        IdListRequest idListRequest = JSONObject.parseObject(request.getRequestContent(), IdListRequest.class);
        List<Integer> idList = idListRequest.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(request.getRequestContent());
            StockoutFaireShipmentSearchRequest searchRequest = jsonObject.get("searchRequest", StockoutFaireShipmentSearchRequest.class);
            idList = faireShipmentMapper.getSearchShipmentIds(searchRequest);
        }
        if (CollectionUtils.isEmpty(idList)) {
            throw new BusinessServiceException("idList不可为空");
        }
        List<String> faireStatusList = new ArrayList<>();
        faireStatusList.add(StockoutFaireShipmentStatusEnum.PACKING_END.name());
        faireStatusList.add(StockoutFaireShipmentStatusEnum.READY_DELIVERY.name());
        faireStatusList.add(StockoutFaireShipmentStatusEnum.SHIPPED.name());
        List<StockoutFaireShipmentEntity> faireShipmentEntityList = faireShipmentService.list(new LambdaQueryWrapper<StockoutFaireShipmentEntity>()
                .in(StockoutFaireShipmentEntity::getId, idList)
                .in(StockoutFaireShipmentEntity::getStatus, faireStatusList)
                .orderByAsc(StockoutFaireShipmentEntity::getBoxCode, StockoutFaireShipmentEntity::getBoxIndex));
        if (CollectionUtils.isEmpty(faireShipmentEntityList))
            return response;

        // 构建导出的faire id 集合
        List<Integer> exportIdList = new ArrayList<>();
        // 导出的数据集合
        List<FaireShipmentDocumentExport> resultList = new ArrayList<>();
        faireShipmentEntityList.forEach(o -> {
            if (o.getIsMerge() != null && o.getIsMerge() == 1) {
                // 合并箱处理
                exportIdList.addAll(getSubIdList(o));
            } else {
                exportIdList.add(o.getId());
            }
        });
        // 普通箱处理
        if (!CollectionUtils.isEmpty(exportIdList)) {
            resultList.addAll(buildByIdList(exportIdList));
        }
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        response.setTotalCount((long) resultList.size());
        return response;
    }

    private List<Integer> getSubIdList(StockoutFaireShipmentEntity mergeFaireEntity) {
        return faireShipmentService.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, mergeFaireEntity.getId()))
                .stream().map(StockoutFaireShipmentEntity::getId).collect(Collectors.toList());
    }

//    private FaireShipmentDocumentExport buildByMergeFaire(StockoutFaireShipmentEntity mergeFaireEntity) {
//        List<Integer> subIdList = faireShipmentService.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, mergeFaireEntity.getId()))
//                .stream().map(StockoutFaireShipmentEntity::getId).collect(Collectors.toList());
//        FaireShipmentDocumentExport result = new FaireShipmentDocumentExport();
//        result.setCustomerCode("000038");
//        // 取其中一个子箱的出库单号
//        List<StockoutFaireShipmentItemEntity> subFaireItemList = faireShipmentItemService.list(new QueryWrapper<StockoutFaireShipmentItemEntity>().lambda()
//                .eq(StockoutFaireShipmentItemEntity::getFaireShipmentId, subIdList.get(0))
//                .eq(StockoutFaireShipmentItemEntity::getIsDelete, 0));
//        Map<String, List<StockoutFaireShipmentExportList>> orderFaireMap = faireShipmentMapper.getExportListByIds(subIdList).stream().collect(Collectors.groupingBy(StockoutFaireShipmentExportList::getStockoutOrderNo));
//        String stockoutOrderNo = subFaireItemList.get(0).getStockoutOrderNo();
//        result.setOrderNo(stockoutOrderNo);
//        result.setBoxQty(orderFaireMap.get(stockoutOrderNo).size());
//        BigDecimal totalWeight = orderFaireMap.get(stockoutOrderNo).stream().map(StockoutFaireShipmentExportList::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
//        result.setWeight(totalWeight);
//        setInfoByStockoutReceive(result, stockoutOrderNo);
//        return result;
//    }

    private List<FaireShipmentDocumentExport> buildByIdList(List<Integer> exportIdList) {
        Map<String, List<StockoutFaireShipmentExportList>> orderFaireMap = faireShipmentMapper.getExportListByIds(exportIdList).stream().collect(Collectors.groupingBy(StockoutFaireShipmentExportList::getStockoutOrderNo));
        List<FaireShipmentDocumentExport> resultList = new ArrayList<>(orderFaireMap.size());
        for (Map.Entry<String, List<StockoutFaireShipmentExportList>> entry : orderFaireMap.entrySet()) {
            FaireShipmentDocumentExport item = new FaireShipmentDocumentExport();
            item.setCustomerCode("000038");
            // 出库单号
            item.setOrderNo(entry.getKey());
            item.setBoxQty(entry.getValue().size());
            BigDecimal totalWeight = entry.getValue().stream().map(StockoutFaireShipmentExportList::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setWeight(totalWeight);
            entry.getValue().stream().filter(it -> StrUtil.isNotBlank(it.getBoxSize()) && !StrUtil.equals(it.getBoxSize(), "袋子")).findFirst().ifPresent(it -> {
                String[] packageDimensions = it.getBoxSize().split("\\*");
                if (packageDimensions.length >= 3) {
                    item.setLength(packageDimensions[0]);
                    item.setWidth(packageDimensions[1]);
                    item.setHeight(packageDimensions[2]);
                }
            });
            setInfoByStockoutReceive(item, entry.getKey());
            BigDecimal totalInvoicePrice = faireShipmentMapper.getProductInvoicePrice(entry.getKey());
            item.setProductInvoicePrice(totalInvoicePrice);
            resultList.add(item);
        }

        return resultList;
    }

    private void setInfoByStockoutReceive(FaireShipmentDocumentExport item, String stockoutOrderNo) {
        try {
            // 收货渠道
            StockoutReceiverInfo receiverInfo = receiverInfoService.getReceiveInfoByStockoutOrderNo(stockoutOrderNo);
            if (StringUtils.hasText(receiverInfo.getReceiverZip())) {
                int startZip = Integer.parseInt(receiverInfo.getReceiverZip().substring(0, 1));
                if (startZip >= 0 && startZip <= 4) {
                    item.setReceivingChannel("FDX_East_H_Client");
                } else if (startZip > 4 && startZip <= 7) {
                    item.setReceivingChannel("FDX_Mid_H_Client");
                } else {
                    item.setReceivingChannel("FDX_West_H_Client");
                }
            }
            item.setCountry("美国");
            item.setProvince(receiverInfo.getReceiverState());
            item.setCity(receiverInfo.getReceiverCity());
            item.setZip(receiverInfo.getReceiverZip());
            item.setReceiveCompany(receiverInfo.getReceiverName());
            item.setReceiveName(receiverInfo.getReceiverName());
            item.setReceivePhone(StringUtils.hasText(receiverInfo.getReceiverMobile()) ? receiverInfo.getReceiverMobile() : receiverInfo.getReceiverPhone());
            item.setReceiveAddress1(receiverInfo.getReceiverAddress());
            item.setBagType("WPX");
            item.setDeclareType("其他");
            item.setPayTaxes("收件人");
        } catch (Exception ex) {
            LOGGER.error("执行异常： {}", ex.getMessage());
            throw new BusinessServiceException(String.format("当前出库单【%s】,设置收件人信息失败", stockoutOrderNo), ex);
        }
    }

}
