package com.nsy.wms.business.service.stockin;

import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSkuPageRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-18 14:57
 */
@Service
public class StockinHeightRecordSkuMappingExportService implements IDownloadService {

    @Autowired
    private StockinHeightRecordMappingService stockinHeightRecordMappingService;


    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_HEIGHT_RECORD_SKU_MAPPING_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        StockinVolumeWeightRecordMappingSkuPageRequest dateRequest = JsonMapper.fromJson(request.getRequestContent(), StockinVolumeWeightRecordMappingSkuPageRequest.class);
        dateRequest.setPageIndex(request.getPageIndex());
        dateRequest.setPageSize(request.getPageSize());
        PageResponse<StockinVolumeWeightRecordMappingSkuPageResponse> pageResult = stockinHeightRecordMappingService.getPageSkuList(dateRequest);
        DownloadResponse response = new DownloadResponse();
        response.setDataJsonStr(JsonMapper.toJson(pageResult.getContent()));
        response.setTotalCount(pageResult.getTotalCount());
        return response;
    }
}
