package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.domain.stockin.QcInboundsPriceItem;
import com.nsy.api.wms.request.qa.StockinQaOperateSecondAuditRequest;
import com.nsy.wms.repository.entity.qa.StockinQaOrderConcessionRecevieEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderConcessionRecevieMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单让步接收表业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderConcessionRecevieService extends ServiceImpl<StockinQaOrderConcessionRecevieMapper, StockinQaOrderConcessionRecevieEntity> {

    @Autowired
    private StockinQaOrderSearchService stockinQaOrderSearchService;
    @Autowired
    private StockinQaOrderItemService stockinQaOrderItemService;
    @Autowired
    private StockinQaOrderDetailService qaOrderDetailService;

    public List<StockinQaOrderConcessionRecevieEntity> listByStockinQaOrderId(Integer stockinQaOrderId) {
        return this.list(new LambdaQueryWrapper<StockinQaOrderConcessionRecevieEntity>()
                .eq(StockinQaOrderConcessionRecevieEntity::getStockinQaOrderId, stockinQaOrderId));
    }

    /**
     * 删除让步接受数据
     *
     * @param stockinQaOrderId
     */
    public void removeConcessionByStockinQaOrderId(Integer stockinQaOrderId) {
        List<StockinQaOrderConcessionRecevieEntity> entityList = this.listByStockinQaOrderId(stockinQaOrderId);
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        //如果原先有让步接受数据需要先删除，保存新的
        this.removeByIds(entityList.stream().map(StockinQaOrderConcessionRecevieEntity::getId).collect(Collectors.toList()));
        qaOrderDetailService.clearConcessions(stockinQaOrderId);
    }

    public void saveConcessionRecevieEntity(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateSecondAuditRequest request) {
        List<StockinQaOrderConcessionRecevieEntity> entityList = this.listByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        //如果原先有让步接受数据需要先删除，保存新的
        if (CollectionUtils.isNotEmpty(entityList)) {
            this.removeByIds(entityList.stream().map(StockinQaOrderConcessionRecevieEntity::getId).collect(Collectors.toList()));
        }
        List<QcInboundsPriceItem> priceList = request.getPriceList();
        if (CollectionUtils.isEmpty(priceList)) {
            //如果不存在，需要按默认自己组装
            List<String> purchasePlanNos = stockinQaOrderItemService.getBaseMapper().getPurchasePlanNoByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
            List<QcInboundsPriceItem> qcInboundsPriceItems = stockinQaOrderSearchService.buildPriceItemList(stockinQaOrderEntity, purchasePlanNos);

            qcInboundsPriceItems.forEach(item -> item.setConcessionsPrice(item.getDiscount().multiply(item.getPurchasePrice())));
            priceList = qcInboundsPriceItems;
            request.setPriceList(qcInboundsPriceItems);
        }

        List<StockinQaOrderConcessionRecevieEntity> collect = priceList.stream().map(item -> {
            StockinQaOrderConcessionRecevieEntity recevieEntity = new StockinQaOrderConcessionRecevieEntity();
            recevieEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
            recevieEntity.setPurchaseOrderNo(item.getPurchasePlanNo());
            recevieEntity.setLocation(stockinQaOrderEntity.getLocation());
            recevieEntity.setDiscount(item.getDiscount());
            recevieEntity.setPurchasePrice(item.getPurchasePrice());
            recevieEntity.setConcessionsPrice(item.getConcessionsPrice());
            return recevieEntity;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }
}
