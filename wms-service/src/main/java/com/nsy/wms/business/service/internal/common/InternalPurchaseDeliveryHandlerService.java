package com.nsy.wms.business.service.internal.common;

import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.business.base.constant.KafkaTopicConstant;
import com.nsy.business.base.enums.etl.BusinessTypeEnum;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/2 10:31
 */
@Service
public class InternalPurchaseDeliveryHandlerService implements StockoutPushHandlerStage {

    @Autowired
    CommonBaseService commonBaseService;
    @Autowired
    StockoutShipmentItemService stockoutShipmentItemService;

    @Override
    public String getHandlerType() {
        return StockoutOrderTypeEnum.INTERNAL_PURCHASE_DELIVERY.name();
    }

    @Override
    public void handlerOrder(StockoutOrderEntity stockoutOrderEntity, StockoutShipmentEntity shipmentEntity) {
        // 销售出库发货=》销售出库单
        if (!stockoutOrderEntity.getLocation().equals(LocationEnum.QUANZHOU.name())) {
            return;
        }
        commonBaseService.sendEtlCommonMessage(shipmentEntity.getShipmentBoxCode(), BusinessTypeEnum.QZ_STOCK_OUT_ORDER, CommonBaseService.STOCKOUT_ORDER_CALLBACK_URL,
                KafkaConstant.WMS_STOCK_OUT_ORDER_SHIPMENTS_NAME, KafkaTopicConstant.WMS_STOCK_OUT_ORDER_SHIPMENTS_TOPIC);

    }
}
