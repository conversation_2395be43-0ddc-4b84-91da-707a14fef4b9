package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.domain.qa.StockinQaProductSampleRecordPageDto;
import com.nsy.api.wms.domain.qc.ProductCategoryDetail;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaProductSampleRecordStatusEnum;
import com.nsy.api.wms.request.base.LogListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.qa.StockinQaTaskPageRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.LogListResponse;
import com.nsy.api.wms.response.qa.StockinQaTaskItemPageResponse;
import com.nsy.api.wms.response.qa.StockinQaTaskPageExport;
import com.nsy.api.wms.response.qa.StockinQaTaskPageResponse;
import com.nsy.api.wms.response.qa.StockinQaTaskStatisticsResponse;
import com.nsy.api.wms.response.stockin.StockinArrivalInfoResponse;
import com.nsy.wms.business.manage.product.ProductApiService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 入库质检任务列表查询相关接口
 * @author: caishaohui
 * @time: 2024/11/18 11:02
 */
@Service
public class StockinQaTaskSearchService extends ServiceImpl<StockinQaTaskMapper, StockinQaTaskEntity> implements IDownloadService {

    @Autowired
    private StockinQaTaskItemService stockinQaTaskItemService;
    @Autowired
    private StockinQaOrderService stockinQaOrderService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private StockinQaOrderLogService stockinQaOrderLogService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private StockinQaProductSampleRecordService recordService;
    @Autowired
    private ProductApiService productApiService;
    @Autowired
    private StockinQaOrderProcessService stockinQaOrderProcessService;
    @Autowired
    private StockinQaOrderSkuTypeInfoService stockinQaOrderSkuTypeInfoService;

    public PageResponse<StockinQaTaskPageResponse> pageList(StockinQaTaskPageRequest request) {

        PageResponse<StockinQaTaskPageResponse> response = new PageResponse<>();

        Page<StockinQaTaskPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        IPage<StockinQaTaskPageResponse> pageList = this.getBaseMapper().pageList(page, request);
        List<StockinQaTaskPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setContent(pageList.getRecords());
            response.setTotalCount(page.getTotal());
            return response;
        }

        List<Integer> taskIds = records.stream().map(StockinQaTaskPageResponse::getTaskId).collect(Collectors.toList());
        Map<Integer, List<StockinQaTaskItemEntity>> itemMap = stockinQaTaskItemService.list(new LambdaQueryWrapper<StockinQaTaskItemEntity>()
                        .select(StockinQaTaskItemEntity::getQty, StockinQaTaskItemEntity::getPackageName,
                                StockinQaTaskItemEntity::getTaskId, StockinQaTaskItemEntity::getBrandName,
                                StockinQaTaskItemEntity::getSupplierDeliveryNo)
                        .in(StockinQaTaskItemEntity::getTaskId, taskIds))
                .stream().collect(Collectors.groupingBy(StockinQaTaskItemEntity::getTaskId));
        Map<Integer, List<StockinQaOrderEntity>> orderMap = stockinQaOrderService.list(new LambdaQueryWrapper<StockinQaOrderEntity>()
                        .in(StockinQaOrderEntity::getTaskId, taskIds).orderByDesc(StockinQaOrderEntity::getCreateDate))
                .stream().collect(Collectors.groupingBy(StockinQaOrderEntity::getTaskId));
        Map<Integer, Date> lastProcessDateMap = stockinQaOrderProcessService.getLastProcessDateInfo(orderMap.values().stream().flatMap(List::stream).map(StockinQaOrderEntity::getStockinQaOrderId).filter(Objects::nonNull).collect(Collectors.toList()),
                StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus());

        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = stockinQaOrderSkuTypeInfoService.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>()
                .select(StockinQaOrderSkuTypeInfoEntity::getSkuType, StockinQaOrderSkuTypeInfoEntity::getTaskId).in(StockinQaOrderSkuTypeInfoEntity::getTaskId, taskIds)).stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getTaskId));

        List<StockinQaProductSampleRecordPageDto> sampleRecordList = recordService.getSamplerInfoByTaskId(taskIds);
        Map<Integer, StockinQaProductSampleRecordPageDto> sampleRecordMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(sampleRecordList)) {
            sampleRecordMap = sampleRecordList.stream().collect(Collectors.toMap(StockinQaProductSampleRecordPageDto::getTaskId, Function.identity(), (v1, v2) -> v1));
        }
        Map<String, String> taskStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_TASK_STATUS.getName());
        Map<String, String> internalBoxTypeMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName());
        Map<String, String> processStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName());
        for (StockinQaTaskPageResponse entity : records) {
            List<StockinQaTaskItemEntity> taskItemEntityList = itemMap.get(entity.getTaskId());
            Map<String, List<StockinQaTaskItemEntity>> collect = taskItemEntityList.stream().filter(item -> StringUtils.hasText(item.getPackageName())).collect(Collectors.groupingBy(StockinQaTaskItemEntity::getPackageName));
            entity.setPackageName(collect.entrySet().stream().map(entry -> String.format("%s(%s)", entry.getKey(), entry.getValue().stream().mapToInt(StockinQaTaskItemEntity::getQty).sum())).collect(Collectors.joining(",")));
            entity.setBrandName(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getBrandName).filter(StringUtils::hasText).findAny().orElse(""));
            entity.setBoxQty(taskItemEntityList.stream().mapToInt(StockinQaTaskItemEntity::getQty).sum());
            StockinArrivalInfoResponse arrivalInfo = stockinOrderItemService.getBaseMapper().getArrivalInfo(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getSupplierDeliveryNo).collect(Collectors.toList()), entity.getSku());
            if (Objects.nonNull(arrivalInfo)) {
                entity.setArrivalCount(arrivalInfo.getQty());
                entity.setStockinUserName(arrivalInfo.getStockinUserName());
            }
            
            entity.setIsReturnOrder(taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getPurchasingApplyType())
                    && item.getPurchasingApplyType().equals(6)) ? 1 : 0);

            entity.setCheckStatusStr(taskStatusMap.get(entity.getCheckStatus()));
            entity.setInternalBoxType(internalBoxTypeMap.get(entity.getInternalBoxType()));
            List<StockinQaOrderEntity> stockinQaOrderEntities = orderMap.get(entity.getTaskId());
            if (!CollectionUtils.isEmpty(stockinQaOrderEntities)) {
                StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderEntities.get(0);
                entity.setCompleteDate(stockinQaOrderEntity.getCompleteDate());
                entity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
                entity.setQcUserName(stockinQaOrderEntity.getQcUserName());
                entity.setProcessStatus(stockinQaOrderEntity.getProcessStatus());
                entity.setProcessStatusStr(processStatusMap.get(stockinQaOrderEntity.getProcessStatus()));
                entity.setFirstAuditEndDate(lastProcessDateMap.get(stockinQaOrderEntity.getStockinQaOrderId()));
            }
            List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = skuTypeMap.get(entity.getTaskId());
            if (!CollectionUtils.isEmpty(skuTypeInfoEntities)) {
                entity.setSkuTypeList(skuTypeInfoEntities.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList()));
            }
            //赋值产前样信息
            if (CollectionUtils.isEmpty(sampleRecordMap) || Objects.isNull(sampleRecordMap.get(entity.getTaskId()))) {
                continue;
            }
            entity.setRecordId(sampleRecordMap.get(entity.getTaskId()).getRecordId());
            entity.setProductSampleComplete(StockinQaProductSampleRecordStatusEnum.QC_COMPLETED.name().equalsIgnoreCase(sampleRecordMap.get(entity.getTaskId()).getStatus()) ? 1 : 0);
        }

        response.setContent(pageList.getRecords());
        response.setTotalCount(this.getBaseMapper().pageCount(request));
        return response;
    }

    /**
     * 统计到货数、箱内数
     *
     * @param request
     * @return
     */
    public StockinQaTaskStatisticsResponse statisticsQty(StockinQaTaskPageRequest request) {
        StockinQaTaskStatisticsResponse response = new StockinQaTaskStatisticsResponse();
        //统计箱内数
        Integer boxQtyTotal = this.getBaseMapper().statisticsQty(request);
        Integer arriveQtyTotal = this.getBaseMapper().statisticsArriveQty(request);
        Integer qaQtyTotal = this.getBaseMapper().statisticsQaQty(request);
        response.setBoxQtyTotal(boxQtyTotal != null ? boxQtyTotal : Integer.valueOf(0));
        response.setArrivalCountTotal(arriveQtyTotal != null ? arriveQtyTotal : Integer.valueOf(0));
        response.setQaQtyTotal(qaQtyTotal != null ? qaQtyTotal : Integer.valueOf(0));
        return response;
    }

    public StockinQaTaskPageResponse getDetail(Integer taskId) {
        StockinQaTaskEntity taskEntity = this.getById(taskId);
        StockinQaTaskPageResponse stockinQaTaskPageResponse = new StockinQaTaskPageResponse();
        BeanUtils.copyProperties(taskEntity, stockinQaTaskPageResponse);

        StockinQaOrderEntity qaOrderEntity = stockinQaOrderService.findTopByTaskId(taskId);
        if (Objects.nonNull(qaOrderEntity)) {
            stockinQaTaskPageResponse.setQcUserName(qaOrderEntity.getQcUserName());
            stockinQaTaskPageResponse.setCompleteDate(qaOrderEntity.getCompleteDate());
        }

        List<StockinQaTaskItemEntity> list = stockinQaTaskItemService.list(new LambdaQueryWrapper<StockinQaTaskItemEntity>()
                .select(StockinQaTaskItemEntity::getPurchasePlanNo, StockinQaTaskItemEntity::getSupplierDeliveryBoxCode,
                        StockinQaTaskItemEntity::getQty, StockinQaTaskItemEntity::getSupplierDeliveryNo)
                .eq(StockinQaTaskItemEntity::getTaskId, taskId));

        stockinQaTaskPageResponse.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(list.stream().map(StockinQaTaskItemEntity::getSupplierDeliveryNo).collect(Collectors.toList()), taskEntity.getSku()));
        stockinQaTaskPageResponse.setCheckStatusStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKIN_QA_TASK_STATUS.getName(), taskEntity.getCheckStatus()));

        stockinQaTaskPageResponse.setSupplierDeliveryBoxCodes(list.stream().map(StockinQaTaskItemEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.joining(",")));
        stockinQaTaskPageResponse.setPurchasePlanNos(list.stream().map(StockinQaTaskItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));
        stockinQaTaskPageResponse.setBoxQty(list.stream().mapToInt(StockinQaTaskItemEntity::getQty).sum());

        return stockinQaTaskPageResponse;
    }

    public List<StockinQaTaskItemPageResponse> getItemList(Integer taskId) {

        List<StockinQaTaskItemPageResponse> list = stockinQaTaskItemService.getBaseMapper().getItemList(taskId);
        if (CollectionUtils.isEmpty(list))
            return Collections.emptyList();

        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(list.get(0).getSku());

        return list.stream().collect(Collectors.groupingBy(StockinQaTaskItemPageResponse::getSupplierDeliveryNo))
                .entrySet().stream().map(entry -> {
                    List<StockinQaTaskItemPageResponse> value = entry.getValue();
                    StockinQaTaskItemPageResponse itemPageResponse = value.get(0);
                    StockinQaTaskItemPageResponse response = new StockinQaTaskItemPageResponse();
                    BeanUtils.copyProperties(itemPageResponse, response);
                    response.setBrandName(value.stream().map(item -> String.format("%s(%s)", item.getBrandName(), item.getQty())).collect(Collectors.joining(",")));
                    response.setPackageName(value.stream().map(item -> String.format("%s(%s)", item.getBrandName(), item.getQty())).collect(Collectors.joining(",")));

                    Map<String, List<StockinQaTaskItemPageResponse>> collect = value.stream().filter(item -> StringUtils.hasText(item.getPackageName())).collect(Collectors.groupingBy(StockinQaTaskItemPageResponse::getPackageName));
                    response.setPackageName(collect.entrySet().stream().map(entryItem -> String.format("%s(%s)", entryItem.getKey(), entryItem.getValue().stream().mapToInt(StockinQaTaskItemPageResponse::getQty).sum())).collect(Collectors.joining(",")));
                    response.setBrandName(value.stream().map(StockinQaTaskItemPageResponse::getBrandName).filter(StringUtils::hasText).findAny().orElse(""));

                    response.setBoxQty(value.stream().mapToInt(StockinQaTaskItemPageResponse::getQty).sum());
                    response.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
                    response.setImageUrl(productSpecInfoEntity.getImageUrl());
                    response.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
                    response.setBarcode(productSpecInfoEntity.getBarcode());

                    response.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(Lists.newArrayList(entry.getKey()), itemPageResponse.getSku()));
                    return response;
                }).collect(Collectors.toList());
    }

    public PageResponse<LogListResponse> pageLogList(LogListRequest request) {
        List<Integer> stockinQaIdList = stockinQaOrderService.findStockinQaIdListByTaskId(request.getId());
        if (CollectionUtils.isEmpty(stockinQaIdList)) {
            return PageResponse.of(Collections.emptyList(), 0L);
        }
        request.setId(null);
        request.setIdList(stockinQaIdList);
        return stockinQaOrderLogService.pageList(request);
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_QA_TASK_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest downloadRequest) {
        DownloadResponse response = new DownloadResponse();
        StockinQaTaskPageRequest request = JsonMapper.fromJson(downloadRequest.getRequestContent(), StockinQaTaskPageRequest.class);
        Page<StockinQaTaskPageResponse> page = new Page<>(downloadRequest.getPageIndex(), downloadRequest.getPageSize());
        page.setSearchCount(false);
        IPage<StockinQaTaskPageResponse> pageList = this.getBaseMapper().pageList(page, request);
        List<StockinQaTaskPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setDataJsonStr(JsonMapper.toJson(records));
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> taskIdList = records.stream().map(StockinQaTaskPageResponse::getTaskId).collect(Collectors.toList());

        List<StockinQaTaskItemEntity> taskItemEntities = new LinkedList<>();
        List<StockinQaOrderEntity> orderEntities = new LinkedList<>();
        List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = new LinkedList<>();
        this.getExportSupplementary(taskIdList, taskItemEntities, orderEntities, skuTypeInfoEntities);

        Map<Integer, List<StockinQaTaskItemEntity>> itemMap = taskItemEntities.stream().collect(Collectors.groupingBy(StockinQaTaskItemEntity::getTaskId));
        Map<Integer, List<StockinQaOrderEntity>> orderMap = orderEntities.stream().collect(Collectors.groupingBy(StockinQaOrderEntity::getTaskId));
        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = skuTypeInfoEntities.stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getTaskId));
        Map<Integer, Date> lastProcessDateMap = stockinQaOrderProcessService.getLastProcessDateInfo(orderEntities.stream().map(StockinQaOrderEntity::getStockinQaOrderId).filter(Objects::nonNull).collect(Collectors.toList()),
                StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus());

        Map<String, String> taskStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_TASK_STATUS.getName());
        Map<String, String> internalBoxTypeMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName());
        Map<Integer, ProductCategoryDetail> productCategoryMap = productApiService.findCategoryDetailByProductIds(records.stream().map(StockinQaTaskPageResponse::getProductId).collect(Collectors.toSet()));
        List<StockinQaTaskPageExport> exportList = records.stream().map(entity -> {
            StockinQaTaskPageExport export = new StockinQaTaskPageExport();
            BeanUtils.copyProperties(entity, export);

            List<StockinQaTaskItemEntity> taskItemEntityList = itemMap.get(entity.getTaskId());
            Map<String, List<StockinQaTaskItemEntity>> collect = taskItemEntityList.stream().filter(item -> StringUtils.hasText(item.getPackageName())).collect(Collectors.groupingBy(StockinQaTaskItemEntity::getPackageName));
            export.setPackageName(collect.entrySet().stream().map(entry -> String.format("%s(%s)", entry.getKey(), entry.getValue().stream().mapToInt(StockinQaTaskItemEntity::getQty).sum())).collect(Collectors.joining(",")));
            export.setBrandName(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getBrandName).filter(StringUtils::hasText).findAny().orElse(""));
            export.setSupplierDeliveryBoxCodes(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.joining(",")));
            export.setPurchasePlanNos(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));

            export.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getSupplierDeliveryNo).collect(Collectors.toList()), entity.getSku()));

            export.setBoxQty(taskItemEntityList.stream().mapToInt(StockinQaTaskItemEntity::getQty).sum());
            export.setIsNewStr(entity.getIsNew().equals(1) ? "是" : "否");

            export.setCheckStatusStr(taskStatusMap.get(entity.getCheckStatus()));
            export.setInternalBoxType(internalBoxTypeMap.get(entity.getInternalBoxType()));

            this.buildStockinQaOrderInfo(orderMap.get(entity.getTaskId()), lastProcessDateMap, export);
            this.buildCategoryInfo(export, productCategoryMap);
            this.buildSkuTypeInfo(export, skuTypeMap.get(entity.getTaskId()));
            return export;
        }).collect(Collectors.toList());
        response.setTotalCount((long) (records.size() >= downloadRequest.getPageSize()
                ? (downloadRequest.getPageIndex() + 1) * downloadRequest.getPageSize()
                : (downloadRequest.getPageIndex() - 1) * downloadRequest.getPageSize() + records.size()));
        response.setDataJsonStr(JsonMapper.toJson(exportList));
        return response;
    }

    private void getExportSupplementary(List<Integer> taskIdList, List<StockinQaTaskItemEntity> taskItemEntities, List<StockinQaOrderEntity> orderEntities, List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities) {
        LambdaQueryWrapper<StockinQaTaskItemEntity> itemEntityQueryWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<StockinQaOrderEntity> orderEntityQueryWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity> skuTypeQueryWrapper = new LambdaQueryWrapper<>();
        Lists.partition(taskIdList, 100).forEach(taskIds -> {
            itemEntityQueryWrapper.select(StockinQaTaskItemEntity::getQty, StockinQaTaskItemEntity::getPackageName,
                    StockinQaTaskItemEntity::getTaskId, StockinQaTaskItemEntity::getBrandName,
                    StockinQaTaskItemEntity::getSupplierDeliveryBoxCode, StockinQaTaskItemEntity::getSupplierDeliveryNo,
                    StockinQaTaskItemEntity::getPurchasePlanNo);
            itemEntityQueryWrapper.in(StockinQaTaskItemEntity::getTaskId, taskIds);
            taskItemEntities.addAll(stockinQaTaskItemService.list(itemEntityQueryWrapper));

            orderEntityQueryWrapper.select(StockinQaOrderEntity::getTaskId, StockinQaOrderEntity::getCompleteDate,
                    StockinQaOrderEntity::getStockinQaOrderId, StockinQaOrderEntity::getQcUserName);
            orderEntityQueryWrapper.in(StockinQaOrderEntity::getTaskId, taskIds);
            orderEntityQueryWrapper.ne(StockinQaOrderEntity::getProcessStatus, StockinQaOrderProcessStatusEnum.CANCELED.name());
            orderEntities.addAll(stockinQaOrderService.list(orderEntityQueryWrapper));

            skuTypeQueryWrapper.select(StockinQaOrderSkuTypeInfoEntity::getTaskId, StockinQaOrderSkuTypeInfoEntity::getSkuType);
            skuTypeQueryWrapper.in(StockinQaOrderSkuTypeInfoEntity::getTaskId, taskIds);
            skuTypeInfoEntities.addAll(stockinQaOrderSkuTypeInfoService.list(skuTypeQueryWrapper));

            itemEntityQueryWrapper.clear();
            orderEntityQueryWrapper.clear();
            skuTypeQueryWrapper.clear();
        });
    }

    private void buildStockinQaOrderInfo(List<StockinQaOrderEntity> stockinQaOrderEntities, Map<Integer, Date> lastProcessDateMap, StockinQaTaskPageExport export) {
        if (CollectionUtils.isEmpty(stockinQaOrderEntities)) {
            return;
        }
        export.setCompleteDate(stockinQaOrderEntities.get(0).getCompleteDate());
        export.setQcUserName(stockinQaOrderEntities.get(0).getQcUserName());
        export.setFirstAuditEndDate(lastProcessDateMap.get(stockinQaOrderEntities.get(0).getStockinQaOrderId()));
    }

    private void buildSkuTypeInfo(StockinQaTaskPageExport export, List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntityList) {
        if (CollectionUtils.isEmpty(skuTypeInfoEntityList)) {
            return;
        }
        export.setSkuType(skuTypeInfoEntityList.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).distinct().collect(Collectors.joining(",")));
    }

    /**
     * 赋值商品品类
     *
     * @param record
     * @param productCategoryMap
     */
    private void buildCategoryInfo(StockinQaTaskPageExport record, Map<Integer, ProductCategoryDetail> productCategoryMap) {
        if (Objects.isNull(record) || Objects.isNull(record.getProductId()) || Objects.isNull(productCategoryMap.get(record.getProductId()))) {
            return;
        }
        record.setCategoryOne(productCategoryMap.get(record.getProductId()).getCategoryOneName());
        record.setCategoryTwo(productCategoryMap.get(record.getProductId()).getCategoryTwoName());
        record.setCategoryThree(productCategoryMap.get(record.getProductId()).getCategoryThreeName());
    }
}
