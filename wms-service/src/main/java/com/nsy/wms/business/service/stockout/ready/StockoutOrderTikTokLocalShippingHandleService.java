package com.nsy.wms.business.service.stockout.ready;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderReadyStatusHandleEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.wms.business.service.stockout.StockoutOrderGetLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutOrderTikTokLocalShippingExtendService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTikTokLocalShippingExtendEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/29 18:28
 */
@Service
public class StockoutOrderTikTokLocalShippingHandleService implements IStockoutOrderReadyStatusHandleService {

    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    private StockoutOrderTikTokLocalShippingExtendService tikTokDirectExtendService;
    @Autowired
    private StockoutOrderGetLabelService orderGetLabelService;

    @Override
    public boolean isSupport(StockoutOrderReadyStatusHandleEnum handleEnum) {
        return StockoutOrderReadyStatusHandleEnum.TTITOK_LOCAL_SHIPPING == handleEnum;
    }

    /**
     * 步骤一：获取面单
     *
     * @param stockoutOrder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleOrder(StockoutOrderEntity stockoutOrder) {
        StockoutOrderTikTokLocalShippingExtendEntity extendEntity = tikTokDirectExtendService.getByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        if (Objects.isNull(extendEntity)) {
            throw new BusinessServiceException("TikTok直邮订单获取不到对应的包裹信息无法获取面单请检查!");
        }
        //没有面单号才去获取,即获取成功了但调用发货失败了重新获取则不在处理
        if (!StringUtils.hasText(stockoutOrder.getLogisticsNo())) {
            //获取物流面单
            orderGetLabelService.startGetLabel(stockoutOrder);
        }
        if (!StringUtils.hasText(stockoutOrder.getLogisticsNo())) {
            throw new BusinessServiceException("获取物流单号失败,获取的物流单号为空!");
        }
        //调用平台发货
        tikTokDirectExtendService.shipOrder(stockoutOrder, extendEntity);
        //修改出库单状态
        stockoutOrder.setStatus(StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
        stockoutOrderService.updateById(stockoutOrder);
        stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_WAVE_GENERATED, String.format("出库单【%s】待生成波次", stockoutOrder.getStockoutOrderNo()));
    }

}