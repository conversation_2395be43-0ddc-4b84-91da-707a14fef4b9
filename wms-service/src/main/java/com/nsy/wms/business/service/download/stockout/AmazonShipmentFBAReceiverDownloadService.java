package com.nsy.wms.business.service.download.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.wms.domain.stockout.AmazonShipmentReferenceIdExport;
import com.nsy.api.wms.domain.stockout.AmazonShipmentShipmentIdReceiverExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.ShipmentDownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AmazonShipmentFBAReceiverDownloadService implements IDownloadService {
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_FBA_SHIPMENT_ID_RECEIVER;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        ShipmentDownloadRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), ShipmentDownloadRequest.class);
        DownloadResponse response = new DownloadResponse();
        if (!CollectionUtils.isEmpty(downloadRequest.getShipmentIds())) {
            downloadRequest.getSearchRequest().setShipmentIdList(downloadRequest.getShipmentIds());
        }
        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(AmazonShipmentShipmentIdReceiverExport.class));
        IPage<AmazonShipmentReferenceIdExport> exportIPage = shipmentService.getBaseMapper().pageSearchFBAShipmentReferenceId(new Page(request.getPageIndex(), request.getPageSize()), downloadRequest.getSearchRequest());
        if (CollectionUtils.isEmpty(exportIPage.getRecords())) {
            response.setTotalCount(0L);
            excelResponse.setData(Collections.emptyList());
            response.setDataJsonStr(JsonMapper.toJson(excelResponse));
            return response;
        }
        Integer totalCount = shipmentService.getBaseMapper().pageSearchFBAShipmentReferenceIdCount(downloadRequest.getSearchRequest());
        List<List<Object>> collect = exportIPage.getRecords().stream().map(export -> {
            AmazonShipmentShipmentIdReceiverExport export1 = new AmazonShipmentShipmentIdReceiverExport();
            export1.setShipmentId(export.getFbaShipmentId());
            export1.setOrderNo(export.getOrderNo());
            if (StrUtil.isNotBlank(export.getStockoutOrderNo())) {
                StockoutOrderEntity stockoutOrderNo = stockoutOrderService.getByStockoutOrderNo(export.getStockoutOrderNo());
                export1.setReceiver(stockoutOrderNo.getReceiverName());
            }
            return NsyExcelUtil.getData(AmazonShipmentShipmentIdReceiverExport.class, export1);
        }).collect(Collectors.toList());
        excelResponse.setData(collect);
        response.setTotalCount(totalCount.longValue());
        response.setDataJsonStr(JsonMapper.toJson(excelResponse));
        return response;
    }


}
