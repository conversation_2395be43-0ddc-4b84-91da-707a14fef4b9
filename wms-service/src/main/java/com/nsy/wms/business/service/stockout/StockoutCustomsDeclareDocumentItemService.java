package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.ContainsConstants;
import com.nsy.api.wms.constants.DeclareDocumentDataMapConstants;
import com.nsy.api.wms.constants.ElectronicSealUrlConstant;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentAggregatedItemResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentDetailResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDocumentCount;
import com.nsy.api.wms.enumeration.CustomsDeclareDocumentFileTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.CurrencyTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareDocumentLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareOrderStatusEnum;
import com.nsy.api.wms.request.stockout.DeclareDocumentItemWeightChangeRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentItemUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentUpdateRequest;
import com.nsy.api.wms.response.config.CustomsPrintListResponse;
import com.nsy.api.wms.response.stockout.DeclareDocumentItemWeightChangeResponse;
import com.nsy.api.wms.response.stockout.ProformaInvoiceItemResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomDeclareDocumentItemTotalResponse;
import com.nsy.wms.business.service.bd.BdCompanyCustomerService;
import com.nsy.wms.business.service.bd.BdCompanyService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.product.ProductCategoryCustomsDeclareCompanyService;
import com.nsy.wms.business.service.product.ProductCategoryCustomsDeclareService;
import com.nsy.wms.repository.entity.bd.BdCompanyEntity;
import com.nsy.wms.repository.entity.product.ProductCategoryCustomsDeclareCompanyEntity;
import com.nsy.wms.repository.entity.product.ProductCategoryCustomsDeclareEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareOrderEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareDocumentItemMapper;
import com.nsy.wms.utils.BeanCompareUtils;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.FreeMarkerTemplateUtils;
import com.nsy.wms.utils.WmsDateUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 报关单据明细业务实现
 * @date: 2022-03-04 14:56
 */
@Service
public class StockoutCustomsDeclareDocumentItemService extends ServiceImpl<StockoutCustomsDeclareDocumentItemMapper, StockoutCustomsDeclareDocumentItemEntity> {

    @Resource
    private StockoutCustomsDeclareDocumentService stockoutCustomsDeclareDocumentService;
    @Resource
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    private BdCompanyService bdCompanyService;
    @Resource
    private StockoutCustomsDeclareOrderService stockoutCustomsDeclareOrderService;
    @Resource
    private ProductCategoryCustomsDeclareService productCategoryCustomsDeclareService;
    @Resource
    private StockoutCustomsDeclareDocumentLogService stockoutCustomsDeclareDocumentLogService;
    @Resource
    private BdCompanyCustomerService customerService;
    @Resource
    private ProductCategoryCustomsDeclareCompanyService productCategoryCustomsDeclareCompanyService;
    @Resource
    private StockoutCustomsDeclareDocumentAggregatedItemService declareDocumentAggregatedItemService;

    private static final BigDecimal WEIGHT_COEFFICIENT = new BigDecimal("1.6");
    @Autowired
    private BdSystemParameterService bdSystemParameterService;


    public List<StockoutCustomsDeclareDocumentItemEntity> listByDocumentId(Integer declareDocumentId) {
        return this.list(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentItemEntity>().eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentId, declareDocumentId));
    }

    public List<StockoutCustomsDeclareDocumentItemEntity> listByDeclareOrderId(Integer declareOrderId) {
        return this.list(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentItemEntity>().eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId, declareOrderId));
    }

    public StockoutCustomsDeclareDocumentDetailResult getStockoutCustomsDeclareDocumentDetail(Integer declareDocumentId) {
        StockoutCustomsDeclareDocumentEntity entity = stockoutCustomsDeclareDocumentService.getById(declareDocumentId);
        if (Objects.isNull(entity) || IsDeletedConstant.DELETED.equals(entity.getIsDeleted()))
            throw new BusinessServiceException("所选单据不存在");
        StockoutCustomsDeclareDocumentDetailResult result = new StockoutCustomsDeclareDocumentDetailResult();
        BeanUtils.copyProperties(entity, result);
        BdCompanyEntity company = bdCompanyService.getById(entity.getCompanyId());
        if (Objects.nonNull(company)) {
            result.setCompanyName(company.getCompanyName());
        }

        if (BigDecimal.ZERO.compareTo(entity.getExchangeRate()) != 0) {
            result.setcAndFPrice(fetchCAndFPrice(entity.getFreight(), entity.getExchangeRate(), entity.getTotalFobPrice()));
        }
        result.setDestinationCn(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_DESTINATION.getName(), result.getDestination()));
        result.setStatusStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_CUSTOMER_DECLARE_STATUS.getName(), result.getStatus()));
        result.setPortCn(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_PORT.getName(), result.getPort()));
        result.setCustomsCn(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_CUSTOMS.getName(), result.getCustoms()));
        result.setBrandName(getBrandName(declareDocumentId));
        return result;
    }

    /**
     * 获取cnf价格
     *
     * @param freight
     * @param exchangeRate
     * @param totalFobPrice
     * @return
     */
    public BigDecimal fetchCAndFPrice(BigDecimal freight, BigDecimal exchangeRate, BigDecimal totalFobPrice) {
        BigDecimal realFreight = freight.divide(exchangeRate, 0, RoundingMode.HALF_DOWN);
        return totalFobPrice.add(realFreight).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取品牌名
     * <p>
     * 单据下面的订单品牌名一致，因此取其中一个获取
     *
     * @param declareDocumentId
     * @return
     */
    public String getBrandName(Integer declareDocumentId) {
        List<StockoutCustomsDeclareDocumentItemEntity> itemList = listByDocumentId(declareDocumentId);
        if (itemList.isEmpty()) return null;
        StockoutCustomsDeclareOrderEntity declareOrderEntity = stockoutCustomsDeclareOrderService.getById(itemList.get(0).getDeclareOrderId());
        if (Objects.isNull(declareOrderEntity)) return null;
        return declareOrderEntity.getBrandName();
    }


    /**
     * @apiNote 毛重，净重, 数据库初始值为0，实际箱数的数据库初始值为NULL，如果未更改则返回计算出的值，已更改则返回数据库中的值
     */


    @Transactional
    public void updateStockoutCustomsDeclareDocument(StockoutCustomsDeclareDocumentUpdateRequest request) {
        if (BigDecimal.ZERO.compareTo(request.getExchangeRate()) == 0)
            request.setExchangeRate(productCategoryCustomsDeclareService.getExchangeRate());
        StockoutCustomsDeclareDocumentEntity entity = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());
        if (Objects.isNull(entity) || IsDeletedConstant.DELETED.equals(entity.getIsDeleted()))
            throw new BusinessServiceException("修改的报关单据不存在");
        if (entity.getIsAudit())
            throw new BusinessServiceException("报关单据已审核无法修改");
        StockoutCustomsDeclareDocumentEntity updateEntity = new StockoutCustomsDeclareDocumentEntity();
        BeanUtils.copyProperties(request, updateEntity);
        //更新明细fob价格
        updateFobPrice(request.getDeclareDocumentId(), request.getOldCurrency(), request.getCurrency(), request.getExchangeRate());
        //计算总的fob价
        List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = this.listByDocumentId(request.getDeclareDocumentId());
        updateEntity.setTotalFobPrice(itemEntityList.stream().map(item -> item.getFobPrice().multiply(BigDecimal.valueOf(item.getQty()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        updateEntity.setStatus(StockoutCustomsDeclareOrderStatusEnum.DEAL.name());
        stockoutCustomsDeclareDocumentService.updateById(updateEntity);
        //设置运费
        StockoutCustomsDeclareDocumentEntity newEntity = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());
        if (Objects.nonNull(newEntity.getFreight()) && Objects.nonNull(newEntity.getTotalFobPrice())
                && BigDecimal.ZERO.compareTo(newEntity.getTotalFobPrice()) != 0) {
            updateApportionFreight(newEntity);
        }
        String content = BeanCompareUtils.compareObject(entity, newEntity);
        if (StringUtils.isNotBlank(content))
            stockoutCustomsDeclareDocumentLogService.addLog(request.getDeclareDocumentId(), StockoutCustomsDeclareDocumentLogTypeEnum.EDIT.name(), content);
    }

    /**
     * 更新明细fob价格
     *
     * @param declareDocumentId
     * @param oldCurrency
     * @param newCurrency
     * @param exchangeRate
     */
    public void updateFobPrice(Integer declareDocumentId, String oldCurrency, String newCurrency, BigDecimal exchangeRate) {
        List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = this.listByDocumentId(declareDocumentId);
        itemEntityList.forEach(item -> item.setFobPrice(calculateExchangeRate(item.getFobPrice(), oldCurrency, newCurrency, exchangeRate, 1)));
        updateBatchById(itemEntityList);
    }

    /**
     * 设置运费
     *
     * @param declareDocument
     */
    public void updateApportionFreight(StockoutCustomsDeclareDocumentEntity declareDocument) {
        List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = this.listByDocumentId(declareDocument.getDeclareDocumentId());
        itemEntityList.stream()
                .filter(itemEntity -> BigDecimal.ZERO.compareTo(itemEntity.getFobPrice()) != 0)
                .forEach(itemEntity -> {
                    StockoutCustomsDeclareDocumentItemFetchService.FetchApportionFreightBo bo = new StockoutCustomsDeclareDocumentItemFetchService
                            .FetchApportionFreightBo(itemEntity.getFobPrice(), itemEntity.getQty(),
                            declareDocument.getTotalFobPrice(), declareDocument.getFreight(), declareDocument.getCurrency(), declareDocument.getExchangeRate());
                    itemEntity.setApportionFreight(StockoutCustomsDeclareDocumentItemFetchService.fetchApportionFreight(bo));
                });
        updateBatchById(itemEntityList);
    }

    @Transactional
    public void updateStockoutCustomsDeclareDocumentItem(StockoutCustomsDeclareDocumentItemUpdateRequest request) {
        if (CollectionUtils.isEmpty(request.getDeclareDocumentItems())) return;
        StockoutCustomsDeclareDocumentEntity entity = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());
        if (entity.getIsAudit())
            throw new BusinessServiceException("报关单据已审核无法修改");
        //更新明细
        updateDocumentItem(request);
        //处理Fob价格
        if (request.getDeclareDocumentItems().stream().anyMatch(item -> Objects.nonNull(item.getFobPrice()))) {
            StockoutCustomsDeclareDocumentEntity documentEntity = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());

            List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = listByDocumentId(request.getDeclareDocumentId());
            BigDecimal totalFobPrice = itemEntityList.stream().map(itemEntity -> itemEntity.getFobPrice().multiply(BigDecimal.valueOf(itemEntity.getQty()))).reduce(BigDecimal::add).orElse(null);
            if (Objects.isNull(totalFobPrice))
                return;
            //修改totalFobPrice
            StockoutCustomsDeclareDocumentEntity updateEntity = new StockoutCustomsDeclareDocumentEntity();
            updateEntity.setDeclareDocumentId(request.getDeclareDocumentId());
            updateEntity.setTotalFobPrice(totalFobPrice);
            stockoutCustomsDeclareDocumentService.updateById(updateEntity);

            StockoutCustomsDeclareDocumentEntity newDocumentEntity = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());
            //记录日志
            String content = BeanCompareUtils.compareObject(documentEntity, newDocumentEntity);
            if (StringUtils.isNotBlank(content))
                stockoutCustomsDeclareDocumentLogService.addLog(request.getDeclareDocumentId(), StockoutCustomsDeclareDocumentLogTypeEnum.EDIT.name(), content);
            //计算分摊运费
            List<StockoutCustomsDeclareDocumentItemEntity> updateItemList = itemEntityList.stream().map(item -> {
                StockoutCustomsDeclareDocumentItemEntity documentItemEntity = new StockoutCustomsDeclareDocumentItemEntity();
                documentItemEntity.setDeclareDocumentItemId(item.getDeclareDocumentItemId());
                if (BigDecimal.ZERO.compareTo(totalFobPrice) != 0 && (Objects.nonNull(item.getFobPrice()) || Objects.nonNull(item.getQty()))) {
                    StockoutCustomsDeclareDocumentItemFetchService.FetchApportionFreightBo bo = new StockoutCustomsDeclareDocumentItemFetchService.FetchApportionFreightBo(item.getFobPrice(), item.getQty(), newDocumentEntity.getTotalFobPrice(), newDocumentEntity.getFreight(), newDocumentEntity.getCurrency(), newDocumentEntity.getExchangeRate());
                    documentItemEntity.setApportionFreight(StockoutCustomsDeclareDocumentItemFetchService.fetchApportionFreight(bo));
                }
                return documentItemEntity;
            }).collect(Collectors.toList());
            this.updateBatchById(updateItemList);
        }
        StockoutCustomsDeclareDocumentEntity newDocument = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());
        //重置聚合明细
        declareDocumentAggregatedItemService.resetAggregatedItem(newDocument.getDeclareDocumentId(), newDocument.getDeclareDocumentNo());
    }

    private void updateDocumentItem(StockoutCustomsDeclareDocumentItemUpdateRequest request) {
        StockoutCustomsDeclareDocumentEntity entity = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());
        int totalBoxNum = request.getDeclareDocumentItems().stream().mapToInt(StockoutCustomsDeclareDocumentItemUpdateRequest.DeclareDocumentItemUpdateRequest::getActualBoxQty).sum();
        if (totalBoxNum != entity.getBoxNum())
            throw new BusinessServiceException("实际箱数的总合与该单据的箱数不匹配，请重新修改");
        if (request.getDeclareDocumentItems().stream().anyMatch(item -> item.getActualRoughWeight().compareTo(item.getActualNetWeight()) <= 0))
            throw new BusinessServiceException("毛重不能小于或等于净重，请重新修改");
        Map<String, String> customsUnitMap = enumConversionChineseUtils.inverseBaseConversion(DictionaryNameEnum.WMS_CUSTOMS_UNIT.getName());
        request.getDeclareDocumentItems().forEach(item -> {
            lambdaUpdate().set(Objects.nonNull(item.getActualNetWeight()), StockoutCustomsDeclareDocumentItemEntity::getNetWeight, item.getActualNetWeight())
                    .set(Objects.nonNull(item.getActualRoughWeight()), StockoutCustomsDeclareDocumentItemEntity::getRoughWeight, item.getActualRoughWeight())
                    .set(Objects.nonNull(item.getFobPrice()), StockoutCustomsDeclareDocumentItemEntity::getFobPrice, item.getFobPrice())
                    .set(Objects.nonNull(item.getCustomsDeclareCn()), StockoutCustomsDeclareDocumentItemEntity::getCustomsDeclareCn, item.getCustomsDeclareCn())
                    .set(Objects.nonNull(item.getCustomsDeclareUnitCn()), StockoutCustomsDeclareDocumentItemEntity::getCustomsDeclareUnitCn, item.getCustomsDeclareUnitCn())
                    .set(Objects.nonNull(item.getCustomsDeclareUnitCn()), StockoutCustomsDeclareDocumentItemEntity::getCustomsDeclareUnit, customsUnitMap.getOrDefault(item.getCustomsDeclareUnitCn(), StringUtils.EMPTY))
                    .set(Objects.nonNull(item.getSpecType()), StockoutCustomsDeclareDocumentItemEntity::getPolymerizedDeclareElement, item.getSpecType())
                    .set(Objects.nonNull(item.getWeight()), StockoutCustomsDeclareDocumentItemEntity::getWeight, item.getWeight())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentAggregatedItemId, item.getDeclareDocumentAggregatedItemId())
                    .update();
            updateActualBoxQty(item.getDeclareDocumentAggregatedItemId(), item.getActualBoxQty());
        });
        updatePlanWeight(request.getDeclareDocumentId());
    }

    private void updatePlanWeight(Integer declareDocumentId) {
        //更新主表的计划重和计划箱数
        List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = this.listByDocumentId(declareDocumentId);
        BigDecimal totalPlanWeight = itemEntityList.stream().map(item -> BigDecimal.valueOf(item.getQty()).multiply(item.getWeight())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        int totalPlanBoxQty = totalPlanWeight.divide(BigDecimal.valueOf(21), 0, RoundingMode.HALF_UP).intValue();
        stockoutCustomsDeclareDocumentService.lambdaUpdate().set(StockoutCustomsDeclareDocumentEntity::getTotalPlanBoxNum, totalPlanBoxQty)
                .set(StockoutCustomsDeclareDocumentEntity::getTotalPlanWeight, totalPlanWeight)
                .eq(StockoutCustomsDeclareDocumentEntity::getDeclareDocumentId, declareDocumentId).update();
    }

    /**
     * 更新实际箱数
     *
     * @param declareDocumentId
     */
    public void updateActualBoxQty(Integer declareDocumentId) {
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList = getBaseMapper().aggregatedItemList(declareDocumentId);
        StockoutCustomsDeclareDocumentEntity document = stockoutCustomsDeclareDocumentService.getEnableByDeclareDocumentId(declareDocumentId);
        aggregatedItemResultList.forEach(itemResult -> {
            if (Objects.isNull(document.getTotalPlanBoxNum()) || 0 == document.getTotalPlanBoxNum())
                return;
            int actualBoxQty = BigDecimal.valueOf(itemResult.getPlanBoxQty()).multiply(BigDecimal.valueOf(document.getBoxNum()))
                    .divide(BigDecimal.valueOf(document.getTotalPlanBoxNum()), 0, RoundingMode.HALF_UP).intValue();

            List<StockoutCustomsDeclareDocumentItemEntity> declareDocumentItemList = list(new LambdaUpdateWrapper<StockoutCustomsDeclareDocumentItemEntity>()
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentId, itemResult.getDeclareDocumentId())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getCustomsDeclareCn, itemResult.getCustomsDeclareCn())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getSpinType, itemResult.getSpinType())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getFabricType, itemResult.getFabricType()));

            StockoutCustomsDeclareDocumentItemEntity firstDocumentItem = declareDocumentItemList.stream().min(Comparator.comparing(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentItemId))
                    .orElseThrow(() -> new BusinessServiceException(String.format("找不到报关单据明细 %s %s %s %s", itemResult.getDeclareDocumentId(), itemResult.getCustomsDeclareCn(), itemResult.getSpinType(), itemResult.getFabricType())));
            firstDocumentItem.setActualBoxQty(actualBoxQty);
            this.updateById(firstDocumentItem);
        });

    }

    /**
     * 更新实际箱数
     *
     * @param declareDocumentAggregatedItemId
     * @param actualBoxQty
     */
    private void updateActualBoxQty(Integer declareDocumentAggregatedItemId, Integer actualBoxQty) {
        List<StockoutCustomsDeclareDocumentItemEntity> declareDocumentItemList = this.getByDeclareDocumentAggregatedItemId(declareDocumentAggregatedItemId);
        if (declareDocumentItemList.isEmpty()) {
            throw new BusinessServiceException(String.format("找不到报关单据明细 %s", declareDocumentAggregatedItemId));
        }
        StockoutCustomsDeclareDocumentItemEntity firstDocumentItem = declareDocumentItemList.stream().min(Comparator.comparing(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentItemId)).orElseThrow(() -> new BusinessServiceException(String.format("报关单据明细不存在 %s", declareDocumentItemList)));
        firstDocumentItem.setActualBoxQty(actualBoxQty);
        this.updateById(firstDocumentItem);
    }

    public String generateDocument(Integer declareDocumentId, String type) {
        CustomsDeclareDocumentFileTypeEnum instance = EnumUtils.getEnum(CustomsDeclareDocumentFileTypeEnum.class, type.toUpperCase(Locale.ENGLISH));
        if (Objects.isNull(instance)) throw new BusinessServiceException("单据类型不存在");
        Map<String, Object> map = new HashMap<>();
        generateDataMap(map, declareDocumentId, instance);
        return FreeMarkerTemplateUtils.renderTemplate(instance.getTemplateName(), map);
    }

    public void generateDataMap(Map<String, Object> map, Integer declareDocumentId, CustomsDeclareDocumentFileTypeEnum type) {
        StockoutCustomsDeclareDocumentEntity declareDocumentEntity = stockoutCustomsDeclareDocumentService.getById(declareDocumentId);
        validate(declareDocumentEntity);
        if (declareDocumentEntity.getExchangeRate().compareTo(BigDecimal.ZERO) == 0)
            declareDocumentEntity.setExchangeRate(productCategoryCustomsDeclareService.getExchangeRate());
        BdCompanyEntity company = bdCompanyService.getById(declareDocumentEntity.getCompanyId());
        if (Objects.isNull(company)) throw new BusinessServiceException("公司信息不存在");
        if (Objects.isNull(company.getElectronicSeal())) company.setElectronicSeal(StringUtils.EMPTY);
        map.put(DeclareDocumentDataMapConstants.COMPANY, company);
        if (StrUtil.isNotEmpty(declareDocumentEntity.getCustomer()))
            map.put(DeclareDocumentDataMapConstants.CUSTOMER, customerService.findByCompanyIdAndCustomerName(company.getCompanyId(), declareDocumentEntity.getCustomer()));
        //组装单据头信息
        this.buildDeclareDocumentEntityInfo(declareDocumentEntity, map);
        if (type == null || CustomsDeclareDocumentFileTypeEnum.PACKING_LIST == type) {
            List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = listByDocumentId(declareDocumentId);
            map.put(DeclareDocumentDataMapConstants.PACKING_ROW_SIZE, itemEntityList.size());
            map.put(DeclareDocumentDataMapConstants.ITEM_MAP, getItemMap(itemEntityList, declareDocumentEntity.getDeclareDocumentNo()));
            map.put(DeclareDocumentDataMapConstants.FIXED_ELECTRONIC_SEAL_ONE, ElectronicSealUrlConstant.FJ_NSY_ELECTRONIC_SEAL_URL);
            map.put(DeclareDocumentDataMapConstants.FIXED_ELECTRONIC_SEAL_TWO, ElectronicSealUrlConstant.QZ_NSY_ELECTRONIC_SEAL_URL);
            if (Objects.nonNull(type)) return;
        }
        //构建 result对象
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList = declareDocumentAggregatedItemService.buildResultList(declareDocumentEntity);
        List<ProductCategoryCustomsDeclareEntity> customsDeclareEntityList = productCategoryCustomsDeclareService.list(Wrappers.<ProductCategoryCustomsDeclareEntity>lambdaQuery()
                .in(ProductCategoryCustomsDeclareEntity::getCategoryCustomsDeclareId, aggregatedItemResultList.stream().map(StockoutCustomsDeclareDocumentAggregatedItemResult::getCategoryCustomsDeclareId).collect(Collectors.toList())));
        String brandName = StrUtil.emptyToDefault(getBrandName(declareDocumentId), "无品牌");
        //设置 品牌 货源地
        aggregatedItemResultList.forEach(item -> {
            customsDeclareEntityList.stream().filter(customsDeclareEntity -> item.getCategoryCustomsDeclareId().equals(customsDeclareEntity.getCategoryCustomsDeclareId()) && Objects.nonNull(customsDeclareEntity.getCustomsDeclareQty()))
                    .findFirst().ifPresent(customsDeclareEntity -> {
                        ProductCategoryCustomsDeclareCompanyEntity productCategoryCustomsDeclareCompanyEntity = productCategoryCustomsDeclareCompanyService.getOne(customsDeclareEntity.getCategoryCustomsDeclareId(), declareDocumentEntity.getCompanyId());
                        if (ObjectUtil.isNotNull(productCategoryCustomsDeclareCompanyEntity)) {
                            item.setOriginProvince(productCategoryCustomsDeclareCompanyEntity.getOriginProvince());
                            item.setOriginCity(productCategoryCustomsDeclareCompanyEntity.getOriginCity());
                        }
                        item.setCustomsDeclareQty(customsDeclareEntity.getCustomsDeclareQty());
                        item.setBrandName(brandName.toUpperCase(Locale.ROOT));
                    });
        });
        //填充明细
        buildItem(map, aggregatedItemResultList);
        map.put(DeclareDocumentDataMapConstants.ROW_SIZE, aggregatedItemResultList.size());
        Map<String, Object> totalValues = getTotalValues(aggregatedItemResultList);
        map.putAll(totalValues);
        String ciOriginPlace = bdSystemParameterService.findConfigValue(BdSystemParameterEnum.WMS_STOCKOUT_DECLARE_CI_ORIGIN_PLACE);
        if (StrUtil.isNotEmpty(ciOriginPlace)) {
            map.put(DeclareDocumentDataMapConstants.CI_ORIGIN_PLACE, ciOriginPlace);
        }
    }

    /**
     * 填充明细
     *
     * @param map
     * @param itemList
     */
    private void buildItem(Map<String, Object> map, List<StockoutCustomsDeclareDocumentAggregatedItemResult> itemList) {
        //组装制单明细信息
        map.put(DeclareDocumentDataMapConstants.ITEM_LIST, itemList.stream().map(item -> {
            JSONObject jobj = JSONUtil.parseObj(item);
            jobj.set("fobPrice", item.getFobPrice().toString());
            jobj.putOnce("cnfPrice", item.getCnfPrice());
            jobj.putOnce("cAndFPrice", item.getCAndFPrice());
            jobj.putOnce("fobTotalPrice", item.getFobTotalPrice());
            jobj.putOnce("planWeight", item.getPlanWeight());
            jobj.putOnce("planBoxQty", item.getPlanBoxQty());
            return jobj;
        }).collect(Collectors.toList()));
    }

    private void buildDeclareDocumentEntityInfo(StockoutCustomsDeclareDocumentEntity declareDocumentEntity, Map<String, Object> map) {
        map.put(DeclareDocumentDataMapConstants.DOCUMENT_INFO, declareDocumentEntity);
        String destination = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_DESTINATION.getName(), declareDocumentEntity.getDestination());
        String destinationCn = Objects.nonNull(destination) && destination.contains(ContainsConstants.FULL_WIDTH_LEFT_BRACKET) ? destination.substring(0, destination.indexOf(ContainsConstants.FULL_WIDTH_LEFT_BRACKET_CHAR)) : Strings.EMPTY;
        map.put(DeclareDocumentDataMapConstants.DESTINATION_CN, destinationCn);
        map.put(DeclareDocumentDataMapConstants.LOCATION_CN, EnumUtils.getEnum(LocationEnum.class, declareDocumentEntity.getLocation().toUpperCase(Locale.ENGLISH)).getName());
        map.put(DeclareDocumentDataMapConstants.DATE, WmsDateUtils.dateToEnglishStr(Optional.ofNullable(declareDocumentEntity.getDocumentationDate()).orElse(new Date())));
        map.put(DeclareDocumentDataMapConstants.SHIPPING_DATE, WmsDateUtils.dateToEnglishStr(Optional.ofNullable(declareDocumentEntity.getShippingDate()).orElse(WmsDateUtils.nextDay())));
        map.put(DeclareDocumentDataMapConstants.EXPORT_DATE, WmsDateUtils.dateToEnglishStr(Optional.ofNullable(declareDocumentEntity.getExportDate()).orElse(WmsDateUtils.nextDay())));
        map.put(DeclareDocumentDataMapConstants.SIGNING_DATE, WmsDateUtils.dateToEnglishStr(Optional.ofNullable(declareDocumentEntity.getSigningDate()).orElse(WmsDateUtils.lastWorkDay(new Date()))));
        map.put(DeclareDocumentDataMapConstants.PORT_CN, enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PORT.getName(), declareDocumentEntity.getPort()));
        map.put(DeclareDocumentDataMapConstants.CUSTOMS_CN, enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_CUSTOMS.getName(), declareDocumentEntity.getCustoms()));
        map.put(DeclareDocumentDataMapConstants.CURRENCY, declareDocumentEntity.getCurrency());
    }


    private void validate(StockoutCustomsDeclareDocumentEntity declareDocumentEntity) {
        if (Objects.isNull(declareDocumentEntity) || IsDeletedConstant.DELETED.equals(declareDocumentEntity.getIsDeleted()))
            throw new BusinessServiceException("单据不存在");
        if (StringUtils.isBlank(declareDocumentEntity.getDestination()))
            throw new BusinessServiceException("请填写目的地");
    }


    private Map<String, Object> getTotalValues(List<StockoutCustomsDeclareDocumentAggregatedItemResult> itemList) {
        Map<String, Object> totalValues = new HashMap<>();
        totalValues.put(DeclareDocumentDataMapConstants.TOTAL_QTY, itemList.stream().mapToInt(StockoutCustomsDeclareDocumentAggregatedItemResult::getQty).sum());
        totalValues.put(DeclareDocumentDataMapConstants.TOTAL_BOX, itemList.stream().mapToInt(StockoutCustomsDeclareDocumentAggregatedItemResult::getActualBoxQty).sum());
        totalValues.put(DeclareDocumentDataMapConstants.TOTAL_FOB_PRICE, itemList.stream().map(item -> item.getFobPrice().multiply(BigDecimal.valueOf(item.getQty()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
        totalValues.put(DeclareDocumentDataMapConstants.TOTAL_NET_WEIGHT, itemList.stream().map(StockoutCustomsDeclareDocumentAggregatedItemResult::getActualNetWeight).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        totalValues.put(DeclareDocumentDataMapConstants.TOTAL_ROUGH_WEIGHT, itemList.stream().map(StockoutCustomsDeclareDocumentAggregatedItemResult::getActualRoughWeight).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        return totalValues;
    }

    // 汇总数据
    public StockoutCustomDeclareDocumentItemTotalResponse getTotalValues(Integer declareDocumentId) {
        StockoutCustomsDeclareDocumentEntity entity = stockoutCustomsDeclareDocumentService.getById(declareDocumentId);
        if (Objects.isNull(entity) || IsDeletedConstant.DELETED.equals(entity.getIsDeleted()))
            throw new BusinessServiceException("所选单据不存在");
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> itemList = declareDocumentAggregatedItemService.buildResultList(entity);
        Map<String, Object> totalValues = this.getTotalValues(itemList);
        StockoutCustomDeclareDocumentItemTotalResponse response = new StockoutCustomDeclareDocumentItemTotalResponse();
        response.setTotalFobPrice((BigDecimal) totalValues.get(DeclareDocumentDataMapConstants.TOTAL_FOB_PRICE));
        response.setTotalQty((Integer) totalValues.get(DeclareDocumentDataMapConstants.TOTAL_QTY));
        response.setTotalActualBoxQty((Integer) totalValues.get(DeclareDocumentDataMapConstants.TOTAL_BOX));
        response.setTotalRoughWeight(((BigDecimal) totalValues.get(DeclareDocumentDataMapConstants.TOTAL_ROUGH_WEIGHT)).setScale(2, BigDecimal.ROUND_HALF_UP));
        response.setTotalNetWeight(((BigDecimal) totalValues.get(DeclareDocumentDataMapConstants.TOTAL_NET_WEIGHT)).setScale(2, BigDecimal.ROUND_HALF_UP));
        response.setOriginTotalApportionFreight(itemList.stream().map(StockoutCustomsDeclareDocumentAggregatedItemResult::getApportionFreight).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        BigDecimal exchangeRate = entity.getExchangeRate();
        exchangeRate = exchangeRate.compareTo(BigDecimal.ZERO) == 0 ? productCategoryCustomsDeclareService.getExchangeRate() : exchangeRate;
        response.setTotalApportionFreight(StockoutCustomsDeclareDocumentItemFetchService.fetchFreight(entity.getFreight(), entity.getCurrency(), exchangeRate));
        response.setTotalPlanBoxQty(itemList.stream().mapToInt(StockoutCustomsDeclareDocumentAggregatedItemResult::getPlanBoxQty).sum());
        response.setTotalVolume(itemList.stream().map(StockoutCustomsDeclareDocumentAggregatedItemResult::getVolume).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).setScale(3, BigDecimal.ROUND_HALF_UP));
        response.setTotalCAndFPrice(itemList.stream().map(StockoutCustomsDeclareDocumentAggregatedItemResult::getCnfPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        return response;
    }

    /**
     * 装箱清单数据分组
     *
     * @param itemEntityList 数据库数据
     * @return 键为orderNo, 值为按装箱id来分组的数据
     */
    private Map<String, List<List<StockoutCustomsDeclareDocumentItemEntity>>> getItemMap(List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList, String orderNo) {
        if (CollectionUtils.isEmpty(itemEntityList)) return Collections.emptyMap();
        //转义
        itemEntityList.forEach(item -> {
            item.setSellerSku(item.getSellerSku().replace("&", "&amp;"));
            item.setDeclareElement(item.getDeclareElement().replace("&", "&amp;"));
        });
        Map<Integer, List<StockoutCustomsDeclareDocumentItemEntity>> listMap = itemEntityList.stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId));
        List<StockoutCustomsDeclareOrderEntity> list = stockoutCustomsDeclareOrderService.list(Wrappers.<StockoutCustomsDeclareOrderEntity>lambdaQuery()
                .in(StockoutCustomsDeclareOrderEntity::getDeclareOrderId, listMap.keySet()));
        Map<String, List<StockoutCustomsDeclareOrderEntity>> groupByOrderNo = list.stream().collect(Collectors.groupingBy(StockoutCustomsDeclareOrderEntity::getFbaShipmentId));
        Map<String, List<List<StockoutCustomsDeclareDocumentItemEntity>>> map = groupByOrderNo.keySet().stream().collect(Collectors.toMap(Function.identity(), item -> {
            List<Integer> declareOrderIdList = groupByOrderNo.get(item).stream().map(StockoutCustomsDeclareOrderEntity::getDeclareOrderId).distinct().collect(Collectors.toList());
            List<List<StockoutCustomsDeclareDocumentItemEntity>> value = new ArrayList<>();
            for (Integer declareOrderId : declareOrderIdList) {
                value.addAll(listMap.get(declareOrderId).stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentItemEntity::getShipmentId)).values());
            }
            return value;
        }));
        // 单据同名订单放第一
        Map<String, List<List<StockoutCustomsDeclareDocumentItemEntity>>> sortMap = new LinkedHashMap<>(map.size());
        if (Objects.nonNull(map.getOrDefault(orderNo, null))) {
            sortMap.put(orderNo, map.get(orderNo));
        }
        // 根据箱数由多到少排序
        List<Map.Entry<String, List<List<StockoutCustomsDeclareDocumentItemEntity>>>> entries = new ArrayList<>(map.entrySet());
        LinkedHashMap<String, List<List<StockoutCustomsDeclareDocumentItemEntity>>> sort = entries.stream().filter(item -> !orderNo.equals(item.getKey())).sorted(Comparator.comparingInt(v -> Math.negateExact(v.getValue().size())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1, LinkedHashMap::new));
        sortMap.putAll(sort);
        return sortMap;
    }

    @Transactional
    public CustomsPrintListResponse printDocument(Integer declareDocumentId, String type) {
        CustomsPrintListResponse response = new CustomsPrintListResponse();
        List<String> htmlList = new ArrayList<>();
        if ("ALL".equals(type)) {
            List<String> types = Arrays.stream(CustomsDeclareDocumentFileTypeEnum.values()).map(CustomsDeclareDocumentFileTypeEnum::name).collect(Collectors.toList());
            types.forEach(item -> htmlList.add(this.generateDocument(declareDocumentId, item)));
        } else {
            htmlList.add(this.generateDocument(declareDocumentId, type));
        }
        response.setHtmlList(htmlList);
        response.setTemplateName(type);
        response.setSpec("210*279");
        BdCompanyEntity company = bdCompanyService.getById(stockoutCustomsDeclareDocumentService.getById(declareDocumentId).getCompanyId());
        response.setImgUrl(company.getElectronicSeal());
        //已解决
        stockoutCustomsDeclareDocumentService.lambdaUpdate().set(StockoutCustomsDeclareDocumentEntity::getStatus, StockoutCustomsDeclareOrderStatusEnum.DEAL.name())
                .eq(StockoutCustomsDeclareDocumentEntity::getDeclareDocumentId, declareDocumentId).update();
        return response;
    }


    @Transactional
    public void resetItem(Integer declareDocumentId) {
        StockoutCustomsDeclareDocumentEntity declareDocument = stockoutCustomsDeclareDocumentService.getById(declareDocumentId);
        if (Objects.isNull(declareDocument) || IsDeletedConstant.DELETED.equals(declareDocument.getIsDeleted()))
            throw new BusinessServiceException("单据不存在");
        if (declareDocument.getIsAudit())
            throw new BusinessServiceException("报关单据已审核无法修改");
        //获取该documentId下的所有报关订单号,
        List<Integer> declareOrderIdList = listByDocumentId(declareDocumentId).stream().map(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId).distinct().collect(Collectors.toList());
        List<StockoutCustomsDocumentCount> documentCount = stockoutCustomsDeclareOrderService.getDocumentCount(declareOrderIdList, declareDocument.getCompanyId(), declareDocument);
        //删除明细
        removeByDeclareDocumentId(declareDocumentId);
        //生成关单明细
        stockoutCustomsDeclareOrderService.buildDocumentItem(documentCount, declareOrderIdList, declareDocument);
        //计算总的fob价
        List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = this.listByDocumentId(declareDocument.getDeclareDocumentId());
        declareDocument.setTotalFobPrice(itemEntityList.stream().map(item -> item.getFobPrice().multiply(BigDecimal.valueOf(item.getQty()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        stockoutCustomsDeclareDocumentService.updateById(declareDocument);
        //更新运费
        updateApportionFreight(declareDocument);
        //更新实际箱数
        updateActualBoxQty(declareDocument.getDeclareDocumentId());
        //更新重量
        updatePlanWeight(declareDocument.getDeclareDocumentId());
        //生成聚合明细
        declareDocumentAggregatedItemService.resetAggregatedItem(declareDocument.getDeclareDocumentId(), declareDocument.getDeclareDocumentNo());
    }

    /**
     * 删除明细
     *
     * @param declareDocumentId
     */
    private void removeByDeclareDocumentId(Integer declareDocumentId) {
        //删除该id下的所有item数据
        remove(Wrappers.<StockoutCustomsDeclareDocumentItemEntity>lambdaQuery()
                .eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentId, declareDocumentId));
    }

    public DeclareDocumentItemWeightChangeResponse getInfoWhenWeightChanged(DeclareDocumentItemWeightChangeRequest request) {
        List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = this.listByIds(request.getDeclareDocumentItemIdList());
        if (CollectionUtils.isEmpty(itemEntityList)) {
            throw new BusinessServiceException("子表数据不存在");
        }
        StockoutCustomsDeclareDocumentEntity entity = stockoutCustomsDeclareDocumentService.getById(itemEntityList.get(0).getDeclareDocumentId());
        DeclareDocumentItemWeightChangeResponse response = new DeclareDocumentItemWeightChangeResponse();
        int sum = itemEntityList.stream().mapToInt(StockoutCustomsDeclareDocumentItemEntity::getQty).sum();
        int planBoxNum = request.getWeight().multiply(BigDecimal.valueOf(sum)).divide(BigDecimal.valueOf(21), 0, RoundingMode.HALF_UP).intValue();
        List<StockoutCustomsDeclareDocumentItemEntity> itemList = this.listByDocumentId(itemEntityList.get(0).getDeclareDocumentId());
        itemList.stream().filter(item -> request.getDeclareDocumentItemIdList().contains(item.getDeclareDocumentItemId())).forEach(item -> item.setWeight(request.getWeight()));
        BigDecimal totalPlanWeight = itemList.stream().map(item -> BigDecimal.valueOf(item.getQty()).multiply(item.getWeight())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        int totalPlanBoxQty = totalPlanWeight.divide(BigDecimal.valueOf(21), 0, RoundingMode.HALF_UP).intValue();
        response.setPlanBoxNum(planBoxNum);
        int actualBoxNum = 0;
        if (totalPlanBoxQty != 0) {
            actualBoxNum = BigDecimal.valueOf(planBoxNum).multiply(BigDecimal.valueOf(entity.getBoxNum()))
                    .divide(BigDecimal.valueOf(totalPlanBoxQty), 0, BigDecimal.ROUND_HALF_UP).intValue();
        }
        response.setActualBoxNum(actualBoxNum);
        BigDecimal actualRoughWeight = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(totalPlanWeight) < 0) {
            actualRoughWeight = request.getWeight().multiply(BigDecimal.valueOf(sum)).multiply(entity.getWeight()
                    .divide(totalPlanWeight, 9, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
        }
        response.setActualRoughWeight(actualRoughWeight);
        BigDecimal actualNetWeight = actualRoughWeight.subtract(WEIGHT_COEFFICIENT.multiply(BigDecimal.valueOf(actualBoxNum)));
        response.setActualNetWeight(actualNetWeight);
        return response;
    }

    /**
     * 获取清关发票列表
     *
     * @param declareOrderIds 报关订单号
     * @return 清关发票列表
     */
    public List<ProformaInvoiceItemResponse> getProformaInvoiceItemList(List<Integer> declareOrderIds) {
        List<ProformaInvoiceItemResponse> proformaInvoiceItemList = baseMapper.getProformaInvoiceItemList(declareOrderIds);
        if (proformaInvoiceItemList.stream().anyMatch(item -> StringUtils.isBlank(item.getFabricTypeEn()) && item.getUserPrice().compareTo(BigDecimal.ZERO) == 0)) {
            proformaInvoiceItemList = complementedItemList(declareOrderIds);
        }
        Map<String, String> makeTypeMap = enumConversionChineseUtils.inverseBaseConversion(DictionaryNameEnum.WMS_MAKE_TYPE.getName());
        proformaInvoiceItemList.forEach(item -> {
            String spinTypeEn = makeTypeMap.get(item.getSpinType());
            item.setUserPrice(item.getUserPrice().divide(productCategoryCustomsDeclareService.getExchangeRate(), 2, BigDecimal.ROUND_HALF_UP));
            item.setDescriptionOfGoods(String.format("%s（MADE IN CHINA） %s %s %s", item.getCustomsDeclareEn(), item.getFabricTypeEn(), spinTypeEn, item.getHsCode()));
        });
        return proformaInvoiceItemList;
    }

    /**
     * 完善数据
     *
     * @param declareOrderIds 报关单号List
     * @return 修改后的数据
     */
    private List<ProformaInvoiceItemResponse> complementedItemList(List<Integer> declareOrderIds) {
        List<StockoutCustomsDeclareDocumentItemEntity> list = this.list(Wrappers.<StockoutCustomsDeclareDocumentItemEntity>lambdaQuery()
                .in(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId, declareOrderIds));
        // 初始化网上价格和成分英文
        list.forEach(item -> {
            ProformaInvoiceItemResponse userPriceAndFabricTypeEn = baseMapper.getUserPriceAndFabricTypeEn(item.getSellerSku());
            item.setFabricTypeEn(userPriceAndFabricTypeEn.getFabricTypeEn());
            item.setUserPrice(userPriceAndFabricTypeEn.getUserPrice());
        });
        updateBatchById(list);
        return baseMapper.getProformaInvoiceItemList(declareOrderIds);
    }

    /**
     * 以订单维度获取箱数
     *
     * @param declareOrderIds 报关订单idList
     * @return 箱数
     */
    public Long getBoxNum(List<Integer> declareOrderIds) {
        List<StockoutCustomsDeclareDocumentItemEntity> list = this.list(Wrappers.<StockoutCustomsDeclareDocumentItemEntity>lambdaQuery()
                .select(StockoutCustomsDeclareDocumentItemEntity::getShipmentId)
                .in(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId, declareOrderIds));
        return list.stream().map(StockoutCustomsDeclareDocumentItemEntity::getShipmentId).distinct().count();
    }

    /**
     * 计算汇率
     *
     * @param price
     * @param oldCurrency
     * @param newCurreny
     * @param exchangeRate
     * @return
     */
    private BigDecimal calculateExchangeRate(BigDecimal price, String oldCurrency, String newCurreny, BigDecimal exchangeRate, int scale) {
        if (oldCurrency.equals(newCurreny))
            return price;
        //美元转人民币 乘以汇率
        if (CurrencyTypeEnum.USD.name().equals(oldCurrency) && CurrencyTypeEnum.CNY.name().equals(newCurreny)) {
            return price.multiply(exchangeRate).setScale(scale, RoundingMode.HALF_UP);
            //美元转人民币 除以汇率
        } else if (CurrencyTypeEnum.CNY.name().equals(oldCurrency) && CurrencyTypeEnum.USD.name().equals(newCurreny)) {
            return price.divide(exchangeRate, scale, RoundingMode.HALF_UP);
        }
        throw new BusinessServiceException(String.format("不存在从 %s 到 %s 的计算", oldCurrency, newCurreny));
    }

    /**
     * 通过聚合表id查找
     *
     * @param declareDocumentAggregatedItemId
     * @return
     */
    public List<StockoutCustomsDeclareDocumentItemEntity> getByDeclareDocumentAggregatedItemId(Integer declareDocumentAggregatedItemId) {
        return list(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentItemEntity>()
                .eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentAggregatedItemId, declareDocumentAggregatedItemId));
    }
}
