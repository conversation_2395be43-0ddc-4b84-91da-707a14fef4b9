package com.nsy.wms.business.service.stockin;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemQcInfo;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.domain.stockin.StockinReturnProductItem;
import com.nsy.api.wms.enumeration.QcInboundsResultStatusEnum;
import com.nsy.api.wms.enumeration.StockinShelveEventEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskStatusEnum;
import com.nsy.api.wms.request.external.ErpBarcodeSkuRequest;
import com.nsy.api.wms.response.external.ErpInternalBoxSkuResponse;
import com.nsy.api.wms.response.external.QcPurchaseCount;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.bo.stockin.StockinQaTaskUpdateBo;
import com.nsy.wms.business.manage.notify.NotifyApiService;
import com.nsy.wms.business.manage.notify.request.DingTalkCorpConversationMessageRequest;
import com.nsy.wms.business.manage.supplier.enumeration.InboundsRequiredCheckPushTypeEnum;
import com.nsy.wms.business.manage.supplier.request.InboundsRequiredCheck;
import com.nsy.wms.business.manage.supplier.request.SyncInboundsRequiredCheckRequest;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.internal.common.PurchaseModuleService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxAdjustService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxQueryWrapper;
import com.nsy.wms.business.service.stockin.facade.StockinQcFacade;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinQcInboundsEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinQcService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQcService.class);

    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockPlatformScheduleService stockPlatformScheduleService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockinShelveTaskService stockinShelveTaskService;
    @Autowired
    StockinReturnProductService stockinReturnProductService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockinOrderLogService stockinOrderLogService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinReturnProductService returnProductService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinQcBuildService stockinQcBuildService;
    @Autowired
    ExternalApiLogService externalApiLogService;
    @Autowired
    StockinShelveLogService stockinShelveLogService;
    @Autowired
    StockinQcInboundsService stockinQcInboundsService;
    @Autowired
    StockinQcPreService stockinQcPreService;
    @Autowired
    StockinOrderAutoCompleteService stockinOrderAutoCompleteService;
    @Autowired
    StockinQcFacade stoCkinQcFacade;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    PurchaseModuleService purchaseModuleService;
    @Autowired
    StockInternalBoxAdjustService stockInternalBoxAdjustService;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private NotifyApiService notifyApiService;
    @Autowired
    private UserApiService userApiService;

    /**
     * 入库生成质检任务
     */
    public void generateQcTask(StockInternalBoxEntity boxEntity, StockinOrderTaskEntity taskEntity, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(taskEntity.getTaskId());
        //校验质检箱，判断商品是否需要质检
        List<StockInternalBoxItemEntity> boxItemEntityList = validQcBox(boxEntity, stockInternalBoxItemEntityList);
        if (CollectionUtils.isEmpty(boxItemEntityList)) return;
        Map<String, List<StockinOrderTaskItemEntity>> collect = stockinOrderTaskItemService.list(new LambdaQueryWrapper<StockinOrderTaskItemEntity>()
                .eq(StockinOrderTaskItemEntity::getTaskId, taskEntity.getTaskId())).stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getSku));
        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(boxItemEntityList.stream().map(StockInternalBoxItemEntity::getSku).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))));
        StockPlatformScheduleEntity stockPlatformScheduleEntity = stockPlatformScheduleService.getBySupplierDeliveryNo(taskEntity.getSupplierDeliveryNo());
        List<InboundsRequiredCheck> list = new ArrayList<>(8);
        for (StockInternalBoxItemEntity itemEntity : boxItemEntityList) {
            InboundsRequiredCheck inboundsRequiredCheck = stoCkinQcFacade.getInboundsRequiredCheck(boxEntity, taskEntity, collect, specMap, itemEntity);
            if (inboundsRequiredCheck == null) continue;
            List<QcPurchaseCount> purchaseCount = stoCkinQcFacade.getPurchaseCount(Collections.singletonList(stockinOrderEntity.getStockinOrderId()), itemEntity.getInternalBoxCode(), itemEntity.getSku(), itemEntity.getPurchasePlanNo());
            inboundsRequiredCheck.setPurchaseCountList(purchaseCount);
            inboundsRequiredCheck.setProductInboundsCount(purchaseCount.stream().mapToInt(QcPurchaseCount::getQty).sum());
            //全部退货则赋值为数量为生成已关闭的质检任务，避免在后续质检时错误
            if (purchaseCount.stream().mapToInt(QcPurchaseCount::getQty).sum() == purchaseCount.stream().mapToInt(QcPurchaseCount::getReturnQty).sum()) {
                inboundsRequiredCheck.setProductInboundsCount(0);
            }
            if (Objects.nonNull(stockPlatformScheduleEntity)) {
                stockInternalBoxAdjustService.buildSupplierInfoByPlatformScheduleId(inboundsRequiredCheck, stockPlatformScheduleEntity.getPlatformScheduleId(), inboundsRequiredCheck.getSku());
            } else {
                inboundsRequiredCheck.setSupplierId(stockinOrderEntity.getSupplierId());
                inboundsRequiredCheck.setSupplierName(stockinOrderEntity.getSupplierName());
            }
            list.add(inboundsRequiredCheck);
        }
        if (CollectionUtils.isEmpty(list)) return;
        //分割请求，防止q_message 的content过长报错
        List<List<InboundsRequiredCheck>> lists = ListUtil.partition(list, 5);
        lists.forEach(listItem -> {
            SyncInboundsRequiredCheckRequest syncInboundsRequiredCheckRequest = new SyncInboundsRequiredCheckRequest();
            syncInboundsRequiredCheckRequest.setInboundsRequiredChecks(listItem);
            messageProducer.sendMessage(KafkaConstant.SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC_NAME, KafkaConstant.SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), syncInboundsRequiredCheckRequest));
        });
        // 生成入库单日志
        createQcAddStockinOrderLog(boxEntity.getInternalBoxCode(), taskEntity.getTaskId());
    }

    /**
     * 通过【质检前置】质检通过的SKU不再生成质检任务
     */
    public List<StockInternalBoxItemEntity> validQcBox(StockInternalBoxEntity boxEntity, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        // 1.若是质检箱，默认都要生成质检任务
        if (StockInternalBoxTypeEnum.QA_BOX.name().equals(boxEntity.getInternalBoxType()))
            return stockInternalBoxItemEntityList;
        // 2.校验入库任务明细
        return stockInternalBoxItemEntityList.stream().filter(item -> StockinOrderItemStatusEnum.WAIT_QC.name().equals(item.getStatus())
                || StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus()) && StockConstant.ENABLE.equals(item.getIsFbaQuick())
        ).collect(Collectors.toList());
    }

    private void createQcAddStockinOrderLog(String internalBoxCode, Integer taskId) {
        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.list(new LambdaQueryWrapper<StockinOrderTaskItemEntity>().eq(StockinOrderTaskItemEntity::getTaskId, taskId));
        if (!CollectionUtils.isEmpty(taskItemEntityList)) {
            StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(taskId);
            if (!StockinOrderStatusEnum.PENDING_QC.name().equals(stockinOrderEntity.getStatus()))
                stockinOrderService.changeStatus(stockinOrderEntity, StockinOrderStatusEnum.PENDING_QC.name());
            Integer stockinQty = taskItemEntityList.stream().filter(item -> item.getStockinQty() != null).mapToInt(m -> m.getStockinQty()).sum();
            Integer qaQty = taskItemEntityList.stream().filter(item -> item.getQaQty() != null && item.getQaQty() > 0).mapToInt(m -> m.getQaQty()).sum();
            String content = String.format("内部箱号【%s】,生成质检任务，入库数 %s 件，需质检数 %s 件", internalBoxCode, stockinQty, qaQty);
            stockinOrderLogService.addLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.CREATE_QC_TASK.getName(), content);
        }
    }

    @Transactional
    public void qcComplete(QcInboundsMessage messageContent) {
        StockInternalBox internalBox = stockInternalBoxService.getInternalBox(messageContent.getBoxBarcode());
        if (Objects.isNull(internalBox))
            return;
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.QC_RESULTS_FEEDBACK, "qc/complete",
                JsonMapper.toJson(messageContent), messageContent.getSku(), "QMS质检结果反馈给WMS");
        if (ObjectUtils.isEmpty(messageContent.getGoodsToRefundCount())) {
            messageContent.setGoodsToRefundCount(0);
        }
        // 已推送QA质检
        if (Objects.nonNull(messageContent.getIsQcRoutingInspect())
                && messageContent.getIsQcRoutingInspect().equals(1)
                && !StringUtils.hasText(messageContent.getResult())) {
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
            return;
        }
        try {
            if (StockInternalBoxTypeEnum.RECEIVE_BOX.name().equals(internalBox.getInternalBoxType())
                    || StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBox.getInternalBoxType())) {
                if (Objects.nonNull(messageContent.getIsQcRoutingInspect()) && messageContent.getIsQcRoutingInspect().equals(1))
                    this.startQc(internalBox.getInternalBoxCode(), messageContent.getSku());
                //添加质检结果表
                stockinQcInboundsService.saveQcInbounds(messageContent);

                //入库质检
                stockinQc(messageContent, internalBox);
                //如果带有退货数，可能是巡检，需要退货
                if (Objects.nonNull(messageContent.getGoodsToRefundCount()) && messageContent.getGoodsToRefundCount() > 0)
                    this.addReturnQuantity(messageContent, true);
            }
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    /**
     * 入库质检结果处理
     */
    private void stockinQc(QcInboundsMessage messageContent, StockInternalBox internalBox) {
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxId, internalBox.getInternalBoxId())
                .eq(StockInternalBoxItemEntity::getSku, messageContent.getSku())
                .ne(StockInternalBoxItemEntity::getQty, 0)
                .isNotNull(StockInternalBoxItemEntity::getStockInOrderNo));
        if (CollectionUtils.isEmpty(list))
            return;
        //质检箱质检合格需要校验入库单
        if (StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBox.getInternalBoxType())
                && (Objects.isNull(messageContent.getIsAllCheck()) || messageContent.getIsAllCheck().equals(0))) {
            if (QcInboundsResultStatusEnum.PUT_ON.getDesc().equals(messageContent.getResult())
                    || QcInboundsResultStatusEnum.CONCESSION_RECEIVE.getDesc().equals(messageContent.getResult())
                    || QcInboundsResultStatusEnum.RECEIVE_BUT_SOME_RETURN.getDesc().equals(messageContent.getResult())) {
                //修改任务明细为质检合格上架
                stockinQcPreService.updateTaskItem(list);
            } else if (QcInboundsResultStatusEnum.FACTORY_REWORK.getDesc().equals(messageContent.getResult()))
                //修改任务明细为质检全部退货
                stockinQcPreService.returnTaskItem(list);
        }
        //更新本箱子sku商品状态
        stockinQcBuildService.stockinQcCompleteUpdateItem(messageContent, list);
        stockInternalBoxService.updateInternalBoxStatusByItem(messageContent.getBoxBarcode());
        // 更新入库单状态 + 添加入库单退货日志
        list.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).distinct().forEach(stockInOrderNo -> {
            if (Objects.isNull(messageContent.getGoodsToRefundCount()))
                messageContent.setGoodsToRefundCount(0);
            qcCompleteAddStockinOrderLog(internalBox.getInternalBoxCode(), messageContent.getGoodsToRefundCount(), messageContent.getSku(), stockInOrderNo);
        });
        //让步接收更新入库单明细的让步接收数
        if (Objects.nonNull(messageContent.getConcessionsCount()) && messageContent.getConcessionsCount() > 0) {
            stockinQcBuildService.updateConcessionsCount(list, messageContent);
            messageContent.setAsyncErp(Boolean.TRUE);
        }
    }

    /**
     * 退货
     */
    public List<StockinReturnProductItem> returnProduct(QcInboundsMessage messageContent, List<StockInternalBoxItemEntity> list) {
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(list.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList()));
        List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.listByIds(stockinOrderEntityList.stream().map(StockinOrderEntity::getTaskId).collect(Collectors.toList()));
        Map<Integer, String> collect = stockinOrderTaskEntityList.stream().collect(Collectors.toMap(StockinOrderTaskEntity::getTaskId, StockinOrderTaskEntity::getSupplierDeliveryNo));
        Map<Integer, StockinOrderEntity> stockinOrderEntityMap = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderId, entity -> entity));
        //修改入库单退货数 + 修改库存
        String[] purchaseNumbers = messageContent.getPurchaseNumbers().split(",");
        List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.list(new LambdaQueryWrapper<StockinOrderItemEntity>()
                .in(StockinOrderItemEntity::getStockinOrderId, stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList()))
                .eq(StockinOrderItemEntity::getSku, messageContent.getSku()).eq(StockinOrderItemEntity::getInternalBoxCode, messageContent.getBoxBarcode())
                .in(StockinOrderItemEntity::getPurchasePlanNo, Arrays.asList(purchaseNumbers)));
        if (CollectionUtils.isEmpty(stockinOrderItemEntityList))
            throw new BusinessServiceException("未找到入库单明细!");
        AtomicReference<Integer> resultOperateCount = new AtomicReference<>(messageContent.getGoodsToRefundCount());
        List<StockinOrderItemEntity> orderItemEntityList = new ArrayList<>();
        List<StockInternalBoxItemEntity> boxItemEntityList = new ArrayList<>();
        List<StockinReturnProductItem> returnProductItems = new ArrayList<>();
        //如果存在让步接收数，需要判断是否可以退货
        stoCkinQcFacade.validConcessionsCount(stockinOrderItemEntityList, resultOperateCount.get());
        stockinOrderItemEntityList.forEach(item -> {
            if (resultOperateCount.get() > 0) {
                StockinOrderEntity stockinOrderEntity = stockinOrderEntityMap.get(item.getStockinOrderId());
                Optional<StockInternalBoxItemEntity> any = list.stream().filter(boxItemEntity -> boxItemEntity.getStockInOrderNo().equals(stockinOrderEntity.getStockinOrderNo())
                        && boxItemEntity.getPurchasePlanNo().equals(item.getPurchasePlanNo())).findAny();
                if (!any.isPresent())
                    return;
                StockInternalBoxItemEntity boxItemEntity = any.get();
                Integer canReturnQty = Math.min(item.getQty() - item.getReturnQty() - item.getConcessionsCount(), boxItemEntity.getQty());
                if (canReturnQty <= 0)
                    return;
                Integer qty = Math.min(resultOperateCount.get(), canReturnQty);
                Integer boxQty = boxItemEntity.getQty();
                StockChangeLogTypeEnum qaType = messageContent.getQaType();
                stockInternalBoxItemService.minusStockInternalBoxItemQty(boxItemEntity, qty, qaType, StockChangeLogTypeModuleEnum.QC, null);
                StockinOrderItemEntity orderItemEntity = getStockinOrderItemEntity(messageContent, item, qty);
                if (qty < boxQty)
                    boxItemEntityList.add(getboxItemEntity(boxItemEntity, orderItemEntity));
                orderItemEntityList.add(orderItemEntity);
                resultOperateCount.set(resultOperateCount.get() - qty);
                returnProductItems.add(new StockinReturnProductItem(stockinOrderEntity.getStockinOrderId(), stockinOrderEntity.getStockinOrderNo(), collect.get(stockinOrderEntity.getTaskId()), qty, item.getPurchasePlanNo()));
            }
        });
        stockinOrderItemService.updateBatchById(orderItemEntityList);
        if (!CollectionUtils.isEmpty(boxItemEntityList))
            stockInternalBoxItemService.updateBatchById(boxItemEntityList);
        //更新退货库位
        stoCkinQcFacade.buildStockinReturnProductAddRequest(messageContent, returnProductItems).forEach(request -> returnProductService.alterReturnProduct(request, Boolean.FALSE));
        //判断入库单是否全部退货，是则完结掉
        stockinOrderAutoCompleteService.autoComplete(stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList()));
        return returnProductItems;
    }

    private StockInternalBoxItemEntity getboxItemEntity(StockInternalBoxItemEntity boxItemEntity, StockinOrderItemEntity orderItemEntity) {
        StockInternalBoxItemEntity itemEntity = new StockInternalBoxItemEntity();
        itemEntity.setStatus(orderItemEntity.getStatus());
        itemEntity.setInternalBoxItemId(boxItemEntity.getInternalBoxItemId());
        return itemEntity;
    }

    @NotNull
    private StockinOrderItemEntity getStockinOrderItemEntity(QcInboundsMessage messageContent, StockinOrderItemEntity item, Integer qty) {
        StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
        orderItemEntity.setReturnQty(item.getReturnQty() + qty);
        orderItemEntity.setUnqualifiedReason(messageContent.getUnqualifiedReason());
        orderItemEntity.setUnqualifiedCategory(messageContent.getUnqualifiedCategory());
        orderItemEntity.setStockinOrderItemId(item.getStockinOrderItemId());
        orderItemEntity.setUpdateBy(loginInfoService.getName());
        int sum = orderItemEntity.getReturnQty() + item.getShelvedQty();
        if (sum >= item.getQty()) {
            if (item.getShelvedQty() > 0) {
                orderItemEntity.setStatus(StockinOrderItemStatusEnum.SHELVED.name());
                orderItemEntity.setStockinOrderId(item.getStockinOrderId());
                purchaseModuleService.pushStockinToEtl(orderItemEntity, item.getStockinOrderItemId(), null);
            } else
                orderItemEntity.setStatus(StockinOrderItemStatusEnum.RETURNED.name());

        }
        return orderItemEntity;
    }

    public void qcCompleteAddStockinOrderLog(String internalBoxCode, int returnQty, String sku, String stockinOrderNo) {
        String content = String.format("内部箱【%s】质检完成，SKU【%s】退货【%s】件", internalBoxCode, sku, returnQty);
        StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(stockinOrderNo);
        //更新入库单状态
        if (StockinOrderStatusEnum.RECEIVING.name().equals(stockinOrderEntity.getStatus())
                || StockinOrderStatusEnum.PENDING_QC.name().equals(stockinOrderEntity.getStatus())
                || StockinOrderStatusEnum.QC_PROCESSING.name().equals(stockinOrderEntity.getStatus())) {
            StockinOrderEntity stockinOrderEntityNew = new StockinOrderEntity();
            stockinOrderEntityNew.setStockinOrderId(stockinOrderEntity.getStockinOrderId());
            stockinOrderEntityNew.setStatus(StockinOrderStatusEnum.PENDING_SHELVE.name());
            stockinOrderService.updateById(stockinOrderEntityNew);
        }
        stockinOrderLogService.addLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.SCAN_INTERNALBOX_QC.getName(), content);
        // 是否有上架任务
        StockinShelveTaskEntity taskEntity = stockinShelveTaskService.getOne(StockinShelveTaskService.buildWrapperTaskByInternalBoxCode(internalBoxCode,
                Arrays.asList(StockinShelveTaskStatusEnum.PENDING.name(), StockinShelveTaskStatusEnum.SHELVING.name())).last(MybatisQueryConstant.QUERY_FIRST));
        if (taskEntity != null)
            // 添加上架任务质检信息
            stockinShelveLogService.addShelveLog(StockinShelveEventEnum.SCAN_INTERNALBOX_QC, taskEntity.getShelveTaskId(), content);
    }

    /**
     * 根据箱号和sku查询内部箱中的sku信息
     */
    public ErpInternalBoxSkuResponse getSkuByBarcode(ErpBarcodeSkuRequest request) {
        List<StockInternalBoxItemQcInfo> boxItemQcInfoList = stockInternalBoxItemService.getBaseMapper().searchByInternalBoxCode(request.getInternalBoxNo());
        if (CollectionUtils.isEmpty(boxItemQcInfoList))
            throw new BusinessServiceException(String.format("内部箱号【%s】找不到相关的内部箱明细", request.getInternalBoxNo()));
        List<StockInternalBoxItemQcInfo> boxItemList = boxItemQcInfoList.stream().filter(t -> request.getBarCode().equals(t.getBarcode()) || request.getBarCode().equalsIgnoreCase(t.getSku())
                || request.getBarCode().equalsIgnoreCase(t.getSellerBarcode())
                || request.getBarCode().equalsIgnoreCase(t.getSellerSku())
                || request.getBarCode().equalsIgnoreCase(t.getStockinBarcode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(boxItemList) || boxItemList.stream().mapToInt(StockInternalBoxItemQcInfo::getQty).sum() == 0) {
            ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopByBarcode(request.getBarCode());
            if (Objects.isNull(specInfoEntity))
                throw new BusinessServiceException(request.getInternalBoxNo() + "内部箱中没有找到" + request.getBarCode() + "的信息，请核对sku是否正确");

            request.setBarCode(specInfoEntity.getBarcode());
            boxItemList = boxItemQcInfoList.stream().filter(t -> request.getBarCode().equals(t.getBarcode()) || request.getBarCode().equalsIgnoreCase(t.getSku())
                    || request.getBarCode().equalsIgnoreCase(t.getSellerBarcode())
                    || request.getBarCode().equalsIgnoreCase(t.getSellerSku())
                    || request.getBarCode().equalsIgnoreCase(t.getStockinBarcode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(boxItemList) || boxItemList.stream().mapToInt(StockInternalBoxItemQcInfo::getQty).sum() == 0)
                throw new BusinessServiceException(request.getInternalBoxNo() + "内部箱中没有找到" + request.getBarCode() + "的信息，请核对sku是否正确");
        }

        StockInternalBox internalBox = stockInternalBoxService.getInternalBox(request.getInternalBoxNo());
        ErpInternalBoxSkuResponse erpInternalBoxSkuResponse;
        if (StockInternalBoxTypeEnum.RECEIVE_BOX.name().equals(internalBox.getInternalBoxType())
                || StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBox.getInternalBoxType())) {
            //入库质检
            erpInternalBoxSkuResponse = stockinQcBuildService.buildStockinBoxSkuResponse(internalBox, boxItemList);
        } else
            //销售退货质检
            erpInternalBoxSkuResponse = stockinQcBuildService.buildSkuResponse(internalBox, boxItemList);
        return erpInternalBoxSkuResponse;
    }

    /**
     * 质检扫描内部箱
     */
    public StockInternalBox getInternalBoxByQc(String internalBoxCode) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxService.getOne(queryWrapper);
        if (boxEntity == null)
            throw new BusinessServiceException(String.format("未找到内部箱号为【%s】的内部箱!", internalBoxCode));
        //校验入库任务是否完成
        List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemService.getByInternalBoxCode(boxEntity.getInternalBoxCode()).stream().filter(item -> item.getQty() > 0).collect(Collectors.toList());
        List<String> collect = boxItemEntityList.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect))
            throw new BusinessServiceException("内部箱内商品找不到入库单！");
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(collect);
        if (CollectionUtils.isEmpty(stockinOrderEntityList))
            throw new BusinessServiceException("内部箱内商品找不到入库单！");
        List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.listByIds(stockinOrderEntityList.stream().map(StockinOrderEntity::getTaskId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(stockinOrderTaskEntityList))
            throw new BusinessServiceException("内部箱内商品找不到入库任务！");
//        Optional<StockinOrderTaskEntity> any = stockinOrderTaskEntityList.stream().filter(entity -> StockinTypeEnum.FACTORY.name().equals(entity.getStockinType()) && !StockinOrderTaskStatusEnum.RECEIVED.name().equals(entity.getStatus())).findAny();
//        if (any.isPresent() && !StockInternalBoxTypeEnum.QA_BOX.name().equals(boxEntity.getInternalBoxType())) {
//            StockPlatformScheduleEntity byId = stockPlatformScheduleService.getById(any.get().getPlatformScheduleId());
//            throw new BusinessServiceException(String.format("%s，入库任务号%s，未完成收货，请先完成", byId.getSupplierName(), any.get().getSupplierDeliveryBoxCode()));
//        }
        StockInternalBox stockInternalBox = new StockInternalBox();
        BeanUtilsEx.copyProperties(boxEntity, stockInternalBox);
        stockInternalBox.setStockinType(stockinOrderTaskEntityList.get(0).getStockinType());
        return stockInternalBox;
    }

    /**
     * 开始质检通知
     */
    @Transactional
    public void startQc(String internalBoxCode, String sku) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxService.getOne(queryWrapper);
        stockInternalBoxService.changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.QC_PROCESSING.name());

        LambdaQueryWrapper<StockInternalBoxItemEntity> taskItemQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                .eq(StockInternalBoxItemEntity::getSku, sku)
                .ne(StockInternalBoxItemEntity::getQty, 0)
                .isNotNull(StockInternalBoxItemEntity::getStockInOrderNo);
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemService.list(taskItemQueryWrapper)
                .stream().filter(item -> !StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList))
            return;
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(stockInternalBoxItemEntityList.stream()
                .map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList()));
        //更新入库单状态并记录日志
        stockinOrderLogService.qaRecordStockinOrderLog(internalBoxCode, sku, stockinOrderEntityList);
        //更新内部箱和入库单明细状态
        Map<String, StockinOrderEntity> collect = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderNo, Function.identity()));
        List<StockinOrderItemEntity> orderItemEntityList = new ArrayList<>(stockInternalBoxItemEntityList.size());
        List<StockInternalBoxItemEntity> internalBoxItemEntities = stockInternalBoxItemEntityList.stream().map(item -> {
            StockinOrderEntity stockinOrderEntity = collect.get(item.getStockInOrderNo());
            StockinOrderItemEntity stockinOrderItemEntity = stockinOrderItemService.findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndPurchasePlanNo(stockinOrderEntity.getStockinOrderId(), sku, internalBoxCode, item.getPurchasePlanNo(), "");
            StockInternalBoxItemEntity itemEntity = new StockInternalBoxItemEntity();
            itemEntity.setInternalBoxItemId(item.getInternalBoxItemId());
            itemEntity.setStatus(StockinOrderItemStatusEnum.QC_PROCESSING.name());
            itemEntity.setUpdateBy(loginInfoService.getName());
            StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
            orderItemEntity.setStockinOrderItemId(stockinOrderItemEntity.getStockinOrderItemId());
            orderItemEntity.setStatus(StockinOrderItemStatusEnum.QC_PROCESSING.name());
            orderItemEntity.setUpdateBy(loginInfoService.getName());
            orderItemEntityList.add(orderItemEntity);
            return itemEntity;
        }).collect(Collectors.toList());
        stockinOrderItemService.updateBatchById(orderItemEntityList);
        stockInternalBoxItemService.updateBatchById(internalBoxItemEntities);
    }

    @Transactional
    public void notifyWaitDeal(String internalBoxCode, String sku) {
        LambdaQueryWrapper<StockInternalBoxItemEntity> taskItemQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                .eq(StockInternalBoxItemEntity::getSku, sku)
                .ne(StockInternalBoxItemEntity::getQty, 0)
                .isNotNull(StockInternalBoxItemEntity::getStockInOrderNo);
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemService.list(taskItemQueryWrapper);
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList))
            return;
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(stockInternalBoxItemEntityList.stream()
                .map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList()));
        stoCkinQcFacade.updateBoxItemStatus(stockinOrderEntityList, stockInternalBoxItemEntityList, internalBoxCode, sku, StockinOrderItemStatusEnum.WAIT_DEAL);

        //质检前置的收货箱处理
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxService.getOne(queryWrapper);
        if (StockInternalBoxTypeEnum.QA_BOX.name().equals(boxEntity.getInternalBoxType()))
            stockinQcPreService.notifyWaitDeal(stockinOrderEntityList, sku);
    }

    @Transactional
    public void notifyWaitPurchaseConfirm(String internalBoxCode, String sku) {
        LambdaQueryWrapper<StockInternalBoxItemEntity> taskItemQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                .eq(StockInternalBoxItemEntity::getSku, sku)
                .ne(StockInternalBoxItemEntity::getQty, 0)
                .isNotNull(StockInternalBoxItemEntity::getStockInOrderNo);
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemService.list(taskItemQueryWrapper);
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList))
            return;
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(stockInternalBoxItemEntityList.stream()
                .map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList()));
        stoCkinQcFacade.updateBoxItemStatus(stockinOrderEntityList, stockInternalBoxItemEntityList, internalBoxCode, sku, StockinOrderItemStatusEnum.WAIT_PURCHASE_CONFIRM);
    }

    public void upShelveUpdateQcTask(List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        try {


            Map<String, List<StockInternalBoxItemEntity>> collect = stockInternalBoxItemEntityList.stream().collect(Collectors.groupingBy(item -> item.getInternalBoxCode() + "_" + item.getSku()));
            collect.forEach((key, value) -> {
                StockInternalBoxItemEntity stockInternalBoxItemInfo = value.get(0);
                //生成或更新质检任务
                messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_TASK_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_TASK_TOPIC,
                        Key.of(stockInternalBoxItemInfo.getInternalBoxCode() + "_" + stockInternalBoxItemInfo.getSku()),
                        new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(),
                                new StockinQaTaskUpdateBo(stockInternalBoxItemInfo.getInternalBoxCode(), stockInternalBoxItemInfo.getSku())));
            });

            List<InboundsRequiredCheck> list = new ArrayList<>(stockInternalBoxItemEntityList.size());
            for (StockInternalBoxItemEntity itemEntity : stockInternalBoxItemEntityList) {
                InboundsRequiredCheck inboundsRequiredCheck = new InboundsRequiredCheck();
                if (Objects.nonNull(itemEntity.getStockInOrderNo())) {
                    StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(itemEntity.getStockInOrderNo());
                    StockinOrderTaskEntity byId = stockinOrderTaskService.getById(stockinOrderEntity.getTaskId());
                    List<StockinOrderTaskItemEntity> allByTaskIdAndSpecId = stockinOrderTaskItemService.findAllByTaskIdAndSpecId(byId.getTaskId(), itemEntity.getSpecId());
                    if (CollectionUtils.isEmpty(allByTaskIdAndSpecId))
                        continue;
                    if (!allByTaskIdAndSpecId.stream().allMatch(item -> item.getIsNeedQa().equals(1)))
                        continue;
                    if (allByTaskIdAndSpecId.stream().allMatch(item -> item.getIsPreQaQualified().equals(1) || item.getIsPreQaReturn().equals(1)))
                        continue;
                    inboundsRequiredCheck.setReceiveOrder(byId.getSupplierDeliveryBoxCode());
                } else
                    continue;
                inboundsRequiredCheck.setBoxBarcode(itemEntity.getInternalBoxCode());
                inboundsRequiredCheck.setPurchaseNumber(itemEntity.getPurchasePlanNo());
                inboundsRequiredCheck.setPushType(InboundsRequiredCheckPushTypeEnum.ON_SHELVE.getDesc());
                inboundsRequiredCheck.setSku(itemEntity.getSku());
                list.add(inboundsRequiredCheck);
            }
            if (CollectionUtils.isEmpty(list))
                return;
            SyncInboundsRequiredCheckRequest syncInboundsRequiredCheckRequest = new SyncInboundsRequiredCheckRequest();
            syncInboundsRequiredCheckRequest.setInboundsRequiredChecks(list);
            messageProducer.sendMessage(KafkaConstant.SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC_NAME, KafkaConstant.SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), syncInboundsRequiredCheckRequest));
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    /**
     * 质检增加退货数
     *
     * @param messageContent
     * @param isComplete     1质检完结，不需要添加质检结果
     *                       0 可能是直接退货、增加退货数等操作，需要添加质检结果
     */
    @Transactional
    public void addReturnQuantity(QcInboundsMessage messageContent, boolean isComplete) {
        StockInternalBox internalBox = stockInternalBoxService.getInternalBox(messageContent.getBoxBarcode());
        if (StockInternalBoxStatusEnum.SHELVED.name().equals(internalBox.getStatus()))
            throw new BusinessServiceException("内部箱已上架!");
        if (IsDeletedConstant.DELETED.equals(internalBox.getIsDeleted()))
            throw new BusinessServiceException("内部箱已被删除!");
        validShelveTask(messageContent.getBoxBarcode());
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.QC_ADD_RETURN_QUANTITY, "/qc/add-return-quantity",
                JsonMapper.toJson(messageContent), messageContent.getSku(), "QMS质检增加退货数");
        //添加质检结果表
        StockinQcInboundsEntity stockinQcInboundsEntity = new StockinQcInboundsEntity();
        if (!isComplete) {
            stockinQcInboundsEntity = stockinQcInboundsService.saveQcInbounds(messageContent);
        }
        try {
            //添加退货
            if ((Objects.isNull(messageContent.getGoodsToRefundCount()) || messageContent.getGoodsToRefundCount().equals(0)) && Objects.nonNull(messageContent.getDirectReturnCount()) && messageContent.getDirectReturnCount() > 0) {
                messageContent.setGoodsToRefundCount(messageContent.getDirectReturnCount());
            }
            if (Objects.nonNull(messageContent.getGoodsToRefundCount()) && messageContent.getGoodsToRefundCount() > 0) {
                Boolean noDirectReturn = Objects.isNull(messageContent.getDirectReturnCount()) || messageContent.getDirectReturnCount().equals(0);
                List<StockInternalBoxItemEntity> list;
                if (Objects.nonNull(messageContent.getIsQcRoutingInspect()) && messageContent.getIsQcRoutingInspect() == 1)
                    list = getStockInternalBoxItemByMessageContent(messageContent, internalBox);
                else
                    list = getStockInternalBoxItemEntities(messageContent, internalBox);
                int sum = list.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
                if (sum < messageContent.getGoodsToRefundCount())
                    throw new BusinessServiceException(String.format("内部箱中SKU【%s】仅剩【%s】件", messageContent.getSku(), sum));
                List<StockinReturnProductItem> returnProductItems = returnProduct(messageContent, list);
                //记录退货数到质检结果明细表
                if (noDirectReturn && !returnProductItems.isEmpty()) {
                    stockinQcInboundsService.addReturnQcInboundsItem(stockinQcInboundsEntity, returnProductItems);
                }
                externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);

                //发送消息通知
                this.sendReturnMsg(messageContent);
            }
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
        stockInternalBoxService.updateInternalBoxStatusByItem(messageContent.getBoxBarcode());
    }

    private void sendReturnMsg(QcInboundsMessage messageContent) {
        try {
            SupplierEntity supplier = supplierService.getBySupplierId(messageContent.getSupplierId());
            List<String> userNameList = new ArrayList<>(2);
            if (Objects.nonNull(supplier.getContactQcEmpId())) {
                SysUserInfo userInfoByUserId = userApiService.getUserInfoByUserId(supplier.getContactQcEmpId());
                Optional.ofNullable(userInfoByUserId).ifPresent(user -> userNameList.add(user.getUserAccount()));
            }

            if (Objects.nonNull(supplier.getContactPurchaserEmpId())) {
                SysUserInfo userInfoByUserId = userApiService.getUserInfoByUserId(supplier.getContactPurchaserEmpId());
                Optional.ofNullable(userInfoByUserId).ifPresent(user -> userNameList.add(user.getUserAccount()));
            }
            if (CollectionUtils.isEmpty(userNameList)) {
                throw new BusinessServiceException("发送人员为空");
            }
            DingTalkCorpConversationMessageRequest request = new DingTalkCorpConversationMessageRequest();
            request.setUserNameList(userNameList);
            DingTalkCorpConversationMessageRequest.Markdown markdown = new DingTalkCorpConversationMessageRequest.Markdown();
            markdown.setTitle(String.format("工厂入库质检退货:【%s-%s】", supplier.getSupplierNo(), supplier.getContactPurchaserEmpName()));


            String content = String.format("QA质检退货：%s工厂，%s，%s件，%s。该款即将退回工厂，请跟进返工进度及质检。",
                    supplier.getSupplierCode(), messageContent.getSku(), messageContent.getGoodsToRefundCount(), messageContent.getUnqualifiedReason());

            markdown.setText(content);
            request.setMarkdown(markdown);
            request.setMsgType(DingTalkCorpConversationMessageRequest.MsgType.MARKDOWN);
            notifyApiService.sendCorpConversation(request);
        } catch (Exception e) {
            LOGGER.error(String.format("质检退货【%s】发送通知失败：%s", messageContent.getSku(), e.getMessage()), e);
        }
    }

    @NotNull
    private List<StockInternalBoxItemEntity> getStockInternalBoxItemEntities(QcInboundsMessage messageContent, StockInternalBox internalBox) {
        if (!StringUtils.hasText(messageContent.getPurchaseNumbers()))
            throw new BusinessServiceException("未找到采购单号!");
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxId, internalBox.getInternalBoxId())
                .eq(StockInternalBoxItemEntity::getSku, messageContent.getSku()));
        if (CollectionUtils.isEmpty(list))
            throw new BusinessServiceException("内部箱中没有该商品!");
        return list;
    }

    @NotNull
    private List<StockInternalBoxItemEntity> getStockInternalBoxItemByMessageContent(QcInboundsMessage messageContent, StockInternalBox internalBox) {
        LambdaQueryWrapper<StockInternalBoxItemEntity> queryWrapper = stockInternalBoxItemService.buildQueryByInternalBoxIdAndSkuAndPurchaseNumberAndStockinOrderNo(
                internalBox.getInternalBoxId(), messageContent.getSku(), messageContent.getPurchaseNumbers(), messageContent.getStockinOrderNo());
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list))
            throw new BusinessServiceException("内部箱中没有该商品!");
        return list;
    }

    /**
     * 减少退货数，并放入指定箱子
     */
    @Transactional
    public void reduceReturnQuantity(QcInboundsMessage messageContent) {
        StockInternalBox internalBox = stockInternalBoxService.getInternalBox(messageContent.getBoxBarcode());
        if (StockInternalBoxStatusEnum.SHELVED.name().equals(internalBox.getStatus()))
            throw new BusinessServiceException("内部箱已上架!");
        validShelveTask(messageContent.getBoxBarcode());
        StockInternalBox internalBoxIn = stockInternalBoxService.getInternalBox(messageContent.getInternalBoxNo());
        if (IsDeletedConstant.DELETED.equals(internalBoxIn.getIsDeleted()))
            throw new BusinessServiceException("调入箱已被删除!");
        if (!messageContent.getBoxBarcode().equals(messageContent.getInternalBoxNo())
                && StockInternalBoxStatusEnum.SHELVED.name().equals(internalBoxIn.getStatus()))
            throw new BusinessServiceException("调入箱已上架!");

        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.QC_REDUCE_RETURN_QUANTITY, "/qc/reduce-return-quantity",
                JsonMapper.toJson(messageContent), messageContent.getSku(), "QMS质检减少退货数");
        //添加质检结果表
        Integer goodsToRefundCount = messageContent.getGoodsToRefundCount();
        messageContent.setGoodsToRefundCount(0 - goodsToRefundCount);
        StockinQcInboundsEntity stockinQcInboundsEntity = stockinQcInboundsService.saveQcInbounds(messageContent);
        messageContent.setGoodsToRefundCount(goodsToRefundCount);
        try {
            //添加退货
            List<StockinReturnProductItem> returnProductItems = reduceReturnProduct(messageContent);
            //减少退货数到质检结果明细表
            stockinQcInboundsService.reduceReturnQcInboundsItem(stockinQcInboundsEntity, returnProductItems);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
        stockInternalBoxService.updateInternalBoxStatusByItem(messageContent.getBoxBarcode());
        //调入箱不等于原箱子
        if (!messageContent.getBoxBarcode().equals(messageContent.getInternalBoxNo()))
            stockInternalBoxService.updateInternalBoxStatusByItem(messageContent.getInternalBoxNo());
    }

    private void validShelveTask(String internalBoxNo) {
        List<StockinShelveTaskEntity> entityList = stockinShelveTaskService.list(StockinShelveTaskService.buildWrapperTaskByInternalBoxCode(internalBoxNo, null));
        if (CollectionUtils.isEmpty(entityList))
            return;
        if (StockinShelveTaskStatusEnum.SHELVED.name().equals(entityList.get(0).getStatus()))
            throw new BusinessServiceException("上架任务已完成，不可再修改！");
    }

    /**
     * 质检减少退货数
     * 1.更新内部箱和库存
     * 2.删除退货库位数据
     * 3.更新上架任务
     *
     * @param messageContent
     */
    private List<StockinReturnProductItem> reduceReturnProduct(QcInboundsMessage messageContent) {
        String[] receiveOrderNos = messageContent.getReceiveOrderNos().split(",");
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.list(new LambdaQueryWrapper<StockinOrderEntity>()
                .in(StockinOrderEntity::getSupplierDeliveryBoxCode, Arrays.asList(receiveOrderNos)));
        if (CollectionUtils.isEmpty(stockinOrderEntityList))
            throw new BusinessServiceException("未找到入库单!");
        if (stockinOrderEntityList.stream().anyMatch(order -> StockinOrderStatusEnum.COMPLETED.name().equals(order.getStatus())))
            throw new BusinessServiceException("入库单已完结，请使用退货调拨上架!");
        //1.更新内部箱和库存
        Map<String, StockinOrderEntity> stockinOrderEntityMap = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderNo, entity -> entity));
        //更新入库单明细 ， 增加调入箱库存
        List<StockinReturnProductItem> returnProductItems = reduceReturnUpdateInternalBox(messageContent, stockinOrderEntityList);
        returnProductItems.stream().collect(Collectors.groupingBy(StockinReturnProductItem::getStockinOrderNo))
                .forEach((stockinOrderNo, itemList) ->
                        itemList.stream().collect(Collectors.groupingBy(StockinReturnProductItem::getPurchasePlanNo, Collectors.summingInt(StockinReturnProductItem::getReturnQty)))
                                .forEach((purchasePlanNo, returnQty) -> {
                                    // 2.删除退货库位数据
                                    StockinOrderEntity stockinOrderEntity = stockinOrderEntityMap.get(stockinOrderNo);
                                    stockinReturnProductService.backReturnProduct(stockinQcBuildService.buildExternalQcReturnBackRequest(messageContent, stockinOrderNo, purchasePlanNo, returnQty, stockinOrderEntity));
                                }));
        return returnProductItems;
    }

    private List<StockinReturnProductItem> reduceReturnUpdateInternalBox(QcInboundsMessage messageContent, List<StockinOrderEntity> stockinOrderEntityList) {
        String[] purchaseNumbers = messageContent.getPurchaseNumbers().split(",");
        List<String> stockinOrderNoList = stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderNo).collect(Collectors.toList());
        //先查询退货库位库存
        List<StockinReturnProductEntity> returnProductEntityList = stockinReturnProductService.list(new LambdaQueryWrapper<StockinReturnProductEntity>()
                .eq(StockinReturnProductEntity::getSku, messageContent.getSku())
                .in(StockinReturnProductEntity::getStockinOrderNo, stockinOrderNoList)
                .in(StockinReturnProductEntity::getPurchasePlanNo, Arrays.asList(purchaseNumbers)));
        Integer goodsToRefundCount = messageContent.getGoodsToRefundCount();
        if (CollectionUtils.isEmpty(returnProductEntityList)
                || returnProductEntityList.stream().mapToInt(StockinReturnProductEntity::getReturnQty).sum() < goodsToRefundCount)
            throw new BusinessServiceException("退货库位库存不足！");

        List<StockinOrderItemEntity> oriItemEntityList = stockinOrderItemService.list(new LambdaQueryWrapper<StockinOrderItemEntity>()
                .in(StockinOrderItemEntity::getStockinOrderId, stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList()))
                .eq(StockinOrderItemEntity::getSku, messageContent.getSku())
                .in(StockinOrderItemEntity::getPurchasePlanNo, Arrays.asList(messageContent.getPurchaseNumbers().split(",")))
                .eq(StockinOrderItemEntity::getInternalBoxCode, messageContent.getBoxBarcode()));
        if (CollectionUtils.isEmpty(oriItemEntityList))
            throw new BusinessServiceException("该箱子的sku已清空!");

        Map<String, StockinOrderEntity> stockinOrderEntityMap = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderNo, entity -> entity));

        List<StockinReturnProductItem> returnProductItems = new LinkedList<>();
        //根据退货库位的库存回填退货数
        for (int i = 0; goodsToRefundCount > 0 && i < returnProductEntityList.size(); i++) {
            StockinReturnProductEntity returnProductEntity = returnProductEntityList.get(i);
            StockinOrderEntity stockinOrderEntity = stockinOrderEntityMap.get(returnProductEntity.getStockinOrderNo());

            List<StockinOrderItemEntity> itemEntityList = oriItemEntityList.stream().filter(item -> item.getStockinOrderId().equals(stockinOrderEntity.getStockinOrderId())
                    && item.getPurchasePlanNo().equals(returnProductEntity.getPurchasePlanNo())).collect(Collectors.toList());
            int returnProductQty = returnProductEntity.getReturnQty();
            int returnQty = 0;
            for (StockinOrderItemEntity orderItemEntity : itemEntityList) {

                if (goodsToRefundCount <= 0)
                    break;

                if (returnProductQty <= 0)
                    break;

                if (orderItemEntity.getReturnQty() == 0)
                    continue;

                Integer qty = Math.min(returnProductQty, Math.min(goodsToRefundCount, orderItemEntity.getReturnQty()));
                operateInternalBoxItem(messageContent, orderItemEntity, stockinOrderEntity, qty);
                goodsToRefundCount -= qty;
                returnProductQty -= qty;
                returnQty += qty;
            }
            returnProductItems.add(new StockinReturnProductItem(stockinOrderEntity.getStockinOrderId(), stockinOrderEntity.getStockinOrderNo(), returnProductEntity.getSupplierDeliveryNo(), returnQty, returnProductEntity.getPurchasePlanNo()));
        }
        return returnProductItems;
    }

    private void operateInternalBoxItem(QcInboundsMessage messageContent, StockinOrderItemEntity item, StockinOrderEntity stockinOrderEntity, Integer qty) {
        // 1.减少原箱子的退货数
        StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
        orderItemEntity.setReturnQty(item.getReturnQty() - qty);
        orderItemEntity.setQty(item.getQty() - qty);
        orderItemEntity.setStockinOrderItemId(item.getStockinOrderItemId());
        orderItemEntity.setUpdateBy(loginInfoService.getName());
        orderItemEntity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
        stockinOrderItemService.updateById(orderItemEntity);
        // 2.增加调入箱的入库数，和库存
        StockinOrderItemEntity inItemEntity = stockinOrderItemService.findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndPurchasePlanNo(item.getStockinOrderId(), item.getSku(), messageContent.getInternalBoxNo(), item.getPurchasePlanNo(), null);
        if (Objects.isNull(inItemEntity)) {
            StockinOrderItemEntity itemEntity = new StockinOrderItemEntity();
            BeanUtilsEx.copyProperties(item, itemEntity, "stockinOrderItemId", "stockinReturnQty", "returnQty", "concessionsCount", "pushedQty", "createDate", "createBy", "updateDate", "updateBy", "version");
            itemEntity.setQty(qty);
            itemEntity.setInternalBoxCode(messageContent.getInternalBoxNo());
            itemEntity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
            itemEntity.setCreateBy(loginInfoService.getName());
            stockinOrderItemService.save(itemEntity);
            inItemEntity = itemEntity;
        } else {
            inItemEntity.setQty(qty + inItemEntity.getQty());
            inItemEntity.setUpdateBy(loginInfoService.getName());
            inItemEntity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
            stockinOrderItemService.updateById(inItemEntity);
            if (inItemEntity.getStockinOrderItemId().equals(item.getStockinOrderItemId())) {
                //赋值回原数据进行下一次循环
                item.setReturnQty(orderItemEntity.getReturnQty());
                item.setQty(inItemEntity.getQty());
            }
        }
        StockInternalBoxItemEntity boxItemEntity = stockInternalBoxItemService.findByStockinOrderItem(stockinOrderEntity.getStockinOrderNo(), inItemEntity);
        if (Objects.nonNull(boxItemEntity)) {
            boxItemEntity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
            stockInternalBoxItemService.addStockInternalBoxItemQty(boxItemEntity, qty, StockChangeLogTypeEnum.RETURN_QC, StockChangeLogTypeModuleEnum.QC, null);
        } else {
            StockInternalBoxItemEntity boxItem = stoCkinQcFacade.createBoxItem(messageContent, stockinOrderEntity.getStockinOrderNo(), item);
            stockInternalBoxItemService.addStockInternalBoxItemQty(boxItem, qty, StockChangeLogTypeEnum.RETURN_QC, StockChangeLogTypeModuleEnum.QC, null);
        }
    }
}