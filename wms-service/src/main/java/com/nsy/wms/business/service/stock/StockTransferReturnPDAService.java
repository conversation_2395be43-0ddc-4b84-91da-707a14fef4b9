package com.nsy.wms.business.service.stock;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.ErpConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferInternalBoxTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.request.bd.BdSupplierPositionMappingSearchRequest;
import com.nsy.api.wms.request.stock.PDATransferReturnProductRequest;
import com.nsy.api.wms.request.stock.TransferReturnRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductAddRequest;
import com.nsy.api.wms.response.stock.PDAGetSupplierPositionCodeResponse;
import com.nsy.api.wms.response.stock.PDATransferConfirmRequest;
import com.nsy.api.wms.response.stock.PDATransferReturnScanResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.bo.stockin.StockinQaTaskUpdateBo;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSupplierPositionMappingService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.bd.query.BdPositionQueryWrapper;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSkcLabelService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockin.StockinOrderAutoCompleteService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.business.service.stockin.StockinQcService;
import com.nsy.wms.business.service.stockin.StockinReturnProductService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSupplierPositionMappingEntity;
import com.nsy.wms.repository.entity.product.ProductSkcLabelEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockTransferInternalBoxTaskEntity;
import com.nsy.wms.repository.entity.stock.StockTransferInternalBoxTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockTransferReturnPDAService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockTransferReturnPDAService.class);

    @Resource
    StockTransferInternalBoxTaskService taskService;
    @Resource
    StockTransferInternalBoxTaskItemService taskItemService;
    @Resource
    StockInternalBoxService stockInternalBoxService;
    @Resource
    StockInternalBoxItemService stockInternalBoxItemService;
    @Resource
    StockinReturnProductService returnProductService;
    @Resource
    StockService stockService;
    @Resource
    StockinQcService stockinQcService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    BdPositionService positionService;
    @Resource
    ProductSpecInfoService specInfoService;
    @Resource
    ProductStoreSkuMappingService productStoreSkuMappingService;
    @Resource
    BdSupplierPositionMappingService positionMappingService;
    @Resource
    StockTransferRecordService transferRecordService;
    @Resource
    StockinOrderService stockinOrderService;
    @Resource
    StockinOrderItemService stockinOrderItemService;
    @Resource
    StockinOrderTaskService stockinOrderTaskService;
    @Resource
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Resource
    BdSupplierPositionMappingService supplierPositionMappingService;
    @Resource
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    StockinOrderAutoCompleteService stockinOrderAutoCompleteService;
    @Resource
    ProductSkcLabelService productSkcLabelService;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private BdAreaService bdAreaService;

    /**
     * 校验退货调拨箱是否存在
     */
    public void validTransferBoxCode(String transferBoxCode) {
        StockInternalBoxEntity internalBoxEntity = stockInternalBoxService.getOne(new QueryWrapper<StockInternalBoxEntity>().lambda()
                .eq(StockInternalBoxEntity::getInternalBoxCode, transferBoxCode)
                .eq(StockInternalBoxEntity::getIsDeleted, 0)
                .last(MybatisQueryConstant.QUERY_FIRST));
        if (internalBoxEntity == null) {
            // 不是内部箱，那就是库位调拨，查找库位
            LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionByPositionCode(transferBoxCode);
            BdPositionEntity bdPositionEntity = positionService.getOne(wrapper);
            if (bdPositionEntity == null)
                throw new BusinessServiceException(String.format("未找到待质检的内部箱【%s】调拨任务,且不存在该库位，请确认", transferBoxCode));
            return;
        }
        if (internalBoxEntity.getInternalBoxType().equals(StockInternalBoxTypeEnum.RECEIVE_BOX.name())
                || internalBoxEntity.getInternalBoxType().equals(StockInternalBoxTypeEnum.QA_BOX.name())) {
            if (internalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.SHELVED.name())
                    || internalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.EMPTY.name())) {
                throw new BusinessServiceException("请确认该收货箱状态");
            }
        } else {
            StockTransferInternalBoxTaskEntity taskEntity = taskService.getOne(new QueryWrapper<StockTransferInternalBoxTaskEntity>().lambda()
                    .eq(StockTransferInternalBoxTaskEntity::getTransferBoxCode, transferBoxCode)
                    .eq(StockTransferInternalBoxTaskEntity::getStatus, StockTransferInternalBoxTaskStatusEnum.WAIT_QC.name())
                    .last("limit 1"));
            if (taskEntity == null)
                throw new BusinessServiceException(String.format("未找到待质检的内部箱【%s】调拨任务, 且不存在该库位，请确认", transferBoxCode));
        }
    }

    /**
     * 获取退货调拨箱内信息
     */
    public PDATransferReturnScanResponse scanReturnBarcode(String transferBoxCode, String barcode) {
        ProductSpecInfoEntity specInfoEntity = productStoreSkuMappingService.validBarcode(barcode);
        StockTransferInternalBoxTaskEntity taskEntity = taskService.getOne(new QueryWrapper<StockTransferInternalBoxTaskEntity>().lambda()
                .eq(StockTransferInternalBoxTaskEntity::getTransferBoxCode, transferBoxCode)
                .eq(StockTransferInternalBoxTaskEntity::getStatus, StockTransferInternalBoxTaskStatusEnum.WAIT_QC.name())
                .last("limit 1"));
        StockInternalBoxEntity internalBoxEntity = stockInternalBoxService.getOne(new QueryWrapper<StockInternalBoxEntity>().lambda()
                .eq(StockInternalBoxEntity::getInternalBoxCode, transferBoxCode)
                .eq(StockInternalBoxEntity::getIsDeleted, 0)
                .last(MybatisQueryConstant.QUERY_FIRST));
        if (internalBoxEntity == null) {
            // 库位退货调拨
            return buildPositionCallBack(transferBoxCode, specInfoEntity.getBarcode());
        }
        if (internalBoxEntity.getInternalBoxType().equals(StockInternalBoxTypeEnum.RECEIVE_BOX.name())
                || internalBoxEntity.getInternalBoxType().equals(StockInternalBoxTypeEnum.QA_BOX.name())) {
            // 收获箱调拨
            return buildReceiveBoxCallBack(internalBoxEntity, specInfoEntity.getBarcode());
        }
        List<StockTransferInternalBoxTaskItemEntity> taskItemList = taskItemService.list(new QueryWrapper<StockTransferInternalBoxTaskItemEntity>().lambda()
                .eq(StockTransferInternalBoxTaskItemEntity::getTransferTaskId, taskEntity.getTransferTaskId())
                .eq(StockTransferInternalBoxTaskItemEntity::getBarcode, specInfoEntity.getBarcode()));
        if (taskItemList.isEmpty())
            throw new BusinessServiceException("未找到箱内对应条形码，请确认");
        if (!taskItemList.get(0).getStatus().equals(StockTransferInternalBoxTaskStatusEnum.WAIT_QC.name()))
            throw new BusinessServiceException("该条码已退货，请确认！");
        PDATransferReturnScanResponse result = new PDATransferReturnScanResponse();
        result.setSku(taskItemList.get(0).getSku());
        result.setBoxQty(taskItemList.stream().mapToInt(StockTransferInternalBoxTaskItemEntity::getExpectedTransferQty).sum());
        result.setQcQty(taskItemList.stream().mapToInt(StockTransferInternalBoxTaskItemEntity::getExpectedTransferQty).sum());
        result.setReturnQty(taskItemList.stream().mapToInt(StockTransferInternalBoxTaskItemEntity::getReturnQty).sum());
        result.setProductId(taskItemList.get(0).getProductId());
        return result;
    }


    private PDATransferReturnScanResponse buildPositionCallBack(String transferBoxCode, String barcode) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionByPositionCode(transferBoxCode);
        BdPositionEntity bdPositionEntity = positionService.getOne(wrapper);
        if (bdPositionEntity == null) {
            throw new BusinessServiceException(String.format("未找到待质检的内部箱【%s】调拨任务, 且不存在该库位，请确认", transferBoxCode));
        }
        if (!BdPositionTypeEnum.STOCK_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.SPARE_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.STORE_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.OEM_POSITION.name().equals(bdPositionEntity.getPositionType())) {
            throw new BusinessServiceException("库位必须是零拣库位或储存库位或活动库位或OEM库位或店铺库位");
        }
        ProductSpecInfoEntity result = specInfoService.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda()
                .eq(ProductSpecInfoEntity::getBarcode, barcode).last("limit 1"));
        if (result == null) {
            throw new BusinessServiceException(String.format("未找到该条码【%s】的商品详细信息, 请确认条码", barcode));
        }
        List<StockEntity> skuPositionStock = stockService.getSkuPositionStock(result.getSku(), Collections
                .singletonList(bdPositionEntity.getPositionId()));
        if (CollectionUtils.isEmpty(skuPositionStock) || skuPositionStock.stream().noneMatch(item -> item.getStock() > 0)) {
            throw new BusinessServiceException("该商品在该库位上的库存为0，请确认");
        }
        PDATransferReturnScanResponse response = new PDATransferReturnScanResponse();
        response.setSku(result.getSku());
        response.setReturnQty(0);
        response.setBoxQty(stockService.getSkuPositionUnPrematchStock(result.getSku(), bdPositionEntity.getPositionId()));
        response.setQcQty(0);
        response.setBox(Boolean.FALSE);
        //构建提示
        List<ProductSkcLabelEntity> productSkcLabelList = productSkcLabelService.listBySkc(result.getSkc());
        List<String> labelNameList = productSkcLabelList.stream().map(ProductSkcLabelEntity::getLabelName).collect(Collectors.toList());
        if (labelNameList.contains("清仓") || labelNameList.contains("店铺淘汰款")) {
            response.setIsShowNoticeMsg(Boolean.TRUE);
            response.setNoticeMsg("该商品为清仓或店铺淘汰款，是否继续调拨？");
        }
        response.setProductId(result.getProductId());
        return response;
    }


    private PDATransferReturnScanResponse buildReceiveBoxCallBack(StockInternalBoxEntity internalBoxEntity, String barcode) {
        PDATransferReturnScanResponse result = new PDATransferReturnScanResponse();
        ProductSpecInfoEntity specInfoEntity = specInfoService.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda()
                .eq(ProductSpecInfoEntity::getBarcode, barcode).last("limit 1"));
        if (specInfoEntity == null) {
            throw new BusinessServiceException(String.format("未找到该条码【%s】的商品详细信息, 请确认条码", barcode));
        }
        List<StockInternalBoxItemEntity> boxItems = stockInternalBoxItemService.list(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
                .eq(StockInternalBoxItemEntity::getInternalBoxId, internalBoxEntity.getInternalBoxId())
                .eq(StockInternalBoxItemEntity::getSku, specInfoEntity.getSku()));
        if (CollectionUtils.isEmpty(boxItems)) {
            throw new BusinessServiceException(String.format("未在内部箱【%s】找到该条码【%s】的商品详细信息, 请确认", internalBoxEntity.getInternalBoxCode(), barcode));
        }

        //是否都有原单
        boolean isAllRework = boxItems.stream().allMatch(item -> StringUtils.hasText(item.getStockInOrderNo()));
        //是否都无原单
        boolean isAllRefund = boxItems.stream().noneMatch(item -> StringUtils.hasText(item.getStockInOrderNo()));
        //同时存在有原单和无原单
        if (!isAllRework && !isAllRefund)
            LOGGER.warn("【 {} 】 【 {} 】 同时存在有原单和无原单", internalBoxEntity.getInternalBoxCode(), barcode);

        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(boxItems.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList()))
                .stream().filter(entity -> Objects.nonNull(entity.getSupplierId()) && entity.getSupplierId() != 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stockinOrderEntityList)) {
            Map<Integer, String> collect = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getSupplierId, StockinOrderEntity::getSupplierName, (value1, value2) -> value1));
            List<SelectIntegerModel> supplierInfo = new ArrayList<>(collect.size());
            collect.forEach((key, value) -> supplierInfo.add(new SelectIntegerModel(key, value)));
            supplierInfo.add(new SelectIntegerModel(ErpConstant.SUPPLIERGUANGZHOUID, ErpConstant.SUPPLIERGUANGZHOUNAME));
            supplierInfo.add(new SelectIntegerModel(ErpConstant.SUPPLIERXIAMENID, ErpConstant.SUPPLIERXIAMENNAME));
            result.setSupplierInfo(supplierInfo);
            //有原单
            result.setHasOriginOrder(Boolean.TRUE);
        }
        result.setSku(specInfoEntity.getSku());
        result.setBoxQty(boxItems.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum());
        result.setQcQty(0);
        result.setReturnQty(0);
        result.setProductId(specInfoEntity.getProductId());

        StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(boxItems.get(0).getStockInOrderNo());
        if (stockinOrderEntity != null) {
            List<StockinOrderTaskItemEntity> orderItemEntities = stockinOrderTaskItemService.list(new QueryWrapper<StockinOrderTaskItemEntity>().lambda()
                    .eq(StockinOrderTaskItemEntity::getTaskId, stockinOrderEntity.getTaskId())
                    .eq(StockinOrderTaskItemEntity::getSku, specInfoEntity.getSku())
                    .orderByDesc(StockinOrderTaskItemEntity::getCreateDate)
                    .last("limit 1"));
            if (!CollectionUtils.isEmpty(orderItemEntities)) {
                result.setVersionNo(orderItemEntities.get(0).getWorkmanshipVersion());
                result.setPackingMethod(orderItemEntities.get(0).getPackageName());
            }
        }

        return result;
    }

    /**
     * 退货调拨
     */
    @Transactional
    public void returnProduct(PDATransferReturnProductRequest request) {
        String barcode = this.decodeInfo(request.getBarcode());
        ProductSpecInfoEntity specInfo = specInfoService.findTopByBarcode(barcode);
        // 找条码
        if (specInfo == null) {
            throw new BusinessServiceException(String.format("未找到该条码【%s】的商品详细信息, 请确认条码", request.getBarcode()));
        }
        StockInternalBoxEntity internalBoxEntity = stockInternalBoxService.getOne(new QueryWrapper<StockInternalBoxEntity>().lambda()
                .eq(StockInternalBoxEntity::getInternalBoxCode, request.getTransferBoxCode())
                .eq(StockInternalBoxEntity::getIsDeleted, 0)
                .last(MybatisQueryConstant.QUERY_FIRST));
        if (internalBoxEntity == null) {
            returnPositionProduct(request, specInfo);
            return;
        }
        if (internalBoxEntity.getInternalBoxType().equals(StockInternalBoxTypeEnum.RECEIVE_BOX.name())
                || internalBoxEntity.getInternalBoxType().equals(StockInternalBoxTypeEnum.QA_BOX.name())) {
            transferReturnProduct(request, internalBoxEntity, specInfo);
            //更新内部箱状态
            stockInternalBoxService.updateInternalBoxStatusByItem(internalBoxEntity.getInternalBoxCode());
            return;
        }
        StockTransferInternalBoxTaskEntity taskEntity = taskService.getOne(new QueryWrapper<StockTransferInternalBoxTaskEntity>().lambda()
                .eq(StockTransferInternalBoxTaskEntity::getTransferBoxCode, request.getTransferBoxCode())
                .eq(StockTransferInternalBoxTaskEntity::getStatus, StockTransferInternalBoxTaskStatusEnum.WAIT_QC.name())
                .last("limit 1"));
        List<StockTransferInternalBoxTaskItemEntity> taskItemList = taskItemService.list(new QueryWrapper<StockTransferInternalBoxTaskItemEntity>().lambda()
                .eq(StockTransferInternalBoxTaskItemEntity::getTransferTaskId, taskEntity.getTransferTaskId())
                .eq(StockTransferInternalBoxTaskItemEntity::getBarcode, barcode));
        if (taskItemList == null)
            throw new BusinessServiceException("未找到箱内对应条形码，请确认");
        // 调拨箱明细  库存变化
        transferBoxItemHandle(request.getTransferBoxCode(), taskEntity.getTransferTaskId(), taskItemList.get(0).getSku(), request.getQty());
        // 退货明细表+ 退货库位库存+
        StockinReturnProductAddRequest returnProductAddRequest = new StockinReturnProductAddRequest();
        returnProductAddRequest.setReturnQty(request.getQty());
        returnProductAddRequest.setSku(taskItemList.get(0).getSku());
        returnProductAddRequest.setSupplierId(request.getSupplierId());
        returnProductAddRequest.setInternalBoxCode(request.getTransferBoxCode());
        returnProductAddRequest.setOperator(loginInfoService.getUserName());
        returnProductAddRequest.setQaType(StockChangeLogTypeEnum.RETURN_TRANSFER);
        returnProductAddRequest.setPackingMethod(request.getPackingMethod());
        returnProductAddRequest.setVersionNo(request.getVersionNo());
        returnProductAddRequest.setUnqualifiedCategory(request.getUnqualifiedCategory());
        returnProductAddRequest.setUnqualifiedReason(request.getUnqualifiedReason());
        if (StringUtils.hasText(request.getPositionCode())) {
            BdPositionEntity positionByCode = positionService.getPositionByCode(request.getPositionCode());
            returnProductAddRequest.setSpaceId(Objects.isNull(positionByCode) ? null : positionByCode.getSpaceId());
        }
        returnProductService.alterReturnProduct(returnProductAddRequest, Boolean.FALSE);
        // 调拨任务明细
        taskService.returnHandle(returnProductAddRequest, loginInfoService.getUserName());
    }

    /**
     * 退货调拨，内部箱属于收货箱和质检箱的逻辑
     *
     * @param request
     * @param internalBoxEntity
     */
    private void transferReturnProduct(PDATransferReturnProductRequest request, StockInternalBoxEntity internalBoxEntity, ProductSpecInfoEntity productSpecEntity) {
        List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxId, internalBoxEntity.getInternalBoxId())
                .eq(StockInternalBoxItemEntity::getSku, productSpecEntity.getSku())
                .orderByDesc(StockInternalBoxItemEntity::getCreateDate));
        validItemStatus(internalBoxEntity, internalBoxItemEntityList);
        Integer minusQty = request.getQty();
        if (CollectionUtils.isEmpty(internalBoxItemEntityList))
            throw new BusinessServiceException("找不到内部箱明细，无法扣减");
        int totalQty = internalBoxItemEntityList.stream().filter(boxItemEntity -> boxItemEntity.getQty() != null).mapToInt(StockInternalBoxItemEntity::getQty).sum();
        if (totalQty < minusQty)
            throw new BusinessServiceException(String.format("内部箱【%s】，箱中商品【%s】总数不足，无法扣减数量", internalBoxEntity.getInternalBoxCode(), productSpecEntity.getSku()));
        List<Integer> stockinOrderIds = new ArrayList<>(internalBoxItemEntityList.size());
        for (StockInternalBoxItemEntity itemEntity : internalBoxItemEntityList) {
            if (minusQty <= 0)
                break;
            TransferReturnRequest transferReturnRequest = new TransferReturnRequest();
            BeanUtils.copyProperties(request, transferReturnRequest);
            transferReturnRequest.setSku(productSpecEntity.getSku());
            transferReturnRequest.setInternalBoxCode(internalBoxEntity.getInternalBoxCode());
            transferReturnRequest.setPurchasePlanNo(itemEntity.getPurchasePlanNo());
            transferReturnRequest.setStockinOrderNo(itemEntity.getStockInOrderNo());
            int tempQty = Math.min(itemEntity.getQty(), minusQty);
            transferReturnRequest.setQty(tempQty);
            // 确定是否有原单
            StockinOrderTaskEntity orderTaskEntity = null;
            if (itemEntity.getStockInOrderNo() != null) {
                StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(itemEntity.getStockInOrderNo());
                stockinOrderIds.add(stockinOrderEntity.getStockinOrderId());
                orderTaskEntity = stockinOrderTaskService.getById(stockinOrderEntity.getTaskId());
                transferReturnRequest.setSupplierDeliveryNo(orderTaskEntity == null ? "" : orderTaskEntity.getSupplierDeliveryNo());
            }
            //更新入库单明细退货数
            this.updateStockinOrder(transferReturnRequest, internalBoxEntity);
            // 退货明细表+ 退货库位库存+
            StockinReturnProductAddRequest returnProductAddRequest = new StockinReturnProductAddRequest();
            BeanUtils.copyProperties(transferReturnRequest, returnProductAddRequest);
            returnProductAddRequest.setReturnQty(tempQty);
            returnProductAddRequest.setOperator(loginInfoService.getName());
            returnProductAddRequest.setInternalBoxCode(internalBoxEntity.getInternalBoxCode());
            returnProductAddRequest.setQaType(StockChangeLogTypeEnum.RETURN_TRANSFER);
            // 扣减内部箱明细数,同步扣减内部箱库存数
            stockInternalBoxItemService.minusStockInternalBoxItemQty(Collections.singletonList(itemEntity), tempQty, StockChangeLogTypeEnum.RETURN_TRANSFER, StockChangeLogTypeModuleEnum.QC, null);

            returnProductService.alterReturnProduct(returnProductAddRequest, Boolean.FALSE);

            // 调拨箱状态
            QueryWrapper<StockInternalBoxItemEntity> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxEntity.getInternalBoxCode());
            Integer count = stockInternalBoxItemService.count(wrapper);
            if (count.equals(0))
                stockInternalBoxService.changeStockInternalBoxStatus(internalBoxEntity.getInternalBoxCode(), StockTransferBoxStatusEnum.EMPTY.name());
            //更新质检任务
            if (orderTaskEntity != null) {
                //生成或更新质检任务
                messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_TASK_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_TASK_TOPIC,
                        Key.of(internalBoxEntity.getInternalBoxCode() + "_" + productSpecEntity.getSku()),
                        new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(),
                                new StockinQaTaskUpdateBo(internalBoxEntity.getInternalBoxCode(), productSpecEntity.getSku())));
                //质检新系统生成和更新质检任务
                stockinQcService.generateQcTask(internalBoxEntity, orderTaskEntity, Collections.singletonList(itemEntity));
            }
            minusQty -= tempQty;
        }
        //判断入库单是否可以直接完成
        stockinOrderAutoCompleteService.autoComplete(stockinOrderIds);
    }

    private void validItemStatus(StockInternalBoxEntity internalBoxEntity, List<StockInternalBoxItemEntity> internalBoxItemEntityList) {
        List<StockInternalBoxItemEntity> collect = internalBoxItemEntityList.stream().filter(item -> item.getQty() > 0 && (StockinOrderItemStatusEnum.QC_PROCESSING.name().equals(item.getStatus()) || StockinOrderItemStatusEnum.WAIT_DEAL.name().equals(item.getStatus()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            String format = String.format("内部箱【%s】存在状态为【%s】的SKU，无法完成整箱装箱，请确定", internalBoxEntity.getInternalBoxCode(), enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_SKU_STATUS.getName(), collect.get(0).getStatus()));
            LOGGER.error(format);
            throw new BusinessServiceException(format);
        }
    }

    /**
     * 退货调拨
     */
    @Transactional
    public void returnPositionProduct(PDATransferReturnProductRequest request, ProductSpecInfoEntity specInfo) {
        // 1、找库位
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionByPositionCode(request.getTransferBoxCode());
        BdPositionEntity bdPositionEntity = positionService.getOne(wrapper);
        if (bdPositionEntity == null) {
            throw new BusinessServiceException(String.format("未找到待质检的内部箱【%s】调拨任务, 且不存在该库位，请确认", request.getTransferBoxCode()));
        }
        if (!BdPositionTypeEnum.STOCK_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.SPARE_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.STORE_POSITION.name().equals(bdPositionEntity.getPositionType())
                && !BdPositionTypeEnum.OEM_POSITION.name().equals(bdPositionEntity.getPositionType())) {
            throw new BusinessServiceException("库位必须是零拣库位或储存库位或活动库位或OEM库位或店铺库位");
        }
        List<StockEntity> skuPositionStock = stockService.getSkuPositionStock(specInfo.getSku(), Collections
                .singletonList(bdPositionEntity.getPositionId()));
        if (CollectionUtils.isEmpty(skuPositionStock) || skuPositionStock.stream().noneMatch(item -> item.getStock() > 0)) {
            throw new BusinessServiceException("该商品在该库位上的库存为0，请确认");
        }
        Integer stock = stockService.getSkuPositionUnPrematchStock(specInfo.getSku(), bdPositionEntity.getPositionId());
        if (skuPositionStock.stream().noneMatch(item -> item.getStock() >= stock)) {
            throw new BusinessServiceException("退货数量大于原有库位上的数量，请确认");
        }
        // 获取 供应商库位关系  退款退货库位
        BdSupplierPositionMappingEntity mappingEntity = supplierPositionMappingService
                .getSupplierPositionMappingEntity(request.getSupplierId(), StockinReturnNatureEnum.REFUND_RETURN.name(), bdPositionEntity.getSpaceId());
        // 获取 库位
        BdPositionEntity positionEntity = positionService.getById(mappingEntity.getPositionId());
        if (Objects.isNull(positionEntity))
            throw new BusinessServiceException(String.format("库位编码【%s】未找到！", mappingEntity.getPositionCode()));
//        if (!StringConstant.UNCHECK_POSITION_CODE.equals(positionEntity.getPositionCode()) && !scmApiService.checkSupplierExistsSku(request.getSupplierId(), specInfo.getSku())) {
//            throw new BusinessServiceException("当前工厂没有生产该商品，请确认调入工厂");
//        }
        // 库内调拨
        PDATransferConfirmRequest transferRequest = new PDATransferConfirmRequest();
        transferRequest.setTransferInCode(positionEntity.getPositionCode());
        transferRequest.setTransferOutCode(request.getTransferBoxCode());
        transferRequest.setBarcode(specInfo.getBarcode());
        transferRequest.setSku(specInfo.getSku());
        transferRequest.setQty(request.getQty());
        transferRequest.setVersionNo(request.getVersionNo());
        transferRequest.setPackingMethod(request.getPackingMethod());
        transferRequest.setUnqualifiedCategory(request.getUnqualifiedCategory());
        transferRequest.setUnqualifiedReason(request.getUnqualifiedReason());
        transferRecordService.confirmTransfer(transferRequest);
    }

    private void updateStockinOrder(TransferReturnRequest request, StockInternalBoxEntity internalBoxEntity) {
        if (StringUtils.hasText(request.getStockinOrderNo())) {
            StockinOrderEntity byStockinOrderNo = stockinOrderService.getByStockinOrderNo(request.getStockinOrderNo());
            StockinOrderItemEntity stockinOrderItemEntity = stockinOrderItemService.findTopByStockinOrderIdAndPurchasePlanNo(byStockinOrderNo.getStockinOrderId(), request.getSku(), request.getPurchasePlanNo(), internalBoxEntity.getInternalBoxCode());
            if (Objects.nonNull(stockinOrderItemEntity) && stockinOrderItemEntity.getConcessionsCount() > 0)
                throw new BusinessServiceException("该SKU存在让步接收商品，无法进行退货调拨");
            if (Objects.nonNull(stockinOrderItemEntity)) {
                StockinOrderItemEntity entity = new StockinOrderItemEntity();
                entity.setStockinOrderItemId(stockinOrderItemEntity.getStockinOrderItemId());
                entity.setUpdateBy(loginInfoService.getName());
                int returnQty = Objects.nonNull(stockinOrderItemEntity.getReturnQty()) ? stockinOrderItemEntity.getReturnQty() + request.getQty() : request.getQty();
                entity.setReturnQty(returnQty);
                //如果为已退货、已上架修改为待上架其余则不变
                String status = StockinOrderItemStatusEnum.RETURNED.name().equals(stockinOrderItemEntity.getStatus()) || StockinOrderItemStatusEnum.SHELVED.name().equals(stockinOrderItemEntity.getStatus()) ? StockinOrderItemStatusEnum.WAIT_SHELVE.name()
                        : stockinOrderItemEntity.getStatus();
                entity.setStatus(returnQty == stockinOrderItemEntity.getQty() ? StockinOrderItemStatusEnum.RETURNED.name()
                        : (returnQty + stockinOrderItemEntity.getShelvedQty() == stockinOrderItemEntity.getQty()) ? StockinOrderItemStatusEnum.SHELVED.name() : status);
                stockinOrderItemService.updateById(entity);
            }
        }
    }

    private void transferBoxItemHandle(String transferBoxCode, Integer taskId, String sku, Integer qty) {
        List<StockInternalBoxItemEntity> boxItemList = stockInternalBoxItemService.list(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, transferBoxCode)
                .eq(StockInternalBoxItemEntity::getSku, sku)
                .eq(StockInternalBoxItemEntity::getTransferInternalBoxTaskId, taskId));
        if (boxItemList.isEmpty())
            throw new BusinessServiceException("调拨箱明细未找到，请确认");
        stockInternalBoxItemService.minusStockInternalBoxItemQty(boxItemList, qty, StockChangeLogTypeEnum.RETURN_TRANSFER, StockChangeLogTypeModuleEnum.QC, null);
        // 调拨箱状态
        Integer count = stockInternalBoxItemService.count(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, transferBoxCode));
        if (count.equals(0)) {
            LOGGER.info("{}退货调拨更新内部箱{}为空箱", loginInfoService.getName(), transferBoxCode);
            stockInternalBoxService.changeStockInternalBoxStatus(transferBoxCode, StockTransferBoxStatusEnum.EMPTY.name());
        } else {
            StockTransferInternalBoxTaskEntity taskEntity = taskService.getById(taskId);
            if (taskEntity.getStatus().equals(StockTransferInternalBoxTaskStatusEnum.WAIT_SORT.name()))
                stockInternalBoxService.changeStockInternalBoxStatus(transferBoxCode, StockTransferBoxStatusEnum.WAIT_SORT.name());
        }
    }

    /**
     * 获取供应商对应库位
     *
     * @param request
     * @return
     */
    public PDAGetSupplierPositionCodeResponse getSupplierPositionCode(BdSupplierPositionMappingSearchRequest request) {
        String returnNature = returnProductService.getReturnNatureBySupplierId(request.getSupplierId(), request.getHasOriginOrder());

        Integer spaceId;
        if (Objects.isNull(request.getSourceAreaId())) {
            spaceId = this.getSpaceIdByInfo(request);
        } else {
            BdAreaEntity areaEntity = bdAreaService.getAreaById(request.getSourceAreaId());
            spaceId = areaEntity.getSpaceId();
        }

        BdSupplierPositionMappingEntity positionMapping = positionMappingService.getSupplierPositionMappingEntity(request.getSupplierId(),
                returnNature,
                spaceId);
        PDAGetSupplierPositionCodeResponse response = new PDAGetSupplierPositionCodeResponse();
        response.setPositionCode(positionMapping.getPositionCode());

        Boolean isUse = positionMappingService.getSupplierIsUse(positionMapping);
        if (!isUse) {
            response.setIsShowNoticeMsg(Boolean.TRUE);
            response.setNoticeMsg("该供应商已不合作，是否继续调拨？");

        }
        return response;
    }

    /**
     * 获取退货库位的id
     * 1.如果positon能找到对应的库位则找对应spaceid
     * 2.如果不行则找对应内部箱名，对的才入库单明细上的spaceid
     *
     * @param request
     * @return
     */
    public Integer getSpaceIdByInfo(BdSupplierPositionMappingSearchRequest request) {
        if (!StringUtils.hasText(request.getPositionCode())) {
            return null;
        }
        BdPositionEntity positionInfo = positionService.getPositionByCode(request.getPositionCode());
        if (Objects.nonNull(positionInfo)) {
            return positionInfo.getSpaceId();
        }
        if (!StringUtils.hasText(request.getSkuBarcode())) {
            return null;
        }
        ProductSpecInfoEntity skuInfo = specInfoService.findTopByBarcode(request.getSkuBarcode());
        if (Objects.isNull(skuInfo)) {
            return null;
        }
        return stockInternalBoxItemService.getSpaceIdByInfo(request.getPositionCode(), skuInfo.getSku());
    }

    private String decodeInfo(String text) {
        try {
            return URLDecoder.decode(text, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            return text;
        }
    }
}
