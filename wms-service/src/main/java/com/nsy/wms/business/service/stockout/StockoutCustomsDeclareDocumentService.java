package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.SimplifiedObjectMeta;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.ContainsConstants;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.ProductCodeConstants;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.bd.BdHsCodeConfigModel;
import com.nsy.api.wms.domain.stockout.FileInfo;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDocumentCount;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDocumentCountResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDocumentProcessedCount;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.CurrencyTypeEnum;
import com.nsy.api.wms.enumeration.stockout.ExportDrawbackInformationTypeEnum;
import com.nsy.api.wms.enumeration.stockout.OcrDrawbackTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareDocumentLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareOrderStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.DrawbackFileUpdateRequest;
import com.nsy.api.wms.request.stockout.OcrRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareOrderAddRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareOrderDeleteRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareUploadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdHsCodeConfigListResponse;
import com.nsy.api.wms.response.ocr.OcrKeyResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.api.wms.response.stockout.StockoutDeclareDocumentFilesResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareDocumentFileBo;
import com.nsy.wms.business.service.bd.BdExportPortService;
import com.nsy.wms.business.service.bd.BdHsCodeConfigService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.system.AliyunOssService;
import com.nsy.wms.business.service.system.OcrService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentFileEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsFilesKeyCacheEntity;
import com.nsy.wms.repository.entity.stockout.StockoutExportDrawbackInformationEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareDocumentMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 报关单据列表业务实现
 * @date: 2022-03-04 14:56
 */
@Service
public class StockoutCustomsDeclareDocumentService extends ServiceImpl<StockoutCustomsDeclareDocumentMapper, StockoutCustomsDeclareDocumentEntity> implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareDocumentService.class);

    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockoutCustomsDeclareDocumentLogService stockoutCustomsDeclareDocumentLogService;
    @Autowired
    private StockoutCustomsDeclareOrderService stockoutCustomsDeclareOrderService;
    @Autowired
    private StockoutCustomsDeclareDocumentItemService stockoutCustomsDeclareDocumentItemService;
    @Autowired
    private StockoutCustomsDeclareDocumentFileService stockoutCustomsDeclareDocumentFileService;
    @Autowired
    private StockoutExportDrawbackInformationService stockoutExportDrawbackInformationService;
    @Autowired
    private OcrService ocrService;
    @Autowired
    private AliyunOssService aliyunOssService;
    @Autowired
    private StockoutCustomsFilesKeyCacheService stockoutCustomsFilesKeyCacheService;
    @Resource
    StockoutCustomsDeclareDocumentAggregatedItemService declareDocumentAggregatedItemService;
    @Resource
    BdExportPortService exportPortService;

    static final String AUDIT_OPERATOR = "林志杰";

    private static final BigDecimal WEIGHT_COEFFICIENT = new BigDecimal("1.6");
    @Autowired
    private BdHsCodeConfigService bdHsCodeConfigService;

    public PageResponse<StockoutCustomsDeclareDocumentResult> pageList(StockoutCustomsDeclareDocumentRequest request) {
        if ("ALL".equals(request.getStatus())) {
            request.setStatus(null);
        }
        PageResponse<StockoutCustomsDeclareDocumentResult> pageResponse = new PageResponse<>();
        Page<StockoutCustomsDeclareDocumentResult> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        IPage<StockoutCustomsDeclareDocumentResult> pageResult = this.getBaseMapper().pageList(page, request);
        if (CollectionUtils.isEmpty(pageResult.getRecords()))
            return PageResponse.of(Collections.emptyList(), pageResult.getTotal());
        List<Integer> declareDocumentIdList = pageResult.getRecords().stream().map(StockoutCustomsDeclareDocumentResult::getDeclareDocumentId).collect(Collectors.toList());
        List<StockoutCustomsDeclareDocumentFileEntity> fileList = stockoutCustomsDeclareDocumentFileService.list(Wrappers.<StockoutCustomsDeclareDocumentFileEntity>lambdaQuery()
                .eq(StockoutCustomsDeclareDocumentFileEntity::getInformationType, ExportDrawbackInformationTypeEnum.BILL_OF_LANDING.getCode())
                .eq(StockoutCustomsDeclareDocumentFileEntity::getIsDelete, IsDeletedConstant.NOT_DELETED)
                .in(StockoutCustomsDeclareDocumentFileEntity::getDeclareDocumentId, declareDocumentIdList));
        Map<String, String> customerDeclareDocumentTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_CUSTOMER_DECLARE_DOCUMENT_TYPE.getName());
        Map<String, String> customerDeclareStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_CUSTOMER_DECLARE_STATUS.getName());
        Map<String, String> portEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PORT.getName());
        Map<String, String> destinationEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_DESTINATION.getName());
        pageResult.getRecords().forEach(result -> {
            result.setDeclareDocumentTypeStr(customerDeclareDocumentTypeEnumMap.get(result.getDeclareDocumentType()));
            result.setStatusStr(customerDeclareStatusEnumMap.get(result.getStatus()));
            result.setPort(portEnumMap.get(result.getPort()));
            if (StrUtil.isNotEmpty(result.getExportPort())) {
                result.setExportPort(exportPortService.getByCode(result.getExportPort()));
            }
            result.setCurrency(CurrencyTypeEnum.valueOf(result.getCurrency()).getType());
            result.setcAndFPrice(stockoutCustomsDeclareDocumentItemService.fetchCAndFPrice(result.getFreight(), result.getExchangeRate(), result.getTotalFobPrice()));
            result.setBrandName(stockoutCustomsDeclareDocumentItemService.getBrandName(result.getDeclareDocumentId()));
            if (StringUtils.isNotBlank(result.getShipmentIdStr())) {
                result.setShipmentIdList(Arrays.stream(result.getShipmentIdStr().split(StringConstant.COMMA)).map(Integer::valueOf).collect(Collectors.toList()));
            }
            String destination = destinationEnumMap.get(result.getDestination());
            if (StringUtils.isNotBlank(destination) && destination.contains(ContainsConstants.FULL_WIDTH_LEFT_BRACKET)) {
                result.setDestination(destination.substring(0, destination.indexOf(ContainsConstants.FULL_WIDTH_LEFT_BRACKET_CHAR)));
            }
            if (!CollectionUtils.isEmpty(fileList)) {
                fileList.stream().filter(item -> item.getDeclareDocumentId().equals(result.getDeclareDocumentId())).findAny().ifPresent(item -> result.setHasBillOfLanding(true));
            }
            result.setWriter("葛秀丹");
            result.setIsAuditStr(result.getIsAudit() ? "是" : "否");
            result.setDocumentDate(result.getDocumentationDate());
            buildInformationKey(result);
        });
        pageResponse.setContent(pageResult.getRecords());
        pageResponse.setTotalCount(baseMapper.pageCount(request));
        return pageResponse;
    }

    private void buildInformationKey(StockoutCustomsDeclareDocumentResult result) {
        Map<Integer, List<StockoutCustomsDeclareDocumentFileEntity>> fileBoMap = stockoutCustomsDeclareDocumentFileService.getEnableList(result.getDeclareDocumentId())
                .stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentFileEntity::getInformationType));
        //出口发票
        result.setExportInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_INVOICE.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //进项发票
        result.setInputInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.INPUT_INVOICE.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //采购合同
        result.setPurchaseContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.PURCHASE_CONTRACT.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //采购物流发票
        result.setPurchaseLogisticsInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.PURCHASE_LOGISTICS_INVOICE.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //出口国内段物流发票
        result.setExportNationalLogisticsInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_NATIONAL_LOGISTICS_INVOICE.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //出口国内段物流发票
        result.setExportInternationalLogisticsInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_INTERNATIONAL_LOGISTICS_INVOICE.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //采购物流合同
        result.setPurchaseLogisticsContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.PURCHASE_LOGISTICS_CONTRACT.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //出口国内段物流合同
        result.setExportNationalLogisticsContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_NATIONAL_LOGISTICS_CONTRACT.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //出口国外段物流合同
        result.setExportInternationalLogisticsContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_INTERNATIONAL_LOGISTICS_CONTRACT.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //海关报关单
        result.setCustomsDeclarationList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.CUSTOMS_DECLARATION.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));
        //放行通知书
        result.setReleaseNoticeList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.RELEASE_NOTICE.getCode(), new ArrayList<>()).stream().map(StockoutCustomsDeclareDocumentFileEntity::getInformationKey).collect(Collectors.toList()));

        //出口发票
        result.setExportInvoice(String.join("/", result.getExportInvoiceList()));
        //进项发票
        result.setInputInvoice(String.join("/", result.getInputInvoiceList()));
        //采购合同
        result.setPurchaseContract(String.join("/", result.getPurchaseContractList()));
        //采购物流发票
        result.setPurchaseLogisticsInvoice(String.join("/", result.getPurchaseLogisticsContractList()));
        //出口国内段物流发票
        result.setExportNationalLogisticsInvoice(String.join("/", result.getExportNationalLogisticsInvoiceList()));
        //出口国内段物流发票
        result.setExportInternationalLogisticsInvoice(String.join("/", result.getExportInternationalLogisticsInvoiceList()));
        //采购物流合同
        result.setPurchaseLogisticsContract(String.join("/", result.getPurchaseLogisticsContractList()));
        //出口国内段物流合同
        result.setExportNationalLogisticsContract(String.join("/", result.getExportNationalLogisticsContractList()));
        //出口国外段物流合同
        result.setExportInternationalLogisticsContract(String.join("/", result.getExportInternationalLogisticsContractList()));
        //海关报关单
        result.setCustomsDeclaration(String.join("/", result.getCustomsDeclarationList()));
        //放行通知书
        result.setReleaseNotice(String.join("/", result.getReleaseNoticeList()));
    }


    @Transactional
    public void upload(StockoutCustomsDeclareUploadRequest request) {
        StockoutCustomsDeclareDocumentEntity documentEntity = this.getById(request.getDeclareDocumentId());
        if (Objects.isNull(documentEntity) || IsDeletedConstant.DELETED.equals(documentEntity.getIsDeleted())) {
            throw new BusinessServiceException("未找到报关单据！");
        }
        if (StringUtils.isBlank(request.getFileInfo().getFileName()) || StringUtils.isBlank(request.getFileInfo().getFileUrl())) {
            throw new BusinessServiceException("文件名或链接不能为空！");
        }
        //关联退税资料
        StockoutCustomsDeclareDocumentFileService.RelateBo relateBo = new StockoutCustomsDeclareDocumentFileService.RelateBo(documentEntity, ExportDrawbackInformationTypeEnum.BILL_OF_LANDING, documentEntity.getDeclareDocumentNo(), request.getFileInfo().getFileUrl(), request.getFileInfo().getFileName(), Boolean.FALSE);
        stockoutCustomsDeclareDocumentFileService.relate(relateBo);
    }

    @Transactional
    public void cancel(Integer declareDocumentId) {
        StockoutCustomsDeclareDocumentEntity documentEntity = this.getById(declareDocumentId);
        if (Objects.isNull(documentEntity) || IsDeletedConstant.DELETED.equals(documentEntity.getIsDeleted())) {
            throw new BusinessServiceException("未找到报关单据！");
        }
        if (documentEntity.getIsAudit())
            throw new BusinessServiceException("报关单据已审核无法修改");
        StockoutCustomsDeclareDocumentEntity declareDocumentEntity = new StockoutCustomsDeclareDocumentEntity();
        declareDocumentEntity.setDeclareDocumentId(documentEntity.getDeclareDocumentId());
        declareDocumentEntity.setUpdateBy(loginInfoService.getName());
        declareDocumentEntity.setIsDeleted(IsDeletedConstant.DELETED);
        this.updateById(declareDocumentEntity);
        stockoutCustomsDeclareDocumentLogService.addLog(documentEntity.getDeclareDocumentId(), StockoutCustomsDeclareDocumentLogTypeEnum.CANCEL_BATCH.name(),
                String.format("取消报关单据%s, 对应订单变成未报关", documentEntity.getDeclareDocumentNo()));
        //删除对应的退税单据，报关文件
        stockoutCustomsDeclareDocumentFileService.lambdaUpdate().set(StockoutCustomsDeclareDocumentFileEntity::getIsDelete, IsDeletedConstant.DELETED)
                .eq(StockoutCustomsDeclareDocumentFileEntity::getDeclareDocumentId, declareDocumentId).update();
        //更新报关订单为待处理
        List<StockoutCustomsDeclareDocumentItemEntity> list = stockoutCustomsDeclareDocumentItemService.list(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentItemEntity>()
                .select(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId)
                .eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentId, declareDocumentId));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<StockoutCustomsDeclareOrderEntity> orderEntityList = stockoutCustomsDeclareOrderService.listByIds(list.stream().map(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId).distinct().collect(Collectors.toList()));
        List<StockoutCustomsDeclareOrderEntity> collect = orderEntityList.stream().map(entity -> {
            StockoutCustomsDeclareOrderEntity orderEntity = new StockoutCustomsDeclareOrderEntity();
            orderEntity.setDeclareOrderId(entity.getDeclareOrderId());
            orderEntity.setUpdateBy(loginInfoService.getName());
            orderEntity.setStatus(StockoutCustomsDeclareOrderStatusEnum.WAIT_DEAL.name());
            return orderEntity;
        }).collect(Collectors.toList());
        stockoutCustomsDeclareOrderService.updateBatchById(collect);
    }

    public List<StatusCountResponse> getStockoutCustomsDeclareDocumentCount() {
        Map<String, List<StatusCountResponse>> collect = this.getBaseMapper().countByStatus().stream().collect(Collectors.groupingBy(StatusCountResponse::getStatus));
        List<StatusCountResponse> list = Arrays.stream(StockoutCustomsDeclareOrderStatusEnum.values()).map(statusEnum -> {
            StatusCountResponse response = new StatusCountResponse();
            List<StatusCountResponse> responses = collect.get(statusEnum.name());
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getName());
            return response;
        }).collect(Collectors.toList());
        StatusCountResponse response = new StatusCountResponse();
        response.setQty(list.stream().mapToInt(StatusCountResponse::getQty).sum());
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_CUSTOMS_DECLARE_DOCUMENT;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        if (request.getPageIndex() * request.getPageIndex() <= 1000) {
            StockoutCustomsDeclareDocumentRequest stockoutCustomsDeclareDocumentRequest = JSONObject.parseObject(request.getRequestContent(), StockoutCustomsDeclareDocumentRequest.class);
            stockoutCustomsDeclareDocumentRequest.setPageIndex(request.getPageIndex());
            stockoutCustomsDeclareDocumentRequest.setPageSize(request.getPageSize());
            PageResponse<StockoutCustomsDeclareDocumentResult> resultPageResponse = this.pageList(stockoutCustomsDeclareDocumentRequest);
            response.setTotalCount(resultPageResponse.getTotalCount());
            response.setDataJsonStr(JsonMapper.toJson(resultPageResponse.getContent()));
        } else {
            response.setTotalCount(0L);
            response.setDataJsonStr(JsonMapper.toJson(new ArrayList<StockoutCustomsDeclareDocumentResult>()));
        }

        return response;
    }

    @Transactional
    public void addOrder(StockoutCustomsDeclareOrderAddRequest request) {
        StockoutCustomsDeclareDocumentEntity documentEntity = this.getById(request.getDeclareDocumentId());
        if (Objects.isNull(documentEntity) || IsDeletedConstant.DELETED.equals(documentEntity.getIsDeleted())) {
            throw new BusinessServiceException("未找到报关单据！");
        }
        List<StockoutCustomsDeclareOrderEntity> list = stockoutCustomsDeclareOrderService.list(new LambdaQueryWrapper<StockoutCustomsDeclareOrderEntity>()
                .eq(StockoutCustomsDeclareOrderEntity::getOrderNo, request.getOrderNo())
                .eq(StockoutCustomsDeclareOrderEntity::getStatus, StockoutCustomsDeclareOrderStatusEnum.WAIT_DEAL.name()));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("该订单号未找到待处理的报关订单，请确认！");
        }
        List<Integer> collect = list.stream().map(StockoutCustomsDeclareOrderEntity::getDeclareOrderId).collect(Collectors.toList());
        List<StockoutCustomsDocumentCount> documentCountNew = stockoutCustomsDeclareOrderService.getDocumentCount(collect, documentEntity.getCompanyId(), documentEntity);
        collect.addAll(stockoutCustomsDeclareDocumentItemService.getBaseMapper().getOrderIdsByDocumentId(request.getDeclareDocumentId()));
        List<StockoutCustomsDocumentCount> documentCountAll = stockoutCustomsDeclareOrderService.getDocumentCount(collect, documentEntity.getCompanyId(), documentEntity);
        if (CollectionUtils.isEmpty(documentCountAll)) {
            throw new BusinessServiceException("未找到报关订单！");
        }
        stockoutCustomsDeclareOrderService.validateOrder(documentCountNew);
        addStockoutCustomsDeclareDocumentItem(documentEntity, documentCountAll, documentCountNew);
        stockoutCustomsDeclareDocumentLogService.addLog(documentEntity.getDeclareDocumentId(), StockoutCustomsDeclareDocumentLogTypeEnum.ADD.name(),
                String.format("报关单据%s,增加订单号%s", documentEntity.getDeclareDocumentNo(), request.getOrderNo()));
        //更新报告订单为已处理
        List<StockoutCustomsDeclareOrderEntity> orderEntityList = list.stream().map(entity -> {
            StockoutCustomsDeclareOrderEntity orderEntity = new StockoutCustomsDeclareOrderEntity();
            orderEntity.setDeclareOrderId(entity.getDeclareOrderId());
            orderEntity.setUpdateBy(loginInfoService.getName());
            orderEntity.setStatus(StockoutCustomsDeclareOrderStatusEnum.DEAL.name());
            return orderEntity;
        }).collect(Collectors.toList());
        stockoutCustomsDeclareOrderService.updateBatchById(orderEntityList);
    }


    private void addStockoutCustomsDeclareDocumentItem(StockoutCustomsDeclareDocumentEntity documentEntity, List<StockoutCustomsDocumentCount> documentCount, List<StockoutCustomsDocumentCount> list) {
        updateDocumentWhenDocumentItemChanged(documentEntity, documentCount);
        Map<String, String> unitDictionaryMap = enumConversionChineseUtils.inverseBaseConversion(DictionaryNameEnum.WMS_CUSTOMS_UNIT.getName());
        List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = list.stream()
                .map(item -> buildDocumentItem(documentEntity.getDeclareDocumentId(), item, unitDictionaryMap))
                .collect(Collectors.toList());
        stockoutCustomsDeclareDocumentItemService.saveBatch(itemEntityList);
        //生成聚合明细
        declareDocumentAggregatedItemService.resetAggregatedItem(documentEntity.getDeclareDocumentId(), documentEntity.getDeclareDocumentNo());
    }

    /**
     * 生成明细
     *
     * @param declareDocumentId
     * @param item
     * @param unitDictionaryMap
     * @return
     */
    public StockoutCustomsDeclareDocumentItemEntity buildDocumentItem(Integer declareDocumentId, StockoutCustomsDocumentCount item, Map<String, String> unitDictionaryMap) {
        StockoutCustomsDeclareDocumentItemEntity itemEntity = new StockoutCustomsDeclareDocumentItemEntity();
        BeanUtilsEx.copyProperties(item, itemEntity);
        if (StringUtils.isNotBlank(item.getCustomsDeclareUnitCn()) && item.getCustomsDeclareUnitCn().contains(StringConstant.FORWARD_SLASH_STR)) {
            itemEntity.setCustomsDeclareUnitCn(item.getCustomsDeclareUnitCn().substring(0, item.getCustomsDeclareUnitCn().indexOf(StringConstant.FORWARD_SLASH)));
        }
        itemEntity.setCustomsDeclareUnit(unitDictionaryMap.get(itemEntity.getCustomsDeclareUnitCn()));
        itemEntity.setDeclareDocumentId(declareDocumentId);
        itemEntity.setLocation(TenantContext.getTenant());
        itemEntity.setCreateBy(loginInfoService.getName());
        String elementStr = String.join(StringConstant.FORWARD_SLASH_STR, removeSpu(item));
        String declareElement = elementStr + (StringUtils.isNotBlank(item.getSellerSku()) ? ProductCodeConstants.surroundConstants(item.getSellerSku()) : Strings.EMPTY);
        if (StrUtil.isNotEmpty(item.getBrandName())) {
            declareElement = declareElement.replace("无品牌", item.getBrandName().toUpperCase(Locale.ROOT));
            declareElement = "1" + declareElement.substring(1);
        }
        itemEntity.setDeclareElement(declareElement);
        return itemEntity;
    }

    /**
     * 删除spu
     *
     * @param item
     * @return
     */
    private List<String> removeSpu(StockoutCustomsDocumentCount item) {
        BdHsCodeConfigListResponse elementList = bdHsCodeConfigService.getElementList(item.getHsCode());
        List<BdHsCodeConfigModel> bdHsCodeConfigList = elementList.getBdHsCodeConfigList();
        BdHsCodeConfigModel spuConfigModel = bdHsCodeConfigList.stream()
                .filter(hsCodeConfigModel -> hsCodeConfigModel.getElementComment().contains("货号"))
                .findFirst().orElse(null);
        List<String> valuesList = StrUtil.split(item.getElementValue(), "|");
        if (Objects.isNull(spuConfigModel)) {
            LOGGER.info("海关编码找不到货号映射 {} ", item.getHsCode());
            return valuesList;
        }
        if (valuesList.size() <= spuConfigModel.getElementNo().intValue()) {
            LOGGER.info("申报元素和海关编码长度不对 {} {} ", item.getElementValue(), item.getHsCode());
            return valuesList;
        }
        valuesList.remove(spuConfigModel.getElementNo().intValue());
        return valuesList;
    }

    public void computeValues(List<StockoutCustomsDocumentCount> documentCount, StockoutCustomsDeclareDocumentEntity entity) {
        entity.setBoxNum(Math.toIntExact(documentCount.stream().map(StockoutCustomsDocumentCount::getShipmentId).distinct().count()));
        entity.setOrderNum(Math.toIntExact(documentCount.stream().map(StockoutCustomsDocumentCount::getDeclareOrderId).distinct().count()));
        entity.setSkuNum(documentCount.stream().mapToInt(StockoutCustomsDocumentCount::getQty).sum());
        // 以shipmentId维度计算箱子信息
        Collection<StockoutCustomsDocumentCount> groupByShipment = documentCount.stream()
                .collect(Collectors.toMap(StockoutCustomsDocumentCount::getShipmentId, Function.identity(), (v1, v2) -> v1)).values();
        List<String> orderList = groupByShipment.stream()
                .filter(item -> Objects.isNull(item.getBoxWeight()) || BigDecimal.ZERO.compareTo(item.getBoxWeight()) == 0)
                .map(StockoutCustomsDocumentCount::getOrderNo).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderList)) {
            throw new BusinessServiceException(String.format("订单号：%s存在没有重量的装箱单", orderList.toString()));
        }
        groupByShipment.stream().map(StockoutCustomsDocumentCount::getBoxWeight).filter(Objects::nonNull).reduce(BigDecimal::add).ifPresent(entity::setWeight);
        entity.setNetWeight(entity.getWeight().subtract(WEIGHT_COEFFICIENT.multiply(BigDecimal.valueOf(entity.getBoxNum()))));
        documentCount.stream().map(item -> item.getWeight().multiply(BigDecimal.valueOf(item.getQty()))).reduce(BigDecimal::add).ifPresent(totalPlanWeight -> {
            entity.setTotalPlanWeight(totalPlanWeight);
            entity.setTotalPlanBoxNum(totalPlanWeight.divide(BigDecimal.valueOf(21), 0, RoundingMode.HALF_UP).intValue());
            //不能小于一箱
            entity.setTotalPlanBoxNum(Math.max(entity.getTotalPlanBoxNum(), 1));
        });
        List<BigDecimal> volumes = groupByShipment.stream().map(StockoutCustomsDocumentCount::getBoxSize).filter(StringUtils::isNotBlank)
                .map(boxSize -> Arrays.stream(boxSize.split(StringConstant.ASTERISK_WITH_ESCAPE)).map(BigDecimal::new).reduce(BigDecimal::multiply).orElse(BigDecimal.ZERO)).collect(Collectors.toList());
        volumes.stream().reduce(BigDecimal::add).ifPresent(item -> entity.setVolume(item.divide(BigDecimal.valueOf(1000000), 3, RoundingMode.HALF_UP)));
        documentCount.stream().map(item -> {
            if (Objects.isNull(item.getFobPrice()))
                throw new BusinessServiceException(String.format("%s 报关分类价格未维护", item.getSku()));
            return item.getFobPrice().multiply(BigDecimal.valueOf(item.getQty()));
        }).reduce(BigDecimal::add).ifPresent(entity::setTotalFobPrice);
    }


    @Transactional
    public void deleteOrder(StockoutCustomsDeclareOrderDeleteRequest request) {
        StockoutCustomsDeclareDocumentEntity documentEntity = this.getById(request.getDeclareDocumentId());
        if (Objects.isNull(documentEntity) || IsDeletedConstant.DELETED.equals(documentEntity.getIsDeleted())) {
            throw new BusinessServiceException("未找到报关单据！");
        }
        if (documentEntity.getIsAudit())
            throw new BusinessServiceException("报关单据已审核无法修改");

        request.getDeclareOrderIdList().forEach(declareOrderId -> {
            StockoutCustomsDeclareOrderEntity entity = stockoutCustomsDeclareOrderService.getById(declareOrderId);
            if (Objects.isNull(entity)) {
                throw new BusinessServiceException("未找到报关订单！");
            }
            List<StockoutCustomsDeclareDocumentItemEntity> stockoutCustomsDeclareDocumentItemEntityList = stockoutCustomsDeclareDocumentItemService.listByDeclareOrderId(declareOrderId);
            if (CollectionUtils.isEmpty(stockoutCustomsDeclareDocumentItemEntityList) || stockoutCustomsDeclareDocumentItemEntityList.stream().noneMatch(item -> item.getDeclareDocumentId().equals(request.getDeclareDocumentId()))) {
                throw new BusinessServiceException("单据中没有该订单，请刷新页面！");
            }
            stockoutCustomsDeclareDocumentItemService.removeByIds(stockoutCustomsDeclareDocumentItemEntityList.stream().map(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentItemId).collect(Collectors.toList()));
            StockoutCustomsDeclareOrderEntity orderEntity = new StockoutCustomsDeclareOrderEntity();
            orderEntity.setDeclareOrderId(entity.getDeclareOrderId());
            orderEntity.setUpdateBy(loginInfoService.getName());
            orderEntity.setStatus(StockoutCustomsDeclareOrderStatusEnum.WAIT_DEAL.name());
            orderEntity.setProformaInvoiceUrl(StringUtils.EMPTY);
            stockoutCustomsDeclareOrderService.updateById(orderEntity);
            stockoutCustomsDeclareDocumentLogService.addLog(documentEntity.getDeclareDocumentId(), StockoutCustomsDeclareDocumentLogTypeEnum.REDUCE.name(),
                    String.format("报关单据%s,删除订单号%s", documentEntity.getDeclareDocumentNo(), stockoutCustomsDeclareDocumentItemEntityList.get(0).getOrderNo()));

        });

        List<Integer> declareOrderIdList = stockoutCustomsDeclareDocumentItemService.getBaseMapper().getOrderIdsByDocumentId(request.getDeclareDocumentId());

        if (CollectionUtils.isEmpty(declareOrderIdList)) {
            this.cancel(request.getDeclareDocumentId());
            return;
        }
        List<StockoutCustomsDocumentCount> documentCountAll = stockoutCustomsDeclareOrderService.getDocumentCount(declareOrderIdList, documentEntity.getCompanyId(), documentEntity);
        updateDocumentWhenDocumentItemChanged(documentEntity, documentCountAll);
    }


    private void updateDocumentWhenDocumentItemChanged(StockoutCustomsDeclareDocumentEntity documentEntity, List<StockoutCustomsDocumentCount> documentCount) {
        StockoutCustomsDeclareDocumentEntity entity = new StockoutCustomsDeclareDocumentEntity();
        entity.setDeclareDocumentId(documentEntity.getDeclareDocumentId());
        entity.setStatus(StockoutCustomsDeclareOrderStatusEnum.WAIT_DEAL.name());
        entity.setUpdateBy(loginInfoService.getName());
        computeValues(documentCount, entity);
        this.updateById(entity);
    }

    public StockoutCustomsDocumentCountResult generateDocumentCount(Integer declareDocumentId) {
        List<StockoutCustomsDeclareDocumentItemEntity> list = stockoutCustomsDeclareDocumentItemService.list(Wrappers.<StockoutCustomsDeclareDocumentItemEntity>lambdaQuery()
                .eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentId, declareDocumentId));
        if (list.isEmpty()) throw new BusinessServiceException("该报关单据未绑定任何报关订单");
        List<Integer> declareOrderIds = list.stream().map(StockoutCustomsDeclareDocumentItemEntity::getDeclareOrderId).distinct().collect(Collectors.toList());
        IdListRequest request = new IdListRequest();
        request.setIdList(declareOrderIds);
        return stockoutCustomsDeclareOrderService.generateDocumentCount(request);
    }

    public StockoutCustomsDocumentProcessedCount getStockoutCustomsDocumentProcessedCount() {
        StockoutCustomsDocumentProcessedCount response = new StockoutCustomsDocumentProcessedCount();
        Integer processedNum = baseMapper.selectCount(Wrappers.<StockoutCustomsDeclareDocumentEntity>lambdaQuery().eq(StockoutCustomsDeclareDocumentEntity::getStatus, StockoutCustomsDeclareOrderStatusEnum.DEAL.name())
                .eq(StockoutCustomsDeclareDocumentEntity::getIsDeleted, 0));
        Integer todayProcessedNum = baseMapper.selectCount(Wrappers.<StockoutCustomsDeclareDocumentEntity>lambdaQuery().eq(StockoutCustomsDeclareDocumentEntity::getStatus, StockoutCustomsDeclareOrderStatusEnum.DEAL.name())
                .eq(StockoutCustomsDeclareDocumentEntity::getIsDeleted, 0).gt(StockoutCustomsDeclareDocumentEntity::getUpdateDate, LocalDateTime.of(LocalDate.now(), LocalTime.MIN).toString()));
        response.setProcessedNum(processedNum);
        response.setTodayProcessedNum(todayProcessedNum);
        return response;
    }

    public OcrKeyResponse ocrFile(OcrRequest request) {
        String objectName = aliyunOssService.getKeyByUrl(request.getUrl());
        String host = URLUtil.url(request.getUrl()).getHost();
        String bucket = host.split("\\.")[0]; //前端的bucket

        SimplifiedObjectMeta simplifiedObjectMeta = Optional.ofNullable(aliyunOssService.getSimplifiedObjectMeta(bucket, objectName))
                .orElseThrow(() -> new BusinessServiceException("获取不到对象信息"));
        String eTag = simplifiedObjectMeta.getETag();
        // 从缓存表取出值
        StockoutCustomsFilesKeyCacheEntity firstOne = stockoutCustomsFilesKeyCacheService.getFirstOne(eTag);

        OcrDrawbackTypeEnum type = Optional.ofNullable(EnumUtils.getEnum(OcrDrawbackTypeEnum.class, request.getType()))
                .orElseThrow(() -> new BusinessServiceException("类型有误"));

        if (ObjectUtil.isNotNull(firstOne) && StrUtil.isNotBlank(firstOne.getInformationKey())) {
            return new OcrKeyResponse(firstOne.getInformationKey().trim());
        }
        String informationKey = ocrService.getInformationKey(request.getUrl(), type);
        stockoutCustomsFilesKeyCacheService.removeAndSave(objectName, eTag, informationKey);
        return new OcrKeyResponse(informationKey);
    }

    public StockoutDeclareDocumentFilesResponse getDeclareDocumentFilesResponse(Integer documentId) {
        Map<Integer, List<StockoutCustomsDeclareDocumentFileBo>> fileBoMap = stockoutCustomsDeclareDocumentFileService.getBaseMapper().getFileList(documentId).stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentFileBo::getInformationType));

        StockoutDeclareDocumentFilesResponse response = new StockoutDeclareDocumentFilesResponse();
        //出口发票
        response.setExportInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_INVOICE.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //进项发票
        response.setInputInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.INPUT_INVOICE.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //采购合同
        response.setPurchaseContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.PURCHASE_CONTRACT.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //采购物流发票
        response.setPurchaseLogisticsInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.PURCHASE_LOGISTICS_INVOICE.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //出口国内段物流发票
        response.setExportNationalLogisticsInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_NATIONAL_LOGISTICS_INVOICE.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //出口国内段物流发票
        response.setExportInternationalLogisticsInvoiceList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_INTERNATIONAL_LOGISTICS_INVOICE.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //采购物流合同
        response.setPurchaseLogisticsContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.PURCHASE_LOGISTICS_CONTRACT.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //出口国内段物流合同
        response.setExportInternationalLogisticsContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_INTERNATIONAL_LOGISTICS_CONTRACT.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //出口国外段物流合同
        response.setExportNationalLogisticsContractList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.EXPORT_NATIONAL_LOGISTICS_CONTRACT.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //海关报关单
        response.setCustomsDeclarationList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.CUSTOMS_DECLARATION.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //放行通知书
        response.setReleaseNoticeList(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.RELEASE_NOTICE.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //提单
        response.setBillOfLanding(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.BILL_OF_LANDING.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //报关单据(新)
        response.setDeclareNewDocument(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.NEW_DOCUMENT.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        //报关单据
        response.setDeclareDocument(fileBoMap.getOrDefault(ExportDrawbackInformationTypeEnum.DOCUMENT.getCode(), new ArrayList<>()).stream().map(item -> buildFileInfo(item.getFileUrl(), item.getFileName())).collect(Collectors.toList()));
        return response;
    }

    private FileInfo buildFileInfo(String fileUrl, String fileName) {
        return new FileInfo(fileUrl.replace("+", "%2B"), fileName);
    }

    @Transactional
    public void uploadDrawbackFile(DrawbackFileUpdateRequest request) {
        if (Objects.isNull(request.getFile()))
            throw new BusinessServiceException("文件信息不能为空");
        if (StringUtils.isBlank(request.getFile().getFileName()) && StringUtils.isBlank(request.getFile().getFileUrl()))
            throw new BusinessServiceException("文件名或文件链接地址为空");
        Integer informationType = null;
        if (request.getType() == OcrDrawbackTypeEnum.CUSTOMS_DECLARATION) {
            informationType = ExportDrawbackInformationTypeEnum.CUSTOMS_DECLARATION.getCode();
        }
        if (request.getType() == OcrDrawbackTypeEnum.RELEASE_NOTICE) {
            informationType = ExportDrawbackInformationTypeEnum.RELEASE_NOTICE.getCode();
        }

        StockoutExportDrawbackInformationEntity drawbackEntity = new StockoutExportDrawbackInformationEntity();
        drawbackEntity.setInformationType(informationType);
        drawbackEntity.setInformationKey(request.getInformationKey());
        drawbackEntity.setFileUrl(request.getFile().getFileUrl());
        drawbackEntity.setFileName(request.getFile().getFileName());
        drawbackEntity.setCreateBy(loginInfoService.getName());
        drawbackEntity.setCreateDate(new Date());
        stockoutExportDrawbackInformationService.save(drawbackEntity);
    }

    public StockoutCustomsDeclareDocumentEntity findByDeclareDocumentNo(String contrNo) {
        LambdaQueryWrapper<StockoutCustomsDeclareDocumentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutCustomsDeclareDocumentEntity::getDeclareDocumentNo, contrNo);
        wrapper.eq(StockoutCustomsDeclareDocumentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<StockoutCustomsDeclareDocumentEntity> list = list(wrapper);
        if (list.size() > 1) {
            throw new BusinessServiceException("报关数据中有多个此报关单号：" + contrNo);
        }
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public StockoutCustomsDeclareDocumentEntity getByDeclareDocumentNo(String contrNo) {
        StockoutCustomsDeclareDocumentEntity documentEntity = findByDeclareDocumentNo(contrNo);
        if (Objects.isNull(documentEntity))
            throw new BusinessServiceException(String.format("报关单据 %s 不存在", contrNo));
        return documentEntity;
    }

    public StockoutCustomsDeclareDocumentEntity getEnableByDeclareDocumentId(Integer declareDocumentId) {
        StockoutCustomsDeclareDocumentEntity declareDocumentEntity = getOne(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentEntity>()
                .eq(StockoutCustomsDeclareDocumentEntity::getDeclareDocumentId, declareDocumentId)
                .eq(StockoutCustomsDeclareDocumentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED));

        if (Objects.isNull(declareDocumentEntity))
            throw new BusinessServiceException(String.format("报关单据 %s 不存在", declareDocumentId));
        return declareDocumentEntity;
    }

    /**
     * 审核
     *
     * @param request
     */
    @Transactional
    public void audit(IdListRequest request) {

        this.update(new LambdaUpdateWrapper<StockoutCustomsDeclareDocumentEntity>()
                .set(StockoutCustomsDeclareDocumentEntity::getAuditor, AUDIT_OPERATOR)
                .set(StockoutCustomsDeclareDocumentEntity::getAuditTime, new Date())
                .set(StockoutCustomsDeclareDocumentEntity::getIsAudit, Boolean.TRUE)
                .in(StockoutCustomsDeclareDocumentEntity::getDeclareDocumentId, request.getIdList()));

        request.getIdList().forEach(declareDocumentId -> {
            StockoutCustomsDeclareDocumentEntity declareDocumentEntity = getById(declareDocumentId);
            stockoutCustomsDeclareDocumentLogService.addLogWithOperator(declareDocumentEntity.getDeclareDocumentId(),
                    StockoutCustomsDeclareDocumentLogTypeEnum.AUDIT.name(), "审核", AUDIT_OPERATOR);
        });
    }

    /**
     * 反审
     *
     * @param request
     */
    @Transactional
    public void cancelAudit(IdListRequest request) {
        this.update(new LambdaUpdateWrapper<StockoutCustomsDeclareDocumentEntity>()
                .set(StockoutCustomsDeclareDocumentEntity::getAuditor, "")
                .set(StockoutCustomsDeclareDocumentEntity::getAuditTime, null)
                .set(StockoutCustomsDeclareDocumentEntity::getIsAudit, Boolean.FALSE)
                .in(StockoutCustomsDeclareDocumentEntity::getDeclareDocumentId, request.getIdList()));

        request.getIdList().forEach(declareDocumentId -> {
            StockoutCustomsDeclareDocumentEntity declareDocumentEntity = getById(declareDocumentId);
            stockoutCustomsDeclareDocumentLogService.addLogWithOperator(declareDocumentEntity.getDeclareDocumentId(),
                    StockoutCustomsDeclareDocumentLogTypeEnum.CANCEL_AUDIT.name(), "反审核", AUDIT_OPERATOR);
        });
    }
}
