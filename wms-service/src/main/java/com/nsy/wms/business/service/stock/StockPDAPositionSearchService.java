package com.nsy.wms.business.service.stock;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stock.SkuStockInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.request.stock.StockListRequest;
import com.nsy.api.wms.response.stock.PDAPositionSearchResponse;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockPrematchInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class StockPDAPositionSearchService {
    @Autowired
    StockService stockService;
    @Autowired
    StockInternalBoxService internalBoxService;
    @Autowired
    BdPositionMapper bdPositionMapper;
    @Autowired
    StockPrematchInfoMapper prematchInfoMapper;

    public PDAPositionSearchResponse stockSearch(String code) {
        PDAPositionSearchResponse response = new PDAPositionSearchResponse();

        StockInternalBoxEntity internalBox = internalBoxService.getOne(new LambdaQueryWrapper<StockInternalBoxEntity>()
                .eq(StockInternalBoxEntity::getInternalBoxCode, code)
                .eq(StockInternalBoxEntity::getIsDeleted, 0));
        if (internalBox != null) {
            throw new BusinessServiceException("暂不支持内部箱盘点！");
            //return stockSearchByBox(internalBox);
        }

        BdPositionEntity positionEntity = bdPositionMapper.selectOne(new LambdaQueryWrapper<BdPositionEntity>()
                .eq(BdPositionEntity::getPositionCode, code).eq(BdPositionEntity::getIsDeleted, 0));
        if (Objects.isNull(positionEntity)) {
            throw new BusinessServiceException("库位不存在");
        }
        if (BdPositionTypeEnum.EXCEPTION_POSITION.name().equals(positionEntity.getPositionType())
                || BdPositionTypeEnum.SHIPPING_POSITION.name().equals(positionEntity.getPositionType())) {
            throw new BusinessServiceException("虚拟库位不允许在PDA查询，请移步PC端操作");
        }
        response.setPositionType(BdPositionTypeEnum.resolve(positionEntity.getPositionType()));
        StockListRequest request = new StockListRequest();
        request.setPositionCode(code);
        response.setAllStock(stockService.getBaseMapper().stockStatistics(request));
        List<SkuStockInfo> skuStockInfoList = prematchInfoMapper.statisticsByPositionId(positionEntity.getPositionId());
        response.setSkuList(skuStockInfoList);
        return response;
    }

   /* private PDAPositionSearchResponse stockSearchByBox(StockInternalBoxEntity internalBox) {
        // 退货箱 | 调拨箱 |归还箱 | 撤货箱
        if (!StockInternalBoxTypeEnum.RETURN_BOX.name().equals(internalBox.getInternalBoxType())
                && !StockInternalBoxTypeEnum.TRANSFER_BOX.name().equals(internalBox.getInternalBoxType())
                && !StockInternalBoxTypeEnum.BORROW_RETURN_BOX.name().equals(internalBox.getInternalBoxType())
                && !StockInternalBoxTypeEnum.WITHDRAWAL_BOX.name().equals(internalBox.getInternalBoxType())) {
            throw new BusinessServiceException(String.format("当前仅支持【%s】【%s】【%s】【%s】",
                    StockInternalBoxTypeEnum.RETURN_BOX.getName(), StockInternalBoxTypeEnum.TRANSFER_BOX.getName(),
                    StockInternalBoxTypeEnum.BORROW_RETURN_BOX.getName(), StockInternalBoxTypeEnum.WITHDRAWAL_BOX.getName()));
        }
        PDAPositionSearchResponse response = new PDAPositionSearchResponse();
        StockListRequest request = new StockListRequest();
        request.setInternalBoxId(internalBox.getInternalBoxId());
        response.setAllStock(stockService.getBaseMapper().stockStatistics(request));
        response.setBoxType(StockInternalBoxTypeEnum.getNameBy(internalBox.getInternalBoxType()));
        // 箱内库存
        List<StockEntity> boxStockList = stockService.list(new LambdaQueryWrapper<StockEntity>().eq(StockEntity::getInternalBoxId, internalBox.getInternalBoxId()));
        List<SkuStockInfo> skuStockInfoList = new LinkedList<>();
        for (StockEntity stockEntity : boxStockList) {
            SkuStockInfo stockInfo = new SkuStockInfo();
            stockInfo.setStockId(stockEntity.getStockId());
            stockInfo.setStock(stockEntity.getStock());
            stockInfo.setSku(stockEntity.getSku());
            skuStockInfoList.add(stockInfo);
        }
        response.setSkuList(skuStockInfoList);
        return response;
    }*/
}
