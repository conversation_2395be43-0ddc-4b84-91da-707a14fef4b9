package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinMeasureTypeEnum;
import com.nsy.api.wms.enumeration.stockout.VolumeWeightStandardTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingPageRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingExportResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingExportResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.UnitUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-18 14:58
 */
@Service
public class StockinHeightRecordMappingDetailExportService implements IDownloadService {

    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private StockinVolumeWeightRecordMappingService volumeWeightRecordMappingService;


    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_HEIGHT_RECORD_DETAIL_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinVolumeWeightRecordMappingPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockinVolumeWeightRecordMappingPageRequest.class);
        // 设置每次的查询数量
        Page<StockinVolumeWeightRecordMappingExportResponse> pageRequest = new Page<>(request.getPageIndex(), request.getPageSize());
        pageRequest.setSearchCount(false);
        downloadRequest.setMeasureType(StockinMeasureTypeEnum.HEIGHT.name());
        IPage<StockinHeightRecordMappingExportResponse> pageResponse = volumeWeightRecordMappingService.getBaseMapper().getHeightPageExportList(pageRequest, downloadRequest);
        if (CollectionUtils.isEmpty(pageResponse.getRecords())) {
            response.setTotalCount(0L);
            return response;
        }
        pageResponse.getRecords().forEach(detail -> {
            detail.setStandardTypeStandard(VolumeWeightStandardTypeEnum.of(detail.getStandardTypeStandard()));
            detail.setVolumeWeightKgStandard(UnitUtils.poundsToKilograms(detail.getVolumeWeightPoundStandard()));
            detail.setChargedWeightKgStandard(UnitUtils.poundsToKilograms(detail.getChargedWeightStandard()));
            detail.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(Collections.singletonList(detail.getSupplierDeliveryNo()), detail.getSku()));
        });
        response.setTotalCount((long) (pageResponse.getRecords().size() >= request.getPageSize()
                ? (request.getPageIndex() + 1) * request.getPageSize()
                : (request.getPageIndex() - 1) * request.getPageSize() + pageResponse.getRecords().size()));
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getRecords()));
        return response;
    }
}
