package com.nsy.wms.business.manage.thirdparty.request.tiktokshp;

/**
 * <AUTHOR>
 * @date 2024/6/20 15:08
 */
public class TikTokShopReserveDateInfo {

    /**
     * 预约揽收日期（配送方式若为平台物流 必填）
     */
    private Integer predictedPickupTime;

    /**
     * 预约揽收时段开始时间（配送方式若为平台物流 必填）
     */
    private Integer predictedPickupGe;

    /**
     * 预约揽收时段结束时间（配送方式若为平台物流 必填）
     */
    private Integer predictedPickupLt;

    public Integer getPredictedPickupTime() {
        return predictedPickupTime;
    }

    public void setPredictedPickupTime(Integer predictedPickupTime) {
        this.predictedPickupTime = predictedPickupTime;
    }

    public Integer getPredictedPickupGe() {
        return predictedPickupGe;
    }

    public void setPredictedPickupGe(Integer predictedPickupGe) {
        this.predictedPickupGe = predictedPickupGe;
    }

    public Integer getPredictedPickupLt() {
        return predictedPickupLt;
    }

    public void setPredictedPickupLt(Integer predictedPickupLt) {
        this.predictedPickupLt = predictedPickupLt;
    }
}
