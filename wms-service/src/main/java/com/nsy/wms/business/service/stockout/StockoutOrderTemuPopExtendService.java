package com.nsy.wms.business.service.stockout;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopCreateLogisticsShipmentRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopGetLogisticsShipmentResultRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopGetShipmentDocumentRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopGetShippingServicesRequest;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopCreateLogisticsShipmentResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopGetLogisticsShipmentResultResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopGetShipmentDocumentResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopGetShippingServicesResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopWarehouseListResponse;
import com.nsy.api.wms.domain.product.ProductInfo;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.wms.business.manage.transfer.TransferApiService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.system.AliyunOssService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLabelEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuPopExtendEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderTemuPopExtendMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TemuPop半托管出库单扩展服务
 */
@Service
public class StockoutOrderTemuPopExtendService extends ServiceImpl<StockoutOrderTemuPopExtendMapper, StockoutOrderTemuPopExtendEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderTemuPopExtendService.class);

    @Resource
    private TransferApiService transferApiService;

    @Resource
    private StockoutOrderService stockoutOrderService;

    @Resource
    private StockoutOrderItemService stockoutOrderItemService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private LoginInfoService loginInfoService;

    @Resource
    private AliyunOssService aliyunOssService;

    @Resource
    private StockoutOrderLabelService stockoutOrderLabelService;

    /**
     * 根据出库单ID获取扩展信息
     */
    public StockoutOrderTemuPopExtendEntity getByStockoutOrderId(Integer stockoutOrderId) {
        return this.lambdaQuery()
                .eq(StockoutOrderTemuPopExtendEntity::getStockoutOrderId, stockoutOrderId)
                .one();
    }

    /**
     * 查询需要创建物流发货的出库单
     */
    public List<StockoutOrderTemuPopExtendEntity> listOrdersForCreateShipment() {
        return baseMapper.listOrdersForCreateShipment();
    }

    /**
     * 查询需要获取发货结果的出库单
     */
    public List<StockoutOrderTemuPopExtendEntity> listOrdersForGetShipmentResult() {
        return baseMapper.listOrdersForGetShipmentResult();
    }

    /**
     * 创建TemuPop扩展表记录
     */
    @Transactional
    public void createExtend(StockoutOrderEntity stockoutOrder) {
        // 获取出库单明细信息
        List<StockoutOrderItemEntity> orderItems = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        if (CollectionUtils.isEmpty(orderItems)) {
            LOGGER.warn("出库单明细为空，跳过创建TemuPop扩展表，出库单号: {}", stockoutOrder.getStockoutOrderNo());
            return;
        }

        // 创建扩展表记录
        StockoutOrderTemuPopExtendEntity extendEntity = new StockoutOrderTemuPopExtendEntity();
        extendEntity.setStockoutOrderId(stockoutOrder.getStockoutOrderId());
        extendEntity.setStockoutOrderNo(stockoutOrder.getStockoutOrderNo());
        extendEntity.setOrderNo(orderItems.get(0).getOrderNo()); // 取第一个订单号
        extendEntity.setCreateBy(stockoutOrder.getCreateBy());
        extendEntity.setUpdateBy(stockoutOrder.getUpdateBy());

        // 保存扩展表记录
        save(extendEntity);

        LOGGER.info("创建TemuPop扩展表成功，出库单号: {}, 订单号: {}",
                stockoutOrder.getStockoutOrderNo(), extendEntity.getOrderNo());
    }

    /**
     * 创建物流发货
     */
    @Transactional
    public void createShipment(StockoutOrderTemuPopExtendEntity extendEntity) {
        // 1. 获取出库单信息
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getById(extendEntity.getStockoutOrderId());
        if (stockoutOrder == null) {
            throw new BusinessServiceException("出库单不存在: " + extendEntity.getStockoutOrderId());
        }

        // 2. 获取zwin仓库ID
        String warehouseId = getZwinWarehouseId(stockoutOrder.getStoreId());

        // 3. 获取出库单明细信息
        List<StockoutOrderItemEntity> orderItems = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());

        // 4. 获取物流服务信息, 获取不到渠道，则更新出库单获取面单失败
        ShippingServiceInfo shippingInfo = getShippingServiceInfo(stockoutOrder.getStoreId(), warehouseId, stockoutOrder, orderItems);
        if (Objects.isNull(shippingInfo)) {
            stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrder, StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name());
            return;
        }


        // 5. 构建请求对象
        TemuPopCreateLogisticsShipmentRequest request = buildCreateShipmentRequest(stockoutOrder, warehouseId, shippingInfo, orderItems);

        // 6. 调用transfer API创建物流发货
        TemuPopCreateLogisticsShipmentResponse response = transferApiService.createTemuPopShipment(stockoutOrder.getStoreId(), request);

        // 7. 更新扩展表信息，使用实际返回的packageSn
        if (response.getPackageSnList() == null || response.getPackageSnList().isEmpty()) {
            throw new BusinessServiceException("创建物流发货成功，但API响应中未返回packageSn，无法继续后续流程");
        }
        String packageSn = response.getPackageSnList().get(0); // 取第一个包裹号
        LOGGER.info("获取到实际的packageSn: {}", packageSn);
        extendEntity.setPackageSn(packageSn);
        extendEntity.setLogisticsCompanyName(shippingInfo.getShippingCompanyName());
        extendEntity.setLogisticsCompanyId(shippingInfo.getShipCompanyId());
        extendEntity.setUpdateBy(loginInfoService.getName());
        updateById(extendEntity);


        LOGGER.info("创建物流发货成功，出库单号: {}, packageSn: {}",
                extendEntity.getStockoutOrderNo(), packageSn);
    }


    /**
     * 获取发货结果
     */
    @Transactional
    public void getShipmentResult(StockoutOrderTemuPopExtendEntity extendEntity) {
        // 1. 获取出库单信息
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getById(extendEntity.getStockoutOrderId());
        if (stockoutOrder == null) {
            throw new BusinessServiceException("出库单不存在: " + extendEntity.getStockoutOrderId());
        }

        // 2. 构建获取发货结果请求
        TemuPopGetLogisticsShipmentResultRequest request = new TemuPopGetLogisticsShipmentResultRequest();
        List<String> packageSnList = new ArrayList<>();
        packageSnList.add(extendEntity.getPackageSn());
        request.setPackageSnList(packageSnList);

        // 3. 调用transfer API获取发货结果
        TemuPopGetLogisticsShipmentResultResponse data =
                transferApiService.getTemuPopShipmentResult(stockoutOrder.getStoreId(), request);

        // 4. 验证响应数据
        if (CollectionUtils.isEmpty(data.getPackageInfoResultList())) {
            LOGGER.warn("获取发货结果响应为空，出库单号: {}", extendEntity.getStockoutOrderNo());
            return;
        }

        TemuPopGetLogisticsShipmentResultResponse.PackageInfoResult packageResult =
                data.getPackageInfoResultList().get(0);

        // 5. 先检查运输标签状态：1 - 成功
        if (packageResult.getShippingLabelStatus() == null || packageResult.getShippingLabelStatus() != 1) {
            Integer status = packageResult.getShippingLabelStatus();
            String failReason = packageResult.getFailReasonText();
            LOGGER.info("运输标签状态异常: {}, 失败原因: {}, 出库单号: {}",
                    status, failReason, extendEntity.getStockoutOrderNo());
            return;
        }

        // 6. 检查是否有物流单号，没有物流单号就返回
        if (!StringUtils.hasText(packageResult.getTrackingNumber())) {
            LOGGER.info("运输标签状态为成功，但没有物流单号，出库单号: {}", extendEntity.getStockoutOrderNo());
            return;
        }

        // 7. 保存物流单号到扩展表
        extendEntity.setLogisticsNo(packageResult.getTrackingNumber());
        LOGGER.info("保存物流单号到扩展表，出库单号: {}, 物流单号: {}",
                extendEntity.getStockoutOrderNo(), packageResult.getTrackingNumber());

        // 8. 获取面单文档
        String labelUrl = getShippingLabelUrl(stockoutOrder.getStoreId(), extendEntity.getPackageSn());
        if (StrUtil.isEmpty(labelUrl)) {
            LOGGER.warn("运输标签状态为成功，但未获取到面单URL，出库单号: {}", extendEntity.getStockoutOrderNo());
            return;
        }

        // 9. 下载面单并保存到stockout_order_label表
        downloadShippingLabel(extendEntity, stockoutOrder, labelUrl);

        LOGGER.info("获取发货结果成功，出库单号: {}, packageSn: {}",
                extendEntity.getStockoutOrderNo(), extendEntity.getPackageSn());
    }

    /**
     * 获取面单URL
     */
    private String getShippingLabelUrl(Integer storeId, String packageSn) {
        TemuPopGetShipmentDocumentRequest request = new TemuPopGetShipmentDocumentRequest();
        List<String> packageSnList = new ArrayList<>();
        packageSnList.add(packageSn);
        request.setPackageSnList(packageSnList);

        TemuPopGetShipmentDocumentResponse response =
                transferApiService.getTemuPopShipmentDocument(storeId, request);

        if (response.getShippingLabelUrlList() != null && !response.getShippingLabelUrlList().isEmpty()) {
            String url = response.getShippingLabelUrlList().get(0).getUrl();
            if (!StrUtil.isEmpty(url)) {
                return url;
            }
        }
        return null;
    }

    /**
     * 下载面单并上传到OSS，保存到stockout_order_label表
     */
    private void downloadShippingLabel(StockoutOrderTemuPopExtendEntity extendEntity, StockoutOrderEntity stockoutOrder, String labelUrl) {
        // 1. 下载面单数据
        byte[] labelData = transferApiService.executeTemuPopShippingLabelUrl(labelUrl);
        LOGGER.info("下载面单成功，出库单号: {}, 数据大小: {} bytes",
                extendEntity.getStockoutOrderNo(), labelData.length);

        // 2. 上传到OSS
        String fileName = String.format("temuPopLabel_%s_%s.pdf",
                extendEntity.getStockoutOrderNo(), extendEntity.getPackageSn());
        String ossUrl = aliyunOssService.putObject(IoUtil.toStream(labelData), "temuPopLabel", fileName);
        LOGGER.info("面单上传OSS成功，出库单号: {}, OSS地址: {}",
                extendEntity.getStockoutOrderNo(), ossUrl);

        // 3. 更新扩展表的面单URL
        extendEntity.setShippingLabelUrl(ossUrl);
        extendEntity.setUpdateBy(loginInfoService.getName());
        updateById(extendEntity);

        // 4. 保存面单到stockout_order_label表
        saveToStockoutOrderLabel(stockoutOrder, extendEntity, ossUrl);

        // 5. 更新出库单状态为待出库
        String oldStatus = stockoutOrder.getStatus();
        stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrder,
                StockoutOrderStatusEnum.READY_OUTBOUND.name());

        LOGGER.info("更新出库单状态成功，出库单号: {}, 状态: {} -> {}",
                extendEntity.getStockoutOrderNo(),
                oldStatus,
                StockoutOrderStatusEnum.READY_OUTBOUND.name());
    }

    /**
     * 保存面单到stockout_order_label表
     */
    private void saveToStockoutOrderLabel(StockoutOrderEntity stockoutOrder, StockoutOrderTemuPopExtendEntity extendEntity, String labelUrl) {
        // 验证必要参数
        if (!StringUtils.hasText(extendEntity.getLogisticsNo())) {
            LOGGER.warn("物流单号为空，跳过保存面单到stockout_order_label表，出库单号: {}",
                    extendEntity.getStockoutOrderNo());
            return;
        }

        StockoutOrderLabelEntity labelEntity = new StockoutOrderLabelEntity();
        labelEntity.setLocation(stockoutOrder.getLocation());
        labelEntity.setStockoutOrderId(stockoutOrder.getStockoutOrderId());
        labelEntity.setLabelUrl(labelUrl);
        labelEntity.setSource("system"); // 面单来源设置为system
        labelEntity.setLogisticsNo(extendEntity.getLogisticsNo()); // 使用扩展表中的物流单号
        labelEntity.setIsPrint(0); // 默认未打印
        labelEntity.setCreateBy(loginInfoService.getName());
        labelEntity.setUpdateBy(loginInfoService.getName());

        stockoutOrderLabelService.save(labelEntity);

        LOGGER.info("保存面单到stockout_order_label表成功，出库单号: {}, 物流单号: {}",
                extendEntity.getStockoutOrderNo(), extendEntity.getLogisticsNo());
    }

    /**
     * 构建创建物流发货请求
     */
    private TemuPopCreateLogisticsShipmentRequest buildCreateShipmentRequest(
            StockoutOrderEntity stockoutOrder,
            String warehouseId,
            ShippingServiceInfo shippingInfo,
            List<StockoutOrderItemEntity> orderItems) {

        TemuPopCreateLogisticsShipmentRequest request = new TemuPopCreateLogisticsShipmentRequest();
        request.setSendType(0); // 固定值
        request.setShipLater(Boolean.FALSE); // 立即发货，不延迟

        // 获取商品包装尺寸信息
        PackageDimensions packageDimensions = getPackageDimensions(orderItems);

        // 从出库单的estimateWeight获取重量
        double weightInLb = getWeightFromStockoutOrder(stockoutOrder);

        // 构建sendRequestList
        TemuPopCreateLogisticsShipmentRequest.SendRequest sendRequest = new TemuPopCreateLogisticsShipmentRequest.SendRequest();
        // TODO: 类型转换，可能需要Long类型
        sendRequest.setShipCompanyId(Long.valueOf(shippingInfo.getShipCompanyId()));
        sendRequest.setWarehouseId(warehouseId);
        sendRequest.setChannelId(Long.valueOf(shippingInfo.getChannelId()));

        // 设置重量和尺寸信息（重量从出库单获取，尺寸从ProductInfo获取）
        sendRequest.setWeight(String.valueOf(weightInLb));
        sendRequest.setWeightUnit("lb");
        sendRequest.setLength(String.valueOf(packageDimensions.getLengthInInch()));
        sendRequest.setWidth(String.valueOf(packageDimensions.getWidthInInch()));
        sendRequest.setHeight(String.valueOf(packageDimensions.getHeightInInch()));
        sendRequest.setDimensionUnit("in");

        // 构建订单信息列表
        List<TemuPopCreateLogisticsShipmentRequest.OrderSendInfo> orderSendInfoList = new ArrayList<>();
        Map<String, List<StockoutOrderItemEntity>> groupedItems = orderItems.stream()
                .collect(Collectors.groupingBy(StockoutOrderItemEntity::getOrderNo));

        groupedItems.forEach((orderNo, items) -> {
            for (StockoutOrderItemEntity item : items) {
                TemuPopCreateLogisticsShipmentRequest.OrderSendInfo orderSendInfo =
                        new TemuPopCreateLogisticsShipmentRequest.OrderSendInfo();
                orderSendInfo.setParentOrderSn(item.getOrderNo()); // parentOrderSn 是出库单明细的orderNo
                orderSendInfo.setOrderSn(item.getOrderItemId()); // orderSn是出库单明细的order_item_id
                orderSendInfo.setQuantity(item.getQty()); // 修正类型转换
                orderSendInfoList.add(orderSendInfo);
            }
        });

        sendRequest.setOrderSendInfoList(orderSendInfoList);

        List<TemuPopCreateLogisticsShipmentRequest.SendRequest> sendRequestList = new ArrayList<>();
        sendRequestList.add(sendRequest);
        request.setSendRequestList(sendRequestList);

        return request;
    }

    /**
     * 获取包装尺寸信息
     */
    private PackageDimensions getPackageDimensions(List<StockoutOrderItemEntity> orderItems) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return new PackageDimensions(); // 返回默认值
        }

        // 取第一个商品的包装信息作为参考
        StockoutOrderItemEntity firstItem = orderItems.get(0);
        try {
            ProductInfo productInfo = productInfoService.getByProductId(firstItem.getProductId());

            // 解析packageSize，格式类似 "10*10" 表示长*宽
            double length = 45.0; // 默认长度45cm
            double width = 60.0;  // 默认宽度60cm
            double height = 25.0; // 默认高度25cm

            // 解析packageSize
            if (!StringUtils.hasText(productInfo.getPackageSize())) {
                return new PackageDimensions(length, width, height);
            }

            String packageSize = productInfo.getPackageSize().trim();
            // 移除可能的"cm"后缀
            packageSize = packageSize.replace("cm", "").replace("CM", "");

            if (!packageSize.contains("*")) {
                return new PackageDimensions(length, width, height);
            }

            String[] dimensions = packageSize.split("\\*");
            if (dimensions.length != 2) {
                return new PackageDimensions(length, width, height);
            }

            length = Double.parseDouble(dimensions[0].trim());
            width = Double.parseDouble(dimensions[1].trim());
            LOGGER.info("从productInfo.packageSize解析得到尺寸: 长={}cm, 宽={}cm", length, width);

            // 解析packageHeight
            if (Objects.isNull(productInfo.getPackageHeight())) {
                return new PackageDimensions(length, width, height);
            }

            height = productInfo.getPackageHeight().doubleValue();
            LOGGER.info("从productInfo.packageHeight获得高度: {}cm", height);

            return new PackageDimensions(length, width, height);

        } catch (Exception e) {
            LOGGER.warn("获取商品包装信息失败，使用默认值: {}", e.getMessage());
            return new PackageDimensions(); // 返回默认值
        }
    }

    /**
     * 从出库单的estimateWeight获取重量，并转换为磅
     */
    private double getWeightFromStockoutOrder(StockoutOrderEntity stockoutOrder) {
        try {
            if (stockoutOrder.getEstimateWeight() != null) {
                // estimateWeight单位为千克，转换为磅
                // 1千克 = 2.20462磅
                double weightInKg = stockoutOrder.getEstimateWeight().doubleValue();
                double weightInLb = weightInKg * 2.20462;

                // 保留2位小数
                double roundedWeight = Math.round(weightInLb * 100.0) / 100.0;

                LOGGER.info("从出库单获取重量: {}千克 -> {}磅", weightInKg, roundedWeight);
                return roundedWeight;
            } else {
                // 如果没有预估重量，使用默认值0.53磅
                LOGGER.warn("出库单预估重量为空，使用默认重量: 0.53磅");
                return 0.53;
            }
        } catch (Exception e) {
            LOGGER.error("获取出库单重量失败，使用默认重量: {}", e.getMessage(), e);
            return 0.53;
        }
    }

    /**
     * 获取zwin仓库的warehouseId
     */
    private String getZwinWarehouseId(Integer storeId) {
        try {
            TemuPopWarehouseListResponse response = transferApiService.getTemuPopWarehouseList(storeId);

            // 查找warehouseName为"zwin"的仓库
            if (CollectionUtils.isEmpty(response.getWarehouseList())) {
                throw new BusinessServiceException("仓库列表为空");
            }

            for (TemuPopWarehouseListResponse.Warehouse warehouse : response.getWarehouseList()) {
                if ("zwin".equalsIgnoreCase(warehouse.getWarehouseName())) {
                    LOGGER.info("找到zwin仓库，warehouseId: {}, warehouseName: {}", warehouse.getWarehouseId(), warehouse.getWarehouseName());
                    return warehouse.getWarehouseId();
                }
            }
            throw new BusinessServiceException("未找到zwin仓库");

        } catch (Exception e) {
            LOGGER.error("获取zwin仓库ID失败，storeId: {}, 错误: {}", storeId, e.getMessage(), e);
            throw new BusinessServiceException("获取zwin仓库ID失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取物流服务信息
     */
    private ShippingServiceInfo getShippingServiceInfo(Integer storeId, String warehouseId,
                                                       StockoutOrderEntity stockoutOrder, List<StockoutOrderItemEntity> orderItems) {
        // 构建获取物流服务的请求参数
        TemuPopGetShippingServicesRequest request = buildShippingServicesRequest(warehouseId, stockoutOrder, orderItems);

        TemuPopGetShippingServicesResponse response = transferApiService.getTemuPopShippingServices(storeId, request);

        // 查找匹配的物流公司和渠道
        return findMatchingShippingService(response, stockoutOrder.getLogisticsCompany());
    }

    /**
     * 构建获取物流服务的请求参数（复用创建发货时的参数）
     */
    private TemuPopGetShippingServicesRequest buildShippingServicesRequest(
            String warehouseId, StockoutOrderEntity stockoutOrder, List<StockoutOrderItemEntity> orderItems) {

        TemuPopGetShippingServicesRequest request = new TemuPopGetShippingServicesRequest();
        request.setWarehouseId(warehouseId);

        // 构建订单号列表
        List<String> orderSnList = orderItems.stream()
                .map(StockoutOrderItemEntity::getOrderNo)
                .distinct()
                .collect(Collectors.toList());
        request.setOrderSnList(orderSnList);

        // 获取重量和尺寸信息（复用创建发货的逻辑）
        double weightInLb = getWeightFromStockoutOrder(stockoutOrder);
        PackageDimensions packageDimensions = getPackageDimensions(orderItems);

        request.setWeight(String.valueOf(weightInLb));
        request.setWeightUnit("lb");
        request.setLength(String.valueOf(packageDimensions.getLengthInInch()));
        request.setWidth(String.valueOf(packageDimensions.getWidthInInch()));
        request.setHeight(String.valueOf(packageDimensions.getHeightInInch()));
        request.setDimensionUnit("in");

        return request;
    }

    /**
     * 查找匹配的物流服务
     */
    private ShippingServiceInfo findMatchingShippingService(TemuPopGetShippingServicesResponse response, String stockoutLogisticsCompany) {

        // 匹配逻辑：检查返回的shippingCompanyName是否包含在出库单的物流公司中
        // 例如：返回"usps"，出库单物流公司"智运网-USPS-自提(泉州)"，则匹配成功
        if (CollectionUtils.isEmpty(response.getOnlineChannelDtoList())) {
            throw new BusinessServiceException("返回渠道为空");
        }

        for (TemuPopGetShippingServicesResponse.OnlineChannelDto service : response.getOnlineChannelDtoList()) {
            String shippingCompanyName = service.getShippingCompanyName();
            if (StrUtil.isNotEmpty(shippingCompanyName) && StrUtil.isNotEmpty(stockoutLogisticsCompany)
                    && stockoutLogisticsCompany.toLowerCase(Locale.ROOT).contains(shippingCompanyName.toLowerCase(Locale.ROOT))) {

                LOGGER.info("找到匹配的物流服务，API返回: {}, 出库单物流公司: {}, channelId: {}, shipCompanyId: {}",
                        shippingCompanyName, stockoutLogisticsCompany,
                        service.getChannelId(), service.getShipCompanyId());

                ShippingServiceInfo info = new ShippingServiceInfo();
                info.setShipCompanyId(String.valueOf(service.getShipCompanyId()));
                info.setChannelId(String.valueOf(service.getChannelId()));
                info.setShippingCompanyName(shippingCompanyName);
                return info;
            }
        }

        // 如果没有找到匹配的物流服务，记录日志并抛出异常
        LOGGER.error("未找到匹配的物流服务，出库单物流公司: {}, 可用服务列表: {}",
                stockoutLogisticsCompany,
                response.getOnlineChannelDtoList() != null
                        ? response.getOnlineChannelDtoList()
                        .stream()
                        .map(TemuPopGetShippingServicesResponse.OnlineChannelDto::getShippingCompanyName)
                        .collect(Collectors.toList()) : "空");

        return null;
    }

    /**
     * 物流服务信息内部类
     */
    private static class ShippingServiceInfo {
        private String shipCompanyId;
        private String channelId;
        private String shippingCompanyName;

        public String getShipCompanyId() {
            return shipCompanyId;
        }

        public void setShipCompanyId(String shipCompanyId) {
            this.shipCompanyId = shipCompanyId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getShippingCompanyName() {
            return shippingCompanyName;
        }

        public void setShippingCompanyName(String shippingCompanyName) {
            this.shippingCompanyName = shippingCompanyName;
        }
    }

    /**
     * 包装尺寸信息类
     */
    private static class PackageDimensions {
        private static final double CM_TO_INCH = 0.393701; // 1cm = 0.393701英寸

        private final double lengthCm;
        private final double widthCm;
        private final double heightCm;

        // 默认构造函数，使用默认值45*60*25cm
        PackageDimensions() {
            this(45.0, 60.0, 25.0);
        }

        PackageDimensions(double lengthCm, double widthCm, double heightCm) {
            this.lengthCm = lengthCm;
            this.widthCm = widthCm;
            this.heightCm = heightCm;
        }

        public double getLengthInInch() {
            return round(lengthCm * CM_TO_INCH, 2);
        }

        public double getWidthInInch() {
            return round(widthCm * CM_TO_INCH, 2);
        }

        public double getHeightInInch() {
            return round(heightCm * CM_TO_INCH, 2);
        }

        private double round(double value, int places) {
            BigDecimal bd = new BigDecimal(Double.toString(value));
            bd = bd.setScale(places, RoundingMode.HALF_UP);
            return bd.doubleValue();
        }
    }
} 