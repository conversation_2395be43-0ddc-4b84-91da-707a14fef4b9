package com.nsy.wms.business.service.stock;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemSourcePositionBo;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPrematchLackOriginTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPrematchTypeEnum;
import com.nsy.api.wms.request.stockout.PrematchCancelRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemCancelRequest;
import com.nsy.wms.business.domain.bo.stock.StockPrematchMinusBo;
import com.nsy.wms.business.service.bd.BdAreaCommonPositionService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutBatchLogService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderService;
import com.nsy.wms.business.service.stockout.StockoutBatchService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutPickingExceptionService;
import com.nsy.wms.business.service.stockout.StockoutTransparencyCodeService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutTransparencyCodeEntity;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockPrematchRemoveService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockPrematchRemoveService.class);
    @Resource
    StockoutBatchService batchService;
    @Resource
    StockPrematchInfoService prematchInfoService;
    @Resource
    StockoutBatchLogService stockoutBatchLogService;
    @Resource
    StockoutOrderService stockoutOrderService;
    @Resource
    StockoutOrderLogService stockoutOrderLogService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;
    @Resource
    BdAreaCommonPositionService bdAreaCommonPositionService;
    @Resource
    BdPositionService bdPositionService;
    @Resource
    LoginInfoService loginInfoService;
    @Autowired
    private StockoutTransparencyCodeService transparencyCodeService;
    @Resource
    StockoutBatchOrderService stockoutBatchOrderService;
    @Resource
    StockInternalBoxItemService stockInternalBoxItemService;
    @Resource
    StockoutPickingExceptionService stockoutPickingExceptionService;

    /**
     * 删除
     * 通过波次删除预配信息，根据缺货来源（分拣或者复核），分拣可以删，复核不能删
     * 记录日志
     *
     * @param batchId
     */
    public void removeByBatch(Integer batchId, StockoutBatchLogTypeEnum logType) {
        StockoutBatchEntity batch = batchService.getStockoutBatchById(batchId);
        List<StockoutBatchEntity> batchList = new ArrayList<>();
        batchList.add(batch);
        if (1 == batch.getIsMergeBatch()) {
            batchList.addAll(batchService.getSubBatch(batchId));
        }
        List<Integer> batchIdList = batchList.stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList());
        LOGGER.info("【通过波次删除预配信息】波次 {} ", batchIdList.stream().map(Objects::toString).collect(Collectors.joining(",")));
        prematchInfoService.remove(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .in(StockPrematchInfoEntity::getBatchId, batchIdList)
                .and(wrapper -> wrapper.isNull(StockPrematchInfoEntity::getLackOriginType).or().ne(StockPrematchInfoEntity::getLackOriginType, StockoutPrematchLackOriginTypeEnum.SCAN.name())));

        batchList.forEach(batchEntity -> {
            stockoutBatchLogService.addLog(batchEntity.getBatchId(), logType.getStockoutBatchLogType(), "清除预配");
        });

    }

    /**
     * 删除
     * 通过波次删除预配信息，根据缺货来源（分拣或者复核），删除复核
     * 记录日志
     *
     * @param stockoutOrderIdList
     */
    public void removeByStockoutOrder(List<Integer> stockoutOrderIdList, StockoutOrderLogTypeEnum logType) {
        if (CollectionUtils.isEmpty(stockoutOrderIdList)) {
            LOGGER.error("出库单号不存在");
            return;
        }
        prematchInfoService.remove(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .in(StockPrematchInfoEntity::getStockoutOrderId, stockoutOrderIdList));

        stockoutOrderIdList.forEach(stockoutOrderId -> {
            StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderId(stockoutOrderId);
            //透明计划变更为已使用
            transparencyCodeService.syncOmsTCode(stockoutOrder, -1);
            stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), logType, "清除预配");
        });
    }

    /**
     * 删除
     * 通过波次删除预配信息，根据缺货来源（分拣或者复核），删除复核
     * 记录日志
     *
     * @param stockoutOrderNoList
     */
    public void removeByStockoutOrderNoList(List<String> stockoutOrderNoList, StockoutOrderLogTypeEnum logType) {
        List<Integer> stockoutOrderIdList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList).stream()
                .map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList());

        removeByStockoutOrder(stockoutOrderIdList, logType);
    }


    /**
     * 缺货预占删除
     * 记录日志
     *
     * @param stockoutOrderNoList
     */
    public void removeLackByStockoutOrderNoList(List<String> stockoutOrderNoList, StockoutOrderLogTypeEnum logType) {

        List<Integer> stockoutOrderIdList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList).stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(stockoutOrderIdList)) {
            LOGGER.error("出库单号不存在");
            return;
        }

        prematchInfoService.remove(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .isNotNull(StockPrematchInfoEntity::getLackOriginType)
                .ne(StockPrematchInfoEntity::getLackOriginType, "")
                .in(StockPrematchInfoEntity::getStockoutOrderId, stockoutOrderIdList));

        stockoutOrderIdList.forEach(stockoutOrderId -> {
            StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderId(stockoutOrderId);
            stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), logType, "清除预配");
        });
    }

    /**
     * 加工缺货拣货完成删除预占
     * 记录日志
     *
     * @param batchId
     */
    public void removeProcessLackByBatchId(Integer batchId, Integer pickingTaskId) {

        prematchInfoService.remove(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getLackOriginType, StockoutPrematchLackOriginTypeEnum.PROCESS.name())
                .eq(StockPrematchInfoEntity::getBatchId, batchId));

        stockoutBatchLogService.addLog(batchId, StockoutBatchLogTypeEnum.PROCESS.getStockoutBatchLogType(), String.format("加工缺货拣货任务 %s 完成，清除预配", pickingTaskId));
    }

    /**
     * 生成缺货拣货任务前，需要将旧预占删除
     * 记录日志
     *
     * @param stockoutOrderId
     * @param skuList
     * @param lackOriginType
     */
    public void removeForCreateLackPickingTask(Integer stockoutOrderId, List<String> skuList, StockoutPrematchLackOriginTypeEnum lackOriginType) {
        if (skuList.isEmpty()) return;

        LOGGER.info("生成缺货拣货任务前， {} 清除旧预占 {} ", stockoutOrderId, StrUtil.join(",", skuList));

        List<StockPrematchInfoEntity> prematchInfoList = prematchInfoService.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getStockoutOrderId, stockoutOrderId)
                .in(StockPrematchInfoEntity::getSku, skuList));
        //非缺货的预占
        List<StockPrematchInfoEntity> filterPrematchInfoList = prematchInfoList.stream().filter(stockPrematchInfoEntity -> StrUtil.isEmpty(stockPrematchInfoEntity.getLackOriginType())).collect(Collectors.toList());

        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderId(stockoutOrderId);
        String msg = filterPrematchInfoList.stream().map(prematchInfo -> String.format("【%s】清 %s件 ", prematchInfo.getSku(), prematchInfo.getPrematchQty())).collect(Collectors.joining(";"));
        stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CANCEL_PREMATCH, String.format("【%s】生成缺货拣货任务，清预配 %s", lackOriginType.getName(), msg));
        prematchInfoService.removeByIds(filterPrematchInfoList.stream().map(StockPrematchInfoEntity::getId).collect(Collectors.toList()));
    }

    /**
     * 取消预配
     * <p>
     * 1. 删除预配
     * 2. 统一库位改回去
     * 3. 出库单状态修改
     *
     * @param stockoutOrderIdList
     */
    @Transactional
    public void cancelPrematch(List<Integer> stockoutOrderIdList) {
        if (CollectionUtils.isEmpty(stockoutOrderIdList))
            throw new BusinessServiceException("请选择出库单");
        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.listByIds(stockoutOrderIdList);
        stockoutOrderList.forEach(stockoutOrder -> {
            //【部分预配】 【准备中】 【待生成波次】可以进行取消
            if (!StockoutOrderStatusEnum.UN_FULL_PRE_MATCH.name().equals(stockoutOrder.getStatus())
                    && !StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(stockoutOrder.getStatus())
                    && !StockoutOrderStatusEnum.READY.name().equals(stockoutOrder.getStatus())
                    && !StockoutOrderStatusEnum.READY_WAVE_GENERATED.name().equals(stockoutOrder.getStatus())
                    && !StockoutOrderStatusEnum.TRANSPARENCY_APPLY.name().equals(stockoutOrder.getStatus())) {
                throw new BusinessServiceException(String.format("出库单 %s 非【部分预配】 【获取面单失败】 【准备中】【Tcode申请中】【待生成波次】不可以进行取消", stockoutOrder.getStockoutOrderNo()));
            }
        });
        //1. 删除预配
        removeByStockoutOrderNoList(stockoutOrderList.stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList()), StockoutOrderLogTypeEnum.CANCEL_PREMATCH);
        //2. 统一库位改回去
        Map<Integer, StockoutOrderEntity> stockoutOrderMap = stockoutOrderList.stream().collect(Collectors.toMap(StockoutOrderEntity::getStockoutOrderId, Function.identity()));
        List<StockoutOrderItemEntity> stockoutOrderItemList = stockoutOrderItemService.listByStockoutOrderIds(stockoutOrderIdList);
        Map<Integer, List<StockoutOrderItemEntity>> stockoutOrderItemMap = stockoutOrderItemList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getStockoutOrderId));
        stockoutOrderItemMap.forEach((stockoutOrderId, tempStockoutOrderItemList) -> {
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderMap.get(stockoutOrderId);
            List<String> commonPositionCodeList = bdAreaCommonPositionService.getSpaceCommonPositionCodeBySpaceId(stockoutOrderEntity.getSpaceId());
            tempStockoutOrderItemList.forEach(item -> {
                //原本就是统一库位，则跳过
                if (commonPositionCodeList.contains(item.getPositionCode()))
                    return;
                //库位不存在，跳过
                BdPositionEntity positionEntity = bdPositionService.getPositionByCode(item.getPositionCode());
                if (Objects.isNull(positionEntity)) {
                    LOGGER.error("库位 {} 不存在", item.getPositionCode());
                    return;
                }
                //统一库位不存在，跳过
                String commonPositionCode = bdAreaCommonPositionService.getCommonPositionCode(positionEntity.getAreaId());
                if (StrUtil.isEmpty(commonPositionCode)) {
                    LOGGER.error("统一库位 {} 不存在", positionEntity.getAreaId());
                    return;
                }
                stockoutOrderItemService.update(new LambdaUpdateWrapper<StockoutOrderItemEntity>()
                        .set(StockoutOrderItemEntity::getPositionCode, commonPositionCode)
                        .set(StockoutOrderItemEntity::getPositionId, null)
                        .set(StockoutOrderItemEntity::getSpaceAreaName, null)
                        .eq(StockoutOrderItemEntity::getStockoutOrderItemId, item.getStockoutOrderItemId()));
            });
        });
        //3. 出库单状态修改
        stockoutOrderList.forEach(stockoutOrder -> {
            stockoutOrder.setStatus(StockoutOrderStatusEnum.WAIT_PRE_MATCH.name());
            stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CANCEL_PREMATCH, "更改出库单到【待分配】");
        });
        stockoutOrderService.updateBatchById(stockoutOrderList);
        removeTransparencyCode(stockoutOrderList);
    }

    /**
     * 删除预配的T标，后续这段代码要废除掉 11.22
     */
    private void removeTransparencyCode(List<StockoutOrderEntity> stockoutOrderList) {
        stockoutOrderList.forEach(item -> {
            List<StockoutTransparencyCodeEntity> stockoutTransparencyCodeEntities = transparencyCodeService.findByStockouOrderNo(item.getStockoutOrderNo());
            if (CollectionUtils.isEmpty(stockoutTransparencyCodeEntities)) {
                return;
            }
            transparencyCodeService.syncOmsTCode(item, 0);
            LOGGER.info("取消预配，删除预占T标：{}", JsonMapper.toJson(stockoutTransparencyCodeEntities));
            stockoutTransparencyCodeEntities.forEach(code -> code.setIsDeleted(Boolean.TRUE));
            transparencyCodeService.updateBatchById(stockoutTransparencyCodeEntities);
        });

    }

    /**
     * 拣货删除预配库存
     *
     * @param taskEntity
     * @param taskItemEntity
     * @param qty
     */
    @Transactional
    public void pickRemovePrematch(StockoutPickingTaskEntity taskEntity, StockoutPickingTaskItemEntity
            taskItemEntity, Integer qty) {
        LOGGER.info(" {} , {} 拣货删预占【 {} 】【 {} 】 {} 件 ", taskEntity.getBatchId(), taskEntity.getTaskId(), taskItemEntity.getPositionCode(), taskItemEntity.getSku(), qty);

        List<StockPrematchInfoEntity> list = prematchInfoService.findByBatchIdSkuAndPositionCode(taskEntity.getBatchId(), taskItemEntity.getSku(), taskItemEntity.getPositionCode());
        if (CollectionUtils.isEmpty(list))
            return;
        LOGGER.info(" {} , {} 找到预占 {} 条", taskEntity.getBatchId(), taskEntity.getTaskId(), list.size());

        minusPrematch(list, qty);
    }

    /**
     * 扣减预占统一方法
     *
     * @param prematchInfoList
     * @param qty
     * @return
     */
    public List<StockPrematchMinusBo> minusPrematch(List<StockPrematchInfoEntity> prematchInfoList, Integer qty) {
        int minusQty = qty;
        List<Integer> delList = new ArrayList<>();
        List<StockPrematchMinusBo> result = new ArrayList<>();
        for (StockPrematchInfoEntity prematchInfoEntity : prematchInfoList) {
            Integer prematchQty = prematchInfoEntity.getPrematchQty();
            if (prematchQty > minusQty) {
                StockPrematchInfoEntity entity = new StockPrematchInfoEntity();
                entity.setUpdateBy(loginInfoService.getName());
                entity.setId(prematchInfoEntity.getId());
                entity.setPrematchQty(prematchQty - minusQty);
                prematchInfoService.updateById(entity);
                result.add(buildStockPrematchMinusBo(prematchInfoEntity, -minusQty));
                minusQty = 0;
            } else {
                result.add(buildStockPrematchMinusBo(prematchInfoEntity, -prematchQty));
                minusQty -= prematchQty;
                delList.add(prematchInfoEntity.getId());
            }
            if (minusQty <= 0)
                break;
        }
        if (!CollectionUtils.isEmpty(delList))
            prematchInfoService.removeByIds(delList);

        return result;
    }

    private StockPrematchMinusBo buildStockPrematchMinusBo(StockPrematchInfoEntity prematchInfo, Integer qty) {
        StockPrematchMinusBo bo = new StockPrematchMinusBo();
        bo.setPrematchId(prematchInfo.getId());
        bo.setBatchId(prematchInfo.getBatchId());
        bo.setPositionCode(prematchInfo.getPositionCode());
        bo.setQty(qty);
        bo.setSku(prematchInfo.getSku());
        return bo;
    }

    /**
     * 出库质检补货，清除预配
     *
     * @param stockoutOrderId
     * @param stockoutOrderItemIds
     */
    @Transactional
    public void removeByStockoutOrderItem(Integer stockoutOrderId, List<Integer> stockoutOrderItemIds) {

        List<StockPrematchInfoEntity> prematchInfoList = prematchInfoService.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getStockoutOrderId, stockoutOrderId)
                .in(StockPrematchInfoEntity::getStockoutOrderItemId, stockoutOrderItemIds));

        LOGGER.info("生成质检补货任务前， {} 清除旧预占 {} ", stockoutOrderId, JsonMapper.toJson(prematchInfoList));

        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderId(stockoutOrderId);
        String msg = prematchInfoList.stream().map(prematchInfo -> String.format("【%s】清 %s件 ", prematchInfo.getSku(), prematchInfo.getPrematchQty())).collect(Collectors.joining(";"));
        stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CANCEL_PREMATCH, String.format("生成质检补货拣货任务，清预配 %s", msg));
        prematchInfoService.removeByIds(prematchInfoList.stream().map(StockPrematchInfoEntity::getId).collect(Collectors.toList()));
    }

    /**
     * 出库单-取消明细数量-通过出库单明细ID
     * 1.已装箱 - 订单数不能大于订单数-装箱数
     * 2.未预配  未完全预配无预配信息 - 直接增加取消数
     * 3.未拣货 - 删除预配信息
     * 4.已拣货 - 归还原库位
     * 5.增加出库单明细取消数
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "stockoutOrderItemCancel", lockKey = "#request.stockoutOrderItemId")
    public void stockoutOrderItemCancel(StockoutOrderItemCancelRequest request) {
        StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getByStockoutOrderItemId(request.getStockoutOrderItemId());
        if (Objects.isNull(stockoutOrderItemEntity))
            throw new BusinessServiceException("找不到出库单明细！");
        if (request.getCancelQty() > stockoutOrderItemEntity.getQty())
            throw new BusinessServiceException("取消数不能大于订单数！");

        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(stockoutOrderItemEntity.getStockoutOrderId());
        //增加的取消数
        Integer addQty = request.getCancelQty() - stockoutOrderItemEntity.getCancelQty();
        if (addQty == 0)
            return;
        if (addQty < 0)
            throw new BusinessServiceException("不支持减少取消数！");
        //1.已装箱 - 订单数不能大于订单数 - 装箱数
        if (stockoutOrderItemEntity.getShipmentQty() > 0 && request.getCancelQty() + stockoutOrderItemEntity.getShipmentQty() > stockoutOrderItemEntity.getQty())
            throw new BusinessServiceException(String.format("已装箱%s件，当前只允许取消%s件", stockoutOrderItemEntity.getShipmentQty(), stockoutOrderItemEntity.getQty() - stockoutOrderItemEntity.getShipmentQty()));

        List<StockPrematchInfoEntity> list = prematchInfoService.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getStockoutOrderItemId, stockoutOrderItemEntity.getStockoutOrderItemId()));
        int prematchQty = list.stream().mapToInt(StockPrematchInfoEntity::getPrematchQty).sum();
        //2.未预配  未完全预配无预配信息 - 直接增加取消数
        if (prematchQty == 0 && StockoutOrderStatusEnum.valueOf(stockoutOrderEntity.getStatus()).getIndex() <= StockoutOrderStatusEnum.UN_FULL_PRE_MATCH.getIndex()) {
            String logContent = String.format("%s未预配，增加取消数%s，总共取消%s件", stockoutOrderItemEntity.getSku(), addQty, request.getCancelQty());
            addOrderItemCancelQty(stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderItemEntity, addQty, logContent);
            return;
        }
        // 3.已预配，未拣货 - 删除预配信息
        Integer cancelPrematchQty = 0;
        if (prematchQty > 0 && StockoutOrderStatusEnum.valueOf(stockoutOrderEntity.getStatus()).getIndex() > StockoutOrderStatusEnum.UN_FULL_PRE_MATCH.getIndex()) {
            cancelPrematchQty = this.cancelPrematchQty(list, addQty);
        }

        // 4.已拣货 - 归还原库位
        Integer returnPositionQty = 0;
        if (addQty > cancelPrematchQty) {
            returnPositionQty = this.returnPositionQty(stockoutOrderEntity, stockoutOrderItemEntity, addQty - cancelPrematchQty);
        }

        //5.增加出库单明细取消数
        String logContent = String.format("%s，增加取消数%s，%s%s总共取消%s件", stockoutOrderItemEntity.getSku(), addQty,
                cancelPrematchQty > 0 ? String.format("删除预配%s件，", cancelPrematchQty) : "",
                returnPositionQty > 0 ? String.format("归还拣货箱%s件，", returnPositionQty) : "",
                request.getCancelQty());
        addOrderItemCancelQty(stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderItemEntity, addQty, logContent);

    }

    private Integer returnPositionQty(StockoutOrderEntity stockoutOrderEntity, StockoutOrderItemEntity stockoutOrderItemEntity, int qty) {
        StockoutBatchOrderEntity batchOrderEntity = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        StockoutBatchEntity stockoutBatchEntity = batchService.getById(batchOrderEntity.getBatchId());
        List<Integer> batchIds = new LinkedList<>();
        batchIds.add(batchOrderEntity.getBatchId());
        if (stockoutBatchEntity.getMergeBatchId() != null)
            batchIds.add(stockoutBatchEntity.getMergeBatchId());
        List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemListByBatchIdsAndSku(batchIds, StockInternalBoxTypeEnum.PICKING_BOX.name(), stockoutOrderItemEntity.getSku());
        if (CollectionUtils.isEmpty(internalBoxItemEntityList))
            return 0;
        //先排序，来源库位与出库单明细库位一致的排在前面
        internalBoxItemEntityList.sort(Comparator.comparing(item -> {
            if (StringUtils.hasText(item.getSourcePositionCode()) && item.getSourcePositionCode().equals(stockoutOrderItemEntity.getPositionCode()))
                return 0;
            return 1;
        }));
        int returnQty = qty;
        List<StockInternalBoxItemSourcePositionBo> sourceList = new LinkedList<>();
        for (StockInternalBoxItemEntity internalBoxItemEntity : internalBoxItemEntityList) {
            if (returnQty <= 0) {
                break;
            }
            int minusQty;
            if (internalBoxItemEntity.getQty() < returnQty) {
                minusQty = internalBoxItemEntity.getQty();
                returnQty -= minusQty;
            } else {
                minusQty = returnQty;
                returnQty = 0;
            }
            stockInternalBoxItemService.minusStockInternalBoxItemQty(internalBoxItemEntity, minusQty, StockChangeLogTypeEnum.SPLIT, StockChangeLogTypeModuleEnum.PINGKING, null);
            sourceList.add(new StockInternalBoxItemSourcePositionBo(internalBoxItemEntity.getSourceAreaId(), internalBoxItemEntity.getSourcePositionCode(), internalBoxItemEntity.getSku(), minusQty, internalBoxItemEntity.getInternalBoxCode()));
        }
        if (!CollectionUtils.isEmpty(sourceList))
            stockoutPickingExceptionService.addSourcePositionStock(sourceList, batchIds.get(0), StockChangeLogTypeEnum.CANCEL_STOCKOUT_ORDER_ITEM, stockoutOrderEntity.getStockoutOrderNo());

        return sourceList.stream().mapToInt(StockInternalBoxItemSourcePositionBo::getQty).sum();
    }

    private Integer cancelPrematchQty(List<StockPrematchInfoEntity> list, Integer addQty) {
        int qty = addQty;
        for (StockPrematchInfoEntity prematchInfoEntity : list) {
            if (qty <= 0)
                break;
            Integer prematchQty = prematchInfoEntity.getPrematchQty();
            if (prematchQty <= qty) {
                prematchInfoService.removeById(prematchInfoEntity.getId());
                qty -= prematchQty;
            } else {
                StockPrematchInfoEntity stockPrematchInfoEntity = new StockPrematchInfoEntity();
                stockPrematchInfoEntity.setId(prematchInfoEntity.getId());
                stockPrematchInfoEntity.setPrematchQty(prematchInfoEntity.getPrematchQty() - qty);
                stockPrematchInfoEntity.setUpdateBy(loginInfoService.getName());
                prematchInfoService.updateById(stockPrematchInfoEntity);
                qty = 0;
            }
        }


        return addQty - qty;
    }

    /**
     * 增加出库单明细取消数，并记录日志
     *
     * @param stockoutOrderNo
     * @param stockoutOrderItemEntity
     * @param addQty
     * @param logContent
     */
    private void addOrderItemCancelQty(String stockoutOrderNo, StockoutOrderItemEntity stockoutOrderItemEntity, Integer addQty, String logContent) {
        StockoutOrderItemEntity orderItemEntity = new StockoutOrderItemEntity();
        orderItemEntity.setStockoutOrderItemId(stockoutOrderItemEntity.getStockoutOrderItemId());
        orderItemEntity.setCancelQty(stockoutOrderItemEntity.getCancelQty() + addQty);
        orderItemEntity.setUpdateBy(loginInfoService.getName());
        stockoutOrderItemService.updateById(orderItemEntity);

        stockoutOrderLogService.addLog(stockoutOrderNo, StockoutOrderLogTypeEnum.ADD_CANCEL_QTY, logContent);

    }


    /**
     * 出库单-取消明细数量-预配表主键ID
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "stockoutOrderItemCancelByPrematchId", lockKey = "#request.prematchId")
    public void stockoutOrderItemCancelByPrematchId(PrematchCancelRequest.PrematchCancelItem request) {
        StockPrematchInfoEntity stockPrematchInfoEntity = prematchInfoService.getById(request.getPrematchId());
        if (Objects.isNull(stockPrematchInfoEntity))
            throw new BusinessServiceException("未找到预配信息，请刷新页面！");
        Integer cancelQty = request.getCancelQty();
        if (cancelQty < 0)
            throw new BusinessServiceException("取消数不可小于0！");
        if (cancelQty > stockPrematchInfoEntity.getPrematchQty())
            throw new BusinessServiceException("取消数不可大于预配数！");
        if (cancelQty == 0)
            return;
        StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getByStockoutOrderItemId(stockPrematchInfoEntity.getStockoutOrderItemId());
        if (stockoutOrderItemEntity.getCancelQty() + cancelQty > stockoutOrderItemEntity.getQty())
            throw new BusinessServiceException("取消数不能大于订单数！");

        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(stockoutOrderItemEntity.getStockoutOrderId());
        Integer cancelPrematchQty = this.cancelPrematchQty(Lists.newArrayList(stockPrematchInfoEntity), cancelQty);
        //增加出库单明细取消数
        String logContent = String.format("%s，增加取消数%s，%s总共取消%s件", stockoutOrderItemEntity.getSku(), cancelQty,
                cancelPrematchQty > 0 ? String.format("删除预配%s件，", cancelPrematchQty) : "",
                stockoutOrderItemEntity.getCancelQty() + cancelQty);
        addOrderItemCancelQty(stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderItemEntity, cancelQty, logContent);

    }

    /**
     * 通过调拨任务删除
     *
     * @param transferTaskId
     */
    public void removeByTransferTaskId(Integer transferTaskId) {
        List<StockPrematchInfoEntity> prematchInfoList = prematchInfoService.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getPrematchType, StockoutPrematchTypeEnum.TRANSFER.name())
                .eq(StockPrematchInfoEntity::getTransferTaskId, transferTaskId));

        LOGGER.info("通过调拨任务删除预占， {} 清除旧预占 {} ", transferTaskId, JsonMapper.toJson(prematchInfoList));
        prematchInfoService.removeByIds(prematchInfoList.stream().map(StockPrematchInfoEntity::getId).collect(Collectors.toList()));
    }

    /**
     * 通过调拨任务 sku 库位删除
     *
     * @param transferTaskId
     * @param sku
     * @param positionCode
     */
    public void removeByTransferTaskIdAndSkuAndPositionCode(Integer transferTaskId, String sku, String positionCode) {
        List<StockPrematchInfoEntity> prematchInfoList = prematchInfoService.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getPrematchType, StockoutPrematchTypeEnum.TRANSFER.name())
                .eq(StockPrematchInfoEntity::getTransferTaskId, transferTaskId)
                .eq(StockPrematchInfoEntity::getSku, sku)
                .eq(StockPrematchInfoEntity::getPositionCode, positionCode));

        LOGGER.info("通过调拨任务删除SKU和库位上的预占， {} 清除旧预占 {} ", transferTaskId, JsonMapper.toJson(prematchInfoList));
        prematchInfoService.removeByIds(prematchInfoList.stream().map(StockPrematchInfoEntity::getId).collect(Collectors.toList()));
    }

    /**
     * 通过调拨任务 sku 库位删除
     *
     * @param transferTaskId
     * @param sku
     * @param positionCode
     */
    public void subtractByTransferTaskIdAndSkuAndPositionCode(Integer transferTaskId, String sku, String positionCode, Integer qty) {
        List<StockPrematchInfoEntity> prematchInfoList = prematchInfoService.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getPrematchType, StockoutPrematchTypeEnum.TRANSFER.name())
                .eq(StockPrematchInfoEntity::getTransferTaskId, transferTaskId)
                .eq(StockPrematchInfoEntity::getSku, sku)
                .eq(StockPrematchInfoEntity::getPositionCode, positionCode));
        int totalPrematchQty = prematchInfoList.stream().mapToInt(StockPrematchInfoEntity::getPrematchQty).sum();
        if (totalPrematchQty < qty) {
            throw new BusinessServiceException(String.format("%s 再库位 %s 上的预配数量不足 %s", sku, positionCode, totalPrematchQty));
        }
        int needQty = qty;
        List<StockPrematchInfoEntity> updatePrematchInfo = new ArrayList<>();
        List<StockPrematchInfoEntity> removePrematchInfo = new ArrayList<>();

        for (StockPrematchInfoEntity prematchInfo : prematchInfoList) {
            int useQty = Math.min(qty, prematchInfo.getPrematchQty());
            prematchInfo.setPrematchQty(prematchInfo.getPrematchQty() - useQty);
            if (prematchInfo.getPrematchQty() <= 0)
                removePrematchInfo.add(prematchInfo);
            else
                updatePrematchInfo.add(prematchInfo);
            needQty -= useQty;
            if (needQty <= 0)
                break;
        }
        prematchInfoService.updateBatchById(updatePrematchInfo);

        prematchInfoService.removeByIds(removePrematchInfo.stream().map(StockPrematchInfoEntity::getId).collect(Collectors.toList()));
    }
}
