package com.nsy.wms.business.service.stockin.facade;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.domain.stockin.StockinReturnProductItem;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.request.stockin.StockinReturnProductAddRequest;
import com.nsy.api.wms.response.external.QcPurchaseCount;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.supplier.enumeration.InboundsRequiredCheckCheckStatusEnum;
import com.nsy.wms.business.manage.supplier.enumeration.InboundsRequiredCheckPushTypeEnum;
import com.nsy.wms.business.manage.supplier.request.InboundsRequiredCheck;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinQcBuildService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinQcFacade {


    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    StockinQcBuildService stockinQcBuildService;
    @Autowired
    StockPlatformScheduleService stockPlatformScheduleService;

    /**
     * 更新内部箱明细和入库单明细的状态
     *
     * @param stockinOrderEntityList
     * @param stockInternalBoxItemEntityList
     * @param internalBoxCode
     * @param sku
     * @param statusEnum
     */
    public void updateBoxItemStatus(List<StockinOrderEntity> stockinOrderEntityList, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList, String internalBoxCode, String sku, StockinOrderItemStatusEnum statusEnum) {
        //更新内部箱和入库单明细状态
        Map<String, StockinOrderEntity> collect = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderNo, Function.identity()));
        List<StockinOrderItemEntity> orderItemEntityList = new ArrayList<>(stockInternalBoxItemEntityList.size());
        List<StockInternalBoxItemEntity> internalBoxItemEntities = stockInternalBoxItemEntityList.stream().map(item -> {
            StockinOrderEntity stockinOrderEntity = collect.get(item.getStockInOrderNo());
            StockinOrderItemEntity stockinOrderItemEntity = stockinOrderItemService.findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndPurchasePlanNo(stockinOrderEntity.getStockinOrderId(), sku, internalBoxCode, item.getPurchasePlanNo(), "");
            StockInternalBoxItemEntity itemEntity = new StockInternalBoxItemEntity();
            itemEntity.setInternalBoxItemId(item.getInternalBoxItemId());
            itemEntity.setStatus(statusEnum.name());
            itemEntity.setUpdateBy(loginInfoService.getName());
            StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
            orderItemEntity.setStockinOrderItemId(stockinOrderItemEntity.getStockinOrderItemId());
            orderItemEntity.setStatus(statusEnum.name());
            orderItemEntity.setUpdateBy(loginInfoService.getName());
            orderItemEntityList.add(orderItemEntity);
            return itemEntity;
        }).collect(Collectors.toList());
        stockinOrderItemService.updateBatchById(orderItemEntityList);
        stockInternalBoxItemService.updateBatchById(internalBoxItemEntities);
        stockInternalBoxService.updateInternalBoxStatusByItem(internalBoxCode);
    }


    public StockInternalBoxItemEntity createBoxItem(QcInboundsMessage messageContent, String stockinOrderNo, StockinOrderItemEntity orderItemEntity) {
        StockInternalBox internalBox = stockInternalBoxService.getInternalBox(messageContent.getInternalBoxNo());
        StockInternalBoxItemEntity entity = new StockInternalBoxItemEntity();
        entity.setProductId(orderItemEntity.getProductId());
        entity.setQty(0);
        entity.setSku(orderItemEntity.getSku());
        entity.setSellerSku(orderItemEntity.getSellerSku());
        entity.setSellerBarcode(orderItemEntity.getSellerBarcode());
        entity.setInternalBoxCode(internalBox.getInternalBoxCode());
        entity.setInternalBoxId(internalBox.getInternalBoxId());
        entity.setLocation(internalBox.getLocation());
        entity.setSpaceId(internalBox.getSpaceId());
        entity.setSpecId(orderItemEntity.getSpecId());
        entity.setCreateBy(loginInfoService.getName());
        entity.setStockInOrderNo(stockinOrderNo);
        entity.setPurchasePlanNo(orderItemEntity.getPurchasePlanNo());
        entity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
        return entity;
    }


    @Nullable
    public InboundsRequiredCheck getInboundsRequiredCheck(StockInternalBoxEntity boxEntity, StockinOrderTaskEntity taskEntity, Map<String, List<StockinOrderTaskItemEntity>> collect, Map<String, ProductSpecInfoEntity> specMap, StockInternalBoxItemEntity itemEntity) {
        InboundsRequiredCheck inboundsRequiredCheck = new InboundsRequiredCheck();
        inboundsRequiredCheck.setBoxBarcode(itemEntity.getInternalBoxCode());
        inboundsRequiredCheck.setCheckStatus(InboundsRequiredCheckCheckStatusEnum.TO_BE_INSPECTED.getDesc());
        List<StockinOrderTaskItemEntity> taskItemEntityList = collect.get(itemEntity.getSku());
        ProductSpecInfoEntity specInfoEntity = specMap.get(itemEntity.getSku());
        //如果为空可能是 入库收货多收了非入库任务下的sku
        if (CollectionUtils.isEmpty(taskItemEntityList))
            return null;
        if (taskItemEntityList.get(0).getIsNeedQa().equals(0))
            return null;
        inboundsRequiredCheck.setLocation(boxEntity.getLocation());
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames()) && item.getLabelAttributeNames().contains(StringConstant.FIRST_ORDER_LABEL_OEM)))
            inboundsRequiredCheck.setFirstLabel(StringConstant.FIRST_ORDER_LABEL_OEM);
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames()) && item.getLabelAttributeNames().contains(StringConstant.FIRST_ORDER_LABEL_ODM)))
            inboundsRequiredCheck.setFirstLabel(StringConstant.FIRST_ORDER_LABEL_ODM);
        inboundsRequiredCheck.setBrandName(taskItemEntityList.stream().map(StockinOrderTaskItemEntity::getBrandName).distinct().filter(brandName -> StringUtils.hasText(brandName)).findAny().orElse(null));
        inboundsRequiredCheck.setIsNew(taskItemEntityList.stream().anyMatch(item -> StringConstant.FIRST_ORDER_LABEL.equals(item.getFirstOrderLabel())
            || StringUtils.hasText(item.getLabelAttributeNames()) && item.getLabelAttributeNames().contains("首单")) ? 1 : 0);
        inboundsRequiredCheck.setPurchasingApplyType(taskItemEntityList.get(0).getPurchasingApplyType());
        inboundsRequiredCheck.setIsAllCheck(taskItemEntityList.stream().anyMatch(item -> item.getIsPreQaQualified().equals(3)) ? 1 : 0);
        Integer needQty = taskItemEntityList.get(0).getIsNeedQa().equals(1) ? itemEntity.getQty() : 0;
        if (Objects.nonNull(taskEntity.getPlatformScheduleId())) {
            StockPlatformScheduleEntity stockPlatformScheduleEntity = stockPlatformScheduleService.getById(taskEntity.getPlatformScheduleId());
            needQty = taskItemEntityList.get(0).getIsNeedQa().equals(1) ? StockInternalBoxTypeEnum.QA_BOX.name().equals(boxEntity.getInternalBoxType()) ? itemEntity.getQty() : stockinQcBuildService.getQaQty(itemEntity.getSpaceId(), stockPlatformScheduleEntity.getSupplierId(), itemEntity.getQty()) : 0;
        }
        inboundsRequiredCheck.setDemandQcCount(needQty);
        inboundsRequiredCheck.setInboundsDate(new Date());
        inboundsRequiredCheck.setNeedHeight(Objects.isNull(specInfoEntity.getPackageHeight()) ? 1 : 0);
        boolean isNeedWeight = Objects.isNull(specInfoEntity.getActualWeight()) || specInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0;
        inboundsRequiredCheck.setNeedWeight(isNeedWeight ? 1 : 0);
        inboundsRequiredCheck.setProductId(specInfoEntity.getProductId());
        inboundsRequiredCheck.setProductInboundsCount(itemEntity.getQty());
        inboundsRequiredCheck.setSkc(specInfoEntity.getSkc());
        inboundsRequiredCheck.setProductSku(specInfoEntity.getSku());
        inboundsRequiredCheck.setPurchaseNumber(itemEntity.getPurchasePlanNo());
        inboundsRequiredCheck.setPushType(InboundsRequiredCheckPushTypeEnum.INBOUNDS.getDesc());
        inboundsRequiredCheck.setReceiveOrder(taskEntity.getSupplierDeliveryBoxCode());
        inboundsRequiredCheck.setSku(itemEntity.getSku());
        return inboundsRequiredCheck;
    }

    public List<QcPurchaseCount> getPurchaseCount(List<Integer> stockinOrderIds, String internalBoxCode, String sku, String purchasePlanNo) {
        return stockinOrderItemService.getBaseMapper().getPurchaseCount(stockinOrderIds, internalBoxCode, sku, purchasePlanNo);
/*        List<QcPurchaseCount> packageList = list.stream().filter(item -> StringUtils.hasText(item.getPackageName())).collect(Collectors.toList());
        List<PurchasePackageInfoDto> purchasePackageInfo = new ArrayList<>();
        if (!CollectionUtils.isEmpty(packageList)) {
            GetPurchasePackageInfoRequest request = new GetPurchasePackageInfoRequest();
            List<GetPurchasePackageInfoRequest.GetPurchasePackageInfoItem> collect = packageList.stream().map(item -> {
                GetPurchasePackageInfoRequest.GetPurchasePackageInfoItem getPurchasePackageInfoItem = new GetPurchasePackageInfoRequest.GetPurchasePackageInfoItem();
                getPurchasePackageInfoItem.setSku(item.getSku());
                getPurchasePackageInfoItem.setPurchasePlanNo(item.getPurchasePlanNo());
                return getPurchasePackageInfoItem;
            }).distinct().collect(Collectors.toList());
            request.setQueryList(collect);
            purchasePackageInfo = scmApiService.getPurchasePackageInfo(request);
        }
        if (CollectionUtils.isEmpty(purchasePackageInfo))
            return list;
        for (QcPurchaseCount item : list) {
            purchasePackageInfo.stream().filter(info -> item.getPurchasePlanNo().equals(info.getPurchasePlanNo()) && item.getSku().equals(info.getSku()))
                    .findAny().ifPresent(any -> item.setPackageName(any.getPackageName()));
        }*/
    }

    public void validConcessionsCount(List<StockinOrderItemEntity> stockinOrderItemEntityList, Integer addReturnQty) {
        int concessionsCount = stockinOrderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getConcessionsCount).sum();
        if (concessionsCount > 0) {
            int qty = stockinOrderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum();
            int returnQty = stockinOrderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getReturnQty).sum();
            if (qty - returnQty - concessionsCount < addReturnQty)
                throw new BusinessServiceException(String.format("箱中还有【%s】%s件,存在让步接收数%s件，无法退货!", stockinOrderItemEntityList.get(0).getSku(), qty - returnQty, concessionsCount));
        }
    }

    public List<StockinReturnProductAddRequest> buildStockinReturnProductAddRequest(QcInboundsMessage messageContent, List<StockinReturnProductItem> collect) {
        List<StockinReturnProductAddRequest> requests = new ArrayList<>();
        collect.stream().collect(Collectors.groupingBy(StockinReturnProductItem::getStockinOrderNo)).forEach((stockinOrderNo, itemList) ->
            itemList.stream().collect(Collectors.groupingBy(StockinReturnProductItem::getPurchasePlanNo))
                .forEach((purchasePlanNo, itemList1) -> {
                    StockinReturnProductAddRequest request = new StockinReturnProductAddRequest();
                    request.setInternalBoxCode(messageContent.getBoxBarcode());
                    request.setReturnQty(itemList1.stream().mapToInt(StockinReturnProductItem::getReturnQty).sum());
                    request.setSku(messageContent.getSku());
                    request.setSupplierDeliveryNo(itemList1.get(0).getSupplierDeliveryNo());
                    request.setSupplierId(messageContent.getSupplierId());
                    request.setSupplierName(messageContent.getSupplierName());
                    request.setOperator(messageContent.getQcUserName());
                    request.setPurchasePlanNo(purchasePlanNo);
                    request.setIsQcRoutingInspect(messageContent.getIsQcRoutingInspect());
                    request.setStockinOrderNo(stockinOrderNo);
                    request.setUnqualifiedReason(messageContent.getUnqualifiedReason());
                    request.setUnqualifiedCategory(messageContent.getUnqualifiedCategory());
                    request.setAsyncErp(messageContent.getAsyncErp());
                    request.setQcInboundsId(messageContent.getQcInboundsId());
                    request.setQaType(messageContent.getQaType());
                    requests.add(request);
                })
        );
        return requests;
    }
}
