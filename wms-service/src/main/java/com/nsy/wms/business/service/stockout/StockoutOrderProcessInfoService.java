package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.ProcessConstant;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemSourcePositionBo;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.domain.stockout.WithdrawalDto;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.ProcessCustomTypeEnum;
import com.nsy.api.wms.enumeration.stockout.ProcessOperateTypeEnum;
import com.nsy.api.wms.enumeration.stockout.ProcessTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockout.ProcessBatchCompleteRequest;
import com.nsy.api.wms.request.stockout.ProcessCompleteItemRequest;
import com.nsy.api.wms.request.stockout.ProcessCompleteRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderProcessElementImageInfo;
import com.nsy.api.wms.request.stockout.StockoutOrderProcessImageInfo;
import com.nsy.api.wms.request.stockout.WithdrawalRequest;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.erp.ErpTransferApiService;
import com.nsy.wms.business.manage.erp.request.process.ErpProcessOperateInfo;
import com.nsy.wms.business.manage.erp.request.process.ErpProcessOperateRequest;
import com.nsy.wms.business.manage.scm.request.PlanDTO;
import com.nsy.wms.business.manage.scm.request.PlanItemDTO;
import com.nsy.wms.business.service.bd.BdAreaCommonPositionService;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockCenterCommonService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stockout.building.StockoutOrderProcessInfoBuild;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemProcessInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderProcessImageInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderProcessInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderProcessInfoMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockoutOrderProcessInfoService extends ServiceImpl<StockoutOrderProcessInfoMapper, StockoutOrderProcessInfoEntity> {

    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderItemProcessInfoService itemProcessInfoService;
    @Autowired
    StockoutOrderProcessImageInfoService processImageInfoService;
    @Autowired
    ProductSpecInfoService specInfoService;
    @Autowired
    StockoutPickingTaskService taskService;
    @Autowired
    StockoutPickingTaskItemService taskItemService;
    @Autowired
    StockoutBatchService batchService;
    @Autowired
    StockService stockService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockoutBatchOrderItemService batchOrderItemService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockInternalBoxItemMapper stockInternalBoxItemMapper;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    StockoutBatchSplitTaskItemService splitTaskItemService;
    @Autowired
    StockoutOrderScanTaskItemService scanTaskItemService;
    @Autowired
    BdAreaCommonPositionService bdAreaCommonPositionService;
    @Autowired
    ErpTransferApiService erpTransferApiService;
    @Autowired
    StockCenterCommonService stockCenterCommonService;
    @Autowired
    BdErpSpaceMappingService bdErpSpaceMappingService;
    @Autowired
    StockoutPickingExceptionService stockoutPickingExceptionService;
    @Autowired
    StockoutOrderProcessInfoBuild stockoutOrderProcessInfoBuild;

    /**
     * 出库单加工信息
     */
    public void addByStockoutOrderInfo(StockoutOrderInfo stockoutOrderInfo, StockoutOrderEntity stockoutOrderEntity) {
        StockoutOrderProcessInfoEntity processInfoEntity = new StockoutOrderProcessInfoEntity();
        processInfoEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        processInfoEntity.setErpPickId(stockoutOrderEntity.getErpPickId());
        processInfoEntity.setProcessType(stockoutOrderInfo.getProcessType());
        processInfoEntity.setStoreOwner(stockoutOrderInfo.getStoreOwner());
        processInfoEntity.setCreateBy(stockoutOrderEntity.getCreateBy());
        this.save(processInfoEntity);
    }

    /**
     * 波次下，出库单加工信息
     */
    public List<StockoutOrderProcessInfoEntity> getListByBatchId(StockoutBatchEntity batchEntity) {
        List<Integer> stockoutOrderIds = batchOrderService.getBatchOrderList(batchEntity)
                .stream().map(StockoutBatchOrderEntity::getStockoutOrderId).collect(Collectors.toList());
        return this.list(new QueryWrapper<StockoutOrderProcessInfoEntity>().lambda().in(StockoutOrderProcessInfoEntity::getStockoutOrderId, stockoutOrderIds));
    }

    /**
     * 撤货
     * 胚款撤货：拣货数 - 加工完成数   ---> 【拣货箱】胚款 -  【撤货箱】胚款 +
     * 定制款撤货：加工合格数  --->   【拣货箱】胚款 -  【加工发货库位】定制款 +  【加工发货库位】定制款 - 【撤货箱】定制款+
     * 次品撤货：加工次品数  ---> 【拣货箱】胚款 -  【加工次品库位】胚款 +
     */
    @Transactional
    public void withdrawal(WithdrawalRequest request) {
        StockoutBatchEntity batchEntity = batchService.getById(request.getList().get(0).getBatchId());
        if (Objects.isNull(batchEntity)) {
            throw new BusinessServiceException(String.format("波次号：%s不存在", request.getList().get(0).getBatchId()));
        }
        List<ErpProcessOperateInfo> processOperateInfoList = new LinkedList<>();
        request.getList().forEach(item -> {
            ProductSpecInfoEntity baseSpec = specInfoService.findTopBySku(item.getBaseSku());
            if (Objects.isNull(baseSpec))
                throw new BusinessServiceException(String.format("%s商品条形码不存在", item.getBaseSku()));
            ProductSpecInfoEntity customerSpec = specInfoService.findTopBySku(item.getCustomerSku());
            if (Objects.isNull(customerSpec))
                throw new BusinessServiceException(String.format("%s商品条形码不存在", item.getCustomerSku()));
            ProductInfoEntity customerProd = productInfoService.findTopByProductId(customerSpec.getProductId());
            if (Objects.isNull(customerProd))
                throw new BusinessServiceException(String.format("%s商品id不存在", customerSpec.getProductId()));

            List<StockInternalBoxItemEntity> pickingBoxItemList = stockInternalBoxItemMapper.findListByTypeBatchIdAndSku(StockInternalBoxTypeEnum.PICKING_BOX.name(), item.getBatchId(), item.getBaseSku());
            if (!pickingBoxItemList.isEmpty()) {
                //胚款撤货  【撤货箱】胚款 +
                if (item.getType() == 0) {
                    //扣减拣货箱数据  【拣货箱】胚款 -
                    List<StockInternalBoxItemSourcePositionBo> itemSourceBoList = stockInternalBoxItemService.minusStockInternalBoxItemQty(pickingBoxItemList, Math.min(item.getQty(), pickingBoxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum()), StockChangeLogTypeEnum.PROCESS_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
                    baseWithdrawal(itemSourceBoList, batchEntity);
                } else if (item.getType() == 1) { // 【加工发货库位】定制款 +  【加工发货库位】定制款 - 【撤货箱】定制款+
                    //扣减拣货箱数据  【拣货箱】胚款 -
                    List<StockInternalBoxItemSourcePositionBo> sourcePositionBoList = stockInternalBoxItemService.minusStockInternalBoxItemQty(pickingBoxItemList, Math.min(item.getQty(), pickingBoxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum()), StockChangeLogTypeEnum.PROCESS_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
                    customWithdrawal(item, batchEntity);
                    // 构建撤货推送erp request  并同步商通
                    processOperateInfoList.addAll(buildByWithdrawalDto(item, baseSpec, customerSpec, customerProd, sourcePositionBoList));
                } else if (item.getType() == 2) {  //【加工次品库位】胚款 +
                    //扣减拣货箱数据  【拣货箱】胚款 -
                    List<StockInternalBoxItemSourcePositionBo> sourcePositionBoList = stockInternalBoxItemService.minusStockInternalBoxItemQty(pickingBoxItemList, Math.min(item.getQty(), pickingBoxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum()), StockChangeLogTypeEnum.PROCESS_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
                    ProcessCompleteItemRequest itemRequest = new ProcessCompleteItemRequest();
                    itemRequest.setBaseSku(item.getBaseSku());
                    itemRequest.setBadQty(item.getQty());
                    wmsProcessBadQty(Collections.singletonList(itemRequest), batchEntity);
                    //同步商通 拣货箱原库位->次品仓
                    stockoutOrderProcessInfoBuild.buildUpdateErpRequest(sourcePositionBoList).forEach(updateRequest -> erpTransferApiService.updateStockSpaceStock(updateRequest));
                }
            }
        });
        if (!processOperateInfoList.isEmpty()) {
            Map<String, List<ErpProcessOperateInfo>> collect = processOperateInfoList.stream().collect(Collectors.groupingBy(ErpProcessOperateInfo::getPositionCode));
            collect.forEach((key, value) -> {
                ErpProcessOperateRequest operateRequest = new ErpProcessOperateRequest();
                operateRequest.setBatchId(batchEntity.getBatchId());
                operateRequest.setOperateType(ProcessOperateTypeEnum.PROCESS_OUT_WITHDRAW.getType());
                operateRequest.setUserName(loginInfoService.getName());
                operateRequest.setVirtualPositionCode(key);
                operateRequest.setItemList(value);
                LocationWrapperMessage<ErpProcessOperateRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), operateRequest);
                messageProducer.sendMessage(KafkaConstant.SYNC_PROCESS_OPERATE_BUSINESS_MARK, KafkaConstant.SYNC_PROCESS_OPERATE_TOPIC, message);
            });
        }
    }

    /**
     * 胚款撤货：拣货数 - 加工完成数   ---> 【拣货箱】胚款 -  【撤货箱】胚款 +
     *
     * @param itemSourceBoList
     * @param batchEntity
     */
    private void baseWithdrawal(List<StockInternalBoxItemSourcePositionBo> itemSourceBoList, StockoutBatchEntity batchEntity) {
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getWithdrawalByWorkspace(batchEntity.getWorkspace(), batchEntity.getSpaceId());
        if (!stockInternalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.PACKING.name())) {
            stockInternalBoxService.changeStockInternalBoxStatus(stockInternalBoxEntity.getInternalBoxCode(), StockInternalBoxStatusEnum.PACKING.name());
        }
        itemSourceBoList.forEach(sourceBo -> {
            LambdaQueryWrapper<StockInternalBoxItemEntity> stockInternalBoxItemEntityLambdaQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                    .eq(StockInternalBoxItemEntity::getInternalBoxCode, stockInternalBoxEntity.getInternalBoxCode())
                    .eq(StockInternalBoxItemEntity::getSku, sourceBo.getSku())
                    .eq(StockInternalBoxItemEntity::getSourcePositionCode, sourceBo.getSourcePositionCode())
                    .eq(StockInternalBoxItemEntity::getIsProcess, 0)
                    .eq(StockInternalBoxItemEntity::getBatchId, batchEntity.getBatchId())
                    .last("limit 1");
            StockInternalBoxItemEntity stockInternalBoxItemEntity = stockInternalBoxItemService.getOne(stockInternalBoxItemEntityLambdaQueryWrapper);
            if (Objects.isNull(stockInternalBoxItemEntity)) {
                ProductSpecInfoEntity specInfoEntity = specInfoService.findTopBySku(sourceBo.getSku());
                stockInternalBoxItemEntity = new StockInternalBoxItemEntity();
                stockInternalBoxItemEntity.setSpaceId(stockInternalBoxEntity.getSpaceId());
                stockInternalBoxItemEntity.setInternalBoxId(stockInternalBoxEntity.getInternalBoxId());
                stockInternalBoxItemEntity.setInternalBoxCode(stockInternalBoxEntity.getInternalBoxCode());
                stockInternalBoxItemEntity.setProductId(specInfoEntity.getProductId());
                stockInternalBoxItemEntity.setSpecId(specInfoEntity.getSpecId());
                stockInternalBoxItemEntity.setSku(specInfoEntity.getSku());
                stockInternalBoxItemEntity.setBatchId(batchEntity.getBatchId());
                stockInternalBoxItemEntity.setIsProcess(0);
                stockInternalBoxItemEntity.setSourcePositionCode(sourceBo.getSourcePositionCode());
                stockInternalBoxItemEntity.setSourceAreaId(bdPositionService.getPositionByCode(sourceBo.getSourcePositionCode()).getAreaId());
                stockInternalBoxItemEntity.setQty(0);
            }
            stockInternalBoxItemService.addStockInternalBoxItemQty(stockInternalBoxItemEntity, sourceBo.getQty(), StockChangeLogTypeEnum.PROCESS_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
        });
    }

    /**
     * 定制款撤货：加工合格数  --->   【拣货箱】胚款 -  【加工发货库位】定制款 +  【加工发货库位】定制款 - 【撤货箱】定制款+
     *
     * @param item
     * @param batchEntity
     */
    private void customWithdrawal(WithdrawalDto item, StockoutBatchEntity batchEntity) {
        // 虚拟库位定制款+
        virtualPositionStockProcess(item.getCustomerSku(), item.getQty(), batchEntity.getBatchId());
        // 虚拟库位定制款-
        virtualPositionStockProcess(item.getCustomerSku(), -item.getQty(), batchEntity.getBatchId());

        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getWithdrawalByWorkspace(batchEntity.getWorkspace(), batchEntity.getSpaceId());
        if (!stockInternalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.PACKING.name())) {
            stockInternalBoxService.changeStockInternalBoxStatus(stockInternalBoxEntity.getInternalBoxCode(), StockInternalBoxStatusEnum.PACKING.name());
        }

        LambdaQueryWrapper<StockInternalBoxItemEntity> stockInternalBoxItemEntityLambdaQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, stockInternalBoxEntity.getInternalBoxCode())
                .eq(StockInternalBoxItemEntity::getSku, item.getCustomerSku())
                .eq(StockInternalBoxItemEntity::getSourcePositionCode, ProcessConstant.AFTER_PROCESS_POSITION_CODE)
                .eq(StockInternalBoxItemEntity::getIsProcess, 1)
                .eq(StockInternalBoxItemEntity::getBatchId, batchEntity.getBatchId())
                .last("limit 1");
        StockInternalBoxItemEntity stockInternalBoxItemEntity = stockInternalBoxItemService.getOne(stockInternalBoxItemEntityLambdaQueryWrapper);
        if (Objects.isNull(stockInternalBoxItemEntity)) {
            ProductSpecInfoEntity specInfoEntity = specInfoService.findTopBySku(item.getCustomerSku());
            stockInternalBoxItemEntity = new StockInternalBoxItemEntity();
            stockInternalBoxItemEntity.setSpaceId(stockInternalBoxEntity.getSpaceId());
            stockInternalBoxItemEntity.setInternalBoxId(stockInternalBoxEntity.getInternalBoxId());
            stockInternalBoxItemEntity.setInternalBoxCode(stockInternalBoxEntity.getInternalBoxCode());
            stockInternalBoxItemEntity.setProductId(specInfoEntity.getProductId());
            stockInternalBoxItemEntity.setSpecId(specInfoEntity.getSpecId());
            stockInternalBoxItemEntity.setSku(specInfoEntity.getSku());
            stockInternalBoxItemEntity.setBatchId(batchEntity.getBatchId());
            stockInternalBoxItemEntity.setIsProcess(1);
            stockInternalBoxItemEntity.setSourcePositionCode(ProcessConstant.AFTER_PROCESS_POSITION_CODE);
            stockInternalBoxItemEntity.setSourceAreaId(bdPositionService.getPositionByCode(ProcessConstant.AFTER_PROCESS_POSITION_CODE).getAreaId());
            stockInternalBoxItemEntity.setQty(0);
        }
        stockInternalBoxItemService.addStockInternalBoxItemQty(stockInternalBoxItemEntity, item.getQty(), StockChangeLogTypeEnum.PROCESS_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);

    }

    private List<ErpProcessOperateInfo> buildByWithdrawalDto(WithdrawalDto item, ProductSpecInfoEntity baseSpec, ProductSpecInfoEntity customerSpec, ProductInfoEntity customerProd, List<StockInternalBoxItemSourcePositionBo> sourcePositionBoList) {
        List<StockInternalBoxItemSourcePositionBo> collect = sourcePositionBoList.stream().filter(bo -> StringUtils.hasText(bo.getSourceInternalBoxCode())).collect(Collectors.toList());
        collect.forEach(bo -> bo.setSourcePositionCode(stockCenterCommonService.changePositionCode(bo.getSourcePositionCode())));
        StockoutOrderItemEntity itemEntity = stockoutOrderItemService.getOne(new LambdaQueryWrapper<StockoutOrderItemEntity>()
                .select(StockoutOrderItemEntity::getErpPickItemId)
                .eq(StockoutOrderItemEntity::getStockoutOrderItemId, item.getStockoutOrderItemId()));
        return collect.stream().collect(Collectors.groupingBy(StockInternalBoxItemSourcePositionBo::getSourcePositionCode))
                .entrySet().stream().map(entry -> {
                    ErpProcessOperateInfo processOperateInfo = new ErpProcessOperateInfo();
                    processOperateInfo.setPositionCode(entry.getKey());
                    processOperateInfo.setBaseSku(item.getBaseSku());
                    processOperateInfo.setBaseSpecId(baseSpec.getErpSpecId());
                    processOperateInfo.setCustomerSku(item.getCustomerSku());
                    processOperateInfo.setCustomerSpecId(customerSpec.getErpSpecId());
                    processOperateInfo.setCustomerProductId(customerProd.getErpProductId());
                    int sum = entry.getValue().stream().mapToInt(StockInternalBoxItemSourcePositionBo::getQty).sum();
                    processOperateInfo.setQty(sum);
                    processOperateInfo.setTempQty(sum);
                    processOperateInfo.setOrderItemId(item.getOrderItemId());
                    processOperateInfo.setErpPickItemId(itemEntity.getErpPickItemId());
                    return processOperateInfo;
                }).collect(Collectors.toList());
    }

    /**
     * 加工流程完成
     * step1.反馈完成数、次品数
     * step2.整个波次拣货加工完成；备货加工 | 订单加工； 处理缺货拣货任务
     * step3.wms次品处理
     * step4.通知erp加工完成处理 + 次品处理
     */
    @Transactional
    public void processComplete(ProcessCompleteRequest request) {
        StockoutBatchEntity batchEntity = batchService.getById(request.getWmsBatchId());
        if (batchEntity == null)
            throw new BusinessServiceException(String.format("未找到波次：【%s】", request.getWmsBatchId()));
        List<StockoutPickingTaskEntity> pickingTaskEntityList = taskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .eq(StockoutPickingTaskEntity::getBatchId, request.getWmsBatchId())
                .eq(StockoutPickingTaskEntity::getTaskType, StockoutPickingTaskTypeEnum.PROCESSING_PICKING.name()));
        if (!pickingTaskEntityList.isEmpty()) {
            List<Integer> allTaskIds = pickingTaskEntityList.stream().map(StockoutPickingTaskEntity::getTaskId).distinct().collect(Collectors.toList());
            List<StockoutPickingTaskItemEntity> allPickingTaskItems = taskItemService.findAllByTaskIdIn(allTaskIds);
            for (ProcessCompleteItemRequest itemRequest : request.getItemList()) {
                List<StockoutPickingTaskItemEntity> currentItems = allPickingTaskItems.stream().filter(o -> o.getSku().equals(itemRequest.getBaseSku())
                        && o.getCustomerSku().equals(itemRequest.getCustomSku())).collect(Collectors.toList());
                // step1.反馈完成数、次品数 回填拣货任务明细
                updatePickingTaskInfo(currentItems, itemRequest.getCustomSku(), itemRequest.getProcessQty(), itemRequest.getBadQty());
                // 反馈 分拣明细 | 复核明细 加工合格数
                splitTaskItemService.updateProcessQty(itemRequest, request.getOperator());
                scanTaskItemService.updateProcessQty(itemRequest, request.getOperator());
            }
        }
        // 加工类型 0 订单加工 1 备货加工
        Integer processType = getListByBatchId(batchEntity).get(0).getProcessType();
        // step2.当前拣货加工完成；备货加工 | 订单加工 ；
        if (processType.equals(1)) {
            stockProcess(batchEntity, request);
        } else {
            orderProcess(batchEntity, request);
        }
        // step3.wms次品处理
        List<ProcessCompleteItemRequest> badList = request.getItemList().stream().filter(o -> o.getBadQty() > 0).collect(Collectors.toList());
        if (!badList.isEmpty()) {
            wmsProcessBadQty(badList, batchEntity);
        }
        // step4.通知erp加工完成处理 + 次品处理
        ErpProcessOperateRequest operateRequest = new ErpProcessOperateRequest();
        operateRequest.setBatchId(batchEntity.getBatchId());
        operateRequest.setOperateType(processType.equals(1) ? ProcessOperateTypeEnum.STOCK_UP_SHELVE.getType() : ProcessOperateTypeEnum.PROCESS_OUT.getType());
        operateRequest.setVirtualPositionCode(bdAreaCommonPositionService.getSpaceCommonPositionCode(batchEntity.getSpaceId()));
        if (!StringUtils.hasText(operateRequest.getVirtualPositionCode())) {
            operateRequest.setVirtualPositionCode(ProcessConstant.AFTER_PROCESS_POSITION_CODE);
        }
        operateRequest.setUserName(request.getOperator());
        operateRequest.setItemList(getByProcessCompleteItemRequest(request.getItemList()));
        LocationWrapperMessage<ErpProcessOperateRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), operateRequest);
        messageProducer.sendMessage(KafkaConstant.SYNC_PROCESS_OPERATE_BUSINESS_MARK, KafkaConstant.SYNC_PROCESS_OPERATE_TOPIC, message);

        // 波次完成
        if (request.getAllComplete()) {
            ProcessBatchCompleteRequest batchCompleteRequest = new ProcessBatchCompleteRequest(request.getWmsBatchId(), request.getOperator());
            processBatchComplete(batchCompleteRequest);
        }
    }

    /**
     * 加工波次完成
     */
    @Transactional
    public void processBatchComplete(ProcessBatchCompleteRequest request) {
        StockoutBatchEntity batchEntity = batchService.getById(request.getWmsBatchId());
        Integer processType = getListByBatchId(batchEntity).get(0).getProcessType();
        // step2.当前拣货加工完成；备货加工 | 订单加工 ；
        if (processType.equals(1)) {
            stockProcessAllComplete(batchEntity, request.getOperator());
        }
        // 全部结束 处理缺货拣货任务
        lackPickingTaskProcess(batchEntity);
    }

    private List<ErpProcessOperateInfo> getByProcessCompleteItemRequest(List<ProcessCompleteItemRequest> itemRequestList) {
        List<ErpProcessOperateInfo> processOperateInfoList = new LinkedList<>();
        Map<Integer, Integer> collect = stockoutOrderItemService.list(new LambdaQueryWrapper<StockoutOrderItemEntity>()
                        .select(StockoutOrderItemEntity::getErpPickItemId, StockoutOrderItemEntity::getStockoutOrderItemId)
                        .in(StockoutOrderItemEntity::getStockoutOrderItemId, itemRequestList.stream().map(ProcessCompleteItemRequest::getStockoutOrderItemId).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(StockoutOrderItemEntity::getStockoutOrderItemId, StockoutOrderItemEntity::getErpPickItemId));
        for (ProcessCompleteItemRequest itemRequest : itemRequestList) {
            ProductSpecInfoEntity baseSpec = specInfoService.findTopBySku(itemRequest.getBaseSku());
            if (Objects.isNull(baseSpec)) {
                throw new BusinessServiceException(String.format("%s商品条形码不存在", itemRequest.getBaseSku()));
            }
            ProductSpecInfoEntity customerSpec = specInfoService.findTopBySku(itemRequest.getCustomSku());
            if (Objects.isNull(customerSpec)) {
                throw new BusinessServiceException(String.format("%s商品条形码不存在", itemRequest.getCustomSku()));
            }
            ProductInfoEntity customerProd = productInfoService.findTopByProductId(customerSpec.getProductId());
            if (Objects.isNull(customerProd)) {
                throw new BusinessServiceException(String.format("%s商品id不存在", customerSpec.getProductId()));
            }
            ErpProcessOperateInfo processOperateInfo = new ErpProcessOperateInfo();
            processOperateInfo.setBaseSku(itemRequest.getBaseSku());
            processOperateInfo.setBaseSpecId(baseSpec.getErpSpecId());
            processOperateInfo.setCustomerSku(itemRequest.getCustomSku());
            processOperateInfo.setCustomerSpecId(customerSpec.getErpSpecId());
            processOperateInfo.setCustomerProductId(customerProd.getErpProductId());
            processOperateInfo.setOrderItemId(itemRequest.getOrderItemId());
            processOperateInfo.setQty(itemRequest.getProcessQty() - itemRequest.getBadQty());
            processOperateInfo.setTempQty(itemRequest.getProcessQty() - itemRequest.getBadQty());
            processOperateInfo.setBadQty(itemRequest.getBadQty());
            processOperateInfo.setErpPickItemId(collect.get(itemRequest.getStockoutOrderItemId()));
            processOperateInfoList.add(processOperateInfo);
        }
        return processOperateInfoList;
    }

    /**
     * 回填拣货明细、更新拣货任务
     *
     * @param currentItems       拣货明细
     * @param customerSku        定制款sku
     * @param processCompleteQty 加工完成数
     * @param processBadQty      加工次品数
     */
    private void updatePickingTaskInfo(List<StockoutPickingTaskItemEntity> currentItems, String customerSku, Integer processCompleteQty, Integer processBadQty) {
        if (currentItems.isEmpty())
            return;
        Integer leftCompleteQty = processCompleteQty;
        Integer leftBadQty = processBadQty;
        for (StockoutPickingTaskItemEntity taskItemEntity : currentItems) {
            if (leftCompleteQty <= 0 && leftBadQty <= 0) {
                continue;
            }
            int changeCompleteQty = taskItemEntity.getPickedQty() < (leftCompleteQty + taskItemEntity.getProcessCompleteQty())
                    ? taskItemEntity.getPickedQty() - taskItemEntity.getProcessCompleteQty()
                    : leftCompleteQty;
            // 加工完成数 不能大于 拣货数
            if (changeCompleteQty + taskItemEntity.getProcessCompleteQty() > taskItemEntity.getPickedQty()) {
                continue;
            }
            taskItemEntity.setProcessCompleteQty(changeCompleteQty + taskItemEntity.getProcessCompleteQty());
            leftCompleteQty = leftCompleteQty - changeCompleteQty;

            int changeBadQty = taskItemEntity.getPickedQty() < (leftBadQty + taskItemEntity.getProcessBadQty())
                    ? taskItemEntity.getPickedQty() - taskItemEntity.getProcessBadQty()
                    : leftBadQty;
            taskItemEntity.setProcessBadQty(changeBadQty + taskItemEntity.getProcessBadQty());
            leftBadQty = leftBadQty - changeBadQty;

            taskItemEntity.setCustomerSku(customerSku);
            taskItemEntity.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
        }
        taskItemService.updateBatchById(currentItems);
    }

    /**
     * 加工缺货拣货任务处理
     */
    private void lackPickingTaskProcess(StockoutBatchEntity batchEntity) {
        List<StockoutPickingTaskEntity> lackTaskList = taskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .eq(StockoutPickingTaskEntity::getBatchId, batchEntity.getBatchId())
                .eq(StockoutPickingTaskEntity::getTaskType, StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name()));
        if (lackTaskList.isEmpty())
            return;
        List<String> pickingBoxCodes = lackTaskList.stream().map(StockoutPickingTaskEntity::getReceiveBoxNo).distinct().collect(Collectors.toList());
        List<StockInternalBoxItemEntity> pickingBoxItems = stockInternalBoxItemService.list(new QueryWrapper<StockInternalBoxItemEntity>().lambda().in(StockInternalBoxItemEntity::getInternalBoxCode, pickingBoxCodes));
        List<Integer> lackTaskIds = lackTaskList.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
        List<StockoutPickingTaskItemEntity> lackTaskItemList = taskItemService.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda().in(StockoutPickingTaskItemEntity::getTaskId, lackTaskIds));
        for (StockoutPickingTaskEntity task : lackTaskList) {
            List<StockoutPickingTaskItemEntity> taskItems = lackTaskItemList.stream().filter(o -> o.getTaskId().equals(task.getTaskId())).collect(Collectors.toList());
            for (StockoutPickingTaskItemEntity taskItem : taskItems) {
                StockInternalBoxItemEntity internalBoxItemEntity = pickingBoxItems.stream().filter(o -> o.getInternalBoxCode().equals(task.getReceiveBoxNo()) && o.getSku().equals(taskItem.getSku()))
                        .findFirst().orElse(null);
                if (internalBoxItemEntity == null)
                    continue;
                stockoutPickingExceptionService.returnSourcePosition(Collections.singletonList(internalBoxItemEntity), StockChangeLogTypeEnum.PROCESS_RECEIVE, "加工波次完结");
            }
        }
    }

    /**
     * 订单加工
     * 2.1.拣货箱处理
     * 2.2.虚拟库位定制款+
     * 2.3.回填分拣|复核任务
     */
    private void orderProcess(StockoutBatchEntity batchEntity, ProcessCompleteRequest request) {
        // 2.1.拣货箱处理
        for (ProcessCompleteItemRequest itemRequest : request.getItemList()) {
            List<StockInternalBoxItemEntity> internalBoxItemEntities = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemList(batchEntity.getBatchId(), itemRequest.getBaseSku(), StockInternalBoxTypeEnum.PICKING_BOX.name());
            int total = internalBoxItemEntities.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
            // 1.1.拣货箱处理
            if (total < itemRequest.getProcessQty())
                throw new BusinessServiceException("请确认胚款是否拣货完成");
            stockInternalBoxItemService.minusStockInternalBoxItemQty(internalBoxItemEntities, itemRequest.getProcessQty(), StockChangeLogTypeEnum.PROCESS_RECEIVE, StockChangeLogTypeModuleEnum.STOCK_IN, null);
            int customQty = itemRequest.getProcessQty() - itemRequest.getBadQty();
            if (customQty <= 0) continue;
            //2.2.虚拟库位定制款+
            virtualPositionStockProcess(itemRequest.getCustomSku(), customQty, batchEntity.getBatchId());
        }
        // 2.3.回填分拣|复核任务
        batchService.pickingComplete(batchEntity.getBatchId());
    }

    /**
     * 备货加工
     * 1.1.拣货箱处理
     * 1.2.虚拟库位定制款+
     * 1.3.虚拟库位定制款-
     * 1.4.撤货箱定制款+
     * 1.5.波次完成，出库单完成
     * 1.6 通知erp备货单完成
     */
    private void stockProcess(StockoutBatchEntity batchEntity, ProcessCompleteRequest request) {
        List<StockInternalBoxItemEntity> internalBoxItemList = stockInternalBoxItemService.getByBatchIdAndInternalBoxType(batchEntity.getBatchId(), StockInternalBoxTypeEnum.PICKING_BOX.name());
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getStockInternalBoxByInternalBoxCode(ProcessConstant.STOCK_WITHDRAWAL_BOX_CODE);

        for (ProcessCompleteItemRequest itemRequest : request.getItemList()) {
            List<StockInternalBoxItemEntity> filterInternalBoxItemList = internalBoxItemList.stream()
                    .filter(o -> o.getSku().equals(itemRequest.getBaseSku()) && o.getQty() > 0).collect(Collectors.toList());
            int total = filterInternalBoxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
            // 1.1.拣货箱处理
            if (total < itemRequest.getProcessQty())
                throw new BusinessServiceException(String.format("%s 请确认胚款是否拣货完成", itemRequest.getBaseSku()));
            stockInternalBoxItemService.minusStockInternalBoxItemQty(filterInternalBoxItemList, itemRequest.getProcessQty(), StockChangeLogTypeEnum.PROCESS_PICKING, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
            int eligibleQty = itemRequest.getProcessQty() - itemRequest.getBadQty();
            // 1.2.虚拟库位定制款+
            virtualPositionStockProcess(itemRequest.getCustomSku(), eligibleQty, batchEntity.getBatchId());
            // 1.3.虚拟库位定制款-
            virtualPositionStockProcess(itemRequest.getCustomSku(), -eligibleQty, batchEntity.getBatchId());
            // 1.4.撤货箱定制款+

            // 撤货箱状态改为装箱中
            if (!stockInternalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.PACKING.name())) {
                stockInternalBoxService.changeStockInternalBoxStatus(stockInternalBoxEntity.getInternalBoxCode(), StockInternalBoxStatusEnum.PACKING.name());
            }

            StockInternalBoxItemEntity stockInternalBoxItemEntity = getInternalBoxItem(stockInternalBoxEntity.getInternalBoxCode(), itemRequest.getCustomSku(), batchEntity.getBatchId());
            if (Objects.isNull(stockInternalBoxItemEntity)) {
                ProductSpecInfoEntity specInfoEntity = specInfoService.findTopBySku(itemRequest.getCustomSku());
                stockInternalBoxItemEntity = new StockInternalBoxItemEntity();
                stockInternalBoxItemEntity.setSpaceId(stockInternalBoxEntity.getSpaceId());
                stockInternalBoxItemEntity.setInternalBoxId(stockInternalBoxEntity.getInternalBoxId());
                stockInternalBoxItemEntity.setInternalBoxCode(stockInternalBoxEntity.getInternalBoxCode());
                stockInternalBoxItemEntity.setProductId(specInfoEntity.getProductId());
                stockInternalBoxItemEntity.setSpecId(specInfoEntity.getSpecId());
                stockInternalBoxItemEntity.setSku(specInfoEntity.getSku());
                stockInternalBoxItemEntity.setBatchId(batchEntity.getBatchId());
                stockInternalBoxItemEntity.setIsProcess(1);
                stockInternalBoxItemEntity.setSourcePositionCode(ProcessConstant.AFTER_PROCESS_POSITION_CODE);
                stockInternalBoxItemEntity.setSourceAreaId(bdPositionService.getPositionByCode(ProcessConstant.AFTER_PROCESS_POSITION_CODE).getAreaId());
                stockInternalBoxItemEntity.setQty(0);
            }
            stockInternalBoxItemService.addStockInternalBoxItemQty(stockInternalBoxItemEntity, eligibleQty, StockChangeLogTypeEnum.PROCESS_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);

        }
    }

    private StockInternalBoxItemEntity getInternalBoxItem(String internalBoxCode, String sku, Integer batchId) {
        return stockInternalBoxItemService.getOne(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                .eq(StockInternalBoxItemEntity::getSku, sku)
                .eq(StockInternalBoxItemEntity::getSourcePositionCode, ProcessConstant.AFTER_PROCESS_POSITION_CODE)
                .eq(StockInternalBoxItemEntity::getIsProcess, 1)
                .eq(StockInternalBoxItemEntity::getBatchId, batchId)
                .last("limit 1"));
    }

    /**
     * * 1.5.波次完成，出库单完成
     * * 1.6 通知erp备货单完成
     *
     * @param batchEntity
     * @param operator
     */
    private void stockProcessAllComplete(StockoutBatchEntity batchEntity, String operator) {
        // 1.5. 波次完成，波次单完成,出库单完成
        batchService.changeBatchStatus(batchEntity, StockoutWaveTaskStatusEnum.COMPLETED, operator);
        List<StockoutBatchOrderEntity> batchOrderEntityList = batchOrderService.getBatchOrderList(batchEntity);
        batchOrderEntityList.forEach(o -> {
            o.setUpdateBy(operator);
            o.setStatus(StockoutWaveTaskStatusEnum.COMPLETED.name());
        });
        batchOrderService.updateBatchById(batchOrderEntityList);
        List<Integer> stockoutIds = batchOrderEntityList.stream().map(StockoutBatchOrderEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
        List<StockoutOrderEntity> stockoutOrderEntities = stockoutOrderService.listByIds(stockoutIds);
        stockoutOrderEntities.forEach(o -> {
            o.setUpdateBy(operator);
            o.setStatus(StockoutOrderStatusEnum.DELIVERED.name());
            o.setDeliveryDate(new Date());
        });
        List<String> orderNoList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda().in(StockoutOrderItemEntity::getStockoutOrderId, stockoutIds))
                .stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList());
        stockoutOrderService.updateBatchById(stockoutOrderEntities);
        // 1.6 通知erp备货单完成
        ErpProcessOperateRequest operateRequest = new ErpProcessOperateRequest();
        operateRequest.setBatchId(batchEntity.getBatchId());
        operateRequest.setOperateType(ProcessOperateTypeEnum.PROCESS_CANCEL.getType());
        operateRequest.setUserName(operator);
        operateRequest.setOrderNoList(orderNoList);
        operateRequest.setPartialPickIdList(stockoutOrderEntities.stream().map(StockoutOrderEntity::getErpPickId).collect(Collectors.toList()));
        LocationWrapperMessage<ErpProcessOperateRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), operateRequest);
        messageProducer.sendMessage(KafkaConstant.SYNC_PROCESS_OPERATE_BUSINESS_MARK, KafkaConstant.SYNC_PROCESS_OPERATE_TOPIC, message);
    }

    /**
     * 虚拟库位库存变动
     */
    private void virtualPositionStockProcess(String customerSku, Integer changeQty, Integer batchId) {
        BdPositionEntity position = bdPositionService.getPositionByCode(ProcessConstant.AFTER_PROCESS_POSITION_CODE);
        if (position == null)
            throw new BusinessServiceException("未找到加工发货库位，请核对此库位是否存在：" + ProcessConstant.AFTER_PROCESS_POSITION_CODE);
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(customerSku);
        stockUpdateRequest.setPositionCode(position.getPositionCode());
        stockUpdateRequest.setChangeLogType(changeQty > 0 ? StockChangeLogTypeEnum.PROCESS_RECEIVE : StockChangeLogTypeEnum.PROCESS_WITHDRAWAL);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        stockUpdateRequest.setQty(changeQty);
        stockUpdateRequest.setIsProcess(1);
        stockUpdateRequest.setBatchId(batchId);
        stockService.updateStock(stockUpdateRequest);
    }

    /**
     * 次品处理
     */
    private void wmsProcessBadQty(List<ProcessCompleteItemRequest> badList, StockoutBatchEntity batchEntity) {
        // 加工次品库位
        List<BdPositionEntity> positionEntityList = bdPositionService.list(new LambdaQueryWrapper<BdPositionEntity>()
                .eq(BdPositionEntity::getSpaceId, batchEntity.getSpaceId())
                .eq(BdPositionEntity::getPositionType, BdPositionTypeEnum.PROCESS_DEMAGE_POSITION.name())
                .eq(BdPositionEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(positionEntityList)) {
            throw new BusinessServiceException("找不到加工次品库位，请到仓库中添加加工次品库位");
        }
        // 加工次品库位库存++
        for (ProcessCompleteItemRequest itemRequest : badList) {
            StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
            stockUpdateRequest.setSku(itemRequest.getBaseSku());
            stockUpdateRequest.setPositionCode(positionEntityList.get(0).getPositionCode());
            stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.PROCESS_QC_INVALID);
            stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_IN);
            stockUpdateRequest.setQty(itemRequest.getBadQty());
            stockService.updateStock(stockUpdateRequest);
        }
    }

    /**
     * 生成待分拣加工波次实体类
     */
    public List<PlanDTO> getByTaskItemList(Integer batchId) {
        List<PlanDTO> result = new LinkedList<>();
        // 出库单信息
        List<StockoutBatchOrderItemEntity> batchOrderItemEntityList = batchOrderItemService.getByBatchId(batchId);
        List<Integer> stockoutOrderItemIds = batchOrderItemEntityList.stream().map(StockoutBatchOrderItemEntity::getStockoutOrderItemId).collect(Collectors.toList());
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda()
                .in(StockoutOrderItemEntity::getStockoutOrderItemId, stockoutOrderItemIds)
                .eq(StockoutOrderItemEntity::getIsNeedProcess, 1));
        List<Integer> stockoutOrderIds = stockoutOrderItemEntityList.stream().map(StockoutOrderItemEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.list(new QueryWrapper<StockoutOrderEntity>().lambda().in(StockoutOrderEntity::getStockoutOrderId, stockoutOrderIds));
        // 加工信息
        List<StockoutOrderProcessInfoEntity> processInfoEntityList = this.list(new QueryWrapper<StockoutOrderProcessInfoEntity>().lambda().in(StockoutOrderProcessInfoEntity::getStockoutOrderId, stockoutOrderIds));
        List<StockoutOrderItemProcessInfoEntity> itemProcessInfoEntityList = itemProcessInfoService.list(new QueryWrapper<StockoutOrderItemProcessInfoEntity>().lambda().in(StockoutOrderItemProcessInfoEntity::getStockoutOrderItemId, stockoutOrderItemIds));
        List<Integer> processItemIds = itemProcessInfoEntityList.stream().map(StockoutOrderItemProcessInfoEntity::getStockoutItemProcessInfoId).collect(Collectors.toList());
        List<StockoutOrderProcessImageInfoEntity> processImageInfoEntityList = processImageInfoService.list(new QueryWrapper<StockoutOrderProcessImageInfoEntity>().lambda().in(StockoutOrderProcessImageInfoEntity::getStockoutItemProcessInfoId, processItemIds));
        // sku 信息
        List<String> baseSkuList = itemProcessInfoEntityList.stream().map(StockoutOrderItemProcessInfoEntity::getBaseSku).distinct().collect(Collectors.toList());
        List<ProductSpecInfoEntity> baseSkuInfoList = specInfoService.findAllBySkuIn(baseSkuList);

        Map<String, List<StockoutOrderItemEntity>> collect = stockoutOrderItemEntityList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getOrderNo));
        collect.forEach((key, value) -> {
            PlanDTO planDTO = new PlanDTO();
            planDTO.setOrderNo(key);
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderEntityList.stream().filter(o -> o.getStockoutOrderId().equals(value.get(0).getStockoutOrderId())).findFirst().orElse(null);
            if (stockoutOrderEntity == null)
                throw new BusinessServiceException("未找到出库单");
            StockoutOrderProcessInfoEntity processInfoEntity = processInfoEntityList.stream().filter(o -> o.getStockoutOrderId().equals(stockoutOrderEntity.getStockoutOrderId())).findFirst().orElse(null);
            if (processInfoEntity == null)
                throw new BusinessServiceException("未找到出库单加工信息");
            planDTO.setProcessType(processInfoEntity.getProcessType().equals(1) ? ProcessTypeEnum.STOCK_PROCESSING.name() : ProcessTypeEnum.ORDER_PROCESSING.name());
            planDTO.setStoreId(stockoutOrderEntity.getStoreId());
            planDTO.setStoreName(stockoutOrderEntity.getStoreName());
            planDTO.setStoreOwner(stockoutOrderEntity.getAuditName());
            planDTO.setBusinessType(stockoutOrderEntity.getBusinessType());

            List<StockoutOrderItemProcessInfoEntity> processInfos = itemProcessInfoEntityList.stream().filter(o -> o.getOrderNo().equals(key)).collect(Collectors.toList());
            List<Integer> processInfoIds = processInfos.stream().map(StockoutOrderItemProcessInfoEntity::getStockoutItemProcessInfoId).collect(Collectors.toList());
            List<StockoutOrderProcessImageInfoEntity> processImages = processImageInfoEntityList.stream().filter(o -> processInfoIds.contains(o.getStockoutItemProcessInfoId())).collect(Collectors.toList());
            List<String> skuList = processInfos.stream().map(StockoutOrderItemProcessInfoEntity::getBaseSku).collect(Collectors.toList());
            List<ProductSpecInfoEntity> skuInfoList = baseSkuInfoList.stream().filter(o -> skuList.contains(o.getSku())).collect(Collectors.toList());
            List<PlanItemDTO> planItemDTOList = getByProcessInfoList(value, processInfos, processImages, skuInfoList);
            planItemDTOList.forEach(o -> o.setProcessType(planDTO.getProcessType()));
            planDTO.setItemDTOList(planItemDTOList);
            planDTO.setCustomTechnology(planItemDTOList.get(0).getCustomTechnology());
            planDTO.setCustomType(planItemDTOList.get(0).getCustomType());
            result.add(planDTO);
        });
        return result;
    }

    private List<PlanItemDTO> getByProcessInfoList(List<StockoutOrderItemEntity> currentItemList, List<StockoutOrderItemProcessInfoEntity> processInfos, List<StockoutOrderProcessImageInfoEntity> processImages, List<ProductSpecInfoEntity> specInfoEntityList) {
        List<PlanItemDTO> result = new LinkedList<>();
        for (StockoutOrderItemEntity stockoutOrderItemEntity : currentItemList) {
            StockoutOrderItemProcessInfoEntity processInfo = processInfos.stream().filter(o -> o.getStockoutOrderItemId().equals(stockoutOrderItemEntity.getStockoutOrderItemId())).findFirst().orElse(null);
            if (processInfo == null)
                continue;
            PlanItemDTO planItemDTO = new PlanItemDTO();
            planItemDTO.setCustomTechnology(processInfo.getCustomProcess());
            planItemDTO.setCustomType(processInfo.getCustomType().equals(0) || processInfo.getCustomType().equals(1) ? ProcessCustomTypeEnum.CUSTOMIZED.name() : ProcessCustomTypeEnum.LIGHT_CUSTOMIZED.name());
            planItemDTO.setHoleNum(processInfo.getHoleNum());
            planItemDTO.setSupplierMaterials(processInfo.getSupplierMaterials());
            planItemDTO.setPatternSpecificationNum(processInfo.getPatternSpecificationNum());
            planItemDTO.setStockoutOrderItemId(stockoutOrderItemEntity.getStockoutOrderItemId());
            planItemDTO.setStockoutOrderId(stockoutOrderItemEntity.getStockoutOrderId());
            ProductSpecInfoEntity baseSkuInfo = specInfoEntityList.stream().filter(o -> o.getSku().equals(processInfo.getBaseSku())).findFirst().orElse(null);
            if (baseSkuInfo == null)
                continue;
            // 胚款
            planItemDTO.setBaseProductId(baseSkuInfo.getProductId());
            planItemDTO.setBaseSpecId(baseSkuInfo.getSpecId());
            planItemDTO.setBaseSku(baseSkuInfo.getSku());
            planItemDTO.setBaseBarcode(baseSkuInfo.getBarcode());
            List<String> imageUrls = processImages.stream().filter(o -> o.getStockoutItemProcessInfoId().equals(processInfo.getStockoutItemProcessInfoId()) && o.getImageType().equals(0)).map(StockoutOrderProcessImageInfoEntity::getImageUrl).collect(Collectors.toList());
            planItemDTO.setBaseImageUrl(String.join(";", imageUrls));
            // 定制款
            planItemDTO.setCustomProductId(stockoutOrderItemEntity.getProductId());
            planItemDTO.setCustomSpecId(stockoutOrderItemEntity.getSpecId());
            planItemDTO.setCustomSku(stockoutOrderItemEntity.getSku());
            planItemDTO.setCustomBarcode(stockoutOrderItemEntity.getBarcode());
            planItemDTO.setCustomStoreBarcode(stockoutOrderItemEntity.getSellerBarcode());
            List<StockoutOrderProcessImageInfo> customImageInfoList = processImages.stream().filter(o -> o.getStockoutItemProcessInfoId().equals(processInfo.getStockoutItemProcessInfoId()) && o.getImageType().equals(1))
                    .map(temp -> {
                        StockoutOrderProcessImageInfo imageInfo = new StockoutOrderProcessImageInfo();
                        imageInfo.setCustomerImageUrl(temp.getImageUrl());
                        imageInfo.setIsMain(temp.getIsMain());
                        return imageInfo;
                    }).collect(Collectors.toList());
            planItemDTO.setCustomImageInfoList(customImageInfoList);
            // 元素
            List<StockoutOrderProcessElementImageInfo> elementImageInfos = processImages.stream().filter(o -> o.getStockoutItemProcessInfoId().equals(processInfo.getStockoutItemProcessInfoId()) && o.getImageType().equals(2)).map(projection -> {
                StockoutOrderProcessElementImageInfo elementImageInfo = new StockoutOrderProcessElementImageInfo();
                elementImageInfo.setElementImageUrl(projection.getImageUrl());
                elementImageInfo.setElementCode(projection.getElementCode());
                return elementImageInfo;
            }).collect(Collectors.toList());
            planItemDTO.setElementImageInfos(elementImageInfos);
            this.setPlatItemOtherInfo(planItemDTO, stockoutOrderItemEntity);
            result.add(planItemDTO);
        }
        return result;
    }

    /**
     * 构造其他信息
     */
    private void setPlatItemOtherInfo(PlanItemDTO planItemDTO, StockoutOrderItemEntity stockoutOrderItemEntity) {
        planItemDTO.setOrderItemId(stockoutOrderItemEntity.getOrderItemId());
        planItemDTO.setPlanQty(stockoutOrderItemEntity.getQty());
        planItemDTO.setPickQty(stockoutOrderItemEntity.getQty());
        planItemDTO.setChangeType(stockoutOrderItemEntity.getChangeType());
    }
}
