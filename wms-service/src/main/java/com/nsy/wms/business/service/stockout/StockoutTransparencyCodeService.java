package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.TransparencyCodeInfoBusinessTypeEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.ScanPrintRequest;
import com.nsy.api.wms.request.stockout.StockoutTCodePrintByOrderItemRequest;
import com.nsy.api.wms.request.stockout.StockoutTCodeSkuPrintRequest;
import com.nsy.api.wms.request.stockout.SyncTransparencyCodeRequest;
import com.nsy.api.wms.request.stockout.TCodeIndexPrintRequest;
import com.nsy.api.wms.request.stockout.TransparencyCodeInfoOccupyRequest;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.ShipmentTransparencyCodeInfo;
import com.nsy.api.wms.response.stockout.StockoutShipmentTransparencyCodeResponse;
import com.nsy.wms.business.manage.amazon.AmazonApiService;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.oms.publish.response.TransparencyCodeInfoOccupyResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderPackMappingInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutTransparencyCodeEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutTransparencyCodeMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 透明计划T-Code(StockoutTransparencyCode)服务层
 *
 * <AUTHOR>
 * @since 2023-06-12 14:23:18
 */
@Service
public class StockoutTransparencyCodeService extends ServiceImpl<StockoutTransparencyCodeMapper, StockoutTransparencyCodeEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutTransparencyCodeService.class);

    @Resource
    PrintTemplateService printTemplateService;
    @Resource
    StockoutShipmentService shipmentService;
    @Resource
    StockoutShipmentItemService shipmentItemService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;
    @Resource
    StockoutOrderService orderService;
    @Resource
    StockoutOrderPrintService stockoutOrderPrintService;
    @Resource
    StockoutOrderLogService orderLogService;
    @Resource
    private LoginInfoService loginInfoService;
    @Resource
    AmazonApiService amazonApiService;
    @Autowired
    StockoutTransparencyCodeService stockoutTransparencyCodeService;
    @Autowired
    StockoutShipmentPrintService shipmentPrintService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockoutOrderPackMappingInfoService packMappingInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;

    /**
     * 整箱打印
     *
     * <AUTHOR>
     * 2023-06-12
     */
    @Transactional(rollbackFor = Exception.class)
    public PrintListResponse printTransparencyCodeByBox(List<String> stringList, boolean repeatPrint) {
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        PrintTemplateEntity template = printTemplateService.getByName("透明计划T标");
        response.setSpec(template.getSpec());
        response.setTemplateName(template.getName());
        List<String> stockoutOrderNoResult = new ArrayList<>();
        stringList.forEach(i -> {
            StockoutShipmentEntity shipment = shipmentService.findTopByShipmentBoxCode(i);
            List<StockoutShipmentItemEntity> shipmentItemEntities = shipmentItemService.findByShipmentId(shipment.getShipmentId());
            List<String> stockoutOrderNos = shipmentItemEntities.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
            if (stockoutOrderNos.size() > 1) {
                throw new BusinessServiceException("无法同时打印包含 两个出库单的箱子");
            }
            stockoutOrderNoResult.addAll(stockoutOrderNos);
            ScanPrintRequest scanPrintRequest = new ScanPrintRequest();
            scanPrintRequest.setTemplateName("透明计划T标");
            scanPrintRequest.setShipmentBoxCode(i);
            scanPrintRequest.setStockoutOrderNo(stockoutOrderNos.get(0));
            PrintListResponse response1 = shipmentPrintService.printCustomer(scanPrintRequest);
            response.getHtmlList().addAll(response1.getHtmlList());
        });
        if (repeatPrint) {
            List<StockoutOrderEntity> stockoutOrderEntities = orderService.getByStockoutOrderNoList(stockoutOrderNoResult);
            stockoutOrderEntities.forEach(order -> orderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.RE_PRINT_TRANSPARENCY_CODE, "装箱清单打印T标，箱子号：" + String.join(",", stringList)));
        }
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    public PrintListResponse printTransparencyCodeByCode(List<String> stringList) {
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        PrintTemplateEntity template = printTemplateService.getByName("透明计划T标");
        response.setSpec(template.getSpec());
        response.setTemplateName(template.getName());
        List<StockoutTransparencyCodeEntity> transparencyCodePrint = new ArrayList<>();
        Set<Integer> orderId = new HashSet<>(16);
        stringList.forEach(i -> {
            StockoutTransparencyCodeEntity entity = getByTransparencyCode(i);
            /*if (!entity.getIsPrint()) {
                throw new BusinessServiceException("亚马逊透明计划商品T标只能在 复核装箱确认 才能被打印出来！");
            }*/
            StockoutOrderItemEntity orderItem = stockoutOrderItemService.getById(entity.getStockoutOrderItemId());
            orderId.add(orderItem.getStockoutOrderId());
            Map<String, String> map = new HashMap<>();
            map.put("transparencyCodeValue", entity.getTransparencyCode());
            map.put("sku", orderItem.getSku());
            map.put("sellerTitle", orderItem.getSellerTitle());
            map.put("sellerTitleSpecial", orderItem.getSellerTitle().replaceAll("\"", " inch"));
            map.put("sellerSku", orderItem.getSellerSku());
            map.put("sellerText", orderItem.getSellerText());
            map.put("sellerBarcode", orderItem.getSellerBarcode());
            map.put("brandName", orderItem.getChangeStoreName());
            map.put("barcodeIndex", entity.getBarcodeIndex() == null ? "" : String.valueOf(entity.getBarcodeIndex()));
            LOGGER.info("{}根据T code进行打印 : {}", loginInfoService.getName(), entity.getTransparencyCode());
            response.getHtmlList().add(PrintTransferUtils.transfer(template.getContent(), map));
            entity.setIsPrint(Boolean.TRUE);
            entity.setUpdateBy(loginInfoService.getName());
            transparencyCodePrint.add(entity);
        });
        if (!CollectionUtils.isEmpty(transparencyCodePrint)) {
            updateBatchById(transparencyCodePrint);
            List<StockoutOrderEntity> stockoutOrderEntities = orderService.listByIds(orderId);
            stockoutOrderEntities.forEach(order -> orderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.RE_PRINT_TRANSPARENCY_CODE, "打印透明计划T标，T标值是：" + String.join(",", stringList)));
        }
        return response;
    }

    private StockoutTransparencyCodeEntity getByTransparencyCode(String i) {
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getTransparencyCode, i);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<StockoutTransparencyCodeEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("无法找到对应的商品T标：" + i);
        }
        if (list.size() > 1) {
            throw new BusinessServiceException("查到多个商品T标，请核对：" + i);
        }
        return list.get(0);
    }


    public List<StockoutTransparencyCodeEntity> findByStockouOrderNo(String stockoutOrderNo) {
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderNo, stockoutOrderNo);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        return list(wrapper);
    }

    public List<StockoutTransparencyCodeEntity> findByStockouOrderNoInShipment(String stockoutOrderNo) {
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderNo, stockoutOrderNo);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        wrapper.isNotNull(StockoutTransparencyCodeEntity::getShipmentId);
        return list(wrapper);
    }

    public List<StockoutTransparencyCodeEntity> findByStockouOrderNos(List<String> stockoutOrderNos) {
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutTransparencyCodeEntity::getStockoutOrderNo, stockoutOrderNos);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        return list(wrapper);
    }

    public List<StockoutTransparencyCodeEntity> findByStockoutOrderItemIdNotUsed(Integer stockoutOrderItemId) {
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderItemId, stockoutOrderItemId);
        wrapper.isNull(StockoutTransparencyCodeEntity::getShipmentItemId);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        return list(wrapper);
    }

    public List<StockoutTransparencyCodeEntity> findByShipmentItemId(Integer shipmentItemId) {
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getShipmentItemId, shipmentItemId);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        return list(wrapper);
    }

    public List<StockoutTransparencyCodeEntity> findByOrderItemId(Integer orderItemId) {
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderItemId, orderItemId);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        wrapper.orderByAsc(StockoutTransparencyCodeEntity::getBarcodeIndex);
        return list(wrapper);
    }

    /**
     * 预占T-code如果不足则自动补充tcode
     *
     * @param stockoutOrder
     */
    public boolean startPreMatchAndAutoReplenish(StockoutOrderEntity stockoutOrder) {
        return SpringUtil.getBean(StockoutTransparencyCodeService.class).startPreMatch(stockoutOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @JLock(keyConstant = "startPreMatchTCode", lockKey = "#stockoutOrder.stockoutOrderNo", expireSeconds = 60000)
    public boolean startPreMatch(StockoutOrderEntity stockoutOrder) {
        List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        List<StockoutOrderItemEntity> readyItem = stockoutOrderItemEntities.stream().filter(StockoutOrderItemEntity::getIsTransparency).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(readyItem))
            return true;
        List<StockoutTransparencyCodeEntity> byStockouOrderNo = findByStockouOrderNo(stockoutOrder.getStockoutOrderNo());
        if (!CollectionUtils.isEmpty(byStockouOrderNo)) {
            LOGGER.error("{}重复预占T code！", stockoutOrder.getStockoutOrderNo());
            // FBA或者是有快递单号，就可以生成波次
            checkOrderStatus(stockoutOrder);
            return true;
            // 先取消后预占
            // syncOmsTCode(stockoutOrder, 0);
        }
        // 构建预占request
        List<TransparencyCodeInfoOccupyRequest> request = new ArrayList<>();
        Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo = packMappingInfoService.getPackSkuInfoGroupBySellerSku(stockoutOrder.getStockoutOrderId());
        if (stockoutOrder.getHasPack()) {
            // 找出pack商品
            packSkuInfo.forEach((k, v) -> {
                TransparencyCodeInfoOccupyRequest infoOccupyRequest = new TransparencyCodeInfoOccupyRequest();
                infoOccupyRequest.setQty(v.get(0).getOriginQty());
                infoOccupyRequest.setStoreId(stockoutOrder.getStoreId());
                infoOccupyRequest.setSellerSku(k);
                infoOccupyRequest.setSku(v.get(0).getOriginSku());
                infoOccupyRequest.setReferenceKey(v.get(0).getOrderNo());
                infoOccupyRequest.setBusinessType(TransparencyCodeInfoBusinessTypeEnum.STOCKOUT_ORDER.name());
                infoOccupyRequest.setBusinessNo(stockoutOrder.getStockoutOrderNo());
                request.add(infoOccupyRequest);
            });
        } else {
            readyItem.forEach(item -> {
                TransparencyCodeInfoOccupyRequest infoOccupyRequest = new TransparencyCodeInfoOccupyRequest();
                infoOccupyRequest.setQty(item.getQty());
                infoOccupyRequest.setStoreId(stockoutOrder.getStoreId());
                infoOccupyRequest.setSellerSku(item.getSellerSku());
                infoOccupyRequest.setSku(item.getSku());
                infoOccupyRequest.setReferenceKey(item.getOrderNo());
                infoOccupyRequest.setBusinessType(TransparencyCodeInfoBusinessTypeEnum.STOCKOUT_ORDER.name());
                infoOccupyRequest.setBusinessNo(stockoutOrder.getStockoutOrderNo());
                request.add(infoOccupyRequest);
            });
        }
        List<TransparencyCodeInfoOccupyResponse> occupyResponses = amazonApiService.preOccupy(request, stockoutOrder.getStockoutOrderNo());
        //存在预占失败状态不为tcode预占中
        if (occupyResponses.stream().anyMatch(it -> !Objects.equals(it.getStatus(), 1))) {
            return false;
        }
        createOccupyTcode(occupyResponses, readyItem, stockoutOrder, packSkuInfo);
        orderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.PRE_MATCH_TRANSPARENCY_CODE, "预配T标成功");
        // FBA或者是有快递单号，就可以生成波次
        checkOrderStatus(stockoutOrder);
        if (stockoutOrder.getHasPack()) {
            readyItem.forEach(item -> item.setIsTransparency(Boolean.FALSE));
            stockoutOrderItemService.updateBatchById(readyItem);
        }
        return true;
    }

    public void checkOrderStatus(StockoutOrderEntity stockoutOrder) {
        // 没有物流单号 且 不是FBA  就不改状态
        if (!StringUtils.hasText(stockoutOrder.getLogisticsNo()) && stockoutOrder.getWorkspace().equalsIgnoreCase(StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name())) {
            return;
        }
        if (StringUtils.hasText(stockoutOrder.getLogisticsNo())) {
            List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
            if (stockoutOrderItemService.validTransparency(stockoutOrder.getStockoutOrderNo(), stockoutOrderItemEntities)) {
                orderService.syncErpLogistics(stockoutOrder);
            }
        }
        orderService.updateStockoutOrderStatusByEntity(stockoutOrder, StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
    }

    public void createOccupyTcode(List<TransparencyCodeInfoOccupyResponse> occupyResponses, List<StockoutOrderItemEntity> readyItem, StockoutOrderEntity stockoutOrder, Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo) {
        Set<Integer> idSet = new HashSet<>(16);
        String name = loginInfoService.getName();
        Map<String, Integer> skuIndex = new HashMap<>(32);
        occupyResponses.forEach(item -> {
            if (stockoutOrder.getHasPack()) {
                List<StockoutOrderPackMappingInfoEntity> stockoutOrderPackMappingInfoEntities = packSkuInfo.get(item.getSellerSku());
                StockoutOrderPackMappingInfoEntity stockoutOrderPackMappingInfoEntity = stockoutOrderPackMappingInfoEntities.stream().filter(it -> it.getOriginQty() == item.getTransparencyCodeList().size()
                        && Objects.equals(item.getSellerSku(), it.getPackSellerSku())).findAny().orElseThrow(() -> new BusinessServiceException(item.getSellerSku() + "无法找到对应的出库单pack明细"));
                StockoutOrderItemEntity one = getStockoutOrderItemEntity(stockoutOrderPackMappingInfoEntity);
                if (one == null) throw new BusinessServiceException("未找到对应的出库单明细");
                item.getTransparencyCodeList().forEach(code -> {
                    StockoutTransparencyCodeEntity entity = new StockoutTransparencyCodeEntity();
                    entity.setStockoutOrderNo(stockoutOrder.getStockoutOrderNo());
                    entity.setTransparencyCode(code);
                    entity.setOrderNo(stockoutOrderPackMappingInfoEntity.getOrderNo());
                    entity.setSku(stockoutOrderPackMappingInfoEntity.getOriginSku());
                    ProductSpecInfo bySku = productSpecInfoService.getBySku(stockoutOrderPackMappingInfoEntity.getOriginSku());
                    entity.setProductId(bySku.getProductId());
                    entity.setStatus(1);
                    entity.setStockoutOrderItemId(one.getStockoutOrderItemId());
                    entity.setBarcodeIndex(skuIndex.getOrDefault(entity.getSku(), 0) + 1);
                    entity.setCreateBy(name);
                    save(entity);
                    skuIndex.put(entity.getSku(), entity.getBarcodeIndex());
                });
            } else {
                if (CollectionUtils.isEmpty(item.getTransparencyCodeList()))
                    throw new BusinessServiceException("T code 匹配为空，请核对！");
                StockoutOrderItemEntity stockoutOrderItemEntity = readyItem.stream().filter(it -> it.getQty() == item.getTransparencyCodeList().size()
                        && Objects.equals(item.getSellerSku(), it.getSellerSku()) && !idSet.contains(it.getStockoutOrderItemId())).findAny().orElseThrow(() -> new BusinessServiceException(item.getSellerSku() + "无法找到对应的出库单明细"));
                item.getTransparencyCodeList().forEach(code -> {
                    StockoutTransparencyCodeEntity entity = new StockoutTransparencyCodeEntity();
                    entity.setStockoutOrderNo(stockoutOrder.getStockoutOrderNo());
                    entity.setTransparencyCode(code);
                    entity.setOrderNo(stockoutOrderItemEntity.getOrderNo());
                    entity.setSku(stockoutOrderItemEntity.getSku());
                    entity.setProductId(stockoutOrderItemEntity.getProductId());
                    entity.setStatus(1);
                    entity.setStockoutOrderItemId(stockoutOrderItemEntity.getStockoutOrderItemId());
                    entity.setBarcodeIndex(skuIndex.getOrDefault(entity.getSku(), 0) + 1);
                    entity.setCreateBy(name);
                    save(entity);
                    skuIndex.put(entity.getSku(), entity.getBarcodeIndex());
                    StockoutTransparencyCodeEntity byTransparencyCode = getByTransparencyCode(code);
                    idSet.add(byTransparencyCode.getStockoutOrderItemId());
                });
            }
        });
    }

    private StockoutOrderItemEntity getStockoutOrderItemEntity(StockoutOrderPackMappingInfoEntity stockoutOrderPackMappingInfoEntity) {
        LambdaQueryWrapper<StockoutOrderItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutOrderItemEntity::getOrderNo, stockoutOrderPackMappingInfoEntity.getOrderNo());
        wrapper.eq(StockoutOrderItemEntity::getSellerSku, stockoutOrderPackMappingInfoEntity.getPackSellerSku());
        return stockoutOrderItemService.getOne(wrapper.last("limit 1"));
    }


    public PrintListResponse printTransparencyCodeBySku(StockoutTCodeSkuPrintRequest request) {
        if (!StringUtils.hasText(request.getShipmentBoxCode()) && !StringUtils.hasText(request.getStockoutOrderNo())) {
            throw new BusinessServiceException("请输入箱子或者出库单进行打印");
        }
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutTransparencyCodeEntity::getSku, request.getSkuList());
        if (StringUtils.hasText(request.getShipmentBoxCode())) {
            StockoutShipmentEntity topByShipmentBoxCode = shipmentService.findTopByShipmentBoxCode(request.getShipmentBoxCode());
            wrapper.eq(StockoutTransparencyCodeEntity::getShipmentId, topByShipmentBoxCode.getShipmentId());
        }
        if (StringUtils.hasText(request.getStockoutOrderNo())) {
            wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderNo, request.getStockoutOrderNo());
        }
        List<StockoutTransparencyCodeEntity> list = list(wrapper.last("limit " + request.getPrintQty()));
        return stockoutTransparencyCodeService.printTransparencyCodeByCode(list.stream().map(StockoutTransparencyCodeEntity::getTransparencyCode).collect(Collectors.toList()));
    }

    // 透明计划分配t code
    public void buildShipmentInfo(StockoutShipmentEntity stockoutShipmentEntity, String stockoutOrderNo) {
        List<StockoutOrderItemEntity> orderItems = stockoutOrderItemService.getListByStockoutOrderNo(stockoutOrderNo);
        if (!stockoutOrderItemService.validTransparency(stockoutOrderNo, orderItems)) {
            // 不是透明计划
            return;
        }
        List<StockoutShipmentItemEntity> items = shipmentItemService.findByShipmentId(stockoutShipmentEntity.getShipmentId());
        items.forEach(this::matchShipmentItem);
    }

    // 明细匹配
    public void matchShipmentItem(StockoutShipmentItemEntity item) {
        StockoutOrderItemEntity orderItemEntity = stockoutOrderItemService.getById(item.getStockoutOrderItemId());
        if (!stockoutOrderItemService.validTransparency(item.getStockoutOrderNo(), Collections.singletonList(orderItemEntity))) {
            return;
        }
        // 未被分配的code
        List<StockoutTransparencyCodeEntity> codes = findByStockoutOrderItemIdNotUsed(item.getStockoutOrderItemId());
        // 已分配的code
        List<StockoutTransparencyCodeEntity> codeEntities = findByShipmentItemId(item.getShipmentItemId());
        if (codeEntities.size() == item.getQty())
            // 全部被分配完了
            return;
        if (codes.size() < item.getQty() - codeEntities.size()) {
            // 未分配 < 需要分配的
            throw new BusinessServiceException(item.getSku() + "装箱清单有部分T code未被分配！");
        }
        for (int i = 0; i < item.getQty() - codeEntities.size(); i++) {
            StockoutTransparencyCodeEntity entity = codes.get(i);
            entity.setShipmentId(item.getShipmentId());
            entity.setShipmentItemId(item.getShipmentItemId());
            updateById(entity);
        }
    }

    // 移除时剔除T code， i= 要剔除的数量
    public void removeCode(StockoutShipmentItemEntity item, int i) {
        if (i <= 0) {
            return;
        }
        List<StockoutTransparencyCodeEntity> shipmentItemCode = findByShipmentItemId(item.getShipmentItemId());
        if (CollectionUtils.isEmpty(shipmentItemCode)) {
            return;
        }
        int loop = i;
        for (StockoutTransparencyCodeEntity entity : shipmentItemCode) {
            if (loop <= 0) {
                break;
            }
            this.lambdaUpdate().eq(StockoutTransparencyCodeEntity::getId, entity.getId())
                    .set(StockoutTransparencyCodeEntity::getShipmentId, null)
                    .set(StockoutTransparencyCodeEntity::getShipmentItemId, null)
                    .update();
            loop--;
        }
    }

    // 调拨互换箱子编码，如果有编码的话
    public void transferCode(StockoutShipmentItemEntity shipmentItem, Integer qty, StockoutShipmentItemEntity updateEntity) {
        if (qty <= 0) {
            return;
        }
        StockoutOrderItemEntity originOrderItem = stockoutOrderItemService.getById(shipmentItem.getStockoutOrderItemId());
        if (!stockoutOrderItemService.validTransparency(shipmentItem.getStockoutOrderNo(), Collections.singletonList(originOrderItem))) {
            // 不是
            return;
        }
        List<StockoutTransparencyCodeEntity> shipmentItemCode = findByShipmentItemId(shipmentItem.getShipmentItemId());
        if (!CollectionUtils.isEmpty(shipmentItemCode)) {
            List<StockoutTransparencyCodeEntity> finalList = ListUtil.sub(shipmentItemCode, 0, qty);
            // 全部替换成 另外一个箱子的T code
            finalList.forEach(code -> {
                code.setShipmentId(updateEntity.getShipmentId());
                code.setShipmentItemId(updateEntity.getShipmentItemId());
            });
            updateBatchById(finalList);
        }
        // 剩下的也进行分配
        matchShipmentItem(updateEntity);
    }

    public void syncOmsTCode(StockoutOrderEntity stockoutOrderEntity, int status) {
        List<StockoutTransparencyCodeEntity> cancelList = findByStockouOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (CollectionUtils.isEmpty(cancelList)) {
            //如果为取消，为了兼容接口部分成功的场景需调用传单号到amazon取消tcode预占
            if (status != 0) {
                return;
            }
            List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            if (CollectionUtils.isEmpty(stockoutOrderItemEntityList)) {
                return;
            }
            List<SyncTransparencyCodeRequest> requests = stockoutOrderItemEntityList.stream().map(detail -> {
                SyncTransparencyCodeRequest request = new SyncTransparencyCodeRequest();
                request.setStatus(status);
                request.setBusinessNo(stockoutOrderEntity.getStockoutOrderNo());
                request.setBusinessType(TransparencyCodeInfoBusinessTypeEnum.STOCKOUT_ORDER.name());
                request.setSellerSku(detail.getSellerSku());
                request.setStoreId(stockoutOrderEntity.getStoreId());
                return request;
            }).collect(Collectors.toList());
            amazonApiService.cancelOccupyTCode(requests, stockoutOrderEntity.getStockoutOrderNo(), "取消");
            return;
        }
        List<SyncTransparencyCodeRequest> requests = cancelList.stream().map(code -> {
            SyncTransparencyCodeRequest request = new SyncTransparencyCodeRequest();
            StockoutOrderItemEntity orderItem = stockoutOrderItemService.getById(code.getStockoutOrderItemId());
            request.setStatus(status);
            request.setCodeList(Collections.singletonList(code.getTransparencyCode()));
            request.setSellerSku(orderItem.getSellerSku());
            if (status == 0) {
                code.setIsDeleted(Boolean.TRUE);
            } else {
                code.setStatus(status);
            }
            return request;
        }).collect(Collectors.toList());
        this.updateBatchById(cancelList);
        amazonApiService.syncStatus(requests, stockoutOrderEntity.getStockoutOrderNo(), status == 0 ? "取消" : "发货");
    }

    // 按照装箱顺序进行打印,且T code 不能重复
    public List<String> printByScanTaskOrderBy(Set<String> tCodeSet, StockoutShipmentItemEntity entity, StockoutOrderItemEntity itemById, Integer qty, PrintTemplateEntity templateEntity) {
        List<String> resultList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        if (stockoutOrderItemService.validTransparency(stockoutOrderItemService.getStockoutOrderNoById(itemById.getStockoutOrderItemId()), Collections.singletonList(itemById))) {
            LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
            // 目前只能按照sku查找
            if (entity != null) {
                wrapper.eq(StockoutTransparencyCodeEntity::getShipmentId, entity.getShipmentId());
                wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderItemId, entity.getStockoutOrderItemId());
            } else {
                wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderItemId, itemById.getStockoutOrderItemId());
            }
            wrapper.notIn(!CollectionUtils.isEmpty(tCodeSet), StockoutTransparencyCodeEntity::getTransparencyCode, tCodeSet);
            List<StockoutTransparencyCodeEntity> list = list(wrapper.last("limit " + qty));
            if (list.size() < qty) {
                throw new BusinessServiceException("T code打印失败, 数量不足！");
            }
            list.forEach(code -> {
                map.put("transparencyCodeValue", code.getTransparencyCode());
                map.put("sku", code.getSku());
                map.put("sellerTitle", itemById.getSellerTitle());
                map.put("sellerTitleSpecial", itemById.getSellerTitle().replaceAll("\"", " inch"));
                map.put("sellerSku", itemById.getSellerSku());
                map.put("sellerText", itemById.getSellerText());
                map.put("sellerBarcode", itemById.getSellerBarcode());
                map.put("brandName", itemById.getChangeStoreName());
                map.put("barcodeIndex", code.getBarcodeIndex() == null ? "" : String.valueOf(code.getBarcodeIndex()));
                tCodeSet.add(code.getTransparencyCode());
                LOGGER.info("{}根据sku进行打印 T code : {}", loginInfoService.getName(), code.getTransparencyCode());
                String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), map);
                resultList.add(transfer);
                code.setIsPrint(Boolean.TRUE);
                code.setUpdateBy(loginInfoService.getName());
            });
            updateBatchById(list);
        } else {
            map.put("transparencyCodeValue", "");
            map.put("sku", itemById.getSku());
            map.put("sellerTitle", itemById.getSellerTitle());
            map.put("sellerTitleSpecial", itemById.getSellerTitle().replaceAll("\"", " inch"));
            map.put("sellerSku", itemById.getSellerSku());
            map.put("sellerText", itemById.getSellerText());
            map.put("sellerBarcode", itemById.getSellerBarcode());
            map.put("brandName", "");
            map.put("barcodeIndex", "");
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), map);
            for (int i = 0; i < qty; i++) {
                resultList.add(transfer);
            }
        }
        return resultList;
    }

    // 出库单复核任务重新扫描
    public void retryScanOrder(String stockoutOrderNo) {
        this.lambdaUpdate().eq(StockoutTransparencyCodeEntity::getStockoutOrderNo, stockoutOrderNo)
                .eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED)
                .set(StockoutTransparencyCodeEntity::getShipmentId, null)
                .set(StockoutTransparencyCodeEntity::getShipmentItemId, null)
                .update();
    }

    public PrintListResponse printTransparencyCodeByIndex(TCodeIndexPrintRequest request) {
        StockoutShipmentItemEntity byId = shipmentItemService.getById(request.getShipmentItemId());
        if (byId == null) {
            throw new BusinessServiceException("找不到装箱明细");
        }
        StockoutOrderEntity byStockoutOrderNo = orderService.getByStockoutOrderNo(byId.getStockoutOrderNo());
        if (byStockoutOrderNo.getHasPack()) {
            StockoutOrderItemEntity orderItemEntity = stockoutOrderItemService.getById(byId.getStockoutOrderItemId());
            if (orderItemEntity != null) {
                request.setSku(orderItemEntity.getSellerSku());
            }
        }
        PrintTemplateEntity template = printTemplateService.getByName("透明计划T标");
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getStockoutOrderNo, byId.getStockoutOrderNo());
        wrapper.eq(StockoutTransparencyCodeEntity::getSku, request.getSku());
        wrapper.eq(StockoutTransparencyCodeEntity::getBarcodeIndex, request.getIndex());
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        StockoutTransparencyCodeEntity one = this.getOne(wrapper.last("limit 1"));
        if (one == null) {
            throw new BusinessServiceException("找不到该序号的T code");
        }
        StockoutOrderItemEntity itemById = stockoutOrderItemService.getById(one.getStockoutOrderItemId());
        Map<String, String> map = new HashMap<>();
        map.put("transparencyCodeValue", one.getTransparencyCode());
        map.put("sku", itemById.getSku());
        map.put("sellerTitle", itemById.getSellerTitle());
        map.put("sellerTitleSpecial", StringUtils.hasText(itemById.getSellerTitle()) ? itemById.getSellerTitle().replaceAll("\"", " inch") : "");
        map.put("sellerSku", itemById.getSellerSku());
        map.put("sellerText", itemById.getSellerText());
        map.put("sellerBarcode", itemById.getSellerBarcode());
        map.put("brandName", itemById.getChangeStoreName());
        map.put("barcodeIndex", one.getBarcodeIndex() == null ? "" : String.valueOf(one.getBarcodeIndex()));
        LOGGER.info("{}根据序号进行打印 T code : {}", loginInfoService.getName(), one.getTransparencyCode());
        String transfer = PrintTransferUtils.transfer(template.getContent(), map);
        PrintListResponse response = new PrintListResponse();
        response.setTemplateName(template.getName());
        response.setSpec(template.getSpec());
        response.setHtmlList(Collections.singletonList(transfer));
        return response;
    }

    public StockoutShipmentTransparencyCodeResponse getShipmentTCodeInfo(IdListRequest request) {
        StockoutShipmentTransparencyCodeResponse response = new StockoutShipmentTransparencyCodeResponse();
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutTransparencyCodeEntity::getShipmentId, request.getIdList());
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<StockoutTransparencyCodeEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return response;
        }
        list.forEach(item -> {
            ShipmentTransparencyCodeInfo codeInfo = new ShipmentTransparencyCodeInfo();
            codeInfo.setCode(item.getTransparencyCode());
            codeInfo.setShipmentId(item.getShipmentId());
            codeInfo.setOrderNo(item.getOrderNo());
            codeInfo.setShipmentItemId(item.getShipmentItemId());
            codeInfo.setSku(item.getSku());
            codeInfo.setIndex(item.getBarcodeIndex());
            response.getTransparencyCodeInfoList().add(codeInfo);
        });
        return response;
    }

    public StockoutShipmentTransparencyCodeResponse getTransparencyCodeInfo(IdListRequest request) {
        StockoutShipmentTransparencyCodeResponse response = new StockoutShipmentTransparencyCodeResponse();
        if (CollectionUtils.isEmpty(request.getIdList())) {
            return response;
        }
        List<StockoutOrderEntity> stockoutOrderEntities = orderService.findByErpPickIds(request.getIdList());
        stockoutOrderEntities.forEach(orderEntity -> {
            List<StockoutTransparencyCodeEntity> codeEntities = findByStockouOrderNoInShipment(orderEntity.getStockoutOrderNo());
            codeEntities.forEach(code -> {
                ShipmentTransparencyCodeInfo codeInfo = new ShipmentTransparencyCodeInfo();
                codeInfo.setErpPickId(orderEntity.getErpPickId());
                codeInfo.setCode(code.getTransparencyCode());
                codeInfo.setSku(code.getSku());
                codeInfo.setShipmentItemId(code.getShipmentItemId());
                codeInfo.setShipmentId(code.getShipmentId());
                codeInfo.setOrderNo(code.getOrderNo());
                response.getTransparencyCodeInfoList().add(codeInfo);
            });
        });
        return response;
    }

    public StockoutShipmentTransparencyCodeResponse getShipmentItemTCodeInfo(Integer shipmentId, Integer itemId) {
        StockoutShipmentTransparencyCodeResponse response = new StockoutShipmentTransparencyCodeResponse();
        LambdaQueryWrapper<StockoutTransparencyCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutTransparencyCodeEntity::getShipmentId, shipmentId);
        wrapper.eq(StockoutTransparencyCodeEntity::getShipmentItemId, itemId);
        wrapper.eq(StockoutTransparencyCodeEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        wrapper.orderByAsc(StockoutTransparencyCodeEntity::getBarcodeIndex);
        List<StockoutTransparencyCodeEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return response;
        }
        list.forEach(item -> {
            ShipmentTransparencyCodeInfo codeInfo = new ShipmentTransparencyCodeInfo();
            codeInfo.setIndex(item.getBarcodeIndex());
            codeInfo.setCode(item.getTransparencyCode());
            codeInfo.setShipmentId(item.getShipmentId());
            codeInfo.setOrderNo(item.getOrderNo());
            codeInfo.setShipmentItemId(item.getShipmentItemId());
            codeInfo.setSku(item.getSku());
            response.getTransparencyCodeInfoList().add(codeInfo);
        });
        return response;
    }

    public PrintListResponse printTCodeByStockoutOrder(List<Integer> idList) {
        List<StockoutOrderEntity> orderEntityList = orderService.listByIds(idList);
        return stockoutOrderPrintService.printAllTcode(orderEntityList, true);
    }

    public PrintListResponse printTcodeByOrderItemId(StockoutTCodePrintByOrderItemRequest request) {
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        PrintTemplateEntity template = printTemplateService.getByName("透明计划T标");
        Map<String, ProductSpecInfoEntity> specMap = new HashMap<>();
        List<StockoutTransparencyCodeEntity> updateList = new ArrayList<>();
        request.getOrderOrderItemList().forEach(orderItemId -> {
            StockoutOrderItemEntity orderItem = stockoutOrderItemService.getById(orderItemId);
            ProductSpecInfoEntity specInfoEntity = specMap.computeIfAbsent(orderItem.getSku(), entity -> productSpecInfoService.findTopBySku(orderItem.getSku()));
            PrintTemplateEntity template1 = printTemplateService.getByName("系统商品条码");
            String transfer = PrintTransferUtils.transfer(template1.getContent(), specInfoEntity);
            response.getHtmlList().add(transfer);
            if (orderItem.getIsTransparency()) {
                List<StockoutTransparencyCodeEntity> byStockouOrderNo = findByOrderItemId(orderItemId);
                byStockouOrderNo.forEach(code -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("transparencyCodeValue", code.getTransparencyCode());
                    map.put("sku", orderItem.getSku());
                    map.put("sellerTitle", orderItem.getSellerTitle());
                    map.put("sellerTitleSpecial", orderItem.getSellerTitle().replaceAll("\"", " inch"));
                    map.put("sellerSku", orderItem.getSellerSku());
                    map.put("sellerText", orderItem.getSellerText());
                    map.put("sellerBarcode", orderItem.getSellerBarcode());
                    map.put("brandName", orderItem.getChangeStoreName());
                    map.put("barcodeIndex", code.getBarcodeIndex() == null ? "" : String.valueOf(code.getBarcodeIndex()));
                    StockoutTransparencyCodeEntity entity = new StockoutTransparencyCodeEntity();
                    entity.setIsPrint(Boolean.TRUE);
                    entity.setId(code.getId());
                    response.getHtmlList().add(PrintTransferUtils.transfer(template.getContent(), map));
                    updateList.add(entity);
                });
            } else {
                Map<String, String> map = new HashMap<>(16);
                map.put("transparencyCodeValue", "");
                map.put("sku", orderItem.getSku());
                map.put("sellerTitle", orderItem.getSellerTitle());
                map.put("sellerTitleSpecial", orderItem.getSellerTitle().replaceAll("\"", " inch"));
                map.put("sellerSku", orderItem.getSellerSku());
                map.put("sellerText", orderItem.getSellerText());
                map.put("sellerBarcode", orderItem.getSellerBarcode());
                map.put("brandName", "");
                for (int i = 0; i < orderItem.getQty(); i++) {
                    map.put("barcodeIndex", String.valueOf(i + 1));
                    response.getHtmlList().add(PrintTransferUtils.transfer(template.getContent(), map));
                }
            }
        });
        if (!CollectionUtils.isEmpty(updateList))
            updateBatchById(updateList);
        response.setSpec(template.getSpec());
        response.setTemplateName(template.getName());
        return response;
    }
}
