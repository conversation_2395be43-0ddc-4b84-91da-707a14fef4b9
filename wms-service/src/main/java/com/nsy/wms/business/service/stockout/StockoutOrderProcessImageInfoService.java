package com.nsy.wms.business.service.stockout;

import com.nsy.api.wms.request.stockout.StockoutOrderItemProcessInfo;
import com.nsy.api.wms.request.stockout.StockoutOrderProcessElementImageInfo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.request.stockout.StockoutOrderProcessImageInfo;
import com.nsy.wms.repository.entity.stockout.StockoutOrderProcessImageInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderProcessImageInfoMapper;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

@Service
public class StockoutOrderProcessImageInfoService extends ServiceImpl<StockoutOrderProcessImageInfoMapper, StockoutOrderProcessImageInfoEntity> {

    /**
     * 出库单明细加工图片信息
     */
    public void addByProcessItem(StockoutOrderItemProcessInfo processInfo, Integer stockoutItemProcessInfoId) {
        List<StockoutOrderProcessImageInfoEntity> imageInfoEntityList = new LinkedList<>();
        for (String imageUrl : processInfo.getImageUrl()) {
            StockoutOrderProcessImageInfoEntity imageEntity = new StockoutOrderProcessImageInfoEntity();
            imageEntity.setStockoutItemProcessInfoId(stockoutItemProcessInfoId);
            imageEntity.setCustomerSku(processInfo.getCustomerSku());
            imageEntity.setImageType(0);
            imageEntity.setImageUrl(imageUrl);
            imageInfoEntityList.add(imageEntity);
        }
        for (StockoutOrderProcessImageInfo customerImageUrl : processInfo.getCustomerImageUrls()) {
            StockoutOrderProcessImageInfoEntity customerImageEntity = new StockoutOrderProcessImageInfoEntity();
            customerImageEntity.setStockoutItemProcessInfoId(stockoutItemProcessInfoId);
            customerImageEntity.setCustomerSku(processInfo.getCustomerSku());
            customerImageEntity.setImageType(1);
            customerImageEntity.setImageUrl(customerImageUrl.getCustomerImageUrl());
            customerImageEntity.setIsMain(customerImageUrl.getIsMain());
            imageInfoEntityList.add(customerImageEntity);
        }
        for (StockoutOrderProcessElementImageInfo elementImageInfo : processInfo.getElementInfos()) {
            StockoutOrderProcessImageInfoEntity elementImageEntity = new StockoutOrderProcessImageInfoEntity();
            elementImageEntity.setStockoutItemProcessInfoId(stockoutItemProcessInfoId);
            elementImageEntity.setCustomerSku(processInfo.getCustomerSku());
            elementImageEntity.setImageType(2);
            elementImageEntity.setImageUrl(elementImageInfo.getElementImageUrl());
            elementImageEntity.setElementCode(elementImageInfo.getElementCode());
            imageInfoEntityList.add(elementImageEntity);
        }
        if (!imageInfoEntityList.isEmpty())
            this.saveBatch(imageInfoEntityList);
    }
}
