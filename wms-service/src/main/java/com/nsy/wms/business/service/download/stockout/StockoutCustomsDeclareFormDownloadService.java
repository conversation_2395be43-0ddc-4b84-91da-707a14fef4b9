package com.nsy.wms.business.service.download.stockout;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentAggregatedItemResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormSearchRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.bd.BdExportPortService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareDocumentAggregatedItemService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareFormMapper;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class StockoutCustomsDeclareFormDownloadService implements IDownloadService {

    @Resource
    StockoutCustomsDeclareFormMapper declareFormMapper;
    @Resource
    BdExportPortService exportPortService;
    @Resource
    StockoutCustomsDeclareDocumentAggregatedItemService declareDocumentAggregatedItemService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_CUSTOMS_DECLARE_FORM_LIST;
    }

    // 按查询条件导出
    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        StockoutCustomsDeclareFormSearchRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockoutCustomsDeclareFormSearchRequest.class);
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setPageSize(request.getPageSize());
        IPage<StockoutCustomsDeclareFormExport> pageResponse = exportForm(downloadRequest);
        DownloadResponse response = new DownloadResponse();
        response.setTotalCount(pageResponse.getTotal());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getRecords()));
        return response;
    }

    private IPage<StockoutCustomsDeclareFormExport> exportForm(StockoutCustomsDeclareFormSearchRequest request) {
        Page page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutCustomsDeclareFormExport> exportFormPage = declareFormMapper.exportForm(page, request);
        //设置交货时间
        exportFormPage.getRecords().forEach(record -> {
            record.setExportPort(exportPortService.getByCode(record.getExportPort()));
            record.setgUnit(StockoutBuilding.getDeclareUnitMap().get(record.getgUnit()));
            //从单据获取净重
            StockoutCustomsDeclareDocumentAggregatedItemResult aggregatedItemResult = declareDocumentAggregatedItemService.getAggregatedItemResult(record.getProtocolNo(), Integer.valueOf(record.getgNo()));
            if (!Objects.isNull(aggregatedItemResult)) {
                record.setNetWeight(aggregatedItemResult.getActualNetWeight());
            }
        });
        return exportFormPage;
    }
}
