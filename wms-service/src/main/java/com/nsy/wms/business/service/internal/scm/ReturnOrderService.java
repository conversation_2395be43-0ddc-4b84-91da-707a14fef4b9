package com.nsy.wms.business.service.internal.scm;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.ReturnProductConstant;
import com.nsy.api.wms.constants.StatusTabConstants;
import com.nsy.api.wms.domain.product.SendDingDingMessage;
import com.nsy.api.wms.enumeration.PurchaseOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.ReturnProductTaskDeliveryTypeEnum;
import com.nsy.api.wms.enumeration.TabUrlEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnHandleMethodEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReworkStatusEnum;
import com.nsy.api.wms.enumeration.stockout.VacuumEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.FeedBackWmsTaskReviewResultRequest;
import com.nsy.api.wms.request.stockin.ReturnRecordRequest;
import com.nsy.api.wms.request.stockin.ReworkReturnsReceiptRequest;
import com.nsy.api.wms.request.stockin.StockinReturnDeliveryConfirmRequest;
import com.nsy.api.wms.request.stockin.StockinReturnDeliveryLogisticsRequest;
import com.nsy.api.wms.request.stockin.StockinReturnOrderItemPageRequest;
import com.nsy.api.wms.request.stockin.StockinReturnOrderPageRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskItem;
import com.nsy.api.wms.request.stockin.StockinReturnReviewTaskGenerateRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.stockin.QueryReworkedReturnedRecordResponse;
import com.nsy.api.wms.response.stockin.QueryWaitReturnRecordResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderItemPageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderPageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderStatisticsResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductSearchQtyResponse;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.notify.NotifyApiService;
import com.nsy.wms.business.manage.notify.request.DingTalkCorpConversationMessageRequest;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderLogAddRequest;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.business.manage.supplier.request.SyncReturnDeliveryRequest;
import com.nsy.wms.business.manage.supplier.response.PurchaseReturnItemNewOrderNoResponse;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.internal.common.PurchaseModuleService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinReturnProductLogService;
import com.nsy.wms.business.service.stockin.StockinReturnProductService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskItemService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskService;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.business.service.tab.TabService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductLog;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.ImageUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * scm 退货单相关service
 *
 * <AUTHOR>
 * 2022/03/17
 */
@Service
public class ReturnOrderService implements IDownloadService, TabService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReturnOrderService.class);

    @Autowired
    private StockinReturnProductTaskService returnProductTaskService;
    @Autowired
    BdSystemParameterService parameterService;
    @Autowired
    ProductSpecInfoService specInfoService;
    @Autowired
    private StockinReturnProductTaskItemService returnProductTaskItemService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinReturnProductService returnProductService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private StockinReturnProductLogService returnProductLogService;
    @Autowired
    private GcApiService gcApiService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    PurchaseModuleService purchaseModuleService;
    @Autowired
    StockinReturnProductTaskService stockinReturnProductTaskService;
    @Resource
    StockinReturnProductTaskMapper stockinReturnProductTaskMapper;
    @Resource
    StockinReturnProductTaskItemMapper stockinReturnProductTaskItemMapper;
    @Resource
    BdErpSpaceMappingService bdErpSpaceMappingService;
    @Resource
    SupplierService supplierService;
    @Resource
    NotifyApiService notifyApiService;

    /**
     * scm退货单列表数据源
     *
     * @param request
     * @return
     */
    public PageResponse<StockinReturnOrderPageResponse> pageList(StockinReturnOrderPageRequest request) {
        PageResponse<StockinReturnOrderPageResponse> pageResponse = new PageResponse<>();
        Page<StockinReturnOrderPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        buildStockinReturnOrderPageRequest(request);
        List<StockinReturnOrderPageResponse> stockinReturnOrderPageResponses = returnProductTaskService.getBaseMapper().returnOrderPageList(page, request);
        if (!stockinReturnOrderPageResponses.isEmpty()) {
            Map<Integer, Integer> returnQtyMap = returnProductTaskItemService.getBaseMapper().getReturnQty(stockinReturnOrderPageResponses.stream().map(StockinReturnOrderPageResponse::getReturnTaskId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(StockinReturnOrderPageResponse::getReturnTaskId, StockinReturnOrderPageResponse::getReturnTotalQty));

            List<SupplierDto> supplierList = scmApiService.getSupplierInfoList(stockinReturnOrderPageResponses.stream().map(StockinReturnOrderPageResponse::getSupplierId).distinct().collect(Collectors.toList()));
            Map<String, String> supplyReturnStatusMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_STOCKIN_SUPPLY_RETURN_STATUS.getName());
            Map<String, String> returnTaskTypeMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_STOCKIN_RETURN_TASK_TYPE.getName());
            Map<String, String> returnNatureMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_STOCKIN_RETURN_NATURE.getName());
            Map<Integer, SupplierDto> supplierMap = supplierList.stream().collect(Collectors.toMap(SupplierDto::getSupplierId, Function.identity(), (v1, v2) -> v1));
            List<StockinReturnOrderPageResponse> responsesList = stockinReturnOrderPageResponses
                    .stream().peek(response -> {
                        response.setReturnTotalQty(returnQtyMap.getOrDefault(response.getReturnTaskId(), 0));
                        response.setStatusStr(supplyReturnStatusMap.get(response.getStatus()));
                        response.setReturnTypeStr(returnTaskTypeMap.get(response.getReturnType()));
                        response.setReworkStatusStr(StockinReworkStatusEnum.getDescByName(response.getReworkStatus()));
                        response.setReturnNatureStr(returnNatureMap.get(response.getReturnNature()));
                        response.setSupplierName(supplierMap.getOrDefault(response.getSupplierId(), new SupplierDto()).getSupplierName());
                        response.setPurchaserRealName(supplierMap.getOrDefault(response.getSupplierId(), new SupplierDto()).getContactPurchaserEmpName());
                    }).collect(Collectors.toList());
            pageResponse.setContent(responsesList);
        }
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    /**
     * 统计退货单总件数
     *
     * @param request 查询条件
     * @return 统计结果
     */
    public StockinReturnOrderStatisticsResponse statisticsStockinReturnOrderList(StockinReturnOrderPageRequest request) {
        StockinReturnOrderStatisticsResponse response = new StockinReturnOrderStatisticsResponse();
        buildStockinReturnOrderPageRequest(request);

        // 直接在数据库层面进行统计，避免内存溢出
        Integer totalReturnQty = returnProductTaskService.getBaseMapper().statisticsReturnTotalQty(request);
        response.setReturnTotalQty(totalReturnQty);

        return response;
    }

    /**
     * scm退货单明细列表数据源
     *
     * @param request
     * @return
     */
    public PageResponse<StockinReturnOrderItemPageResponse> pageItemList(StockinReturnOrderItemPageRequest request) {
        PageResponse<StockinReturnOrderItemPageResponse> pageResponse = new PageResponse<>();
        Page<StockinReturnProductTaskItemEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        StockinReturnProductTaskEntity returnProductTaskEntity = returnProductTaskService.getById(request.getReturnTaskId());
        if (returnProductTaskEntity == null) {
            throw new BusinessServiceException(String.format("退货任务【%d】不存在", request.getReturnTaskId()));
        }
        if (returnProductTaskEntity.getRealReturnProductTaskId() != null && returnProductTaskEntity.getRealReturnProductTaskId() > 0) {
            StockinReturnProductTaskEntity realReturnProductTaskEntity = returnProductTaskService.getBaseMapper().searchByReturnProductTaskIdListIgnoreTenant(Collections.singletonList(returnProductTaskEntity.getRealReturnProductTaskId())).get(0);
            request.setReturnTaskId(realReturnProductTaskEntity.getReturnProductTaskId());
            TenantContext.setTenant(realReturnProductTaskEntity.getLocation());
        }
        request.setReturnTaskItemIdList(getReturnTaskItemIdListByPurchasePlanNo(request.getPurchasePlanNo()));
        IPage<StockinReturnOrderItemPageResponse> iPage = returnProductTaskItemService.getBaseMapper().taskItemPageList(page, request);
        List<StockinReturnOrderItemPageResponse> recordList = iPage.getRecords();
        Map<Integer, List<PurchaseReturnItemNewOrderNoResponse.Item>> newPurchaseNoMap = Maps.newHashMap();
        if (StockinReturnNatureEnum.REFUND_RETURN.name().equals(returnProductTaskEntity.getReturnNature()) && !CollectionUtils.isEmpty(recordList)) {
            newPurchaseNoMap = gcApiService.getNewPurchaseNo(Collections.singletonList(request.getReturnTaskId())).stream().collect(Collectors.groupingBy(PurchaseReturnItemNewOrderNoResponse.Item::getWmsReturnTaskId));
        }
        Map<Integer, List<PurchaseReturnItemNewOrderNoResponse.Item>> finalNewPurchaseNoMap = newPurchaseNoMap;
        List<StockinReturnOrderItemPageResponse> responsesList = iPage.getRecords().stream().peek(response -> {
            response.setPreviewImageUrl(ImageUtils.convertImageUrl(response.getPreviewImageUrl()));
            response.setThumbnailImageUrl(ImageUtils.convertImageUrl(response.getThumbnailImageUrl()));
            response.setStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_SUPPLY_RETURN_STATUS.getName(), response.getStatus()));
            response.setReworkStatusStr(StockinReworkStatusEnum.getDescByName(response.getReworkStatus()));
            if (StockinReturnNatureEnum.REFUND_RETURN.name().equals(returnProductTaskEntity.getReturnNature())) {
                List<PurchaseReturnItemNewOrderNoResponse.Item> items = finalNewPurchaseNoMap.get(request.getReturnTaskId());
                response.setPurchasePlanNo(CollectionUtils.isEmpty(items) ? null : items.get(0).getNewOrderNo());
                response.setIsNew(StringUtils.hasText(response.getPurchasePlanNo()) ? IsDeletedConstant.DELETED : null);
            } else {
                response.setIsNew(StringUtils.hasText(response.getPurchasePlanNo()) ? IsDeletedConstant.NOT_DELETED : null);
            }
        }).collect(Collectors.toList());
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(responsesList);
        return pageResponse;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_RETURN_ORDER_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinReturnOrderPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockinReturnOrderPageRequest.class);
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.buildSkuAutoMatchList(downloadRequest.getSku());
        PageResponse<StockinReturnOrderPageResponse> pageResponse = pageList(downloadRequest);
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getContent()));
        return response;
    }

    /**
     * 到货确认 运输中=》已完成
     *
     * @param request
     */
    public void arrivalConfirm(IdListRequest request) {
        if (CollectionUtils.isEmpty(request.getIdList())) {
            throw new BusinessServiceException("至少选中一条数据进行确认");
        }
        List<StockinReturnProductTaskEntity> taskEntityList = returnProductTaskService.list(new LambdaQueryWrapper<StockinReturnProductTaskEntity>()
                .in(StockinReturnProductTaskEntity::getReturnProductTaskId, request.getIdList())
                .eq(StockinReturnProductTaskEntity::getStatus, ReturnProductStatusEnum.IN_TRANSIT.name()));
        if (CollectionUtils.isEmpty(taskEntityList)) {
            throw new BusinessServiceException("至少选中一条运输中的退货任务进行操作");
        }
        List<StockinReturnProductLog> logList = Lists.newArrayListWithExpectedSize(taskEntityList.size());
        taskEntityList.forEach(entity -> {
            entity.setStatus(ReturnProductStatusEnum.RETURN_SUCCESS.name());
            entity.setUpdateBy(loginInfoService.getName());
            logList.add(returnProductLogService.getLog(entity.getReturnProductTaskId(), ReturnProductConstant.ARRIVAL_CONFIRM, "操作到货确认", loginInfoService.getName(), loginInfoService.getIpAddress()));
        });
        returnProductTaskService.updateBatchById(taskEntityList);
        returnProductLogService.saveLogs(logList);
        returnProductTaskItemService.update(new UpdateWrapper<StockinReturnProductTaskItemEntity>().lambda()
                .in(StockinReturnProductTaskItemEntity::getReturnProductTaskId, request.getIdList())
                .set(StockinReturnProductTaskItemEntity::getStatus, ReturnProductStatusEnum.RETURN_SUCCESS.name())
                .set(StockinReturnProductTaskItemEntity::getUpdateBy, loginInfoService.getName()));
    }

    /**
     * 发货确认
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "returnDeliveryConfirm", lockKey = "#request.returnProductTaskIdList")
    public void deliveryConfirm(StockinReturnDeliveryConfirmRequest request) {
        if (Objects.isNull(request.getReturnProductTaskIdList()) || request.getReturnProductTaskIdList().isEmpty()) {
            throw new BusinessServiceException("请选择退货任务");
        }
        if (!ObjectUtils.isEmpty(request.getHandleMethod()) && 0 == request.getHandleMethod()) {
            if (StrUtil.isEmpty(request.getDeliveryType())) {
                throw new BusinessServiceException("请填写发货方式");
            }
            if (ObjectUtils.isEmpty(request.getFreightCarrier())) {
                throw new BusinessServiceException("请选择运费承担方");
            }
        }
        if (ReturnProductTaskDeliveryTypeEnum.LOGISTICS.name().equals(request.getDeliveryType())
                && (StrUtil.isEmpty(request.getLogisticsNo()) || Objects.isNull(request.getDeliveryDate()))) {
            throw new BusinessServiceException("【发货方式】为物流，【物流单号】和【发货日期】必填");
        }

        List<StockinReturnProductLog> logList = Lists.newArrayListWithExpectedSize(request.getReturnProductTaskIdList().size());
        List<StockinReturnProductTaskEntity> taskList = request.getReturnProductTaskIdList()
                .stream().distinct()
                .map(id -> {
                    StockinReturnProductTaskEntity returnProductTaskEntity = returnProductTaskService.getById(id);
                    if (returnProductTaskEntity == null) {
                        throw new BusinessServiceException(String.format("找不到退货 id【%s】", id));
                    }
                    if (!ReturnProductStatusEnum.READY_DELIVERY.name().equals(returnProductTaskEntity.getStatus())) {
                        throw new BusinessServiceException(String.format("请选择待发货的退货任务进行操作 id【%s】", id));
                    }
                    if (Objects.nonNull(returnProductTaskEntity.getRealReturnProductTaskId()) && returnProductTaskEntity.getRealReturnProductTaskId() > 0) {
                        StockinReturnProductTaskEntity taskEntity = returnProductTaskService.getBaseMapper().getByIdIgnore(returnProductTaskEntity.getRealReturnProductTaskId());
                        throw new BusinessServiceException(String.format("该单据为【%s】地区单据，请到切换到对应地区发货确认", LocationEnum.getNameBy(taskEntity.getLocation())));
                    }
                    BeanUtils.copyProperties(request, returnProductTaskEntity);
                    returnProductTaskEntity.setUpdateBy(loginInfoService.getName());
                    returnProductTaskEntity.setStatus(ReturnProductStatusEnum.IN_TRANSIT.name());
                    logList.add(returnProductLogService.getLog(id, ReturnProductConstant.DELIVERY_CONFIRM, "操作发货确认", loginInfoService.getName(), loginInfoService.getIpAddress()));

                    //其他备份退货任务确认发货
                    deliveryOthers(returnProductTaskEntity, request);

                    // 推送 api-etl
                    purchaseModuleService.pushReturnTaskToEtl(returnProductTaskEntity);
                    //发货时同步商通，同步.net生成退货单
                    stockinReturnProductTaskService.generateReturnOrderToErp(returnProductTaskEntity);
                    // 同步scm生成审核任务
                    generateReviewTask(returnProductTaskEntity);
                    return returnProductTaskEntity;
                }).collect(Collectors.toList());
        returnProductTaskService.updateBatchById(taskList);
        returnProductLogService.saveLogs(logList);
        List<StockinReturnProductTaskItemEntity> taskItemEntityList = returnProductTaskItemService.list(new LambdaQueryWrapper<StockinReturnProductTaskItemEntity>().in(StockinReturnProductTaskItemEntity::getReturnProductTaskId, request.getReturnProductTaskIdList()));
        taskItemEntityList.forEach(item -> {
            item.setStatus(ReturnProductStatusEnum.IN_TRANSIT.name());
            item.setUpdateBy(loginInfoService.getName());
        });
        returnProductTaskItemService.updateBatchById(taskItemEntityList);
        syncReturnDeliveryToVms(request);
        addPurchaseOrderLogToScm(taskItemEntityList, PurchaseOrderLogTypeEnum.WMS_RETURN_DELIVERY);
        taskList.forEach(this::sendMsg);
    }

    private void sendMsg(StockinReturnProductTaskEntity taskEntity) {
        try {
            SupplierEntity supplier = supplierService.getBySupplierId(taskEntity.getSupplierId());
            List<String> userNameList = new ArrayList<>(2);
            if (Objects.nonNull(supplier.getContactQcEmpId())) {
                SysUserInfo userInfoByUserId = userApiService.getUserInfoByUserId(supplier.getContactQcEmpId());
                Optional.ofNullable(userInfoByUserId).ifPresent(user -> userNameList.add(user.getUserAccount()));
            }

            if (Objects.nonNull(supplier.getContactPurchaserEmpId())) {
                SysUserInfo userInfoByUserId = userApiService.getUserInfoByUserId(supplier.getContactPurchaserEmpId());
                Optional.ofNullable(userInfoByUserId).ifPresent(user -> userNameList.add(user.getUserAccount()));
            }
            DingTalkCorpConversationMessageRequest request = new DingTalkCorpConversationMessageRequest();
            request.setUserNameList(userNameList);
            DingTalkCorpConversationMessageRequest.Markdown markdown = new DingTalkCorpConversationMessageRequest.Markdown();
            markdown.setTitle(String.format("工厂入库质检退货:【%s-%s】", supplier.getSupplierNo(), supplier.getContactPurchaserEmpName()));

            List<StockinReturnProductTaskItemEntity> taskItemList = returnProductTaskItemService.listByTaskId(taskEntity.getReturnProductTaskId());
            int total = taskItemList.stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum();

            StringBuilder content = new StringBuilder();
            content.append(String.format("退货ID:%s\\n退货总数:%s\\n退货日期:%s\\n退货专员:%s\\n退货商品明细:\\n", taskEntity.getReturnProductTaskId(), total, DateUtil.format(taskEntity.getOperateStartDate(), "yyyy-MM-dd"), taskEntity.getOperator()));
            taskItemList.forEach(taskItem -> content.append(String.format("规格编码:%s\\n退货件数:%s\\n退货原因:%s\\n", taskItem.getSku(), taskItem.getActualReturnQty(), taskItem.getUnqualifiedReason())));
            markdown.setText(content.toString());
            request.setMarkdown(markdown);
            request.setMsgType(DingTalkCorpConversationMessageRequest.MsgType.MARKDOWN);
            notifyApiService.sendCorpConversation(request);
        } catch (Exception e) {
            LOGGER.error(String.format("退货单【%s】发送通知失败：%s", taskEntity.getReturnProductTaskId(), e.getMessage()), e);
        }
    }

    // 同步scm生成审核任务
    private void generateReviewTask(StockinReturnProductTaskEntity returnProductTaskEntity) {
        if (!StrUtil.equals(TenantContext.getTenant(), LocationEnum.QUANZHOU.getLocation())
                || !StrUtil.equals(returnProductTaskEntity.getReturnNature(), StockinReturnNatureEnum.REFUND_RETURN.name())
                || !StrUtil.equals(returnProductTaskEntity.getReturnType(), StockinReturnTypeEnum.FACTORY_RETURN.name())) {
            return;
        }
        StockinReturnReviewTaskGenerateRequest reviewTaskGenerateRequest = new StockinReturnReviewTaskGenerateRequest();
        BeanUtilsEx.copyProperties(returnProductTaskEntity, reviewTaskGenerateRequest, "itemList");
        reviewTaskGenerateRequest.setReturnSpaceId(bdErpSpaceMappingService.getDefaultEntityBySpaceId(returnProductTaskEntity.getSpaceId()).getErpReturnSpaceId());
        reviewTaskGenerateRequest.setOperator(loginInfoService.getName());
        List<StockinReturnProductTaskItemEntity> stockinReturnProductTaskItemEntities = returnProductTaskItemService.listByTaskId(returnProductTaskEntity.getReturnProductTaskId());
        Map<String, Integer> skuQtyMapping = new HashMap<>();
        Map<String, String> skuReasonMapping = new HashMap<>();
        reviewTaskGenerateRequest.setItemList(stockinReturnProductTaskItemEntities.stream().map(item -> {
            StockinReturnProductTaskItem item1 = new StockinReturnProductTaskItem();
            BeanUtilsEx.copyProperties(item, item1);
            ProductSpecInfoEntity topBySku = specInfoService.findTopBySku(item.getSku());
            BdErpSpaceMappingEntity bdErpSpaceMappingEntity = stockinReturnProductTaskService.buildItemSpaceId(returnProductTaskEntity.getSpaceId(), item1.getBrandName(), item.getSourceAreaId());
            if (bdErpSpaceMappingEntity == null) {
                throw new BusinessServiceException("未找到区域对应的仓库映射关系");
            }
            item1.setSourceSpaceId(bdErpSpaceMappingEntity.getErpSpaceId());
            item1.setRealAmount(item.getActualReturnQty());
            item1.setSpaceName(bdErpSpaceMappingEntity.getErpSpaceName());
            item1.setSpaceId(bdErpSpaceMappingEntity.getErpSpaceId());
            item1.setSkc(topBySku.getSkc());
            item1.setSize(topBySku.getSize() == null ? "" : topBySku.getSize());
            skuQtyMapping.merge(item.getSku(), item.getActualReturnQty(), Integer::sum);
            if (StrUtil.isNotBlank(item.getUnqualifiedReason())) {
                skuReasonMapping.merge(item.getSku(), item.getUnqualifiedReason(), (v1, v2) -> v1 + ";" + v2);
            }
            return item1;
        }).collect(Collectors.toList()));
        String spaceNameJoin = reviewTaskGenerateRequest.getItemList().stream().map(StockinReturnProductTaskItem::getSpaceName).distinct().collect(Collectors.joining("、"));
        if (Objects.equals(returnProductTaskEntity.getHandleMethod(), 1)) {
            SendDingDingMessage sendDingDingMessage = sendDingDingMsg(spaceNameJoin, returnProductTaskEntity, stockinReturnProductTaskItemEntities, skuQtyMapping, skuReasonMapping);
            messageProducer.sendMessage(KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
            return;
        }
        if (Objects.equals(returnProductTaskEntity.getHandleMethod(), 2)) {
            SendDingDingMessage sendDingDingMessage = sendDingDingMsg(spaceNameJoin, returnProductTaskEntity, stockinReturnProductTaskItemEntities, skuQtyMapping, skuReasonMapping);
            messageProducer.sendMessage(KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
            return;
        }
        if (Objects.equals(returnProductTaskEntity.getHandleMethod(), 0)) {
            List<Integer> taskItemIdList = scmApiService.generateReviewTask(reviewTaskGenerateRequest);
            if (!CollectionUtils.isEmpty(taskItemIdList)) {
                returnProductTaskItemService.update(new LambdaUpdateWrapper<StockinReturnProductTaskItemEntity>()
                        .set(StockinReturnProductTaskItemEntity::getReworkStatus, StockinReworkStatusEnum.REFUSE.name())
                        .set(StockinReturnProductTaskItemEntity::getUpdateBy, "系统自动判定")
                        .in(StockinReturnProductTaskItemEntity::getId, taskItemIdList));
                returnProductTaskEntity.setReworkStatus(StockinReworkStatusEnum.REFUSE.name());
            } else {
                returnProductTaskItemService.update(new LambdaUpdateWrapper<StockinReturnProductTaskItemEntity>()
                        .set(StockinReturnProductTaskItemEntity::getReworkStatus, StockinReworkStatusEnum.PENDING.name())
                        .eq(StockinReturnProductTaskItemEntity::getReturnProductTaskId, returnProductTaskEntity.getReturnProductTaskId())
                        .ne(StockinReturnProductTaskItemEntity::getReworkStatus, StockinReworkStatusEnum.REFUSE.name()));
                returnProductTaskEntity.setReworkStatus(StockinReworkStatusEnum.PENDING.name());
                // 发送钉钉消息-- 通知：陈丽美-003039
                SendDingDingMessage sendDingDingMessage = sendDingDingMsg(spaceNameJoin, returnProductTaskEntity, stockinReturnProductTaskItemEntities, skuQtyMapping, skuReasonMapping);
                sendDingDingMessage.setUserNameList(Collections.singletonList("003039"));
                messageProducer.sendMessage(KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
            }
        }

    }

    private SendDingDingMessage sendDingDingMsg(String spaceName, StockinReturnProductTaskEntity returnProductTaskEntity, List<StockinReturnProductTaskItemEntity> stockinReturnProductTaskItemEntities, Map<String, Integer> skuQtyMapping, Map<String, String> skuReasonMapping) {
        List<String> notifyMsg = new ArrayList<>();
        String titleMsg = "";
        if (Objects.equals(returnProductTaskEntity.getHandleMethod(), 1)) {
            titleMsg = "跟进返修情况，请知悉";
        }
        if (Objects.equals(returnProductTaskEntity.getHandleMethod(), 2)) {
            titleMsg = "出库报次，不进行返工，请知悉";
        }
        if (Objects.equals(returnProductTaskEntity.getHandleMethod(), 0)) {
            titleMsg = "触发退货审核，请及时处理！";
        }
        notifyMsg.add("**退货处理通知**%n%n");
        notifyMsg.add(String.format("退货单ID：%s，%s%n", returnProductTaskEntity.getReturnProductTaskId(), titleMsg));
        notifyMsg.add(String.format("**工厂**：%s%n", returnProductTaskEntity.getSupplierName()));
        notifyMsg.add(String.format("**仓库**：%s%n", spaceName));
        notifyMsg.add(String.format("**处理方式**：%s%n", ReturnHandleMethodEnum.getValueByType(returnProductTaskEntity.getHandleMethod())));
        notifyMsg.add(String.format("**品牌**：%s%n", stockinReturnProductTaskItemEntities.get(0).getBrandName() == null ? "" : stockinReturnProductTaskItemEntities.get(0).getBrandName()));
        notifyMsg.add(String.format("**包装**：%s%n%n", VacuumEnum.getTextByKey(stockinReturnProductTaskItemEntities.get(0).getPackingMethod() == null ? "" : stockinReturnProductTaskItemEntities.get(0).getPackingMethod())));
        notifyMsg.add("**退货明细**：%n");
        skuQtyMapping.forEach((k, v) -> notifyMsg.add(String.format("- %s（%d）【%s】%n", k, v, skuReasonMapping.getOrDefault(k, ""))));
        SendDingDingMessage sendDingDingMessage = new SendDingDingMessage();
        sendDingDingMessage.setText(String.join("", notifyMsg));
        sendDingDingMessage.setUserNameList(Collections.singletonList(returnProductTaskEntity.getPurchaserUserName()));
        sendDingDingMessage.setMarkdownTitle("退货处理通知");
        sendDingDingMessage.setTextType("markdown");
        return sendDingDingMessage;
    }

    //    private void sendMsg(StockinReturnProductTaskEntity taskEntity) {
    //        try {
    //            SupplierEntity supplier = supplierService.getBySupplierId(taskEntity.getSupplierId());
    //            List<String> userNameList = Stream.of(supplier.getContactPurchaserEmpName(), supplier.getContactQcEmpName()).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
    //
    //            if (CollectionUtils.isEmpty(userNameList)) {
    //                throw new BusinessServiceException("发送人员为空");
    //            }
    //            DingTalkCorpConversationMessageRequest request = new DingTalkCorpConversationMessageRequest();
    //            request.setUserNameList(userNameList);
    //            DingTalkCorpConversationMessageRequest.Markdown markdown = new DingTalkCorpConversationMessageRequest.Markdown();
    //            markdown.setTitle(String.format("工厂入库质检退货:【%s-%s】", supplier.getSupplierNo(), supplier.getContactPurchaserEmpName()));
    //
    //            List<StockinReturnProductTaskItemEntity> taskItemList = returnProductTaskItemService.listByTaskId(taskEntity.getReturnProductTaskId());
    //            int total = taskItemList.stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum();
    //
    //            StringBuilder content = new StringBuilder();
    //            content.append(String.format("退货ID:%s\\n退货总数:%s\\n退货日期:%s\\n退货专员:%s\\n退货商品明细:\\n", taskEntity.getReturnProductTaskId(), total, DateUtil.format(taskEntity.getOperateStartDate(), "yyyy-MM-dd"), taskEntity.getOperator()));
    //            taskItemList.forEach(taskItem -> content.append(String.format("规格编码:%s\\n退货件数:%s\\n退货原因:%s\\n", taskItem.getSku(), taskItem.getActualReturnQty(), taskItem.getUnqualifiedReason())));
    //            markdown.setText(content.toString());
    //            request.setMarkdown(markdown);
    //            notifyApiService.sendCorpConversation(request);
    //        } catch (Exception e) {
    //            LOGGER.error(String.format("退货单【%s】发送通知失败：%s", taskEntity.getReturnProductTaskId(), e.getMessage()), e);
    //        }
    //    }

    /**
     * 其他备份退货任务确认发货
     *
     * @param task
     * @param request
     */
    private void deliveryOthers(StockinReturnProductTaskEntity task, StockinReturnDeliveryConfirmRequest request) {
        List<StockinReturnProductTaskEntity> otherTaskList;
        //如果实在总部确认发货，则通过分公司id查找
        if (LocationEnum.QUANZHOU.getLocation().equals(task.getLocation())) {
            otherTaskList = stockinReturnProductTaskMapper.searchByReturnProductTaskIdListIgnoreTenant(Collections.singletonList(task.getRealReturnProductTaskId()));
        } else {  //如果是再分公司确认发货，则找出分公司id为此id的退货单
            otherTaskList = stockinReturnProductTaskMapper.searchByRealReturnProductTaskIdListIgnoreTenant(Collections.singletonList(task.getReturnProductTaskId()));
        }
        if (CollectionUtils.isEmpty(otherTaskList))
            return;
        String oldTenant = TenantContext.getTenant();
        otherTaskList.forEach(otherTask -> {
            TenantContext.setTenant(otherTask.getLocation());
            BeanUtils.copyProperties(request, otherTask);
            otherTask.setUpdateBy(loginInfoService.getName());
            otherTask.setStatus(ReturnProductStatusEnum.IN_TRANSIT.name());
            returnProductTaskService.updateById(otherTask);
            StockinReturnProductLog log = returnProductLogService.getLog(otherTask.getReturnProductTaskId(), ReturnProductConstant.DELIVERY_CONFIRM, "操作发货确认（其他退货单）", loginInfoService.getName(), loginInfoService.getIpAddress());
            log.setLocation(LocationEnum.QUANZHOU.getLocation());
            returnProductLogService.saveLog(log);

            List<StockinReturnProductTaskItemEntity> taskItemEntityList = stockinReturnProductTaskItemMapper.listByTaskIdIgnoreTenant(otherTask.getReturnProductTaskId());
            taskItemEntityList.forEach(item -> {
                item.setStatus(ReturnProductStatusEnum.IN_TRANSIT.name());
                item.setUpdateBy(loginInfoService.getName());
            });
            returnProductTaskItemService.updateBatchById(taskItemEntityList);
        });
        TenantContext.setTenant(oldTenant);
    }

    // 新增采购单日志同步到scm
    public void addPurchaseOrderLogToScm(List<StockinReturnProductTaskItemEntity> allTaskItemEntityList, PurchaseOrderLogTypeEnum logTypeEnum) {
        List<StockinReturnProductTaskItemEntity> taskItemEntityList = allTaskItemEntityList.stream().filter(entity -> StringUtils.hasText(entity.getPurchasePlanNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            return;
        }
        taskItemEntityList.stream().collect(Collectors.groupingBy(StockinReturnProductTaskItemEntity::getReturnProductTaskId))
                .forEach((returnProductTaskId, itemEntityList) -> {
                    List<PurchaseOrderLogAddRequest.PurchaseOrderLogAddItem> addItemList = Lists.newArrayList();
                    itemEntityList.stream().collect(Collectors.groupingBy(t -> String.format("%s#%s", t.getPurchasePlanNo(), t.getSku())))
                            .forEach((key, list) -> {
                                StockinReturnProductTaskItemEntity taskItemEntity = list.get(0);
                                PurchaseOrderLogAddRequest.PurchaseOrderLogAddItem addItem = new PurchaseOrderLogAddRequest.PurchaseOrderLogAddItem();
                                addItem.setOrderNo(taskItemEntity.getPurchasePlanNo());
                                addItem.setContent(buildPurchaseOrderLogContent(logTypeEnum, taskItemEntity.getReturnProductTaskId(),
                                        taskItemEntity.getSku(), list.stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum()));
                                addItemList.add(addItem);
                            });
                    PurchaseOrderLogAddRequest purchaseOrderLogAddRequest = new PurchaseOrderLogAddRequest();
                    purchaseOrderLogAddRequest.setOrderLogType(logTypeEnum.getValue());
                    purchaseOrderLogAddRequest.setOperator(loginInfoService.getName());
                    purchaseOrderLogAddRequest.setReturnProductTaskId(returnProductTaskId);
                    purchaseOrderLogAddRequest.setItemList(addItemList);
                    scmApiService.addPurchaseOrderLog(purchaseOrderLogAddRequest);
                });
    }

    private String buildPurchaseOrderLogContent(PurchaseOrderLogTypeEnum logTypeEnum, Integer returnProductTaskId, String sku, Integer qty) {
        String content = null;
        switch (logTypeEnum) {
            case WMS_REWORK_RETURN:
                content = String.format("工厂退货ID:%d,sku：【%s】,共扫描%d件退货完成,状态为【待仓库发货】", returnProductTaskId, sku, qty);
                break;
            case WMS_RETURN_DELIVERY:
                content = String.format("工厂退货ID:%d,sku:【%s】,退货%d件已发货,状态为【运输中】", returnProductTaskId, sku, qty);
                break;
            case SUPPLIER_REWORK_RETURN_RECEIPT:
                content = String.format("工厂退货ID:%d,sku:【%s】,返工退货件数%d件,工厂已收货", returnProductTaskId, sku, qty);
                break;
            case SUPPLIER_REWORK_RETURN_DELIVERY:
                content = String.format("工厂退货ID:%d,sku:【%s】,返工退货发货件数%d件,工厂已发货", returnProductTaskId, sku, qty);
                break;
            default:
                break;
        }
        return content;
    }

    // 同步退货发货信息到supplier
    private void syncReturnDeliveryToVms(StockinReturnDeliveryConfirmRequest request) {
        SyncReturnDeliveryRequest syncReturnDeliveryRequest = new SyncReturnDeliveryRequest();
        BeanUtils.copyProperties(request, syncReturnDeliveryRequest);
        syncReturnDeliveryRequest.setReturnTaskIdList(request.getReturnProductTaskIdList());
        gcApiService.syncReturnDeliveryToVms(syncReturnDeliveryRequest);
    }

    @Override
    public List<StatusTabResponse> getTabs() {
        List<StatusTabResponse> tabs = returnProductTaskService.getBaseMapper().getTabs(Arrays.asList(ReturnProductStatusEnum.IN_TRANSIT.name(), ReturnProductStatusEnum.READY_DELIVERY.name()));
        List<String> statusList = ReturnProductStatusEnum.allStatusList();
        for (String status : statusList) {
            if (tabs.stream().noneMatch(tab -> status.equals(tab.getStatus()))) {
                StatusTabResponse response = new StatusTabResponse();
                response.setNum(0);
                response.setStatus(status);
                tabs.add(response);
            }
        }
        StatusTabResponse response = new StatusTabResponse();
        List<StatusTabResponse> newTabs = tabs.stream().filter(tab -> statusList.stream().anyMatch(status -> status.equals(tab.getStatus())))
                .sorted(Comparator.comparing(tab -> Enum.valueOf(ReturnProductStatusEnum.class, tab.getStatus()).ordinal())).collect(Collectors.toList());
        newTabs.forEach(tab -> {
            tab.setLabel(Enum.valueOf(ReturnProductStatusEnum.class, tab.getStatus()).getValue());
            tab.setName(tab.getStatus());
        });
        response.setNum(0);
        response.setLabel(StatusTabConstants.CN_ALL);
        response.setName(StatusTabConstants.ALL);
        response.setStatus(StatusTabConstants.ALL);
        newTabs.add(response);
        return newTabs;
    }

    @Override
    public TabUrlEnum tabType() {
        return TabUrlEnum.RETURN_ORDER;
    }

    public List<StockinReturnProductSearchQtyResponse> searchQtyByReturnProductIdList(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return returnProductService.list(new LambdaQueryWrapper<StockinReturnProductEntity>()
                        .in(StockinReturnProductEntity::getReturnProductId, idList))
                .stream().map(entity -> {
                    StockinReturnProductSearchQtyResponse response = new StockinReturnProductSearchQtyResponse();
                    BeanUtilsEx.copyProperties(entity, response);
                    return response;
                }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void returnForeComplete(List<Integer> returnTaskIdList) {
        if (CollectionUtils.isEmpty(returnTaskIdList)) {
            throw new BusinessServiceException("请至少选择一条数据进行强制完成");
        }
        List<StockinReturnProductTaskEntity> taskEntityList = returnProductTaskService.list(new LambdaQueryWrapper<StockinReturnProductTaskEntity>()
                .in(StockinReturnProductTaskEntity::getReturnProductTaskId, returnTaskIdList)
                .in(StockinReturnProductTaskEntity::getStatus, Arrays.asList(ReturnProductStatusEnum.READY_DELIVERY.name(), ReturnProductStatusEnum.IN_TRANSIT.name())));
        if (CollectionUtils.isEmpty(taskEntityList)) {
            throw new BusinessServiceException("只支持状态为【待仓库发货】【运输中】的退货任务进行强制完成");
        }

        // 分公司退货单
        List<Integer> realReturnTaskIdList = taskEntityList.stream().filter(o -> o.getRealReturnProductTaskId() != null && o.getRealReturnProductTaskId() > 0).map(StockinReturnProductTaskEntity::getRealReturnProductTaskId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(realReturnTaskIdList)) {
            taskEntityList = returnProductTaskService.getBaseMapper().searchByReturnProductTaskIdListIgnoreTenant(realReturnTaskIdList);
            TenantContext.setTenant(taskEntityList.get(0).getLocation());
        }

        List<StockinReturnProductLog> logList = Lists.newArrayListWithExpectedSize(taskEntityList.size());
        List<Integer> reworkReturnTaskIdList = taskEntityList.stream().map(StockinReturnProductTaskEntity::getReturnProductTaskId).collect(Collectors.toList());
        taskEntityList.forEach(taskEntity -> {
            if (ReturnProductStatusEnum.READY_DELIVERY.name().equals(taskEntity.getStatus())) {
                //现步骤为运输中推送商通，如强制完成的状态为待仓库发货则也需同步.net
                stockinReturnProductTaskService.generateReturnOrderToErp(taskEntity);
            }
            taskEntity.setStatus(ReturnProductStatusEnum.RETURN_SUCCESS.name());
            taskEntity.setUpdateBy(loginInfoService.getName());
            logList.add(returnProductLogService.getLog(taskEntity.getReturnProductTaskId(), ReturnProductConstant.FORCE_FINISH, "scm操作强制完成", loginInfoService.getName(), loginInfoService.getIpAddress()));
        });
        returnProductTaskService.updateBatchById(taskEntityList);
        returnProductLogService.saveLogs(logList);
        List<StockinReturnProductTaskItemEntity> taskItemEntityList = returnProductTaskItemService.list(new LambdaQueryWrapper<StockinReturnProductTaskItemEntity>().in(StockinReturnProductTaskItemEntity::getReturnProductTaskId, reworkReturnTaskIdList));
        taskItemEntityList.forEach(taskItemEntity -> {
            taskItemEntity.setUpdateBy(loginInfoService.getName());
            taskItemEntity.setStatus(ReturnProductStatusEnum.RETURN_SUCCESS.name());
        });
        returnProductTaskItemService.updateBatchById(taskItemEntityList);
        gcApiService.reworkConfirmReceipt(taskItemEntityList.stream().map(StockinReturnProductTaskItemEntity::getId).collect(Collectors.toList()));
        this.addPurchaseOrderLogToScm(taskItemEntityList, PurchaseOrderLogTypeEnum.SUPPLIER_REWORK_RETURN_RECEIPT);
    }

    @Transactional(rollbackFor = Exception.class)
    public void reworkReturnsReceipt(ReworkReturnsReceiptRequest request) {
        LoginInfoService.setName(request.getOperator());
        List<StockinReturnProductTaskItemEntity> taskItemEntityList = returnProductTaskItemService.list(new LambdaQueryWrapper<StockinReturnProductTaskItemEntity>().in(StockinReturnProductTaskItemEntity::getId, request.getWmsReturnTaskItemIdList()));
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            throw new BusinessServiceException("wms没有找到对应的退货任务明细");
        }
        List<StockinReturnProductLog> logList = Lists.newArrayListWithExpectedSize(taskItemEntityList.size());
        taskItemEntityList.forEach(itemEntity -> {
            itemEntity.setStatus(ReturnProductStatusEnum.RETURN_SUCCESS.getCode());
            itemEntity.setUpdateBy(loginInfoService.getName());
            logList.add(returnProductLogService.getLog(itemEntity.getReturnProductTaskId(), ReturnProductConstant.REWORK_RETURNS_RECEIPT,
                    String.format("supplier操作规格编码【%s】返工退货收货，任务明细id为%s", itemEntity.getSku(), itemEntity.getId()), loginInfoService.getName(), loginInfoService.getIpAddress()));
        });
        returnProductTaskItemService.updateBatchById(taskItemEntityList);
        returnProductLogService.saveLogs(logList);
        List<Integer> returnTaskIdList = taskItemEntityList.stream().map(StockinReturnProductTaskItemEntity::getReturnProductTaskId).distinct().collect(Collectors.toList());
        List<Integer> allReturnSuccessTaskIdList = Lists.newArrayList();
        returnProductTaskItemService.list(new LambdaQueryWrapper<StockinReturnProductTaskItemEntity>()
                        .in(StockinReturnProductTaskItemEntity::getReturnProductTaskId, returnTaskIdList))
                .stream().collect(Collectors.groupingBy(StockinReturnProductTaskItemEntity::getReturnProductTaskId))
                .forEach((returnTaskId, list) -> {
                    boolean allReturnSuccess = list.stream().allMatch(t -> ReturnProductStatusEnum.RETURN_SUCCESS.getCode().equals(t.getStatus()));
                    if (allReturnSuccess) {
                        allReturnSuccessTaskIdList.add(returnTaskId);
                    }
                });
        if (!CollectionUtils.isEmpty(allReturnSuccessTaskIdList)) {
            returnProductTaskService.update(new UpdateWrapper<StockinReturnProductTaskEntity>().lambda()
                    .in(StockinReturnProductTaskEntity::getReturnProductTaskId, allReturnSuccessTaskIdList)
                    .set(StockinReturnProductTaskEntity::getStatus, ReturnProductStatusEnum.RETURN_SUCCESS.name())
                    .set(StockinReturnProductTaskEntity::getUpdateBy, loginInfoService.getName()));
        }
        this.addPurchaseOrderLogToScm(taskItemEntityList, PurchaseOrderLogTypeEnum.SUPPLIER_REWORK_RETURN_RECEIPT);
        LoginInfoService.removeName();
    }

    public PageResponse<QueryWaitReturnRecordResponse> waitReturnRecord(ReturnRecordRequest request) {
        PageResponse<QueryWaitReturnRecordResponse> pageResponse = new PageResponse<>();
        request.setLocation(TenantContext.getTenant());
        if (StringUtils.hasText(request.getStockinOrderNo())) {
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(request.getStockinOrderNo());
            if (stockinOrderEntity.getOrderType().equals(1) && stockinOrderEntity.getRealStockinOrderId() != null && stockinOrderEntity.getRealStockinOrderId() > 0) {
                stockinOrderEntity = stockinOrderService.getBaseMapper().findTopByStockinOrderIdIgnoreTenant(stockinOrderEntity.getRealStockinOrderId());
                request.setStockinOrderNo(stockinOrderEntity.getStockinOrderNo());
            }
        }
        if (StringUtils.hasText(request.getPurchasePlanNo()) && request.getPurchasePlanNo().startsWith("V")) {
            request.setPurchasePlanNo(request.getPurchasePlanNo().substring(1));
        }
        IPage<QueryWaitReturnRecordResponse> iPage = returnProductService.getBaseMapper().queryWaitReturnRecord(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        pageResponse.setTotalCount(iPage.getTotal());
        pageResponse.setContent(iPage.getRecords());
        return pageResponse;
    }

    public PageResponse<QueryReworkedReturnedRecordResponse> reworkedReturnedRecord(ReturnRecordRequest request) {
        PageResponse<QueryReworkedReturnedRecordResponse> pageResponse = new PageResponse<>();
        request.setLocation(TenantContext.getTenant());
        if (StringUtils.hasText(request.getStockinOrderNo())) {
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(request.getStockinOrderNo());
            if (stockinOrderEntity.getOrderType().equals(1) && stockinOrderEntity.getRealStockinOrderId() != null && stockinOrderEntity.getRealStockinOrderId() > 0) {
                stockinOrderEntity = stockinOrderService.getBaseMapper().findTopByStockinOrderIdIgnoreTenant(stockinOrderEntity.getRealStockinOrderId());
                request.setStockinOrderNo(stockinOrderEntity.getStockinOrderNo());
            }
        }
        if (StringUtils.hasText(request.getPurchasePlanNo()) && request.getPurchasePlanNo().startsWith("V")) {
            request.setPurchasePlanNo(request.getPurchasePlanNo().substring(1));
        }
        IPage<QueryReworkedReturnedRecordResponse> iPage = returnProductTaskService.getBaseMapper().queryReworkedReturnedRecord(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        List<QueryReworkedReturnedRecordResponse> collect = iPage.getRecords().stream()
                .peek(t -> t.setStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_SUPPLY_RETURN_STATUS.getName(), t.getStatus()))).collect(Collectors.toList());
        pageResponse.setTotalCount(iPage.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    public PageResponse<QueryReworkedReturnedRecordResponse> reworkedWaitReceiveRecord(ReturnRecordRequest request) {
        PageResponse<QueryReworkedReturnedRecordResponse> pageResponse = new PageResponse<>();
        if (StringUtils.hasText(request.getStockinOrderNo())) {
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(request.getStockinOrderNo());
            if (stockinOrderEntity.getOrderType().equals(1) && stockinOrderEntity.getRealStockinOrderId() != null && stockinOrderEntity.getRealStockinOrderId() > 0) {
                stockinOrderEntity = stockinOrderService.getBaseMapper().findTopByStockinOrderIdIgnoreTenant(stockinOrderEntity.getRealStockinOrderId());
                request.setStockinOrderNo(stockinOrderEntity.getStockinOrderNo());
            }
        }
        request.setStatusList(Arrays.asList(ReturnProductStatusEnum.IN_TRANSIT.name(), ReturnProductStatusEnum.READY_DELIVERY.name()));
        IPage<QueryReworkedReturnedRecordResponse> iPage = returnProductTaskService.getBaseMapper().queryReworkedReturnedRecord(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        List<QueryReworkedReturnedRecordResponse> collect = iPage.getRecords().stream()
                .peek(t -> t.setStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_SUPPLY_RETURN_STATUS.getName(), t.getStatus())))
                .collect(Collectors.toList());
        pageResponse.setTotalCount(iPage.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    public void buildStockinReturnOrderPageRequest(StockinReturnOrderPageRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        request.setReturnTaskItemIdList(getReturnTaskItemIdListByPurchasePlanNo(request.getPurchasePlanNo()));
        if (CollectionUtils.isEmpty(request.getReturnTaskItemIdList()) || request.getReturnTaskItemIdList().contains(-1)) {
            return;
        }
        List<StockinReturnProductTaskItemEntity> taskItemEntityList = returnProductTaskItemService.listByIds(request.getReturnTaskItemIdList());
        // 明细在当前区域查不到，则为分公司原单
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            List<Integer> realReturnProductTaskIdList = returnProductTaskItemService.getBaseMapper().findAllByIdListIgnoreTenant(request.getReturnTaskItemIdList()).stream().map(StockinReturnProductTaskItemEntity::getReturnProductTaskId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(realReturnProductTaskIdList)) {
                return;
            }
            List<Integer> returnProductTaskIdList = returnProductTaskService.getBaseMapper().searchByRealReturnProductTaskIdListIgnoreTenant(realReturnProductTaskIdList).stream().map(StockinReturnProductTaskEntity::getReturnProductTaskId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(returnProductTaskIdList)) {
                request.setReturnIdList(Collections.singletonList(-1));
                return;
            }
            request.setReturnIdList(returnProductTaskIdList);
            request.setReturnTaskItemIdList(Lists.newArrayList());
            request.setPurchasePlanNo(null);
        }
    }

    /**
     * 根据采购单号获取退货明细id
     *
     * @param purchasePlanNo
     * @return
     */
    public List<Integer> getReturnTaskItemIdListByPurchasePlanNo(String purchasePlanNo) {
        if (!StringUtils.hasText(purchasePlanNo)) {
            return Lists.newArrayList();
        }
        List<Integer> returnTaskItemIdList = returnProductTaskItemService.list(new LambdaQueryWrapper<StockinReturnProductTaskItemEntity>()
                        .eq(StockinReturnProductTaskItemEntity::getPurchasePlanNo, purchasePlanNo)).stream()
                .map(StockinReturnProductTaskItemEntity::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(returnTaskItemIdList)) {
            returnTaskItemIdList = gcApiService.getWmsReturnTaskItemIdList(purchasePlanNo);
        }
        if (CollectionUtils.isEmpty(returnTaskItemIdList)) {
            return Collections.singletonList(-1);
        } else {
            return returnTaskItemIdList;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLogisticsNo(StockinReturnDeliveryLogisticsRequest request) {
        if (Objects.isNull(request.getReturnProductTaskId())) {
            throw new BusinessServiceException("请选择退货任务");
        }
        StockinReturnProductTaskEntity returnProductTaskEntity = returnProductTaskService.getById(request.getReturnProductTaskId());
        if (returnProductTaskEntity == null) {
            throw new BusinessServiceException(String.format("找不到退货 id【%s】", request.getReturnProductTaskId()));
        }
        if (ReturnProductStatusEnum.RETURN_SUCCESS.name().equals(returnProductTaskEntity.getStatus())) {
            throw new BusinessServiceException(String.format("的退货任务已退货完成不可修改 id【%s】", request.getReturnProductTaskId()));
        }
        if (!ObjectUtils.isEmpty(request.getHandleMethod()) && 0 == request.getHandleMethod()) {
            if (StrUtil.isEmpty(request.getDeliveryType())) {
                throw new BusinessServiceException("请填写发货方式");
            }
            if (ObjectUtils.isEmpty(request.getFreightCarrier())) {
                throw new BusinessServiceException("请选择运费承担方");
            }
        }
        if (ReturnProductTaskDeliveryTypeEnum.LOGISTICS.name().equals(request.getDeliveryType())
                && (StrUtil.isEmpty(request.getLogisticsNo()) || Objects.isNull(request.getDeliveryDate()))) {
            throw new BusinessServiceException("【发货方式】为物流，【物流单号】和【发货日期】必填");
        }
        BeanUtils.copyProperties(request, returnProductTaskEntity);
        returnProductTaskService.updateById(returnProductTaskEntity);
        StockinReturnDeliveryConfirmRequest syncRequest = new StockinReturnDeliveryConfirmRequest();
        syncRequest.setReturnProductTaskIdList(Collections.singletonList(request.getReturnProductTaskId()));
        BeanUtils.copyProperties(request, syncRequest);
        syncReturnDeliveryToVms(syncRequest);
    }

    public void feedBackReviewResult(FeedBackWmsTaskReviewResultRequest request) {
        List<StockinReturnProductTaskItemEntity> itemEntities = returnProductTaskItemService.listByIds(request.getTaskItemIds());
        List<StockinReturnProductTaskEntity> taskEntityList = returnProductTaskService.listByIds(request.getTaskIds());
        itemEntities.forEach(item -> item.setReworkStatus(request.getReviewStatus()));
        taskEntityList.forEach(task -> {
            if (StrUtil.equals(StockinReworkStatusEnum.REFUSE.name(), task.getReworkStatus())) {
                // 不返工优先展示，不再更改
                return;
            }
            task.setReworkStatus(request.getReviewStatus());
            returnProductTaskService.updateById(task);
        });
        returnProductTaskItemService.updateBatchById(itemEntities);
    }
}
