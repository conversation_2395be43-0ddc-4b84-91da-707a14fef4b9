package com.nsy.wms.business.service.stockout;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.stockout.CustomsDeclareCustomerOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.CustomsDeclareStockoutOrderStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareCustomerOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareCustomerOrderResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareCustomerBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareDocumentItemBo;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareCustomerOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareCustomerOrderMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class StockoutCustomsDeclareCustomerOrderService extends ServiceImpl<StockoutCustomsDeclareCustomerOrderMapper, StockoutCustomsDeclareCustomerOrderEntity> {

    @Resource
    public StockoutCustomsDeclareCustomerOrderItemService stockoutCustomsDeclareCustomerOrderItemService;
    @Resource
    public StockoutCustomsDeclareDocumentService stockoutCustomsDeclareDocumentService;

    private final Map<String, StockoutCustomsDeclareCustomerBo> customerBoMap = new HashMap<>();

    public StockoutCustomsDeclareCustomerOrderService() {
        customerBoMap.put("NSY HOLDING GROUP CO., LIMITED", new StockoutCustomsDeclareCustomerBo("NSY HOLDING GROUP CO., LIMITED", "13960424169", "CHINA HK", "RM 1002 EASEY COMM BLDG 253-261 HENNESSY ROAD WANCHAI HK", "<EMAIL>"));
        customerBoMap.put("MAX GLORY INC", new StockoutCustomsDeclareCustomerBo("MAX GLORY INC", "17632259463", "United States", "489 CYPRESS ST SAN DIMAS, CA 91773", "<EMAIL>"));
        customerBoMap.put("DLM INTERNATIONAL TRADING CO., LIMITED", new StockoutCustomsDeclareCustomerBo("DLM INTERNATIONAL TRADING CO., LIMITED", "15880963444", "CHINA HK", "RM 1002 EASEY COMM BLDG 253-261 HENNESSY ROAD WANCHAI HK", "<EMAIL>"));
        customerBoMap.put("LEYING INTERNATIONAL TRADING LIMITED", new StockoutCustomsDeclareCustomerBo("LEYING INTERNATIONAL TRADING LIMITED", "13960424169", "CHINA HK", "RM 1002,10/F EASEY COMM BLDG 253-261 HENNESSY RD WAN CHAI HONG KONG", "<EMAIL>"));
        customerBoMap.put("MAX VERVE PTE. LTD.", new StockoutCustomsDeclareCustomerBo("MAX VERVE PTE. LTD.", "15880806617", "Singapore", "112ROBINSON ROAD #03-01 ROBINSON 112 SINGAPORE 068902", "<EMAIL>"));
        customerBoMap.put("LEXIN INTERNATIONAL TRADING LIMITED", new StockoutCustomsDeclareCustomerBo("LEXIN INTERNATIONAL TRADING LIMITED", "13960424169", "CHINA HK", "FLAT/RM 604 6/F EASEY COMM BLDG 253-261 HENNESSY ROAD WANCHAI HK", "<EMAIL>"));
    }


    /**
     * 分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutCustomsDeclareCustomerOrderResponse> pageList(StockoutCustomsDeclareCustomerOrderPageRequest request) {
        Page<StockoutCustomsDeclareCustomerOrderResponse> pageResult = this.baseMapper.findPage(new Page<>(request.getPageIndex(), request.getPageSize(), false), request);

        pageResult.getRecords().forEach(temp -> temp.setStatus(CustomsDeclareStockoutOrderStatusEnum.of(temp.getStatus())));
        PageResponse<StockoutCustomsDeclareCustomerOrderResponse> response = new PageResponse<>();
        response.setContent(pageResult.getRecords());
        response.setTotalCount(this.baseMapper.countPage(request));
        return response;
    }

    /**
     * 明细
     *
     * @param declareCustomerOrderId
     * @return
     */
    public StockoutCustomsDeclareCustomerOrderResponse detail(Integer declareCustomerOrderId) {
        StockoutCustomsDeclareCustomerOrderEntity declareCustomerOrder = getById(declareCustomerOrderId);
        if (ObjectUtil.isNull(declareCustomerOrder))
            throw new BusinessServiceException("客户订单不存在");

        StockoutCustomsDeclareCustomerOrderResponse temp = BeanUtil.toBean(declareCustomerOrder, StockoutCustomsDeclareCustomerOrderResponse.class);
        temp.setStatus(CustomsDeclareCustomerOrderStatusEnum.of(temp.getStatus()));
        return temp;
    }


    /**
     * 新增
     *
     * @param declareDocumentItemList
     */
    @Transactional
    public StockoutCustomsDeclareCustomerOrderEntity add(String orderNo, List<StockoutCustomsDeclareDocumentItemBo> declareDocumentItemList, StockoutCustomsDeclareFormEntity form) {
        if (CollectionUtil.isEmpty(declareDocumentItemList)) {
            throw new BusinessServiceException(String.format("报关单据明细为空 %s %s ", form.getProtocolNo(), form.getgNo()));
        }

        //通过fbaShipmentId查找客户订单，存在则跳过，不存在则新增
        StockoutCustomsDeclareCustomerOrderEntity entity = findByOrderNo(orderNo);
        if (ObjectUtil.isNull(entity)) {
            entity = new StockoutCustomsDeclareCustomerOrderEntity();
            entity.setOrderNo(orderNo);
            entity.setStoreName("亚马逊美国9FBA");
            entity.setLogisticsCompany("哈盛美国空运");
            entity.setLogisticsNo("HSMG" + DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(4));
            entity.setSpaceId(1);
            entity.setSpaceName("新时颖主仓库");
            entity.setStatus(CustomsDeclareCustomerOrderStatusEnum.DELIVERY.name());
            entity.setCreateBy("王涵瑜");
            entity.setCreateDate(fetchCreateDate(form.getStockoutDate()));
            //发货时间 - 关单的出库时间
            entity.setDeliveryDate(form.getStockoutDate());
            StockoutCustomsDeclareDocumentEntity declareDocumentEntity = stockoutCustomsDeclareDocumentService.findByDeclareDocumentNo(form.getProtocolNo());
            if (ObjectUtil.isNull(declareDocumentEntity)) {
                throw new BusinessServiceException(String.format("找不到报关单据  %s", form.getProtocolNo()));
            }
            //根据单据的客户查找
            StockoutCustomsDeclareCustomerBo stockoutCustomsDeclareCustomerBo = null;
            if (StrUtil.isNotEmpty(declareDocumentEntity.getCustomer())) {
                stockoutCustomsDeclareCustomerBo = customerBoMap.get(declareDocumentEntity.getCustomer());
            }
            //找不到默认取第一个
            if (ObjectUtil.isNull(stockoutCustomsDeclareCustomerBo)) {
                stockoutCustomsDeclareCustomerBo = customerBoMap.values().iterator().next();
            }
            BeanUtil.copyProperties(stockoutCustomsDeclareCustomerBo, entity);
            save(entity);
        }
        //通过declareDocumentItemId 查找明细，存在则跳过，不存在则新增

        StockoutCustomsDeclareCustomerOrderEntity finalEntity = entity;
        declareDocumentItemList.forEach(declareDocumentItem -> {
            //新增明细
            stockoutCustomsDeclareCustomerOrderItemService.add(finalEntity, declareDocumentItem);
        });

        return entity;
    }


    /**
     * 通过订单号查找
     *
     * @param orderNo
     * @return
     */
    public StockoutCustomsDeclareCustomerOrderEntity findByOrderNo(String orderNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclareCustomerOrderEntity>()
                .eq(StockoutCustomsDeclareCustomerOrderEntity::getOrderNo, orderNo)
                .last("limit 1"));
    }

    /**
     * 通过订单号查询
     *
     * @param orderNo
     * @return
     */
    public StockoutCustomsDeclareCustomerOrderEntity getByOrderNo(String orderNo) {
        StockoutCustomsDeclareCustomerOrderEntity customerOrder = findByOrderNo(orderNo);
        if (ObjectUtil.isNull(customerOrder))
            throw new BusinessServiceException(String.format("订单 %s 不存在", orderNo));
        return customerOrder;
    }

    /**
     * 创建时间 26 day before
     *
     * @param stockoutDate
     * @return
     */
    private Date fetchCreateDate(Date stockoutDate) {
        // 计算26天前的日期
        Date past = DateUtil.offsetDay(stockoutDate, -26);

        // 随机生成9点到19点之间的一个时间
        int hour = RandomUtil.randomInt(9, 19); // 包含9和19
        int minute = RandomUtil.randomInt(0, 60);
        int second = RandomUtil.randomInt(0, 60);
        // 生成具体时间
        return DateUtil.parse(DateUtil.format(past, "yyyy-MM-dd") + " " + String.format("%02d:%02d:%02d", hour, minute, second));
    }

}