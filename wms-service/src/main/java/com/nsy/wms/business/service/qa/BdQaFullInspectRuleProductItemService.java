package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.response.qa.BdQaFullInspectRuleItemInfoResponse;
import com.nsy.api.wms.response.qa.BdQaFullInspectRuleProductItemInfoResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.qa.BdQaFullInspectRuleProductItemEntity;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import com.nsy.wms.repository.jpa.mapper.qa.BdQaFullInspectRuleProductItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 15:37
 */
@Service
public class BdQaFullInspectRuleProductItemService extends ServiceImpl<BdQaFullInspectRuleProductItemMapper, BdQaFullInspectRuleProductItemEntity> {

    @Autowired
    private LoginInfoService loginInfoService;

    @Autowired
    private ProductInfoService productInfoService;

    @Autowired
    private SupplierService supplierService;

    public void buildProductInfo(Integer ruleId, BdQaFullInspectRuleItemInfoResponse response) {
        List<BdQaFullInspectRuleProductItemInfoResponse> prdouctInfoList = this.getBaseMapper().getProductInfoList(ruleId);
        if (CollectionUtil.isEmpty(prdouctInfoList)) {
            return;
        }
        response.setProductInfoList(prdouctInfoList);
    }

    /**
     * 保存全检商品明细信息
     *
     * @param saveInfoList
     * @param ruleId
     */
    public void saveProductInfo(List<BdQaFullInspectRuleProductItemInfoResponse> saveInfoList, Integer ruleId) {
        if (CollectionUtil.isEmpty(saveInfoList)) {
            return;
        }
        List<String> spuList = saveInfoList.stream().filter(detail -> StringUtils.hasText(detail.getSpu())).map(BdQaFullInspectRuleProductItemInfoResponse::getSpu).collect(Collectors.toList());
        List<ProductInfoEntity> productInfoList = productInfoService.findAllBySpuList(spuList);
        if (CollectionUtil.isEmpty(productInfoList)) {
            throw new RuntimeException("未找到商品信息");
        }
        Map<String, Integer> spuMap = productInfoList.stream().filter(it -> it.getProductId() != null && StringUtils.hasText(it.getSpu())).collect(Collectors.toMap(ProductInfoEntity::getSpu, ProductInfoEntity::getProductId, (v1, v2) -> v1));
        List<BdQaFullInspectRuleProductItemEntity> entityList = new ArrayList<>();
        saveInfoList.forEach(detail -> {
            if (!StringUtils.hasText(detail.getSpu())) {
                throw new RuntimeException("SPU为必填项");
            }
            if (Objects.isNull(detail.getSupplierId())) {
                throw new RuntimeException("工厂为必填项");
            }
            SupplierEntity supplierInfo = supplierService.getBySupplierId(detail.getSupplierId());
            if (Objects.isNull(supplierInfo)) {
                throw new RuntimeException(String.format("%s未找到工厂信息", detail.getSupplierName()));
            }
            BdQaFullInspectRuleProductItemEntity entity = new BdQaFullInspectRuleProductItemEntity();
            entity.setRuleId(ruleId);
            entity.setSupplierId(supplierInfo.getSupplierId());
            entity.setSpu(detail.getSpu());
            entity.setProductId(spuMap.get(detail.getSpu()));
            entity.setCreateBy(loginInfoService.getName());
            entity.setUpdateBy(loginInfoService.getName());
            entityList.add(entity);
        });
        this.saveBatch(entityList);
    }

    public void removeAllProductInfoByRuleId(Integer ruleId) {
        LambdaQueryWrapper<BdQaFullInspectRuleProductItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdQaFullInspectRuleProductItemEntity::getRuleId, ruleId);
        // 执行删除操作
        this.remove(wrapper);
    }
}
