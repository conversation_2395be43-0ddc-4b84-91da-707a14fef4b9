package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ErpAddPositionRequest {
    @JsonProperty("PositionCode")
    private String positionCode;

    @JsonProperty("PositionName")
    private String positionName;

    @JsonProperty("SpaceId")
    private Integer spaceId;

    @JsonProperty("SpaceName")
    private String spaceName;

    @JsonProperty("SortOrder")
    private Integer sortOrder;
    // 是否启用（0：启用；1：未启用）
    @JsonProperty("EnableFlag")
    private Integer enableFlag;
    // 库区
    @JsonProperty("GroupNum")
    private String groupNum;
    // 发货地点:泉州.Quanzhou,广州.Guangzhou
    @JsonProperty("DeliveryLocation")
    private String deliveryLocation;
    // 库位类型（0：发货库位，1：库存库位，2：虚拟库位  8:越库库位 9：店铺库位）
    @JsonProperty("PositionType")
    private Integer positionType;

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(Integer enableFlag) {
        this.enableFlag = enableFlag;
    }

    public String getGroupNum() {
        return groupNum;
    }

    public void setGroupNum(String groupNum) {
        this.groupNum = groupNum;
    }

    public String getDeliveryLocation() {
        return deliveryLocation;
    }

    public void setDeliveryLocation(String deliveryLocation) {
        this.deliveryLocation = deliveryLocation;
    }

    public Integer getPositionType() {
        return positionType;
    }

    public void setPositionType(Integer positionType) {
        this.positionType = positionType;
    }
}
