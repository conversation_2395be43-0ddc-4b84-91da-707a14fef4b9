package com.nsy.wms.business.manage.notify;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.wms.business.manage.notify.request.DingTalkCorpConversationMessageRequest;
import com.nsy.wms.business.manage.notify.request.DingTalkMessageRequest;
import com.nsy.wms.business.manage.notify.request.DingTalkRobotWebhookMarkdownSendRequest;
import com.nsy.wms.business.manage.notify.request.DingTalkRobotWebhookSendRequest;
import com.nsy.wms.business.manage.notify.response.DingTalkMessageResponse;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.List;
import java.util.Objects;

/**
 * User: Emily
 * Date: 2019/11/26
 */
@Service
public class NotifyApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NotifyApiService.class);

    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.notify}")
    private String notifyServiceUrl;
    @Value("${dingding.robotAccessToken.orderMonitor}")
    private String notifyAccessToken;
    //抢仓群
    @Value("${dingding.robotAccessToken.temuOrderMonitor}")
    private String temuNotifyAccessToken;
    //抓单群
    @Value("${dingding.robotAccessToken.temuGrabOrderMonitor}")
    private String temuGrabNotifyAccessToken;

    @Value("${dingding.robotAccessToken.spaceOverseaMonitor}")
    private String spaceOverseaMonitor;


    public void sendWebhookMessage(DingTalkRobotWebhookSendRequest request) {
        try {
            request.setAccessToken(notifyAccessToken);
            String uri = String.format("%s/ding-talk/send-webhook-message", notifyServiceUrl);
            this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public void sendWebhookStockCheckMarkdownMessage(DingTalkRobotWebhookMarkdownSendRequest request) {
        try {
            request.setAccessToken(spaceOverseaMonitor);
            String uri = String.format("%s/ding-talk/send-webhook-message", notifyServiceUrl);
            this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public void sendTemuWebhookMessage(DingTalkRobotWebhookSendRequest request) {
        try {
            request.setAccessToken(temuNotifyAccessToken);
            String uri = String.format("%s/ding-talk/send-webhook-message", notifyServiceUrl);
            this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public void sendTemuGrabWebhookMessage(DingTalkRobotWebhookSendRequest request) {
        try {
            request.setAccessToken(temuGrabNotifyAccessToken);
            String uri = String.format("%s/ding-talk/send-webhook-message", notifyServiceUrl);
            this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public void sendOverseaSpaceCheckWebhookMessage(DingTalkRobotWebhookSendRequest request) {
        try {
            request.setAccessToken(spaceOverseaMonitor);
            String uri = String.format("%s/ding-talk/send-webhook-message", notifyServiceUrl);
            this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public DingTalkMessageResponse sendTextDingTalkMessage(List<String> userNameList, String text) {
        try {
            DingTalkMessageRequest request = new DingTalkMessageRequest();
            request.setUserNameList(userNameList);
            request.setMsgType(DingTalkMessageRequest.MsgType.TEXT);
            DingTalkMessageRequest.Text textModel = new DingTalkMessageRequest.Text();
            textModel.setContent(text);
            request.setText(textModel);
            LOGGER.info("dingTalkRequest:{}", JsonMapper.toJson(request));
            String uri = String.format("%s/ding-talk/message", notifyServiceUrl);
            ResponseEntity<DingTalkMessageResponse> responseEntity = this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
            LOGGER.info("dingTalkResponse:{}", JsonMapper.toJson(responseEntity));
            return responseEntity.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new DingTalkMessageResponse();
    }


    public DingTalkMessageResponse sendMarkdownDingTalkMessage(String title, String text, List<String> userNameList) {
        try {
            DingTalkMessageRequest.Markdown markdown = new DingTalkMessageRequest.Markdown();
            markdown.setText(text);
            markdown.setTitle(title);

            DingTalkMessageRequest request = new DingTalkMessageRequest();
            request.setUserNameList(userNameList);
            request.setMsgType(DingTalkMessageRequest.MsgType.MARKDOWN);
            request.setMarkdown(markdown);

            LOGGER.info("dingTalkRequest:{}", JsonMapper.toJson(request));
            String uri = String.format("%s/ding-talk/message", notifyServiceUrl);
            ResponseEntity<DingTalkMessageResponse> responseEntity = this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
            LOGGER.info("dingTalkResponse:{}", JsonMapper.toJson(responseEntity));
            return responseEntity.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new DingTalkMessageResponse();
    }

    //发送工作通知
    public void sendCorpConversation(DingTalkCorpConversationMessageRequest request) {
        try {
            LOGGER.info("发送通知 {} ", JsonMapper.toJson(request));
            String uri = String.format("%s/ding-talk/message", notifyServiceUrl);
            ResponseEntity<DingTalkMessageResponse> responseEntity = this.restTemplate.postForEntity(uri, request, DingTalkMessageResponse.class);
            if (responseEntity.getStatusCode().value() != HttpStatus.OK.value()) {
                throw new BusinessServiceException("发送工作通知失败, 请求失败");
            }
            DingTalkMessageResponse result = responseEntity.getBody();
            if (Objects.isNull(result)) {
                throw new BusinessServiceException("发送工作通知失败, 返回数据为空");
            }
            if (!result.isSuccess()) {
                throw new BusinessServiceException(String.format("发送工作通知失败, %s", result.getMessage()));
            }
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
        }
    }
}
