package com.nsy.wms.business.service.print.stockout;


import com.nsy.api.wms.enumeration.bd.PrintTypeEnum;
import com.nsy.api.wms.request.print.PrintDistributionRequest;
import com.nsy.api.wms.response.stockout.FbaTransparencyPrintRequest;
import com.nsy.wms.business.service.print.IPrintService;
import com.nsy.wms.utils.JsonMapper;
import io.jsonwebtoken.lang.Collections;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 透明计划融合标签打印服务
 * <AUTHOR>
 * @date 2024/11/8 13:43
 */
@Service
public class TransparencyCombinePrintService implements IPrintService {

    @Override
    public String templateType() {
        return PrintTypeEnum.WMS_PRODUCT_TRANSPARENCY_COMBINE.name();
    }

    @Override
    public List<Map<String, Object>> dataAssembly(PrintDistributionRequest request) {
        List<Map<String, Object>> result = new ArrayList<>();
        FbaTransparencyPrintRequest printParams = JsonMapper.fromJson(request.getRequestParams(), FbaTransparencyPrintRequest.class);
        if (!Collections.isEmpty(printParams.getReadyPrintItems())) {
            // 打印测试示例
            printParams.getReadyPrintItems().forEach(item -> {
                Map<String, Object> itemMap = JsonMapper.convertToMap(item);
                if (!CollectionUtils.isEmpty(item.getTransparencyCode())) {
                    AtomicInteger barcodeIndex = new AtomicInteger();
                    item.getTransparencyCode().forEach(i -> {
                        Map<String, Object> map = new HashMap<>(itemMap);
                        map.put("transparencyCodeValue", i);
                        map.put("barcodeIndex", String.valueOf(barcodeIndex.incrementAndGet()));
                        result.add(map);
                    });
                } else {
                    result.add(itemMap);
                }
            });
        }
        return result;
    }
}
