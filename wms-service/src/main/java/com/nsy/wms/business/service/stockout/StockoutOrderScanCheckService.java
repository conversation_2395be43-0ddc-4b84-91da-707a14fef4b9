package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.ProcessConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.bd.StockoutNoticeLackResponse;
import com.nsy.api.wms.domain.product.ProductLocationInfo;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemSourcePositionBo;
import com.nsy.api.wms.domain.stock.StockInternalBoxOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTask;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItem;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItemDetail;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItemId;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockoutScanTaskDetailEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderLackSourceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderScanTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskCreateEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderScanTaskItemDetailRequest;
import com.nsy.api.wms.response.stockout.StockoutConfirmShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderLackResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderScanIsNoticeLackResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderScanTaskValidateResponse;
import com.nsy.api.wms.response.stockout.StockoutScanBarcodeResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.bo.stockout.StockoutNoticeLackBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutNoticeLackItemBo;
import com.nsy.wms.business.domain.dto.stockout.StockoutScanDetailMessage;
import com.nsy.wms.business.domain.dto.stockout.StockoutVasTaskCreateDTO;
import com.nsy.wms.business.manage.erp.request.ErpFinishPartialPickItemRequest;
import com.nsy.wms.business.manage.erp.request.ErpFinishPartialPickRequest;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPrematchRemoveService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stock.StockShippingPositionService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.ship.StockoutShipService;
import com.nsy.wms.business.service.stockout.valid.StockoutOrderValid;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.product.ProductStoreSkuMappingEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockShippingPositionEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class StockoutOrderScanCheckService {

    private final Set<String> platformSet = Sets.newHashSet(StockoutOrderPlatformEnum.AMAZON.getName(), StockoutOrderPlatformEnum.TIKTOK.getName());

    private final Set<String> positionTypeSet = Sets.newHashSet(BdPositionTypeEnum.ACTIVITY_POSITION.name(), BdPositionTypeEnum.STORE_POSITION.name());

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderScanCheckService.class);
    @Autowired
    private StockoutOrderScanTaskService scanTaskService;
    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private StockoutBatchOrderService batchOrderService;
    @Autowired
    private StockoutBatchService batchService;
    @Autowired
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    private StockService stockService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockoutOrderScanLogService scanLogService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private StockoutOrderScanTaskItemService taskItemService;
    @Autowired
    private StockoutOrderLackService lackService;
    @Autowired
    private BdPositionService positionService;
    @Autowired
    private StockoutOrderLackScanConfirmService lackScanConfirmService;
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    private StockoutShipmentItemService shipmentItemService;
    @Autowired
    private StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    private ProductStoreSkuMappingService mappingService;
    @Autowired
    private StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    private StockoutLogQueueService stockoutLogQueueService;
    @Autowired
    private StockoutOrderShipService stockoutOrderShipService;
    @Autowired
    private StockoutOrderScanCheckInfoService stockoutOrderScanCheckInfoService;
    @Autowired
    private StockoutOrderNoticeLackService stockoutOrderNoticeLackService;
    @Autowired
    private StockoutOrderOperateService stockoutOrderOperateService;
    @Autowired
    private StockoutOrderMapper stockoutOrderMapper;
    @Autowired
    private MessageProducer producer;
    @Autowired
    private ApplicationContext context;
    @Autowired
    private StockoutTransparencyCodeService transparencyCodeService;
    @Autowired
    private StockoutBatchSplitCommonService stockoutBatchSortCommonService;
    @Autowired
    private StockShippingPositionService shippingPositionService;
    @Autowired
    private StockoutErpPickService stockoutErpPickService;
    @Autowired
    private StockPrematchRemoveService stockPrematchRemoveService;
    @Autowired
    private StockoutShipService stockoutShipService;
    @Autowired
    private StockoutOrderOperateService operateService;
    @Autowired
    private StockoutPickingExceptionService stockoutPickingExceptionService;
    @Autowired
    private StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    private StockoutBatchService stockoutBatchService;

    @Transactional
    public StockoutOrderScanTask getByStockoutOrderNo(String stockoutOrderNo) {
        StockoutOrderScanTask result = new StockoutOrderScanTask();
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new QueryWrapper<StockoutOrderEntity>().lambda().eq(StockoutOrderEntity::getStockoutOrderNo, stockoutOrderNo).last(MybatisQueryConstant.QUERY_FIRST));
        if (stockoutOrderEntity == null || StockoutOrderStatusEnum.CANCELLED.name().equals(stockoutOrderEntity.getStatus())) {
            result.setShowNotice(Boolean.TRUE);
            result.setNotice("出库单不存在或已取消");
            return result;
        }
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda().eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId()));
        if (stockoutOrderItemEntityList.isEmpty()) {
            result.setShowNotice(Boolean.TRUE);
            result.setNotice("出库单明细不存在");
            return result;
        }
        result.setTotalQty(stockoutOrderItemEntityList.stream().mapToInt(StockoutOrderItemEntity::getQty).sum());
        result.setPlatformName(stockoutOrderEntity.getPlatformName());
        // step1：校验出库单状态，若为取消中，更新至撤货箱中
        if (stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.CANCELLING.name())) {
            stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.CANCELLED.name());
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            stockoutOrderService.updateById(stockoutOrderEntity);
            stockoutOrderLogService.addLog(stockoutOrderNo, StockoutOrderLogTypeEnum.READY_DELIVERY, String.format("出库单【%s】在波次扫描/复核中, 取消成功", stockoutOrderNo));
            updateWithdrawalBoxByStockoutOrder(stockoutOrderEntity);
            result.setShowNotice(Boolean.TRUE);
            result.setNotice("当前出库单已取消，请撤货");
            producer.sendMessage(KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC_MARK, KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutOrderEntity.getStockoutOrderId()));
            return result;
        }
        // step2：校验复核任务状态
        StockoutOrderScanTaskEntity taskEntity = scanTaskService.getOne(new QueryWrapper<StockoutOrderScanTaskEntity>().lambda().eq(StockoutOrderScanTaskEntity::getStockoutOrderNo, stockoutOrderNo).last(MybatisQueryConstant.QUERY_FIRST));
        if (taskEntity == null || taskEntity.getStatus().equals(StockoutOrderScanTaskStatusEnum.REVIEWED.name())) {
            result.setShowNotice(Boolean.TRUE);
            result.setNotice("复核任务不存在，或已完成复核任务");
            return result;
        }
        if (!taskEntity.getStatus().equals(StockoutOrderScanTaskStatusEnum.WAIT_REVIEW.name())
                && !taskEntity.getStatus().equals(StockoutOrderScanTaskStatusEnum.REVIEWING.name())
                && !taskEntity.getStatus().equals(StockoutOrderScanTaskStatusEnum.SUSPEND_REVIEW.name())) {
            result.setShowNotice(Boolean.TRUE);
            result.setNotice("请选择【待复核、复核中、暂停复核】复核任务的出库单");
            return result;
        }
        // 异步是否生成增值任务,避免流程中任务未生成增值任务
        if (stockoutOrderItemEntityList.stream().anyMatch(o -> StringUtils.hasText(o.getVasType()))
                || StockoutOrderPlatformEnum.AMAZON.getName().equalsIgnoreCase(stockoutOrderEntity.getPlatformName()) && stockoutOrderItemEntityList.stream().anyMatch(o -> 1 == o.getIsFirstOrderByStore())) {
            StockoutVasTaskCreateDTO stockoutVasTaskCreateDTO = new StockoutVasTaskCreateDTO();
            stockoutVasTaskCreateDTO.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            stockoutVasTaskCreateDTO.setVasTaskCreateEnum(StockoutVasTaskCreateEnum.REVIEW);
            producer.sendMessage(KafkaConstant.SYNC_VAS_TASK_CREATE_TOPIC_NAME, KafkaConstant.SYNC_VAS_TASK_CREATE_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutVasTaskCreateDTO));
        }
        // step3：获取复核明细列表
        return stockoutOrderScanCheckInfoService.getStockoutOrderScanTask(taskEntity, stockoutOrderEntity, stockoutOrderItemEntityList);
    }

    /**
     * 将该SKU绑定到对应的波次下工作区域的撤货箱中，撤货箱状态变更为：【装箱中】
     */
    @Transactional
    public void getByBarcode(String stockoutOrderNo, String barcode) {
        validateScanTask(stockoutOrderNo);
    }

    /**
     * 通知缺货
     * 1.复核任务状态变更【缺货拣货中】
     * 2.生成缺货单
     * 3.对比库存预配 生成预配
     * 4.有库存 生成拣货任务
     * 5.无库存 确认缺货
     * 6. 若拣货箱内数量多余，归还原库位
     */
    @Transactional
    public StockoutNoticeLackResponse noticeLack(String stockoutOrderNo) {
        StockoutOrderScanTaskEntity scanTaskEntity = scanTaskService.getOne(new QueryWrapper<StockoutOrderScanTaskEntity>().lambda()
                .eq(StockoutOrderScanTaskEntity::getStockoutOrderNo, stockoutOrderNo));
        if (scanTaskEntity == null)
            throw new BusinessServiceException("复核任务不存在");
        List<StockoutOrderScanTaskItemEntity> scanTaskItemList = taskItemService.list(new QueryWrapper<StockoutOrderScanTaskItemEntity>().lambda()
                .eq(StockoutOrderScanTaskItemEntity::getTaskId, scanTaskEntity.getTaskId()));
        List<StockoutOrderScanTaskItemEntity> lackScanTaskItemList = scanTaskItemList.stream().filter(scanTaskItem -> 1 == scanTaskItem.getIsLack()).collect(Collectors.toList());
        if (lackScanTaskItemList.isEmpty()) {
            throw new BusinessServiceException("复核任务不存在缺货, 请进行确认操作");
        }

        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        //若拣货箱内数量多余，归还原库位
        producer.sendMessage(KafkaConstant.STOCKOUT_ORDER_SCAN_NOTICE_LACK_CLEAR_BOX_TOPIC_NAME, KafkaConstant.STOCKOUT_ORDER_SCAN_NOTICE_LACK_CLEAR_BOX, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutOrderNo));
        List<StockoutNoticeLackItemBo> stockoutNoticeLackItemBoList = new ArrayList<>(lackScanTaskItemList.size());
        for (StockoutOrderScanTaskItemEntity scanTaskItem : lackScanTaskItemList) {
            StockoutOrderItemEntity stockoutOrderItem = stockoutOrderItemEntityList.stream()
                    .filter(temp -> scanTaskItem.getStockoutOrderItemId().equals(temp.getStockoutOrderItemId()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(stockoutOrderItem)) {
                throw new BusinessServiceException(String.format("复核任务明细与出库单明细不一致 复核任务明细id %d", scanTaskItem.getTaskItemId()));
            }
            StockoutNoticeLackItemBo stockoutNoticeLackItemBo = new StockoutNoticeLackItemBo();
            stockoutNoticeLackItemBo.setStockoutOrderItem(stockoutOrderItem);
            stockoutNoticeLackItemBo.setExpectedQty(scanTaskItem.getExpectedQty());
            stockoutNoticeLackItemBo.setScanQty(scanTaskItem.getScanQty());
            stockoutNoticeLackItemBo.setLackQty(scanTaskItem.getLackQty());
            stockoutNoticeLackItemBoList.add(stockoutNoticeLackItemBo);
        }

        StockoutNoticeLackBo stockoutNoticeLackBo = new StockoutNoticeLackBo();
        stockoutNoticeLackBo.setStockoutOrder(stockoutOrderEntity);
        stockoutNoticeLackBo.setStockoutNoticeLackItemList(stockoutNoticeLackItemBoList);
        stockoutNoticeLackBo.setLackSource(StockoutOrderLackSourceEnum.RECHECK_PACKING.name());
        stockoutNoticeLackBo.setTaskId(scanTaskEntity.getTaskId());

        // 2.生成缺货单
        // 3.对比库存预配 生成预配
        // 4.有库存 生成拣货任务
        // 5.无库存 确认缺货
        return stockoutOrderNoticeLackService.noticeLack(Collections.singletonList(stockoutNoticeLackBo), StockoutOrderLackSourceEnum.RECHECK_PACKING.name());

    }

    @JLock(keyConstant = "ScanTaskConfirmLockConstant", lockKey = "#stockoutOrderNo+ '-' + #request.shipmentBoxCode")
    public void confirmScanLock(String stockoutOrderNo, StockoutOrderScanTaskItemDetailRequest request) {
        context.getBean(this.getClass()).confirmScan(stockoutOrderNo, request);
    }

    /**
     * 确认扫描数
     * step1：复核任务状态变更
     * step2：更新扫描数
     * step3：出库单状态变更
     */
    @Transactional
    public void confirmScan(String stockoutOrderNo, StockoutOrderScanTaskItemDetailRequest request) {
        StockoutOrderScanTaskValidateResponse validateResponse = validateScanTask(stockoutOrderNo);
        validateResponse.setShipmentBox(request.getShipmentBoxCode());
        validateResponse.setGenerateScanRecord(request.getGenerateScanRecord());
        shipmentItemService.vaildShipmentStoreAndLogistics(stockoutOrderNo, request.getShipmentBoxCode());
        // step1：复核任务状态变更
        // 【暂停复核】->【复核中】
        StockoutOrderScanTask scanTask = validateResponse.getScanTask();
        StockoutOrderScanTaskEntity taskEntity = new StockoutOrderScanTaskEntity();
        taskEntity.setTaskId(scanTask.getTaskId());
        if (scanTask.getStatus().equals(StockoutOrderScanTaskStatusEnum.SUSPEND_REVIEW.name())) {
            taskEntity.setPauseEndDate(new Date());
            taskEntity.setStatus(StockoutOrderScanTaskStatusEnum.REVIEWING.name());
            taskEntity.setUpdateBy(loginInfoService.getName());
            scanTaskService.updateById(taskEntity);
            scanLogService.addScanLog(taskEntity.getTaskId(), "开始复核", "开始复核装箱", loginInfoService.getName());
        }
        // step2：更新扫描数 更新出库单 更新装箱明细
        List<StockoutOrderItemInfo> currentOutOrderItemList = validateResponse.getStockoutOrderItemInfoList();
        List<StockoutOrderScanTaskItem> currentScanItemList = validateResponse.getScanTaskItemList();
        for (StockoutOrderScanTaskItemDetail scanDetail : request.getItemDetailList()) {
            StockoutOrderScanTaskItem currentScanItem = currentScanItemList.stream().filter(o -> o.getTaskItemId().equals(scanDetail.getTaskItemId())).findFirst().orElseThrow(() -> new BusinessServiceException("未找到复核任务sku信息" + scanDetail.getSku()));
            //防止出库单有相同sku不知道回填哪个
            StockoutOrderItemInfo currentOutOrderItem = currentOutOrderItemList.stream()
                    .filter(o -> o.getStockoutOrderItemId().equals(currentScanItem.getStockoutOrderItemId()))
                    .findAny().orElseThrow(() -> new BusinessServiceException("未找到出库单sku信息" + scanDetail.getSku()));
            updateScanQty(validateResponse, currentOutOrderItem, currentScanItem, scanDetail.getScanQty());
        }
        // step3：出库单状态变更
        if (StockoutOrderStatusEnum.valueOf(validateResponse.getStockoutOrderInfo().getStatus()).getIndex() < StockoutOrderStatusEnum.OUTBOUNDING.getIndex()) {
            StockoutOrderEntity stockoutOrderEntity = new StockoutOrderEntity();
            stockoutOrderEntity.setStockoutOrderId(validateResponse.getStockoutOrderInfo().getStockoutOrderId());
            stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.OUTBOUNDING.name());
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            stockoutOrderService.updateById(stockoutOrderEntity);
        }
    }

    /**
     * 更新扫描数
     * step1：出库单明细扫描数、是否缺货赋值
     * step2：复核任务明细扫描数、是否缺货赋值
     * step3：缺货明细列表扫描数更新
     * step4：插入装箱数据 先查询之前有没有该数据 , 如果有则修改数量，如果没有则插入
     * step5：出库单明细发货数更新
     */
    private void updateScanQty(StockoutOrderScanTaskValidateResponse validateResponse, StockoutOrderItemInfo stockoutOrderItemInfo, StockoutOrderScanTaskItem scanTaskItem, Integer qty) {
        // step1：出库单明细扫描数、是否缺货赋值
        StockoutOrderItemEntity stockoutOrderItemEntity = new StockoutOrderItemEntity();
        stockoutOrderItemEntity.setStockoutOrderItemId(stockoutOrderItemInfo.getStockoutOrderItemId());
        stockoutOrderItemEntity.setScanQty(qty);
        stockoutOrderItemEntity.setLack(stockoutOrderItemInfo.getQty() > qty ? Boolean.TRUE : Boolean.FALSE);
        stockoutOrderItemEntity.setUpdateBy(loginInfoService.getName());
        stockoutOrderItemService.updateById(stockoutOrderItemEntity);
        // step2：复核任务明细扫描数、是否缺货赋值
        StockoutOrderScanTaskItemEntity scanTaskItemEntity = new StockoutOrderScanTaskItemEntity();
        StockoutOrderScanTaskItemEntity originScanTask = taskItemService.getById(scanTaskItem.getTaskItemId());
        scanTaskItemEntity.setTaskItemId(scanTaskItem.getTaskItemId());
        // 任务明细扫描数更新
        scanTaskItemEntity.setScanQty(qty);
        // 任务明细发货数更新
        scanTaskItemEntity.setShipmentQty(qty);
        scanTaskItemEntity.setUpdateBy(loginInfoService.getName());
        scanTaskItemEntity.setAuditBy(loginInfoService.getName());
        scanTaskItemEntity.setAuditDate(new Date());
        if (scanTaskItem.getOrderQty() > qty) {
            scanTaskItemEntity.setIsLack(1);
            scanTaskItemEntity.setLackQty(scanTaskItem.getOrderQty() - qty);
        } else if (scanTaskItem.getOrderQty() < qty) {
            throw new BusinessServiceException("复核数量超过出库数！");
        } else {
            scanTaskItemEntity.setIsLack(0);
            scanTaskItemEntity.setLackQty(0);
        }
        taskItemService.updateById(scanTaskItemEntity);
        // step3：缺货明细列表扫描数更新
        lackScanConfirmService.updateStatusByCheckScan(stockoutOrderItemInfo, qty);
        // step4 插入装箱数据 先查询之前有没有该数据 , 如果有则修改数量，如果没有则插入
        addShipmentItem(validateResponse, stockoutOrderItemInfo, qty, originScanTask, scanTaskItem);
        // step5：出库单明细发货数更新
        stockoutOrderItemService.updateById(StockoutBuilding.buildOrderItem(stockoutOrderItemInfo, loginInfoService.getName(), qty));
    }

    private void addShipmentItem(StockoutOrderScanTaskValidateResponse validateResponse, StockoutOrderItemInfo stockoutOrderItemInfo, Integer qty, StockoutOrderScanTaskItemEntity originScanTask, StockoutOrderScanTaskItem scanTaskItem) {
        StockoutShipmentEntity shipment = shipmentService.findTopByShipmentBoxCode(validateResponse.getShipmentBox());
        if (StockoutShipmentStatusEnum.SHIPPED.name().equalsIgnoreCase(shipment.getStatus()))
            throw new BusinessServiceException("已经发货的箱子不能再次复核");
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentItemEntity::getShipmentId, shipment.getShipmentId())
                .eq(StockoutShipmentItemEntity::getStockoutOrderNo, validateResponse.getStockoutOrderInfo().getStockoutOrderNo()).eq(StockoutShipmentItemEntity::getOrderNo, stockoutOrderItemInfo.getOrderNo())
                .eq(StockoutShipmentItemEntity::getStockoutOrderItemId, stockoutOrderItemInfo.getStockoutOrderItemId())
                .eq(StockoutShipmentItemEntity::getIsDeleted, 0).last("limit 1");
        StockoutShipmentItemEntity item = shipmentItemService.getOne(wrapper);
        if (qty - originScanTask.getShipmentQty() > 0)
            // 扫描数增加时，校验加工拣货信息
            validAddQty(stockoutOrderItemInfo, scanTaskItem);
        if (item == null) {
            // 扫描小于已经装箱的数量，且当前箱子没有装箱数
            if (qty < originScanTask.getShipmentQty())
                throw new BusinessServiceException("当前箱子没有装" + scanTaskItem.getSku() + ", 无法向下调整数量");
            item = shipmentService.buildByStockoutOrderItemAndQty(stockoutOrderItemInfo,
                    qty - originScanTask.getShipmentQty(), validateResponse.getStockoutOrderInfo().getStockoutOrderNo(), shipment.getShipmentId());
            if (item.getQty() == null || item.getQty() == 0)
                // 为0不更新装箱数据
                return;
            shipmentItemService.save(item);
            if (item.getQty() > scanTaskItem.getOrderQty())
                throw new BusinessServiceException("装箱数超过扫描数，请核对");
            buildTransparencyCode(stockoutOrderItemInfo, qty, originScanTask, item);
            StockoutScanDetailMessage stockoutScanDetailMessage = StockoutBuilding.detailMessage(item, qty - originScanTask.getShipmentQty(), validateResponse.getShipmentBox(), StockoutScanTaskDetailEnum.SCAN.name());
            stockoutScanDetailMessage.setGenerateScanRecord(validateResponse.getGenerateScanRecord());
            producer.sendMessage(KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC_NAME, KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC, Key.of(item.getSku()),
                    new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockoutScanDetailMessage));
        } else {
            if (item.getQty() + qty - originScanTask.getShipmentQty() < 0)
                throw new BusinessServiceException("当前箱子中" + scanTaskItem.getSku() + "有 " + item.getQty()
                        + "件, 扫描数最少要为" + (originScanTask.getShipmentQty() - item.getQty()));
            // 调整的数量为 qty - originScanTask.getScanQty()
            item.setQty(item.getQty() + qty - originScanTask.getShipmentQty());
            if (item.getQty() == 0)
                item.setIsDeleted(1);
            item.setUpdateBy(loginInfoService.getName());
            shipmentItemService.updateById(item);
            if (item.getQty() > scanTaskItem.getOrderQty()) {
                LOGGER.info("装箱数超过扫描数，请核对 {} {} {}", item.getShipmentItemId(), item.getQty(), scanTaskItem.getOrderQty());
                throw new BusinessServiceException("装箱数超过扫描数，请核对");
            }
            buildTransparencyCode(stockoutOrderItemInfo, qty, originScanTask, item);
            StockoutScanDetailMessage stockoutScanDetailMessage = StockoutBuilding.detailMessage(item, qty - originScanTask.getShipmentQty(), validateResponse.getShipmentBox(), StockoutScanTaskDetailEnum.SCAN.name());
            stockoutScanDetailMessage.setGenerateScanRecord(validateResponse.getGenerateScanRecord());
            producer.sendMessage(KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC_NAME, KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC, Key.of(item.getSku()),
                    new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockoutScanDetailMessage));
        }
        scanLogService.addScanLog(scanTaskItem.getTaskId(), "复核确认",
                String.format("%s确认扫描数【%s】, 箱子【%s】现有【%s】件, 此sku还差【%s】件", scanTaskItem.getSku(), qty, validateResponse.getShipmentBox(), item.getQty(), scanTaskItem.getOrderQty() - qty), loginInfoService.getName());
    }

    private void validAddQty(StockoutOrderItemInfo stockoutOrderItemInfo, StockoutOrderScanTaskItem scanTaskItem) {
        if (scanTaskItem.getIsNeedProcess() == 1) {
            StockoutBatchOrderEntity batchOrderEntity = batchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderItemInfo.getStockoutOrderId());
            List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemListByBatchId(batchOrderEntity.getBatchId(), StockInternalBoxTypeEnum.PICKING_BOX.name());
            List<StockInternalBoxItemEntity> internalBoxItemEntities = internalBoxItemEntityList.stream().filter(o -> o.getSku().equals(scanTaskItem.getSku())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(internalBoxItemEntities))
                throw new BusinessServiceException("需完成加工定制款拣货任务");
        }
    }

    private void buildTransparencyCode(StockoutOrderItemInfo stockoutOrderItemInfo, Integer qty, StockoutOrderScanTaskItemEntity originScanTask, StockoutShipmentItemEntity item) {
        StockoutOrderItemEntity stockoutOrderItemEntity = new StockoutOrderItemEntity();
        BeanUtils.copyProperties(stockoutOrderItemInfo, stockoutOrderItemEntity);
        if (stockoutOrderItemService.validTransparency(item.getStockoutOrderNo(), Collections.singletonList(stockoutOrderItemEntity))) {
            if (qty - originScanTask.getShipmentQty() < 0) {
                transparencyCodeService.removeCode(item, originScanTask.getShipmentQty() - qty);
            } else if (qty - originScanTask.getShipmentQty() > 0)
                transparencyCodeService.matchShipmentItem(item);

        }
    }

    /**
     * 暂停扫描
     */
    @Transactional
    public void pauseScan(String stockoutOrderNo) {
        StockoutOrderScanTaskEntity taskEntity = scanTaskService.getOne(new QueryWrapper<StockoutOrderScanTaskEntity>().lambda()
                .eq(StockoutOrderScanTaskEntity::getStockoutOrderNo, stockoutOrderNo));
        if (taskEntity == null)
            throw new BusinessServiceException("复核任务不存在");
        taskEntity.setStatus(StockoutOrderScanTaskStatusEnum.SUSPEND_REVIEW.name());
        taskEntity.setPauseStartDate(new Date());
        taskEntity.setUpdateBy(loginInfoService.getName());
        scanTaskService.updateById(taskEntity);
        // 记录日志
        scanLogService.addScanLog(taskEntity.getTaskId(), "暂停复核扫描", "暂停复核扫描", loginInfoService.getName());
    }

    /**
     * 完成扫描
     * step1：复核任务明细，未扫描的数据，设为缺货
     * step2：出库单状态变更为【待发货】
     * step3：复核任务状态变更为【复核完成】，记录日志
     * step4：发货库位增加扫描数（非二次分拣：B2B|内贸），整单拣货对应拣货箱减少拣货数
     * step5：.net 拣货单完成
     * step6：缺货出库单完成
     * step7：出库单下所有装箱清单已发货，则出库单变为已发货
     * step8：清除出库单所属的预占信息
     * step9: 剩余拣货箱库存归还原库位
     * step10: 修改装箱清单和出库单的fba补货状态 --> 待申请
     */
    @Transactional
    public StockoutConfirmShipmentResponse completeScan(String stockoutOrderNo, String deliveryNoticeType) {
        StockoutOrderScanTaskValidateResponse validateResponse = validateScanTask(stockoutOrderNo);
        StockoutOrderScanTask scanTask = validateResponse.getScanTask();
        // 全部箱子必须先确认装箱 + 同步erp装箱清单
        shipmentItemService.validAllShipmentConfirm(stockoutOrderNo, "");
        if (scanTask.getStatus().equals(StockoutOrderScanTaskStatusEnum.REVIEWED.name()))
            throw new BusinessServiceException("当前复核任务已完成扫描，请勿重复操作");
        List<StockoutOrderScanTaskItem> scanTaskItemList = validateResponse.getScanTaskItemList();
        Integer orderSum = scanTaskItemList.stream().mapToInt(StockoutOrderScanTaskItem::getOrderQty).sum();
        Integer scanSum = scanTaskItemList.stream().mapToInt(StockoutOrderScanTaskItem::getScanQty).sum();
        // step1：复核任务明细，未扫描的数据，设为缺货
        LOGGER.info("step1");
        List<StockoutOrderScanTaskItemEntity> unScanItemEntityList = scanTaskItemList.stream().filter(o -> o.getScanQty().equals(0))
                .map(o -> StockoutBuilding.scanTaskItemEntity(o, loginInfoService.getName())).collect(Collectors.toList());
        if (!unScanItemEntityList.isEmpty())
            taskItemService.updateBatchById(unScanItemEntityList);
        List<StockoutOrderLackResponse> lackResponseList = stockoutOrderMapper.selectLackList(Collections.singletonList(stockoutOrderNo));
        int lackSum = lackResponseList.stream().mapToInt(StockoutOrderLackResponse::getLackQty).sum();
        // step2：出库单状态变更为【待发货】
        LOGGER.info("step2");
        stockoutOrderOperateService.changeStockoutOrderStatusToReadyDelivery(validateResponse.getStockoutOrderInfo(), scanSum, lackSum);
        // step3：复核任务状态变更为【复核完成】，记录日志
        LOGGER.info("step3");
        scanTaskService.updateById(StockoutBuilding.scanTaskEntity(scanTask, loginInfoService.getName(), lackSum));
        // 记录日志
        scanLogService.addScanLog(scanTask.getTaskId(), "完成复核",
                String.format("完成复核任务，订单总【%s】件，实际扫描【%s】件，缺货【%s】件", orderSum, scanSum, lackSum), loginInfoService.getName());
        stockoutLogQueueService.addLogQueue(Stream.of(validateResponse.getStockoutOrderInfo().getStockoutOrderId()).collect(Collectors.toList()), String.format("%s完成复核", loginInfoService.getName()));

        // step4：发货库位增加扫描数（非二次分拣：B2B|内贸），整单拣货对应拣货箱减少拣货数
        LOGGER.info("step4");
        // step5：.net 拣货单完成
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (validateResponse.getStockoutOrderInfo().getErpPickId() != null) {
            ErpFinishPartialPickRequest request = new ErpFinishPartialPickRequest(validateResponse.getStockoutOrderInfo().getErpPickId(), loginInfoService.getUserName(), loginInfoService.getUserName(), loginInfoService.getIpAddress());
            List<ErpFinishPartialPickItemRequest> itemRequestList = stockoutErpPickService.getErpFinishPartialPickItemRequests(stockoutOrderEntity);
            request.setItems(itemRequestList);

            LocationWrapperMessage<ErpFinishPartialPickRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), request);
            producer.sendLongMessage(KafkaConstant.FINISH_PARTIAL_PICK_BUSINESS_MARK, KafkaConstant.FINISH_PARTIAL_PICK_TOPIC, message);
            // LOGGER.error("同步erp--------------------等通知缺货-------------------------");
        }
        // step6：缺货出库单完成
        lackService.completeLackOrder(stockoutOrderNo);
        // step7：出库单下所有装箱清单已发货，则出库单变为已发货
        stockoutOrderShipService.stockoutOrderShipmentShipped(Collections.singletonList(stockoutOrderNo), "");
        // step8：清除出库单所属的预占信息
        stockPrematchRemoveService.removeByStockoutOrder(Collections.singletonList(validateResponse.getStockoutOrderInfo().getStockoutOrderId()), StockoutOrderLogTypeEnum.SCAN_ORDER_FINISH);

        // step9: 剩余拣货箱库存归还原库位
        stockoutPickingExceptionService.noticeLackClearBox(stockoutOrderEntity);

        // step10: 修改装箱清单和出库单的fba补货状态 --> 待申请
        if (!(StockoutOrderTypeEnum.FBA_FACTORY_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())
                && platformSet.contains(stockoutOrderEntity.getPlatformName()))) {
            producer.sendMessage(KafkaConstant.SCAN_COMPLETE_UPDATE_FBA_LABEL_TOPIC_MARK, KafkaConstant.SCAN_COMPLETE_UPDATE_FBA_LABEL_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockoutOrderEntity.getStockoutOrderNo()));
        }
        return StockoutBuilding.stockoutConfirmShipmentResponse(validateResponse);
    }

    /**
     * 更新发货库位库存
     */
    private StockUpdateRequest getShippingPositionStockUpdateRequest(Integer spaceId, String sku, Integer qty, String stockoutOrderNo) {
        BdPositionEntity position = positionService.getDeliverPosition(spaceId);
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(sku);
        stockUpdateRequest.setPositionCode(position.getPositionCode());
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.STOCKOUT_CHECK);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        stockUpdateRequest.setQty(qty);
        stockUpdateRequest.setStockoutOrderNo(stockoutOrderNo);
        return stockUpdateRequest;
    }


    private void addPickingBoxInScan(List<Integer> batchIds, Integer qty, StockoutShipmentItemEntity shipmentItemEntity) {
        LOGGER.info("复核异常，减少复核数  {} sku ： {} ，qty: {} ", shipmentItemEntity.getStockoutOrderNo(), shipmentItemEntity.getSku(), shipmentItemEntity.getQty());
        List<StockShippingPositionEntity> stockShippingPositionEntities = shippingPositionService.findByStockoutOrderAndSku(shipmentItemEntity.getSku(), shipmentItemEntity.getStockoutOrderNo());
        if (CollectionUtils.isEmpty(stockShippingPositionEntities)) {
            throw new BusinessServiceException(shipmentItemEntity.getStockoutOrderNo() + "无法找到发货库位来源明细sku:" + shipmentItemEntity.getSku());
        }
        //多个来源需要排序，归还正确的
        if (stockShippingPositionEntities.size() > 1) {
            StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(shipmentItemEntity.getStockoutOrderItemId());
            stockShippingPositionEntities.sort(Comparator.comparing(item -> {
                if (StringUtils.hasText(item.getSourcePositionCode()) && item.getSourcePositionCode().equals(stockoutOrderItemEntity.getPositionCode()))
                    return 0;
                return 1;
            }));
        }
        LOGGER.info(" {} 发货来源库位信息 ： {}", shipmentItemEntity.getStockoutOrderNo(), JsonMapper.toJson(stockShippingPositionEntities));
        Integer countQty = Math.abs(qty);

        List<StockShippingPositionEntity> updateList = new LinkedList<>();
        List<Integer> removeIds = new LinkedList<>();

        for (StockShippingPositionEntity shippingPositionEntity : stockShippingPositionEntities) {
            if (countQty <= 0)
                break;

            Integer addQty = Math.min(countQty, shippingPositionEntity.getQty());
            countQty -= addQty;

            if (shippingPositionEntity.getQty() <= addQty) {
                removeIds.add(shippingPositionEntity.getId());
            } else {
                StockShippingPositionEntity stockShippingPositionEntity = new StockShippingPositionEntity();
                stockShippingPositionEntity.setId(shippingPositionEntity.getId());
                stockShippingPositionEntity.setUpdateBy(shippingPositionEntity.getUpdateBy());
                stockShippingPositionEntity.setQty(shippingPositionEntity.getQty() - addQty);
                updateList.add(stockShippingPositionEntity);
            }

            if (!CollectionUtils.isEmpty(updateList))
                shippingPositionService.updateBatchById(updateList);
            if (!CollectionUtils.isEmpty(removeIds))
                shippingPositionService.removeByIds(removeIds);

            if (!StringUtils.hasText(shippingPositionEntity.getInternalBoxCode())) {
                // 没有拣货箱 还到原库位上
                StockInternalBoxItemSourcePositionBo sourcePositionBo = new StockInternalBoxItemSourcePositionBo(shippingPositionEntity.getSourceAreaId(), shippingPositionEntity.getSourcePositionCode(), shipmentItemEntity.getSku(), addQty, null);
                stockoutPickingExceptionService.addSourcePositionStock(Lists.newArrayList(sourcePositionBo), batchIds.get(0), StockChangeLogTypeEnum.SCAN_LEVELLING, shipmentItemEntity.getStockoutOrderNo());
                continue;
            }

            //存在拣货箱
            String boxCode = shippingPositionEntity.getInternalBoxCode();
            List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.getByInternalBoxCodeAndsku(boxCode, shipmentItemEntity.getSku());
            // 拣货箱明细
            StockInternalBoxItemEntity boxItemEntity = internalBoxItemEntityList.stream().filter(it -> it.getSku().equals(shipmentItemEntity.getSku())
                    && Objects.equals(it.getSourcePositionCode(), shippingPositionEntity.getSourcePositionCode())).findFirst().orElse(null);
            if (boxItemEntity == null) {
                StockInternalBoxEntity internalBox = stockInternalBoxService.findByInternalBoxCode(boxCode);
                if (internalBox == null) {
                    throw new BusinessServiceException(boxCode + "无法找到该内部箱，复核扫描数无法向下调整");
                }
                boxItemEntity = new StockInternalBoxItemEntity();
                ProductSpecInfoEntity skuInfo = productSpecInfoService.findTopBySku(shipmentItemEntity.getSku());
                boxItemEntity.setProductId(skuInfo.getProductId());
                boxItemEntity.setQty(0);
                boxItemEntity.setSku(shipmentItemEntity.getSku());
                boxItemEntity.setBatchId(batchIds.get(batchIds.size() - 1));
                boxItemEntity.setSourcePositionCode(shippingPositionEntity.getSourcePositionCode());
                boxItemEntity.setSourceAreaId(shippingPositionEntity.getSourceAreaId());
                boxItemEntity.setInternalBoxCode(internalBox.getInternalBoxCode());
                boxItemEntity.setInternalBoxId(internalBox.getInternalBoxId());
                boxItemEntity.setLocation(internalBox.getLocation());
                boxItemEntity.setSpaceId(internalBox.getSpaceId());
                boxItemEntity.setSpecId(skuInfo.getSpecId());
                boxItemEntity.setCreateBy(loginInfoService.getName());
            }
            stockInternalBoxItemService.addStockInternalBoxItemQty(boxItemEntity, addQty, StockChangeLogTypeEnum.STOCKOUT_CHECK, StockChangeLogTypeModuleEnum.STOCK_OUT, new StockInternalBoxOrderInfo(shippingPositionEntity.getSourceAreaId(), shippingPositionEntity.getSourcePositionCode()));
        }
    }

    /**
     * 扫描时触发扣减操作
     * 发货库位增加扫描数（非二次分拣：B2B|内贸），整单拣货对应拣货箱减少拣货数
     */
    @org.springframework.transaction.annotation.Transactional
    public void handlePickingBoxAndShippingPositionInScan(StockoutOrderEntity stockoutOrderInfo, StockoutShipmentItemEntity shipmentItemEntity, Integer qty) {
        List<StockUpdateRequest> stockUpdateRequestList = new LinkedList<>();
        // sku扫描数量
        stockUpdateRequestList.add(getShippingPositionStockUpdateRequest(stockoutOrderInfo.getSpaceId(), shipmentItemEntity.getSku(), qty, stockoutOrderInfo.getStockoutOrderNo()));

        List<StockInternalBoxItemSourcePositionBo> sourcePositionBos = new ArrayList<>();
        StockoutBatchOrderEntity batchOrderEntity = batchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
        if (batchOrderEntity == null)
            throw new BusinessServiceException(String.format("出库单【%s】未找到波次", stockoutOrderInfo.getStockoutOrderNo()));
        StockoutBatchEntity stockoutBatch = batchService.getStockoutBatchById(batchOrderEntity.getBatchId());
        List<Integer> batchIds = new ArrayList<>();
        batchIds.add(batchOrderEntity.getBatchId());
        if (stockoutBatch.getMergeBatchId() != null)
            batchIds.add(stockoutBatch.getMergeBatchId());

        //是否更新
        boolean isUpdateShippingStockRecord = true;
        if (qty > 0) {
            // 发货库位+，拣货箱-
            sourcePositionBos = stockoutPickingTaskItemService.updatePickingBoxItem(batchIds, stockoutOrderInfo, shipmentItemEntity, qty);
            LOGGER.info(" {} 内部箱来源信息 ： {}", stockoutOrderInfo.getStockoutOrderNo(), JsonMapper.toJson(sourcePositionBos));
        } else {
            // 发货库位-，拣货箱+
            addPickingBoxInScan(batchIds, -qty, shipmentItemEntity);
            isUpdateShippingStockRecord = false;
        }
        Map<String, List<StockInternalBoxItemSourcePositionBo>> collect = sourcePositionBos.stream().collect(Collectors.groupingBy(StockInternalBoxItemSourcePositionBo::getSku));
        boolean finalIsUpdateShippingStockRecord = isUpdateShippingStockRecord;
        stockUpdateRequestList.forEach(item -> {
            item.setSourcePositionBoList(collect.get(item.getSku()));
            item.setUpdateShippingStockRecord(finalIsUpdateShippingStockRecord);
        });

        stockService.updateStockBatch(new ArrayList<>(stockUpdateRequestList.stream().collect(Collectors
                .toMap(it -> it.getPositionCode() + "#" + it.getSku(), a -> a, (o1, o2) -> {
                    StockUpdateRequest req = new StockUpdateRequest();
                    o1.setQty(o1.getQty() + o2.getQty());
                    BeanUtilsEx.copyProperties(o1, req);
                    req.setSourcePositionBoList(collect.get(req.getSku()));
                    return req;
                })).values()));
    }

    /**
     * 将出库单下SKU绑定到对应的波次下工作区域的撤货箱中，撤货箱状态变更为：装箱中
     * 绑定撤货箱数量=拣货箱数量
     */
    public void updateWithdrawalBoxByStockoutOrder(StockoutOrderEntity stockoutOrderEntity) {
        // 获取未取消的波次单
        StockoutBatchOrderEntity batchOrderEntity = batchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (batchOrderEntity == null)
            return;
        StockoutBatchEntity batchEntity = batchService.getOne(new QueryWrapper<StockoutBatchEntity>().lambda()
                .eq(StockoutBatchEntity::getBatchId, batchOrderEntity.getBatchId()));
        if (batchEntity == null)
            return;
        // 整单拣货 无分拣任务
        if (stockoutOrderEntity.getPickingType().equals(StockoutPickingTypeEnum.WHOLE_PICK.name())) {
            // 根据工作区域+内部箱类型查询撤货箱
            StockInternalBoxEntity drawalBoxEntity = stockInternalBoxService.getWithdrawalByWorkspace(batchEntity.getWorkspace(), batchEntity.getSpaceId());
            updateWithDrawalBoxItemByPicking(drawalBoxEntity, batchEntity.getBatchId(), stockoutOrderEntity);
            // 撤货箱状态改为装箱中
            if (!drawalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.PACKING.name()))
                stockInternalBoxService.changeStockInternalBoxStatus(drawalBoxEntity, StockInternalBoxStatusEnum.PACKING.name());
        }
    }

    /**
     * 无分拣任务时，扣减拣货箱数量，增加到撤货箱数量
     */
    private void updateWithDrawalBoxItemByPicking(StockInternalBoxEntity drawalBoxEntity, Integer batchId, StockoutOrderEntity stockoutOrderEntity) {
        // 拣货箱明细
        List<StockInternalBoxItemEntity> pickingBoxItemEntityList = stockInternalBoxItemService.list(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
                .ne(StockInternalBoxItemEntity::getInternalBoxCode, drawalBoxEntity.getInternalBoxCode())
                .eq(StockInternalBoxItemEntity::getBatchId, batchId));
        if (pickingBoxItemEntityList.isEmpty())
            return;
        // 出库单明细
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda()
                .eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId()));
        Map<String, Integer> skuScanMap = stockoutOrderItemEntityList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getSku, Collectors.summingInt(StockoutOrderItemEntity::getQty)));
        for (StockInternalBoxItemEntity pickingBoxItemEntity : pickingBoxItemEntityList) {
            if (!skuScanMap.containsKey(pickingBoxItemEntity.getSku()))
                continue;
            // 拣货箱明细减少
            int changeQty = Math.min(pickingBoxItemEntity.getQty(), skuScanMap.get(pickingBoxItemEntity.getSku()));
            StockInternalBoxItemSourcePositionBo stockInternalBoxItemSourcePositionBo = stockInternalBoxItemService.minusStockInternalBoxItemQty(pickingBoxItemEntity, changeQty, StockChangeLogTypeEnum.STOCKOUT_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
            // 撤货箱明细增加
            ProductSpecInfoEntity topBySku = productSpecInfoService.findTopBySku(pickingBoxItemEntity.getSku());
            stockoutBatchSortCommonService.saveOrUpdateWithdrawalBox(drawalBoxEntity, topBySku, batchId, stockInternalBoxItemSourcePositionBo.getQty(), Collections.singletonList(stockInternalBoxItemSourcePositionBo));
        }
    }

    /**
     * 校验出库单是否存在
     */
    public StockoutOrderScanTaskValidateResponse validateScanTask(String stockoutOrderNo) {
        StockoutOrderScanTaskValidateResponse result = new StockoutOrderScanTaskValidateResponse();
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new QueryWrapper<StockoutOrderEntity>().lambda().eq(StockoutOrderEntity::getStockoutOrderNo, stockoutOrderNo));
        StockoutOrderValid.validScanTaskInScan(stockoutOrderEntity);
        StockoutOrderInfo stockoutOrderInfo = new StockoutOrderInfo();
        BeanUtils.copyProperties(stockoutOrderEntity, stockoutOrderInfo);
        stockoutOrderInfo.setCustomerDeclareTypeStr(stockoutOrderEntity.getCustomsDeclareType());
        result.setStockoutOrderInfo(stockoutOrderInfo);
        StockoutOrderScanTaskEntity taskEntity = scanTaskService.getOne(new QueryWrapper<StockoutOrderScanTaskEntity>().lambda().eq(StockoutOrderScanTaskEntity::getStockoutOrderNo, stockoutOrderNo));
        if (taskEntity == null)
            throw new BusinessServiceException("复核任务不存在");
        StockoutOrderScanTask scanTask = new StockoutOrderScanTask();
        BeanUtils.copyProperties(taskEntity, scanTask);
        result.setScanTask(scanTask);
        List<StockoutOrderItemEntity> currentOutOrderItemList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda().eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId()));
        if (currentOutOrderItemList.isEmpty())
            throw new BusinessServiceException("未找到出库单sku信息");
        List<StockoutOrderItemInfo> stockoutOrderItemInfoList = currentOutOrderItemList.stream().map(o -> {
            StockoutOrderItemInfo itemInfo = new StockoutOrderItemInfo();
            BeanUtils.copyProperties(o, itemInfo);
            return itemInfo;
        }).collect(Collectors.toList());
        result.setStockoutOrderItemInfoList(stockoutOrderItemInfoList);
        List<StockoutOrderScanTaskItemEntity> currentScanItemList = taskItemService.list(new QueryWrapper<StockoutOrderScanTaskItemEntity>().lambda().eq(StockoutOrderScanTaskItemEntity::getTaskId, taskEntity.getTaskId()));
        if (currentScanItemList.isEmpty())
            throw new BusinessServiceException("未找到复核任务sku信息");
        List<StockoutOrderScanTaskItem> scanTaskItemList = currentScanItemList.stream().map(o -> {
            StockoutOrderScanTaskItem scanTaskItem = new StockoutOrderScanTaskItem();
            BeanUtils.copyProperties(o, scanTaskItem);
            return scanTaskItem;
        }).collect(Collectors.toList());
        result.setScanTaskItemList(scanTaskItemList);
        return result;
    }

    public StockoutScanBarcodeResponse replaceBarcode(String barcode) {
        StockoutScanBarcodeResponse response = new StockoutScanBarcodeResponse();
        LambdaQueryWrapper<ProductStoreSkuMappingEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductStoreSkuMappingEntity::getStoreBarcode, barcode);
        List<ProductStoreSkuMappingEntity> list = mappingService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            // 缝制任务 支持扫描出库单明细id
            StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(barcode);
            if (stockoutOrderItemEntity != null) {
                response.setBarcode(stockoutOrderItemEntity.getBarcode());
                response.setSku(stockoutOrderItemEntity.getSku());
            }
            return response;
        }
        ProductStoreSkuMappingEntity one = list.get(0);
        if (list.size() > 1) {
            //若存在多条映射关系，根据地区兼容
            List<ProductLocationInfo> productLocationInfos = mappingService.getBaseMapper().findMappingLocationBySku(list.stream().map(ProductStoreSkuMappingEntity::getSku).distinct().collect(Collectors.toList()));
            Optional<ProductLocationInfo> locationInfoOptional = productLocationInfos.stream().filter(productLocationInfo -> TenantContext.getTenant().equalsIgnoreCase(productLocationInfo.getLocation())).findAny();
            if (locationInfoOptional.isPresent()) {
                response.setBarcode(locationInfoOptional.get().getBarcode());
                response.setSku(locationInfoOptional.get().getSku());
                return response;
            }

            //没有该地区的映射 优先取泉州地区
            Optional<ProductLocationInfo> locationInfoOptionalQuanzhou = productLocationInfos.stream().filter(productLocationInfo -> LocationEnum.QUANZHOU.name().equalsIgnoreCase(productLocationInfo.getLocation())).findAny();
            if (locationInfoOptionalQuanzhou.isPresent()) {
                response.setBarcode(locationInfoOptionalQuanzhou.get().getBarcode());
                response.setSku(locationInfoOptionalQuanzhou.get().getSku());
                return response;
            }

        }
        //否则取第一个
        response.setBarcode(one.getBarcode());
        response.setSku(one.getSku());
        return response;
    }

    /**
     * 完成复核任务，并且发货确认
     */
    @Transactional
    public StockoutConfirmShipmentResponse completeScanAndShip(String stockoutOrderNo) {
        StockoutConfirmShipmentResponse response = completeScan(stockoutOrderNo, null);
        stockoutShipService.getStockoutShipService(stockoutOrderNo).autoShipmentShip(stockoutOrderNo);
        return response;
    }

    public StockoutOrderScanIsNoticeLackResponse checkNoticeLack(String stockoutOrderNo) {
        StockoutOrderScanIsNoticeLackResponse response = new StockoutOrderScanIsNoticeLackResponse();
        response.setNoticeLack(stockoutOrderNoticeLackService.scanCheckNoticeLack(stockoutOrderNo));
        return response;
    }

    /**
     * 完成复核任务
     * step1：复核任务明细，未扫描的数据，设为缺货
     * step2：出库单状态变更为【待发货】
     * step3：复核任务状态变更为【复核完成】，记录日志
     * step4：发货库位增加扫描数（非二次分拣：B2B|内贸），整单拣货对应拣货箱减少拣货数
     * step5：缺货出库单完成
     * step6：.net 拣货单完成
     *
     * @param stockoutOrderEntity 出库单
     */
    public void finishScanTask(StockoutOrderEntity stockoutOrderEntity) {
        StockoutOrderScanTaskEntity scanTaskEntity = scanTaskService.getByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (scanTaskEntity == null || scanTaskEntity.getStatus().equals(StockoutOrderScanTaskStatusEnum.REVIEWED.name())) {
            return;
        }
        List<StockoutOrderScanTaskItemEntity> scanTaskItemList = taskItemService.getListByTaskId(scanTaskEntity.getTaskId());
        Integer orderSum = scanTaskItemList.stream().mapToInt(StockoutOrderScanTaskItemEntity::getOrderQty).sum();
        Integer scanSum = scanTaskItemList.stream().mapToInt(StockoutOrderScanTaskItemEntity::getScanQty).sum();

        // step1：复核任务明细，未扫描的数据，设为缺货
        List<StockoutOrderScanTaskItemEntity> unScanItemEntityList = scanTaskItemList.stream().filter(o -> o.getScanQty().equals(0)).collect(Collectors.toList());
        unScanItemEntityList.forEach(o -> {
            o.setUpdateBy(loginInfoService.getUserName());
            o.setIsLack(1);
            o.setLackQty(o.getExpectedQty());
        });
        if (!unScanItemEntityList.isEmpty())
            taskItemService.updateBatchById(unScanItemEntityList);
        //计算缺货数
        List<StockoutOrderLackResponse> lackResponseList = stockoutOrderMapper.selectLackList(Collections.singletonList(stockoutOrderEntity.getStockoutOrderNo()));
        int lackSum = lackResponseList.stream().mapToInt(StockoutOrderLackResponse::getLackQty).sum();
        // step2：出库单状态变更为【待发货】
        StockoutOrderInfo stockoutOrderInfo = new StockoutOrderInfo();
        BeanUtils.copyProperties(stockoutOrderEntity, stockoutOrderInfo);
        operateService.changeStockoutOrderStatusToReadyDelivery(stockoutOrderInfo, scanSum, lackSum);
        // step3：复核任务状态变更为【复核完成】，记录日志
        scanTaskEntity.setIsLack(lackSum > 0 ? 1 : 0);
        scanTaskEntity.setOperateEndDate(new Date());
        scanTaskEntity.setStatus(StockoutOrderScanTaskStatusEnum.REVIEWED.name());
        scanTaskEntity.setUpdateBy(loginInfoService.getUserName());
        scanTaskService.updateById(scanTaskEntity);
        scanLogService.addScanLog(scanTaskEntity.getTaskId(), "完成复核",
                String.format("完成复核任务，订单总【%s】件，实际扫描【%s】件，缺货【%s】件", orderSum, scanSum, lackSum),
                loginInfoService.getName());
        stockoutLogQueueService.addLogQueue(Collections.singletonList(stockoutOrderEntity.getStockoutOrderId()), String.format("%s完成复核", loginInfoService.getName()));
        // step4：发货库位增加扫描数（非二次分拣：B2B|内贸），整单拣货对应拣货箱减少拣货数
        //        scanCheckService.handlePickingBoxAndShippingPosition(stockoutOrderInfo, scanTaskEntity.getTaskId(), scanTaskEntity.getWorkspace());
        // step5：缺货出库单完成
        lackService.completeLackOrder(stockoutOrderEntity.getStockoutOrderNo());
        // step6：.net 拣货单完成
        if (stockoutOrderEntity.getErpPickId() != null) {
            ErpFinishPartialPickRequest request = new ErpFinishPartialPickRequest(stockoutOrderEntity.getErpPickId(), loginInfoService.getUserName(), loginInfoService.getUserName(), loginInfoService.getIpAddress());
            List<ErpFinishPartialPickItemRequest> itemRequestList = stockoutErpPickService.getErpFinishPartialPickItemRequests(stockoutOrderEntity);
            request.setItems(itemRequestList);
            LocationWrapperMessage<ErpFinishPartialPickRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), request);
            producer.sendLongMessage(KafkaConstant.FINISH_PARTIAL_PICK_BUSINESS_MARK, KafkaConstant.FINISH_PARTIAL_PICK_TOPIC, message);
        }
        // step7：清除出库单所属的预占信息
        stockPrematchRemoveService.removeByStockoutOrder(Collections.singletonList(stockoutOrderEntity.getStockoutOrderId()), StockoutOrderLogTypeEnum.SCAN_FINISH);
    }

    //复核时，返回需要命中的明细Id
    public Integer confirmScanSearchId(StockoutOrderScanTaskItemId request) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>()
                .select(StockoutOrderEntity::getStockoutOrderId)
                .eq(StockoutOrderEntity::getStockoutOrderNo, request.getStockoutOrderNo()));

        StockoutOrderScanTaskEntity taskEntity = scanTaskService.getByStockoutOrderNo(request.getStockoutOrderNo());

        List<StockoutOrderScanTaskItemEntity> list = taskItemService.list(new LambdaQueryWrapper<StockoutOrderScanTaskItemEntity>()
                .select(StockoutOrderScanTaskItemEntity::getTaskItemId, StockoutOrderScanTaskItemEntity::getStockoutOrderItemId,
                        StockoutOrderScanTaskItemEntity::getOrderQty, StockoutOrderScanTaskItemEntity::getScanQty)
                .eq(StockoutOrderScanTaskItemEntity::getTaskId, taskEntity.getTaskId())
                .eq(StockoutOrderScanTaskItemEntity::getSku, request.getSku()));
        if (CollectionUtils.isEmpty(list))
            throw new BusinessServiceException(String.format("未找到明细【%s】", request.getSku()));
        List<StockoutOrderScanTaskItemEntity> itemList = list.stream().filter(item -> item.getOrderQty() > item.getScanQty()).collect(Collectors.toList());
        //都填满了，需要撤货，默认返回第一条
        if (CollectionUtils.isEmpty(itemList))
            return list.get(0).getTaskItemId();

        //只剩下一条。默认返回
        if (itemList.size() == 1)
            return itemList.get(0).getTaskItemId();

        //多条的case
        StockoutBatchOrderEntity batchOrderEntity = stockoutBatchOrderService.getOne(new QueryWrapper<StockoutBatchOrderEntity>().lambda()
                .select(StockoutBatchOrderEntity::getBatchId)
                .ne(StockoutBatchOrderEntity::getStatus, StockoutWaveTaskStatusEnum.CANCELLED.name())
                .eq(StockoutBatchOrderEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId())
                .orderByAsc(StockoutBatchOrderEntity::getCreateDate)
                .last(MybatisQueryConstant.QUERY_FIRST));
        if (batchOrderEntity == null)
            throw new BusinessServiceException(String.format("出库单【%s】未找到波次", stockoutOrderEntity.getStockoutOrderNo()));
        StockoutBatchEntity stockoutBatch = stockoutBatchService.getOne(new QueryWrapper<StockoutBatchEntity>().lambda()
                .select(StockoutBatchEntity::getBatchId, StockoutBatchEntity::getMergeBatchId)
                .eq(StockoutBatchEntity::getBatchId, batchOrderEntity.getBatchId()));
        List<Integer> batchIds = new LinkedList<>();
        batchIds.add(batchOrderEntity.getBatchId());
        if (stockoutBatch.getMergeBatchId() != null)
            batchIds.add(stockoutBatch.getMergeBatchId());

        // 拣货箱明细
        List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemListByBatchIdsAndSku(batchIds, StockInternalBoxTypeEnum.PICKING_BOX.name(), request.getSku());
        for (StockoutOrderScanTaskItemEntity item : itemList) {
            StockoutOrderItemEntity orderItemEntity = getStockoutOrderItemEntity(item);
            List<StockInternalBoxItemEntity> boxItemEntities;
            if (StockConstant.ENABLE.equals(orderItemEntity.getIsNeedProcess())) {
                //加工取来源为加工发货库位明细
                boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> ProcessConstant.AFTER_PROCESS_POSITION_CODE.equals(boxItem.getSourcePositionCode())).collect(Collectors.toList());
            } else {
                //非加工款按区域取箱内数
                BdPositionEntity positionByCode = positionService.getPositionByCode(orderItemEntity.getPositionCode());
                if (positionTypeSet.contains(positionByCode.getPositionType())) {
                    boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> positionByCode.getPositionCode().equals(boxItem.getSourcePositionCode())).collect(Collectors.toList());
                } else {
                    boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> positionByCode.getAreaId().equals(boxItem.getSourceAreaId())).collect(Collectors.toList());
                }

            }
            if (boxItemEntities.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum() > 0) {
                LOGGER.info("存在多条相同sku复核明细，出库单【 {} 】，sku【 {} 】，返回明细【 {} 】，出库单明细【 {} 】", stockoutOrderEntity.getStockoutOrderNo(), item.getSku(), item.getTaskItemId(), item.getStockoutOrderItemId());
                return item.getTaskItemId();
            }
        }


        return itemList.get(0).getTaskItemId();
    }

    private StockoutOrderItemEntity getStockoutOrderItemEntity(StockoutOrderScanTaskItemEntity item) {
        return stockoutOrderItemService.getOne(new LambdaQueryWrapper<StockoutOrderItemEntity>()
                .select(StockoutOrderItemEntity::getPositionCode, StockoutOrderItemEntity::getIsNeedProcess)
                .eq(StockoutOrderItemEntity::getStockoutOrderItemId, item.getStockoutOrderItemId()));
    }
}
