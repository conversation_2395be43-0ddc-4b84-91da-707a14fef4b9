package com.nsy.wms.business.service.stockin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.ReturnProductConstant;
import com.nsy.api.wms.domain.product.SendDingDingMessage;
import com.nsy.api.wms.domain.stockin.StockinReturnProductInfo;
import com.nsy.api.wms.domain.stockin.StockinReturnProductItemModel;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.request.stock.CheckSupplierSkuPriceStatusRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskListRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskScanFinishedItemRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskScanFinishedRequest;
import com.nsy.api.wms.response.stockin.CheckSupplierSkuPriceStatusDto;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskListCountResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskScanPositionItemResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskScanPositionResponse;
import com.nsy.wms.business.domain.bo.stockin.StockinReturnProductInfoGetBo;
import com.nsy.wms.business.domain.bo.stockin.StockinReturnProductTaskScanValidatePositionBo;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSupplierPositionMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSupplierPositionMappingEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductLog;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdSupplierPositionMappingMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinReturnProductTaskScanService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinReturnProductTaskScanService.class);

    @Autowired
    BdSupplierPositionMappingService supplierPositionMappingService;
    @Autowired
    StockinReturnProductMapper stockinReturnProductMapper;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    BdSupplierPositionMappingMapper supplierPositionMappingMapper;
    @Autowired
    StockinReturnProductTaskPdaService stockinReturnProductTaskPdaService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinReturnProductTaskService stockinReturnProductTaskService;
    @Autowired
    StockinReturnProductLogService stockinReturnProductLogService;
    @Autowired
    StockinReturnProductTaskItemService stockinReturnProductTaskItemService;
    @Autowired
    StockinReturnProductTaskMapper stockinReturnProductTaskMapper;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    private SupplierService supplierService;


    /**
     * 扫描库位
     *
     * @param positionCode
     * @return
     */
    public StockinReturnProductTaskScanPositionResponse scanPosition(String positionCode) {
        BdPositionEntity position = bdPositionService.getPositionByCode(positionCode);

        BdSupplierPositionMappingEntity mapping = supplierPositionMappingService.getEnableByPositionCode(positionCode);
        StockinReturnProductTaskScanValidatePositionBo bo = new StockinReturnProductTaskScanValidatePositionBo(positionCode, position, mapping);
        validatePosition(bo);
        StockinReturnProductInfoGetBo productInfoGetBo = new StockinReturnProductInfoGetBo();
        productInfoGetBo.setPositionCode(positionCode);
        List<StockinReturnProductInfo> productInfoList = stockinReturnProductMapper.findInfoList(productInfoGetBo);
        int total = productInfoList.stream().mapToInt(StockinReturnProductInfo::getReturnQty).sum();
        if (total <= 0)
            throw new BusinessServiceException(String.format("库位%s ，退货数是0，无法操作退货出库", positionCode));

        StockinReturnProductTaskScanPositionResponse response = new StockinReturnProductTaskScanPositionResponse();
        response.setSupplierName(mapping.getSupplierName());
        response.setReturnNature(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_RETURN_NATURE.getName(), mapping.getReturnNature()));
        response.setQty(total);

        productInfoList.forEach(item -> {
            item.setBrandName(StringUtils.hasText(item.getBrandName()) ? item.getBrandName() : "非品牌");
            if (Objects.isNull(item.getSourceAreaId())) {
                item.setSourceAreaId(0);
                item.setSourceAreaName("无区域");
            }

            if (item.getSourceAreaId() < 0) {
                item.setSourceAreaId(position.getAreaId());
                item.setSourceAreaName(position.getAreaName());
            }
        });
        Map<String, List<StockinReturnProductInfo>> productInfoMap = productInfoList.stream()
                .filter(returnProduct -> returnProduct.getReturnQty() > 0)
                .collect(Collectors.groupingBy(item -> item.getSku() + "_" + item.getSourceAreaId() + "_" + item.getPackingMethod() + "_" + item.getVersionNo()));

        //查询是否有采购价
        CheckSupplierSkuPriceStatusRequest checkRequest = new CheckSupplierSkuPriceStatusRequest();
        checkRequest.setSupplierId(mapping.getSupplierId());
        checkRequest.setSpecIdList(productInfoList.stream().map(StockinReturnProductInfo::getSpecId).distinct().collect(Collectors.toList()));
        Map<Integer, Integer> priceMap = scmApiService.checkSupplierSkuPriceStatus(checkRequest).stream()
                .collect(Collectors.toMap(CheckSupplierSkuPriceStatusDto::getSpecId, CheckSupplierSkuPriceStatusDto::getIsSetPrice));

        List<StockinReturnProductTaskScanPositionItemResponse> itemList = productInfoMap.values().stream()
                .map(tempList -> {
                    int returnQty = tempList.stream().mapToInt(StockinReturnProductInfo::getReturnQty).sum();
                    StockinReturnProductInfo returnProduct = tempList.get(0);
                    return getStockinReturnProductTaskScanPositionItemResponse(priceMap, returnQty, returnProduct);
                }).sorted(Comparator.comparing(StockinReturnProductTaskScanPositionItemResponse::getCreateDate)).collect(Collectors.toList());
        response.setItemList(itemList);

        //设置工厂是否停用
        response.setUse(supplierPositionMappingService.getSupplierIsUse(mapping));

        return response;
    }


    @NotNull
    private StockinReturnProductTaskScanPositionItemResponse getStockinReturnProductTaskScanPositionItemResponse(Map<Integer, Integer> priceMap, int returnQty, StockinReturnProductInfo returnProduct) {
        StockinReturnProductTaskScanPositionItemResponse item = new StockinReturnProductTaskScanPositionItemResponse();
        item.setSku(returnProduct.getSku());
        item.setBrandName(returnProduct.getBrandName());
        item.setReturnProductId(returnProduct.getReturnProductId());
        item.setQty(returnQty);
        item.setIsSetPrice(priceMap.getOrDefault(returnProduct.getSpecId(), 0));
        item.setBarcode(returnProduct.getBarcode());
        item.setImageUrl(returnProduct.getImageUrl());
        item.setPreviewImageUrl(returnProduct.getPreviewImageUrl());
        item.setThumbnailImageUrl(returnProduct.getThumbnailImageUrl());
        item.setPurchasePlanNo(returnProduct.getPurchasePlanNo());
        item.setCreateDate(returnProduct.getCreateDate());
        item.setSourceAreaId(returnProduct.getSourceAreaId());
        item.setSourceAreaName(returnProduct.getSourceAreaName());
        item.setPackingMethod(returnProduct.getPackingMethod());
        item.setVersionNo(returnProduct.getVersionNo());
        return item;
    }

    /**
     * 验证库位是否合法
     *
     * @param bo
     */
    private void validatePosition(StockinReturnProductTaskScanValidatePositionBo bo) {
        String positionCode = bo.getPositionCode();
        BdPositionEntity position = bo.getPosition();
        BdSupplierPositionMappingEntity mapping = bo.getMapping();

        if (Objects.isNull(position)) throw new BusinessServiceException(String.format("%s 库位不存在", positionCode));
        if (!BdPositionTypeEnum.RETURN_POSITION.name().equals(position.getPositionType()))
            throw new BusinessServiceException("采购退货登记只能用于退货库位退货");

        if (Objects.isNull(mapping))
            throw new BusinessServiceException(String.format("库位%s，请绑定供应商", positionCode));
    }

    /**
     * 退货完成
     * 1. 创建退货任务，记录日志
     * 2. 创建退货任务明细，退货任务明细状态 退货完成
     * 3. 扣减退货明细
     * 4. 扣减退货库位库存
     * 5. 更改退货任务状态 待仓库发货，记录日志
     * 6. 同步.net生成退货单 -- 取消该步骤，等待发货后在同步
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "scanFinished", lockKey = "#request.positionCode")
    public void scanFinished(StockinReturnProductTaskScanFinishedRequest request) {
        request.setItemList(request.getItemList().stream().filter(item -> item.getQty() > 0).collect(Collectors.toList()));
        BdPositionEntity position = bdPositionService.getPositionByCode(request.getPositionCode());
        BdSupplierPositionMappingEntity mapping = supplierPositionMappingService.getEnableByPositionCode(request.getPositionCode());
        StockinReturnProductTaskScanValidatePositionBo bo = new StockinReturnProductTaskScanValidatePositionBo(request.getPositionCode(), position, mapping);
        //验证库位是否合法
        validatePosition(bo);
        //退款退货, 校验当前SKU在SCM是否有该工厂的采购价
        List<String> skuList = request.getItemList().stream().map(StockinReturnProductTaskScanFinishedItemRequest::getSku).collect(Collectors.toList());
        validSupplierSkuPriceStatus(skuList, mapping.getSupplierId(), mapping.getSupplierName());
        //查出该库位所有要退货的商品
        List<StockinReturnProductItemModel> positionItemList = supplierPositionMappingMapper.searchByPositionCode(request.getPositionCode());
        positionItemList.forEach(item -> {
            item.setSourceAreaId(Objects.nonNull(item.getSourceAreaId()) ? item.getSourceAreaId() : 0);
            item.setSourceAreaId(item.getSourceAreaId() < 0 ? position.getAreaId() : item.getSourceAreaId());
            item.setBrandName(StringUtils.hasText(item.getBrandName()) ? item.getBrandName() : "非品牌");
        });
        if (positionItemList.isEmpty()) throw new BusinessServiceException("库位上不存在待退货商品");
        Map<String, List<StockinReturnProductItemModel>> positionItemMap = positionItemList.stream().collect(Collectors.groupingBy(item -> item.getSku() + "_" + item.getSourceAreaId() + "_" + item.getPackingMethod() + "_" + item.getVersionNo()));        //生成退货任务
        StockinReturnProductTaskEntity taskEntity = saveTask(request.getItemList(), positionItemMap);
        sendDt(taskEntity);
    }

    private StockinReturnProductTaskEntity saveTask(List<StockinReturnProductTaskScanFinishedItemRequest> itemList, Map<String, List<StockinReturnProductItemModel>> positionItemMap) {
        //1. 创建退货任务，记录日志
        StockinReturnProductTaskEntity task = stockinReturnProductTaskPdaService.saveReturnTaskEntity(positionItemMap.values().iterator().next().get(0));
        List<StockinReturnProductTaskItemEntity> taskItemList = new ArrayList<>();

        //创建退货任务，记录日志
        itemList.forEach(item -> {
            List<StockinReturnProductItemModel> productItemList = positionItemMap.get(item.getSku() + "_" + item.getSourceAreaId() + "_" + item.getPackingMethod() + "_" + item.getVersionNo());
            int restQty = item.getQty();

            if (CollectionUtils.isEmpty(productItemList))
                throw new BusinessServiceException(String.format("sku【 %s 】 不存在退货商品明细", item.getSku()));
            if (item.getQty() > productItemList.stream().mapToInt(StockinReturnProductItemModel::getWaitReturnQty).sum())
                throw new BusinessServiceException(String.format("sku【 %s 】 扫描数大于待退货数", item.getSku()));
            //按创建时间排序
            productItemList = productItemList.stream()
                    .filter(temp -> temp.getWaitReturnQty() > 0)
                    .sorted(Comparator.comparing(StockinReturnProductItemModel::getCreateDate)).collect(Collectors.toList());
            dealReturnItem(task, taskItemList, productItemList, restQty);
        });

        // 5. 更改退货任务状态 待仓库发货，记录日志
        checkAndChangeTaskStatus(task);
        return task;
    }


    private void dealReturnItem(StockinReturnProductTaskEntity task, List<StockinReturnProductTaskItemEntity> taskItemList, List<StockinReturnProductItemModel> productItemList, int qty) {
        int restQty = qty;
        //同一退货库位，工厂出库单号+入库单+采购单+sku+退货原因相同，生成同一退货明细
        Collection<List<StockinReturnProductItemModel>> values = productItemList.stream()
                .collect(Collectors.groupingBy(item -> item.getSupplierDeliveryNo() + "_" + item.getStockinOrderNo() + "_" + item.getPurchasePlanNo() + "_" + item.getSku() + "_" + item.getUnqualifiedReason(),
                        Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().sorted(Comparator.comparing(StockinReturnProductItemModel::getCreateDate)).collect(Collectors.toList())
                        )))
                .values().stream().sorted(Comparator.comparing(o -> o.get(0).getCreateDate())).collect(Collectors.toList());
        for (List<StockinReturnProductItemModel> itemList : values) {
            int useQty = Math.min(restQty, itemList.stream().mapToInt(StockinReturnProductItemModel::getWaitReturnQty).sum());
            restQty -= useQty;
            // 2. 创建退货任务明细，退货任务明细状态 待仓库发货
            String stockinOrderNo = itemList.get(0).getStockinOrderNo();
            //有源单
            if (StringUtils.hasText(stockinOrderNo))
                taskItemList.add(saveReturnTaskItemEntityList(itemList, useQty, task));
            else {
                //无源单需要根据来源区域ID生成明细
                dealReturnItemNoSource(task, taskItemList, itemList, useQty);
            }
            // 3. 扣减退货明细  4. 扣减退货库位库存
            minusReturnStock(itemList, useQty);
            if (restQty <= 0) break;
        }
    }

    private void dealReturnItemNoSource(StockinReturnProductTaskEntity task, List<StockinReturnProductTaskItemEntity> taskItemList, List<StockinReturnProductItemModel> itemList, int qty) {
        int restQty = qty;
        Map<Integer, List<StockinReturnProductItemModel>> collect = itemList.stream().collect(Collectors.groupingBy(StockinReturnProductItemModel::getSourceAreaId));
        for (List<StockinReturnProductItemModel> modelItemList : collect.values()) {
            int useQty = Math.min(restQty, modelItemList.stream().mapToInt(StockinReturnProductItemModel::getWaitReturnQty).sum());
            taskItemList.add(saveReturnTaskItemEntityList(modelItemList, useQty, task));
            restQty -= useQty;
            if (restQty <= 0) break;
        }
    }

    private void minusReturnStock(List<StockinReturnProductItemModel> itemList, int qty) {
        int useQty = qty;
        for (StockinReturnProductItemModel positionItem : itemList) {
            int waitReturnQty = Math.min(useQty, positionItem.getWaitReturnQty());
            // 3. 扣减退货明细
            stockinReturnProductTaskPdaService.minusReturnProductDetail(positionItem.getReturnProductId(), waitReturnQty);
            // 4. 扣减退货库位库存
            stockinReturnProductTaskPdaService.reduceStock(positionItem, waitReturnQty);
            useQty -= waitReturnQty;
            if (useQty <= 0) break;
        }
    }

    /**
     * 校验当前SKU在SCM是否有该工厂的采购价
     */
    public void validSupplierSkuPriceStatus(List<String> skuList, Integer supplierId, String supplierName) {
        List<ProductSpecInfoEntity> specInfoList = productSpecInfoService.getListBySkuList(skuList);
        Map<String, ProductSpecInfoEntity> specInfoMap = specInfoList.stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity(), (v1, v2) -> v1));

        CheckSupplierSkuPriceStatusRequest checkRequest = new CheckSupplierSkuPriceStatusRequest();
        checkRequest.setSupplierId(supplierId);
        //scm获取采购价
        List<Integer> specIdList = skuList.stream().map(sku -> {
            ProductSpecInfoEntity specInfo = specInfoMap.get(sku);
            if (Objects.isNull(specInfo)) {
                throw new BusinessServiceException(String.format("【%s】 找不到 商品规格信息", sku));
            }
            return specInfo.getSpecId();
        }).collect(Collectors.toList());
        checkRequest.setSpecIdList(specIdList);
        Map<Integer, Integer> priceMap = scmApiService.checkSupplierSkuPriceStatus(checkRequest).stream()
                .collect(Collectors.toMap(CheckSupplierSkuPriceStatusDto::getSpecId, CheckSupplierSkuPriceStatusDto::getIsSetPrice, (v1, v2) -> v1));
        //对比查找没有设置采购价的
        List<String> unSetPriceSkuList = skuList.stream()
                .filter(sku -> {
                    ProductSpecInfoEntity specInfo = specInfoMap.get(sku);
                    Integer isSet = priceMap.getOrDefault(specInfo.getSpecId(), 0);
                    return isSet == 0;
                }).collect(Collectors.toList());
        if (unSetPriceSkuList.isEmpty()) return;

        String errorMsg = "工厂【%s】，规格编码%s采购价为0，无法生成退货单，请联系业务维护";
        String skuStr = unSetPriceSkuList.stream().map(sku -> String.format("【%s】", sku)).collect(Collectors.joining());
        throw new BusinessServiceException(String.format(errorMsg, supplierName, skuStr));
    }

    /**
     * 发送钉钉消息
     * 钉钉提示对应的采购员和工厂对应的QC
     *
     * @param task
     */
    public void sendDt(StockinReturnProductTaskEntity task) {
        try {
            List<StockinReturnProductTaskItemEntity> taskItemList = stockinReturnProductTaskItemService.listByTaskId(task.getReturnProductTaskId());
            // 取supplier表的采购和qc
            SupplierEntity supplier = supplierService.getBySupplierId(task.getSupplierId());

            if (Objects.isNull(supplier)) {
                LOGGER.error("查找不到供应商 【 {} 】", task.getSupplierId());
                return;
            }
            LOGGER.info(JSONUtils.toJSON(supplier));
            List<String> userNameList = new ArrayList<>(2);
            if (Objects.nonNull(supplier.getContactQcEmpId())) {
                SysUserInfo userInfoByUserId = userApiService.getUserInfoByUserId(supplier.getContactQcEmpId());
                Optional.ofNullable(userInfoByUserId).ifPresent(user -> userNameList.add(user.getUserAccount()));
            }

            if (Objects.nonNull(supplier.getContactPurchaserEmpId())) {
                SysUserInfo userInfoByUserId = userApiService.getUserInfoByUserId(supplier.getContactPurchaserEmpId());
                Optional.ofNullable(userInfoByUserId).ifPresent(user -> userNameList.add(user.getUserAccount()));
            }

            if (userNameList.isEmpty()) {
                LOGGER.info("【采购退货登记】钉钉消息发送人为空");
                return;
            }

            String messageInfo = this.buildReturnMessageInfo(task, taskItemList);
            if (!StringUtils.hasText(messageInfo)) {
                LOGGER.info("【采购退货登记】消息内容为空");
                return;
            }
            SendDingDingMessage sendDingDingMessage = new SendDingDingMessage();
            sendDingDingMessage.setText(messageInfo);
            sendDingDingMessage.setUserNameList(userNameList);
            sendDingDingMessage.setTextType("markdown");
            sendDingDingMessage.setMarkdownTitle("采购退货登记");
            //发送kafka消息
            String businessMark = KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME;
            messageProducer.sendMessage(businessMark, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
        } catch (Exception e) {
            LOGGER.error(String.format("采购退货登记发送通知失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 更改退货任务状态 待仓库发货，记录日志
     *
     * @param taskEntity
     */
    public void checkAndChangeTaskStatus(StockinReturnProductTaskEntity taskEntity) {
        taskEntity.setStatus(ReturnProductStatusEnum.READY_DELIVERY.name());
        taskEntity.setUpdateBy(loginInfoService.getName());
        taskEntity.setOperator(loginInfoService.getName());
        taskEntity.setOperateStartDate(new Date());
        taskEntity.setOperateEndDate(new Date());
        stockinReturnProductTaskService.updateById(taskEntity);

        StockinReturnProductLog log = stockinReturnProductLogService.getLog(taskEntity.getReturnProductTaskId(), ReturnProductConstant.FINISH_TASK, String.format("任务ID: %s：退货完成，任务变更【待仓库发货】", taskEntity.getReturnProductTaskId()), loginInfoService.getName(), loginInfoService.getIpAddress());
        stockinReturnProductLogService.saveLog(log);
    }

    /**
     * 创建退货任务明细，退货任务明细状态 退货完成
     *
     * @param itemModel
     * @param returnQty
     * @param task
     * @return
     */
    public StockinReturnProductTaskItemEntity saveReturnTaskItemEntity(StockinReturnProductItemModel itemModel, Integer returnQty, StockinReturnProductTaskEntity task) {
        StockinReturnProductTaskItemEntity itemEntity = new StockinReturnProductTaskItemEntity();
        BeanUtils.copyProperties(itemModel, itemEntity);
        if ("非品牌".equals(itemModel.getBrandName()))
            itemEntity.setBrandName(null);
        itemEntity.setWaitReturnQty(returnQty);
        itemEntity.setActualReturnQty(returnQty);
        itemEntity.setLackProductQty(0);
        itemEntity.setStatus(ReturnProductStatusEnum.READY_DELIVERY.name());
        itemEntity.setReturnProductTaskId(task.getReturnProductTaskId());
        stockinReturnProductTaskItemService.save(itemEntity);

        StockinReturnProductLog log = stockinReturnProductLogService.getLog(task.getReturnProductTaskId(), ReturnProductConstant.SCAN_TASK, String.format("任务ID: %s，sku：%s，共扫描%s件", task.getReturnProductTaskId(), itemModel.getSku(), returnQty), loginInfoService.getName(), loginInfoService.getIpAddress());
        stockinReturnProductLogService.saveLog(log);
        return itemEntity;
    }

    /**
     * 创建退货任务明细，退货任务明细状态 退货完成
     *
     * @param itemModelList
     * @param returnQty
     * @param task
     * @return
     */
    public StockinReturnProductTaskItemEntity saveReturnTaskItemEntityList(List<StockinReturnProductItemModel> itemModelList, Integer returnQty, StockinReturnProductTaskEntity task) {
        StockinReturnProductTaskItemEntity itemEntity = new StockinReturnProductTaskItemEntity();
        StockinReturnProductItemModel itemModel = itemModelList.get(0);
        BeanUtils.copyProperties(itemModel, itemEntity);
        if ("非品牌".equals(itemModel.getBrandName()))
            itemEntity.setBrandName(null);
        itemEntity.setWaitReturnQty(returnQty);
        itemEntity.setActualReturnQty(returnQty);
        itemEntity.setSourceAreaId(itemModelList.stream().filter(item -> Objects.nonNull(item.getSourceAreaId()) && item.getSourceAreaId() != 0)
                .map(StockinReturnProductItemModel::getSourceAreaId).findAny().orElse(null));
        itemEntity.setLackProductQty(0);
        itemEntity.setStatus(ReturnProductStatusEnum.READY_DELIVERY.name());
        itemEntity.setReturnProductTaskId(task.getReturnProductTaskId());
        stockinReturnProductTaskItemService.save(itemEntity);

        StockinReturnProductLog log = stockinReturnProductLogService.getLog(task.getReturnProductTaskId(), ReturnProductConstant.SCAN_TASK, String.format("任务ID: %s，sku：%s，共扫描%s件", task.getReturnProductTaskId(), itemModel.getSku(), returnQty), loginInfoService.getName(), loginInfoService.getIpAddress());
        stockinReturnProductLogService.saveLog(log);
        return itemEntity;
    }

    /**
     * 查询退货任务列表统计
     *
     * @param request
     * @return PageResponse<StockinReturnProductTaskListResponse>
     */
    public StockinReturnProductTaskListCountResponse getTaskListCount(StockinReturnProductTaskListRequest request) {
        request.setLocation(TenantContext.getTenant());
        return stockinReturnProductTaskMapper.getTaskListCount(request);
    }

    /**
     * 组装退货单生成钉钉消息发送
     *
     * @param taskInfo
     * @param taskItemList
     * @return
     */
    private String buildReturnMessageInfo(StockinReturnProductTaskEntity taskInfo, List<StockinReturnProductTaskItemEntity> taskItemList) {

        if (CollUtil.isEmpty(taskItemList)) {
            return null;
        }
        String messageType = StockinReturnNatureEnum.REFUND_RETURN.name().equals(taskInfo.getReturnNature()) ? "架上质检退货" : "工厂入库质检退货";
        String headMessage = messageType + ":【" + taskInfo.getSupplierName() + "】\n退货ID:" + taskInfo.getReturnProductTaskId() + "\n退货总件数:"
                + taskItemList.stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum() + "\n退货日期:"
                + DateUtil.format(taskInfo.getOperateStartDate(), "yyyy-MM-dd") + "\n退货状态:" + ReturnProductStatusEnum.getValueByCode(taskInfo.getStatus()) + "\n退货商品明细:";
        StringBuilder messageInfo = new StringBuilder(headMessage);
        for (StockinReturnProductTaskItemEntity taskItemInfoEntity : taskItemList) {
            messageInfo.append("\n规格编码:").append(taskItemInfoEntity.getSku());
            messageInfo.append("\n退货件数:").append(taskItemInfoEntity.getActualReturnQty());
            messageInfo.append("\n退货原因:").append(StringUtils.hasText(taskItemInfoEntity.getUnqualifiedReason()) ? taskItemInfoEntity.getUnqualifiedReason() : "");
        }
        return messageInfo.toString();
    }


}
