package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.request.qa.StockinQaOperateUnqualifiedRequest;
import com.nsy.api.wms.response.qa.StockinQaOrderProcess;
import com.nsy.api.wms.response.qa.StockinQaOrderUnqualifiedResponse;
import com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderUnqualifiedEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderUnqualifiedMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单不合格原因表业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderUnqualifiedService extends ServiceImpl<StockinQaOrderUnqualifiedMapper, StockinQaOrderUnqualifiedEntity> {

    /**
     * 保存不合格原因信息
     *
     * @param qaOrderProcessEntity
     * @param request
     */
    public void saveUnqualifiedByOperate(StockinQaOrderProcessEntity qaOrderProcessEntity, StockinQaOperateUnqualifiedRequest request) {
        //如果已经存在质检结果的不合格原因，需要覆盖
        this.update(new LambdaUpdateWrapper<StockinQaOrderUnqualifiedEntity>()
                .set(StockinQaOrderUnqualifiedEntity::getIsDeleted, 1)
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, qaOrderProcessEntity.getStockinQaOrderId()));

        StockinQaOrderUnqualifiedEntity entity = new StockinQaOrderUnqualifiedEntity();
        entity.setLocation(TenantContext.getTenant());
        entity.setStockinQaOrderId(qaOrderProcessEntity.getStockinQaOrderId());
        entity.setUnqualifiedCategory(request.getUnqualifiedCategory());
        entity.setUnqualifiedReason(request.getUnqualifiedReason());
        entity.setUnqualifiedQuestion(request.getUnqualifiedQuestion());
        entity.setStockinQaOrderProcessId(qaOrderProcessEntity.getStockinQaOrderProcessId());
        entity.setProcessName(qaOrderProcessEntity.getProcessName());
        entity.setSort(1);
        this.save(entity);

        if (StringUtils.hasText(request.getUnqualifiedCategorySecondary())) {
            StockinQaOrderUnqualifiedEntity unqualifiedEntity = new StockinQaOrderUnqualifiedEntity();
            unqualifiedEntity.setLocation(TenantContext.getTenant());
            unqualifiedEntity.setStockinQaOrderId(qaOrderProcessEntity.getStockinQaOrderId());
            unqualifiedEntity.setUnqualifiedCategory(request.getUnqualifiedCategorySecondary());
            unqualifiedEntity.setUnqualifiedReason(request.getUnqualifiedReasonSecondary());
            unqualifiedEntity.setUnqualifiedQuestion(request.getUnqualifiedQuestionSecondary());
            unqualifiedEntity.setProcessName(qaOrderProcessEntity.getProcessName());
            unqualifiedEntity.setStockinQaOrderProcessId(qaOrderProcessEntity.getStockinQaOrderProcessId());
            unqualifiedEntity.setSort(2);
            this.save(unqualifiedEntity);
        }
    }


    /**
     * 获取稽查不合格原因归类
     *
     * @param stockinQaOrderId
     * @param stockinQaOrderProcessId
     * @return
     */
    public String getInspectUnqualifiedCategory(Integer stockinQaOrderId, Integer stockinQaOrderProcessId) {
        //兼容旧数据，查询未删除的且流程id=0（旧数据流程的id都为0）
        List<StockinQaOrderUnqualifiedEntity> unqualifiedEntityList = this.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                .eq(StockinQaOrderUnqualifiedEntity::getProcessName, StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.getStatus())
                .eq(StockinQaOrderUnqualifiedEntity::getIsDeleted, 0)
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderProcessId, 0)
                .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
        //如果找不到带流程信息去找,即新数据
        if (CollectionUtils.isEmpty(unqualifiedEntityList)) {
            unqualifiedEntityList = this.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                    .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                    .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderProcessId, stockinQaOrderProcessId)
                    .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
        }
        if (CollectionUtils.isEmpty(unqualifiedEntityList)) {
            return "";
        }
        return unqualifiedEntityList.get(0).getUnqualifiedCategory();
    }

    public List<StockinQaOrderUnqualifiedEntity> findByStockinQaOrderId(Integer stockinQaOrderId) {
        return this.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                .eq(StockinQaOrderUnqualifiedEntity::getIsDeleted, 0)
                .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
    }

    /**
     * 获取质检不合格信息，如果存在多条取创建时间最晚一条
     *
     * @param stockinQaOrderIdList
     * @return
     */
    public Map<Integer, List<StockinQaOrderUnqualifiedResponse>> getUnqualifiedList(List<Integer> stockinQaOrderIdList) {
        if (CollectionUtils.isEmpty(stockinQaOrderIdList)) {
            return new HashMap<>();
        }
        List<StockinQaOrderUnqualifiedResponse> unqualifiedList = new ArrayList<>();
        CollectionUtil.split(stockinQaOrderIdList.stream().distinct().collect(Collectors.toList()), 150).stream().forEach(
                detail -> {
                    List<StockinQaOrderUnqualifiedResponse> unqualifiedTempList = this.getBaseMapper().getUnqualifiedList(detail);
                    if (!CollectionUtils.isEmpty(unqualifiedTempList)) {
                        unqualifiedList.addAll(unqualifiedTempList);
                    }
                }
        );
        if (CollectionUtils.isEmpty(unqualifiedList)) {
            return new HashMap<>();
        }
        return unqualifiedList.stream().sorted((o1, o2) -> Integer.compare(o1.getSort(), o2.getSort())).collect(Collectors.groupingBy(StockinQaOrderUnqualifiedResponse::getStockinQaOrderId));
    }

    public List<StockinQaOrderUnqualifiedEntity> findByStockinQaOrderIdAndProcess(Integer stockinQaOrderId, StockinQaOrderProcess process) {
        List<StockinQaOrderUnqualifiedEntity> unqualifiedEntityList = new ArrayList<>();
        if (Objects.nonNull(process.getStockinQaOrderProcessId())) {
            unqualifiedEntityList = this.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                    .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                    .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderProcessId, process.getStockinQaOrderProcessId())
                    .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
        }
        if (!CollectionUtils.isEmpty(unqualifiedEntityList)) {
            return unqualifiedEntityList;
        }
        return this.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                .eq(StockinQaOrderUnqualifiedEntity::getProcessName, process.getProcessName())
                .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
    }
}
