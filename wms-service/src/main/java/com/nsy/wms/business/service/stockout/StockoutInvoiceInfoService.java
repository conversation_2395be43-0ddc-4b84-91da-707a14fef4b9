package com.nsy.wms.business.service.stockout;

import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintItemInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.domain.logistics.documents.request.LogisticsDocumentsBaseRequestInfo;
import com.nsy.wms.repository.entity.stockout.StockoutInvoiceInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutInvoiceInfoMapper;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class StockoutInvoiceInfoService extends ServiceImpl<StockoutInvoiceInfoMapper, StockoutInvoiceInfoEntity> {

    @Autowired
    LoginInfoService loginInfoService;

    public void addStockoutInvoiceInfo(String logisticsCompany, String logisticsNo, String invoiceContent, LogisticsDocumentsBaseRequestInfo baseRequestInfo) {
        StockoutInvoiceInfoEntity stockoutInvoiceInfoEntity = new StockoutInvoiceInfoEntity();
        stockoutInvoiceInfoEntity.setStockoutOrderNo(baseRequestInfo.getStockoutOrderNo());
        stockoutInvoiceInfoEntity.setLogisticsNo(logisticsNo);
        stockoutInvoiceInfoEntity.setLogisticsCompany(logisticsCompany);
        stockoutInvoiceInfoEntity.setLogisticsAccount(baseRequestInfo.getLogisticsAccount());
        stockoutInvoiceInfoEntity.setGoodsPrice(baseRequestInfo.getCommodityList().stream().map(commodity -> commodity.getUnitPrice().multiply(new BigDecimal(commodity.getQty()))).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockoutInvoiceInfoEntity.setHandingFee(baseRequestInfo.getHandingFee());
        stockoutInvoiceInfoEntity.setFreightCharges(baseRequestInfo.getFreightCharges());
        stockoutInvoiceInfoEntity.setTotalWeight(baseRequestInfo.getPackageWeight());
        stockoutInvoiceInfoEntity.setGoodsQty(baseRequestInfo.getCommodityList().stream().mapToInt(LogisticsDocumentsPrintItemInfo::getQty).sum());
        stockoutInvoiceInfoEntity.setInvoiceContent(invoiceContent);
        save(stockoutInvoiceInfoEntity);
    }

    public StockoutInvoiceInfoEntity getByLogisticsNo(String logisticsNo) {
        QueryWrapper<StockoutInvoiceInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockoutInvoiceInfoEntity::getLogisticsNo, logisticsNo).orderByDesc(StockoutInvoiceInfoEntity::getInvoiceInfoId).last("limit 1");
        return this.getOne(queryWrapper);
    }

    public LogisticsDocumentsBaseRequestInfo getItemListSizeByLogisticsNo(String logisticsNo) {
        QueryWrapper<StockoutInvoiceInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockoutInvoiceInfoEntity::getLogisticsNo, logisticsNo).orderByDesc(StockoutInvoiceInfoEntity::getInvoiceInfoId).last("limit 1");
        StockoutInvoiceInfoEntity one = this.getOne(queryWrapper);
        if (one == null) {
            return new LogisticsDocumentsBaseRequestInfo();
        }
        LogisticsDocumentsBaseRequestInfo requestInfo = JsonMapper.fromJson(one.getInvoiceContent(), LogisticsDocumentsBaseRequestInfo.class);
        if (requestInfo == null || requestInfo.getCommodityList() == null) {
            return new LogisticsDocumentsBaseRequestInfo();
        } else {
            requestInfo.setTotalGoodsPrice(one.getGoodsPrice());
            return requestInfo;
        }

    }


}
