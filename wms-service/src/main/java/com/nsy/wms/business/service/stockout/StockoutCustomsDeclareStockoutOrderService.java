package com.nsy.wms.business.service.stockout;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.stockout.CustomsDeclareStockoutOrderStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareStockoutOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareStockoutOrderResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareDocumentItemBo;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareCustomerOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareStockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareStockoutOrderItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareStockoutOrderMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class StockoutCustomsDeclareStockoutOrderService extends ServiceImpl<StockoutCustomsDeclareStockoutOrderMapper, StockoutCustomsDeclareStockoutOrderEntity> {


    @Resource
    public StockoutCustomsDeclareStockoutOrderItemService stockoutCustomsDeclareStockoutOrderItemService;
    @Resource
    StockoutCustomsDeclareCustomerOrderService stockoutCustomsDeclareCustomerOrderService;

    /**
     * 分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutCustomsDeclareStockoutOrderResponse> pageList(StockoutCustomsDeclareStockoutOrderPageRequest request) {
        Page<StockoutCustomsDeclareStockoutOrderResponse> pageResult = this.baseMapper.findPage(new Page<>(request.getPageIndex(), request.getPageSize(), false), request);

        pageResult.getRecords().forEach(temp -> temp.setStatus(CustomsDeclareStockoutOrderStatusEnum.of(temp.getStatus())));
        PageResponse<StockoutCustomsDeclareStockoutOrderResponse> response = new PageResponse<>();
        response.setContent(pageResult.getRecords());
        response.setTotalCount(this.baseMapper.countPage(request));
        return response;
    }

    /**
     * 明细
     *
     * @param declareStockoutOrderId
     * @return
     */
    public StockoutCustomsDeclareStockoutOrderResponse detail(Integer declareStockoutOrderId) {
        StockoutCustomsDeclareStockoutOrderEntity declareStockoutOrder = getById(declareStockoutOrderId);
        if (ObjectUtil.isNull(declareStockoutOrder))
            throw new BusinessServiceException("客户订单不存在");

        return buildResponse(declareStockoutOrder);
    }

    public StockoutCustomsDeclareStockoutOrderResponse buildResponse(StockoutCustomsDeclareStockoutOrderEntity declareStockoutOrder) {
        StockoutCustomsDeclareStockoutOrderResponse temp = BeanUtil.toBean(declareStockoutOrder, StockoutCustomsDeclareStockoutOrderResponse.class);
        temp.setStatus(CustomsDeclareStockoutOrderStatusEnum.of(temp.getStatus()));

        List<StockoutCustomsDeclareStockoutOrderItemEntity> stockoutOrderItemList = stockoutCustomsDeclareStockoutOrderItemService.listByDeclareStockoutOrderId(declareStockoutOrder.getId());
        int pendingQty = stockoutOrderItemList.stream().mapToInt(StockoutCustomsDeclareStockoutOrderItemEntity::getPendingQty).sum();
        int qty = stockoutOrderItemList.stream().mapToInt(StockoutCustomsDeclareStockoutOrderItemEntity::getQty).sum();
        temp.setPendingQty(pendingQty);
        temp.setQty(qty);

        StockoutCustomsDeclareCustomerOrderEntity declareCustomerOrderEntity = stockoutCustomsDeclareCustomerOrderService.findByOrderNo(declareStockoutOrder.getOrderNo());
        if (ObjectUtil.isNotNull(declareCustomerOrderEntity))
            temp.setDeclareCustomerOrderId(declareCustomerOrderEntity.getId());

        return temp;
    }


    /**
     * 新增
     *
     * @param customerOrderEntity
     * @param declareDocumentItemList
     * @param form
     * @return
     */
    @Transactional
    public StockoutCustomsDeclareStockoutOrderEntity add(StockoutCustomsDeclareCustomerOrderEntity customerOrderEntity, List<StockoutCustomsDeclareDocumentItemBo> declareDocumentItemList, StockoutCustomsDeclareFormEntity form) {
        if (CollectionUtil.isEmpty(declareDocumentItemList)) {
            throw new BusinessServiceException(String.format("报关单据明细为空 %s %s ", form.getProtocolNo(), form.getgNo()));
        }

        //不存在, 则新增
        //通过order_no查找客户订单  --  不存在，则新增表头
        StockoutCustomsDeclareStockoutOrderEntity entity = findByOrderNo(customerOrderEntity.getOrderNo());
        if (ObjectUtil.isNull(entity)) {
            entity = BeanUtil.toBean(customerOrderEntity, StockoutCustomsDeclareStockoutOrderEntity.class);
            Date createDate = fetchCreateDate(form.getStockoutDate());
            String code = String.format("%04d", countByDate(createDate) + 1);
            String date = DateUtil.format(createDate, "yyyyMMdd").substring(2);
            entity.setStockoutOrderNo("XSCK" + date + code);
            entity.setStatus(CustomsDeclareStockoutOrderStatusEnum.DELIVERY.name());
            entity.setCreateDate(createDate);
            save(entity);
        }

        StockoutCustomsDeclareStockoutOrderEntity finalEntity = entity;
        declareDocumentItemList.forEach(item -> stockoutCustomsDeclareStockoutOrderItemService.add(finalEntity, item));


        return entity;

    }

    /**
     * 查找今天创建的列表
     *
     * @return
     */
    private Integer countByDate(Date date) {
        String today = DateUtil.format(date, "yyyy-MM-dd");
        String begin = today + " 00:00:00";
        String end = today + " 23:59:59";
        return count(new LambdaQueryWrapper<StockoutCustomsDeclareStockoutOrderEntity>()
                .ge(StockoutCustomsDeclareStockoutOrderEntity::getCreateDate, begin)
                .le(StockoutCustomsDeclareStockoutOrderEntity::getCreateDate, end));
    }

    /**
     * 通过订单号查找
     *
     * @param orderNo
     * @return
     */
    public StockoutCustomsDeclareStockoutOrderEntity findByOrderNo(String orderNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclareStockoutOrderEntity>()
                .eq(StockoutCustomsDeclareStockoutOrderEntity::getOrderNo, orderNo)
                .last("limit 1"));
    }

    private Date fetchCreateDate(Date stockoutDate) {
        // 计算26天前的日期
        Date past = DateUtil.offsetDay(stockoutDate, -2);

        // 随机生成9点到19点之间的一个时间
        int hour = RandomUtil.randomInt(9, 19); // 包含9和19
        int minute = RandomUtil.randomInt(0, 60);
        int second = RandomUtil.randomInt(0, 60);
        // 生成具体时间
        return DateUtil.parse(DateUtil.format(past, "yyyy-MM-dd") + " " + String.format("%02d:%02d:%02d", hour, minute, second));
    }
}
