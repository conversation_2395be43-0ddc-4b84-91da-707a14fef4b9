package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.FaireShipmentBoxSkuExport;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.repository.entity.stockout.StockoutFaireShipmentEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutFaireShipmentMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockoutFaireShipmentShippedDownloadService implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutFaireShipmentShippedDownloadService.class);


    @Autowired
    StockoutFaireShipmentService stockoutFaireShipmentService;
    @Autowired
    private StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    private StockoutFaireShipmentMapper faireShipmentMapper;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_FAIRE_SHIPMENT_SHIPPED;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        IdListRequest idListRequest = JsonMapper.fromJson(request.getRequestContent(), IdListRequest.class);
        List<StockoutFaireShipmentEntity> faireShipmentEntityList = stockoutFaireShipmentService.list(new LambdaQueryWrapper<StockoutFaireShipmentEntity>()
                .in(StockoutFaireShipmentEntity::getId, idListRequest.getIdList()).orderByAsc(StockoutFaireShipmentEntity::getPrintBoxIndex));
        if (CollectionUtils.isEmpty(faireShipmentEntityList))
            return response;
        List<FaireShipmentBoxSkuExport> resultList = new ArrayList<>();
        faireShipmentEntityList.forEach(entity -> {
            List<StockoutFaireShipmentEntity> list = new ArrayList<>(1);
            if (StockConstant.ENABLE.equals(entity.getIsMerge())) {
                list = stockoutFaireShipmentService.getByMergeFaireShipmentIds(Collections.singletonList(entity.getId()));
            } else {
                list.add(entity);
            }
            List<ShipmentBoxSkuExport> shipmentBoxSkuExports = stockoutShipmentMapper.shipmentBoxSkuList(list.stream().map(StockoutFaireShipmentEntity::getShipmentId).collect(Collectors.toList()), null);
            buildExport(resultList, shipmentBoxSkuExports, entity);
        });
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        response.setTotalCount(1L);
        return response;
    }

    private void buildExport(List<FaireShipmentBoxSkuExport> resultList, List<ShipmentBoxSkuExport> shipmentBoxSkuExports, StockoutFaireShipmentEntity entity) {
        FaireShipmentBoxSkuExport faireShipmentBoxSkuExport = getHead(entity);
        resultList.add(faireShipmentBoxSkuExport);
        Map<String, List<ShipmentBoxSkuExport>> collect = shipmentBoxSkuExports.stream().collect(Collectors.groupingBy(ShipmentBoxSkuExport::getStockoutOrderNo));
        LinkedHashSet<String> strings = new LinkedHashSet<>(collect.size());
        for (Map.Entry<String, List<ShipmentBoxSkuExport>> entry : collect.entrySet()) {
            FaireShipmentBoxSkuExport orderExport = new FaireShipmentBoxSkuExport();
            orderExport.setBoxCode(entry.getKey());
            resultList.add(orderExport);
            for (ShipmentBoxSkuExport export : entry.getValue()) {
                FaireShipmentBoxSkuExport itemExport = new FaireShipmentBoxSkuExport();
                BeanUtilsEx.copyProperties(export, itemExport, "weight", "boxSize");
                String boxCode = "NO." + (entity.getPrintBoxIndex() > 0 ? String.valueOf(entity.getPrintBoxIndex()) : "");
                if (Objects.nonNull(export) && Objects.nonNull(export.getQty())) {
                    itemExport.setQty(String.valueOf(export.getQty()));
                }
                if (!strings.contains(boxCode)) {
                    itemExport.setBoxCode(boxCode);
                    strings.add(boxCode);
                    itemExport.setBoxSize(entity.getBoxSize());
                    itemExport.setWeight(entity.getWeight().toEngineeringString());
                    itemExport.setCube(this.calculateCube(entity));
                    BigDecimal priceByShipmentId = faireShipmentMapper.getProductInvoicePriceByShipmentId(export.getShipmentId());
                    itemExport.setProductTotalInvoicePrice(priceByShipmentId);
                }
                resultList.add(itemExport);
            }
        }
        resultList.add(new FaireShipmentBoxSkuExport());
    }

    /**
     * 计算方数
     *
     * @param entity
     * @return
     */
    private BigDecimal calculateCube(StockoutFaireShipmentEntity entity) {
        try {
            if (!StringUtils.hasText(entity.getBoxSize()) && entity.getBoxSize().contains("*")) {
                return null;
            }
            return Arrays.stream(entity.getBoxSize().split("\\*")).map(BigDecimal::new).reduce(BigDecimal::multiply).orElse(BigDecimal.ZERO).divide(new BigDecimal("1000000"));
        } catch (Exception e) {
            LOGGER.error("计算方数错误", e);
            return BigDecimal.ZERO;
        }
    }

    private FaireShipmentBoxSkuExport getHead(StockoutFaireShipmentEntity entity) {
        FaireShipmentBoxSkuExport faireShipmentBoxSkuExport = new FaireShipmentBoxSkuExport();
        faireShipmentBoxSkuExport.setBoxCode(entity.getPrintBoxCode().concat(entity.getPrintBoxIndex() > 0 ? "-" + entity.getPrintBoxIndex() : ""));
        faireShipmentBoxSkuExport.setCustomsDeclareCn("中文品名");
        faireShipmentBoxSkuExport.setCustomsDeclareEn("英文品名");
        faireShipmentBoxSkuExport.setSpinType("织造方式");
        faireShipmentBoxSkuExport.setFabricType("成分");
        faireShipmentBoxSkuExport.setFiller("填充物");
        faireShipmentBoxSkuExport.setStoreSku("客户Sku");
        faireShipmentBoxSkuExport.setStoreBarcode("客户条形码");
        faireShipmentBoxSkuExport.setQty("数量");
        faireShipmentBoxSkuExport.setElementValue("要素备注");
        faireShipmentBoxSkuExport.setWeight("重量");
        faireShipmentBoxSkuExport.setBoxSize("规格");
        return faireShipmentBoxSkuExport;
    }
}
