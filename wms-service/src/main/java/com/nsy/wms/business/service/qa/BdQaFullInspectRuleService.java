package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.request.qa.BdQaFullInspectRuleSaveRequest;
import com.nsy.api.wms.request.qa.BdQaInspectPageRequest;
import com.nsy.api.wms.response.qa.BdQaFullInspectRuleInfoResponse;
import com.nsy.api.wms.response.qa.BdQaFullInspectRuleItemInfoResponse;
import com.nsy.api.wms.response.qa.BdQaFullInspectRulePageResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.BdQaFullInspectRuleEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.jpa.mapper.qa.BdQaFullInspectRuleMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 13:52
 */
@Service
public class BdQaFullInspectRuleService extends ServiceImpl<BdQaFullInspectRuleMapper, BdQaFullInspectRuleEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BdQaFullInspectRuleService.class);

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private BdQaFullInspectRuleItemService qaFullInspectRuleItemService;
    @Autowired
    private StockinQaOrderService qaOrderService;
    @Autowired
    private StockinQaOrderSkuTypeInfoService qaOrderSkuTypeInfoService;
    @Autowired
    private StockinQaOrderItemService qaOrderItemService;


    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    public PageResponse<BdQaFullInspectRulePageResponse> pageList(BdQaInspectPageRequest request) {
        Page<BdQaFullInspectRuleEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        LambdaQueryWrapper<BdQaFullInspectRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.hasText(request.getRuleName()), BdQaFullInspectRuleEntity::getRuleName, request.getRuleName());
        queryWrapper.eq(Objects.nonNull(request.getIsDeleted()), BdQaFullInspectRuleEntity::getIsDeleted, request.getIsDeleted());
        queryWrapper.orderByAsc(BdQaFullInspectRuleEntity::getSort);
        Page<BdQaFullInspectRuleEntity> pageInfo = this.baseMapper.selectPage(page, queryWrapper);
        PageResponse<BdQaFullInspectRulePageResponse> pageResponse = new PageResponse<>();
        pageResponse.setTotalCount(pageInfo.getTotal());
        if (CollectionUtil.isEmpty(pageInfo.getRecords())) {
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }
        List<BdQaFullInspectRulePageResponse> responseList = new ArrayList<>();
        pageInfo.getRecords().forEach(detail -> {
            BdQaFullInspectRulePageResponse info = new BdQaFullInspectRulePageResponse();
            BeanUtils.copyProperties(detail, info);
            responseList.add(info);
        });
        pageResponse.setContent(responseList);
        return pageResponse;
    }

    /**
     * 获取详情信息
     *
     * @param id
     * @return
     */
    public BdQaFullInspectRuleInfoResponse getInspectRuleInfo(Integer id) {
        BdQaFullInspectRuleEntity entity = this.getById(id);
        BdQaFullInspectRuleInfoResponse response = new BdQaFullInspectRuleInfoResponse();
        BeanUtils.copyProperties(entity, response);
        response.setItemInfo(qaFullInspectRuleItemService.getItemInfo(id));
        return response;
    }

    /**
     * 保存规则信息
     *
     * @param saveRequest
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInspectRuleInfo(BdQaFullInspectRuleSaveRequest saveRequest) {
        this.validRuleInfo(saveRequest.getId(), saveRequest.getRuleName());
        BdQaFullInspectRuleEntity entity = new BdQaFullInspectRuleEntity();
        BeanUtils.copyProperties(saveRequest, entity);
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        this.save(entity);
        //保存明细信息
        qaFullInspectRuleItemService.saveItemInfo(saveRequest.getItemInfo(), entity.getId());
    }

    /**
     * 更新规则信息，全删再全增
     *
     * @param saveRequest
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInspectRuleInfo(BdQaFullInspectRuleSaveRequest saveRequest) {
        if (ObjectUtils.isEmpty(saveRequest.getId())) {
            throw new BusinessServiceException("请确认传入的数据是否正确!");
        }
        this.validRuleInfo(saveRequest.getId(), saveRequest.getRuleName());
        BdQaFullInspectRuleEntity entity = new BdQaFullInspectRuleEntity();
        BeanUtils.copyProperties(saveRequest, entity);
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        this.updateById(entity);
        qaFullInspectRuleItemService.updateItemInfo(saveRequest.getItemInfo(), entity.getId());
    }

    /**
     * 更新规则状态
     *
     * @param id
     * @param status
     */
    public void changeStatus(Integer id, Integer status) {
        BdQaFullInspectRuleEntity entity = this.getById(id);
        if (0 == status) {
            this.validRuleInfo(entity.getId(), entity.getRuleName());
        }
        entity.setIsDeleted(status);
        this.updateById(entity);
    }

    /**
     * 校验规则是否重复
     *
     * @param ruleId
     * @param ruleName
     */
    private void validRuleInfo(Integer ruleId, String ruleName) {
        LambdaQueryWrapper<BdQaFullInspectRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaFullInspectRuleEntity::getIsDeleted, Boolean.FALSE);
        queryWrapper.eq(BdQaFullInspectRuleEntity::getRuleName, ruleName);
        queryWrapper.ne(Objects.nonNull(ruleId), BdQaFullInspectRuleEntity::getId, ruleId);
        if (this.count(queryWrapper) > 0) {
            throw new BusinessServiceException("当前规则名已存在对应规则无法新增或启用!");
        }
    }

    /**
     * 是否存在规则
     *
     * @return
     */
    public Boolean existInspect() {
        LambdaQueryWrapper<BdQaFullInspectRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaFullInspectRuleEntity::getIsDeleted, Boolean.FALSE);
        queryWrapper.eq(BdQaFullInspectRuleEntity::getLocation, TenantContext.getTenant());
        return this.count(queryWrapper) > 0;
    }


    /**
     * 根据质检单id校验是否符合稽查规则
     *
     * @param stockinQaOrderId
     * @return
     */
    public boolean validFullInspectByStockinQaOrder(Integer stockinQaOrderId) {
        LambdaQueryWrapper<BdQaFullInspectRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaFullInspectRuleEntity::getIsDeleted, Boolean.FALSE);
        queryWrapper.eq(BdQaFullInspectRuleEntity::getLocation, TenantContext.getTenant());
        List<BdQaFullInspectRuleEntity> ruleInfoList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(ruleInfoList)) {
            return false;
        }
        return this.validConformInspectDetail(stockinQaOrderId, ruleInfoList);
    }

    /**
     * 校验是否符合稽查规则明细
     *
     * @param stockinQaOrderId
     * @param ruleInfoList
     * @return
     */
    private boolean validConformInspectDetail(Integer stockinQaOrderId, List<BdQaFullInspectRuleEntity> ruleInfoList) {
        StockinQaOrderEntity qaOrderEntity = qaOrderService.getById(stockinQaOrderId);
        if (qaOrderEntity == null) {
            return false;
        }
        //商品标签
        List<String> skuTypeList = qaOrderSkuTypeInfoService.getByStockinSkuTypeByQaOrderId(qaOrderEntity.getStockinQaOrderId());
        //是否返工退货
        boolean isReturnApply = qaOrderItemService.validReturnApply(stockinQaOrderId);
        return ruleInfoList.stream().anyMatch(ruleInfo -> {
            try {
                BdQaFullInspectRuleItemInfoResponse itemInfo = qaFullInspectRuleItemService.getItemInfo(ruleInfo.getId());
                if (ObjectUtils.isEmpty(itemInfo)) {
                    return false;
                }
                if (!CollectionUtils.isEmpty(itemInfo.getSkuTypeList()) && (CollectionUtils.isEmpty(skuTypeList) || !itemInfo.getSkuTypeList().stream().anyMatch(skuTypeList::contains))) {
                    return false;
                }
                if (StringUtils.hasText(itemInfo.getIsReturnApply())) {
                    boolean isReturnApplyExpected = "1".equals(itemInfo.getIsReturnApply());
                    if (isReturnApply != isReturnApplyExpected) {
                        return false;
                    }
                }
                if (CollectionUtil.isEmpty(itemInfo.getProductInfoList())) {
                    return true;
                }
                if (itemInfo.getProductInfoList().stream().anyMatch(productInfo -> qaOrderEntity.getSpu().equals(productInfo.getSpu()) && qaOrderEntity.getSupplierId().equals(productInfo.getSupplierId()))) {
                    return true;
                }
                return false;
            } catch (Exception e) {
                LOGGER.error("全检规则id:{}校验失败", ruleInfo.getId(), e);
                return false;
            }
        });
    }
}
