package com.nsy.wms.business.service.stock;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ExceptionConstants;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.bd.BdPositionInfo;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stock.StockInternalBoxCodePrint;
import com.nsy.api.wms.domain.stock.StockInternalBoxExport;
import com.nsy.api.wms.domain.stock.StockInternalBoxSupplierDeliveryNoInfo;
import com.nsy.api.wms.domain.stock.StockInternalBoxTypeResponse;
import com.nsy.api.wms.domain.stockin.RecommendParamsInfo;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskMessage;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxMaterialSizeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.RecommendTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxAddRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxFullRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxListRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stock.StockInternalBoxItemPdaResponse;
import com.nsy.api.wms.response.stock.StockInternalBoxQcResponse;
import com.nsy.api.wms.response.stock.StockInternalBoxResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordWaitMeasureResponse;
import com.nsy.wms.business.manage.user.response.BdDictionaryItem;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxItemQueryWrapper;
import com.nsy.wms.business.service.stock.query.StockInternalBoxQueryWrapper;
import com.nsy.wms.business.service.stockin.StockinOrderLogService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.business.service.stockin.StockinQcService;
import com.nsy.wms.business.service.stockin.StockinShelveSearchPositionService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxItemMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockPlatformScheduleMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.StockInternalBoxCodeGenerateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.inject.Inject;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockInternalBoxService extends ServiceImpl<StockInternalBoxMapper, StockInternalBoxEntity> implements IDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockInternalBoxService.class);
    @Autowired
    private StockInternalBoxMapper stockInternalBoxMapper;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockInternalBoxLogService logService;
    @Inject
    StockInternalBoxItemMapper stockInternalBoxItemMapper;
    @Inject
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Inject
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockService stockService;
    @Autowired
    StockPlatformScheduleMapper platformScheduleRepository;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockinOrderLogService stockinOrderLogService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockinQcService stockinQcService;
    @Autowired
    StockinShelveSearchPositionService stockinShelveSearchPositionService;
    @Autowired
    BdAreaService bdAreaService;
    @Autowired
    StockInternalBoxLogService stockInternalBoxLogService;
    @Autowired
    StockInternalBoxCodeGenerateUtil stockInternalBoxCodeGenerateUtil;

    public static final Integer ZERO = 0;


    @Transactional
    public void batchSaveStockInternalBox(List<StockInternalBox> request) {
        request.stream().forEach(item -> {
            StockInternalBoxEntity entity = this.findByInternalBoxCode(item.getInternalBoxCode());
            StockInternalBoxEntity stockInternalBoxEntity = new StockInternalBoxEntity();
            BeanUtilsEx.copyProperties(item, stockInternalBoxEntity);
            if (Objects.nonNull(entity)) {
                stockInternalBoxEntity.setInternalBoxId(entity.getInternalBoxId());
            }
            this.saveOrUpdate(stockInternalBoxEntity);
        });
    }

    /**
     * 新增内部箱
     */
    @Transactional
    @JLock(keyConstant = "saveStockInternalBox", lockKey = "#request.internalBoxMaterialSize")
    public List<Integer> saveStockInternalBox(StockinInternalBoxAddRequest request) {
        if (StockInternalBoxTypeEnum.RETURN_BOX.name().equals(request.getInternalBoxType()))
            validateReturnBox(request);
        List<Integer> resultList = new ArrayList<>();
        String internalBoxCode;
        if (request.getInternalBoxMaterialSize() != null && request.getInternalBoxMaterialSize().equals(StockInternalBoxMaterialSizeEnum.SINGLE_USE_BOX.name())) {
            for (int i = 1; i <= request.getInternalBoxNum(); i++) {
                internalBoxCode = stockInternalBoxCodeGenerateUtil.buildSingleUserCode();
                resultList.add(buildSaveByInternalBoxCode(request, internalBoxCode));
            }
        } else {
            String boxCodePre = getBoxCodePre(request.getInternalBoxType());
            for (int i = 1; i <= request.getInternalBoxNum(); i++) {
                internalBoxCode = stockInternalBoxCodeGenerateUtil.buildBoxCode(boxCodePre, request);
                resultList.add(buildSaveByInternalBoxCode(request, internalBoxCode));
            }
        }
        return resultList;
    }

    void validateReturnBox(StockinInternalBoxAddRequest request) {
        if (!StringUtils.hasText(request.getWorkspace()))
            throw new BusinessServiceException("新增退货箱工作区域不能为空");
    }

    Integer buildSaveByInternalBoxCode(StockinInternalBoxAddRequest request, String internalBoxCode) {
        StockInternalBoxEntity stockInternalBoxEntity = new StockInternalBoxEntity();
        BeanUtilsEx.copyProperties(request, stockInternalBoxEntity);
        stockInternalBoxEntity.setInternalBoxCode(internalBoxCode);
        stockInternalBoxEntity.setStatus(StockInternalBoxStatusEnum.EMPTY.name());
        stockInternalBoxEntity.setCreateBy(loginInfoService.getName());
        stockInternalBoxEntity.setUpdateBy(loginInfoService.getName());
        stockInternalBoxEntity.setUpdateDate(new Date());
        stockInternalBoxEntity.setLocation(TenantContext.getTenant());
        this.save(stockInternalBoxEntity);
        logService.addLog(StockInternalBoxLogTypeEnum.ADD, internalBoxCode, String.format("%s 新增 内部箱：%s", loginInfoService.getName(), internalBoxCode));
        return stockInternalBoxEntity.getInternalBoxId();
    }

    // 根据箱类型设定箱号前缀
    public String getBoxCodePre(String internalBoxType) {
        if (internalBoxType.equals(StockInternalBoxTypeEnum.QA_BOX.name())) { // 质检
            return "011";
        }
        if (internalBoxType.equals(StockInternalBoxTypeEnum.RECEIVE_BOX.name())) { // 收货箱
            return "010";
        }
        if (internalBoxType.equals(StockInternalBoxTypeEnum.PICKING_BOX.name())) { // 拣货箱
            return "020";
        }
        if (internalBoxType.equals(StockInternalBoxTypeEnum.TRANSFER_BOX.name())) { // 调拨箱
            return "030";
        }
        if (internalBoxType.equals(StockInternalBoxTypeEnum.RETURN_BOX.name())) { // 退货箱
            return "040";
        }
        if (internalBoxType.equals(StockInternalBoxTypeEnum.WITHDRAWAL_BOX.name())) { // 撤货箱
            return "050";
        }
        if (internalBoxType.equals(StockInternalBoxTypeEnum.BORROW_RETURN_BOX.name())) { // 借用归还箱
            return "060";
        } else {
            throw new BusinessServiceException("找不到对应的内部箱类型");
        }
    }

    /**
     * 停用内部箱
     */
    @Transactional
    public void deleteStockInternalBox(Integer internalBoxId) {
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxMapper.selectById(internalBoxId);
        if (stockInternalBoxEntity == null) {
            throw new BusinessServiceException("没有找到内部箱记录");
        }
        if (IsDeletedConstant.DELETED.equals(stockInternalBoxEntity.getIsDeleted()))
            throw new BusinessServiceException(String.format("内部箱: %s 已删除", stockInternalBoxEntity.getInternalBoxCode()));
        LambdaQueryWrapper<StockInternalBoxItemEntity> queryWrapper = StockInternalBoxItemQueryWrapper.buildWrapperByInternalBoxId(internalBoxId);
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemMapper.selectList(queryWrapper);
        if (!stockInternalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.EMPTY.name()) || !CollectionUtils.isEmpty(stockInternalBoxItemEntityList)) {
            throw new BusinessServiceException(String.format("内部箱: %s  不为空箱，无法进行删除。", stockInternalBoxEntity.getInternalBoxCode()));
        }
        stockInternalBoxEntity.setIsDeleted(IsDeletedConstant.DELETED);
        stockInternalBoxEntity.setUpdateBy(loginInfoService.getName());
        stockInternalBoxEntity.setUpdateDate(new Date());
        stockInternalBoxMapper.updateById(stockInternalBoxEntity);
        logService.addLog(StockInternalBoxLogTypeEnum.CHANGE_STATUS, stockInternalBoxEntity.getInternalBoxCode(), String.format("停用 内部箱：%s", stockInternalBoxEntity.getInternalBoxCode()));
    }

    /**
     * 启用内部箱
     *
     * @param internalBoxId
     */
    @Transactional
    public void enableStockInternalBox(Integer internalBoxId) {
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxMapper.selectById(internalBoxId);
        if (stockInternalBoxEntity == null) {
            throw new BusinessServiceException("没有找到内部箱记录");
        }
        stockInternalBoxEntity.setIsDeleted(IsDeletedConstant.NOT_DELETED);
        stockInternalBoxEntity.setUpdateBy(loginInfoService.getName());
        stockInternalBoxEntity.setUpdateDate(new Date());
        stockInternalBoxMapper.updateById(stockInternalBoxEntity);
        logService.addLog(StockInternalBoxLogTypeEnum.CHANGE_STATUS, stockInternalBoxEntity.getInternalBoxCode(), String.format("启用 内部箱：%s", stockInternalBoxEntity.getInternalBoxCode()));
    }

    /**
     * 更新内部箱状态（无须查找实体）
     */
    public void changeStockInternalBoxStatus(StockInternalBoxEntity internalBoxEntity, String status) {
        String oldStatus = internalBoxEntity.getStatus();
        LOGGER.info("更新内部箱 {} 状态 {} -> {} ", internalBoxEntity.getInternalBoxCode(), oldStatus, status);
        if (status.equals(oldStatus)) {
            return;
        }
        internalBoxEntity.setUpdateBy(loginInfoService.getName());
        internalBoxEntity.setStatus(status);
        stockInternalBoxMapper.updateById(internalBoxEntity);
    }

    /**
     * 更新内部箱状态 (根据内部箱号找实体)
     */
    @Transactional
    public void changeStockInternalBoxStatus(String internalBoxCode, String status) {
        StockInternalBoxEntity stockInternalBoxEntity = getStockInternalBoxByInternalBoxCode(internalBoxCode);
        String oldStatus = stockInternalBoxEntity.getStatus();
        if (status.equals(oldStatus)) {
            return;
        }
        if (status.equals(StockInternalBoxStatusEnum.EMPTY.name())) {
            Integer boxItemCount = stockInternalBoxItemService.count(new QueryWrapper<StockInternalBoxItemEntity>().lambda().eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                    .ne(StockInternalBoxItemEntity::getQty, 0));
            if (!boxItemCount.equals(0))
                return;
            stockInternalBoxLogService.addLog(StockInternalBoxLogTypeEnum.CHANGE_STATUS, internalBoxCode, String.format("%s 修改内部箱状态为：%s", loginInfoService.getName(), StockInternalBoxStatusEnum.EMPTY.getStatus()));
        }

        stockInternalBoxEntity.setUpdateBy(loginInfoService.getName());
        stockInternalBoxEntity.setStatus(status);
        stockInternalBoxEntity.setUpdateDate(new Date());
        stockInternalBoxMapper.updateById(stockInternalBoxEntity);
        if (StockInternalBoxStatusEnum.EMPTY.name().equalsIgnoreCase(status)) {
            // 上架后删除空箱
            updateAfterShelved(internalBoxCode);
        }
    }


    public StockInternalBoxEntity findByInternalBoxCode(String internalBoxCode) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        return stockInternalBoxMapper.selectOne(queryWrapper);
    }

    public StockInternalBoxEntity getStockInternalBoxByInternalBoxCode(String internalBoxCode) {
        StockInternalBoxEntity entity = findByInternalBoxCode(internalBoxCode);
        if (entity == null) {
            LOGGER.error("resource sync StockInternalBox  internalBoxCode={} is not found", internalBoxCode);
            throw new BusinessServiceException("找不到内部箱: " + internalBoxCode + " 记录", null);
        }
        return entity;
    }

    public StockInternalBoxEntity getByInternalBoxCodeAndStatusList(String internalBoxCode, List<String> statusList) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.wrapperByInternalBoxCodeAndStatusList(internalBoxCode, statusList);
        return stockInternalBoxMapper.selectOne(queryWrapper);
    }

    /**
     * 根据内部箱号，状态status查内部箱
     */
    public StockInternalBoxEntity getByInternalBoxCodeAndStatus(String internalBoxCode, String status) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCodeAndStatus(internalBoxCode, status);
        return stockInternalBoxMapper.selectOne(queryWrapper);
    }

    /**
     * 根据内部箱号list，状态status查内部箱
     */
    public List<StockInternalBoxEntity> getByInternalBoxCodeListAndStatus(List<String> internalBoxCodeList, String status) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.wrapperByInternalBoxCodeListAndStatus(internalBoxCodeList, status);
        return this.list(queryWrapper);
    }

    /**
     * 现撤货都绑定到同一撤货箱，不校验工作区域（查询触发撤货的方法去掉校验）  都绑定到一个撤货箱
     */
    public StockInternalBoxEntity getWithdrawalByWorkspace(String workspace, Integer spaceId) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.getInternalBoxByWorkSpaceAndInternalBoxTypeAndSpaceId(workspace, StockInternalBoxTypeEnum.WITHDRAWAL_BOX.name(), spaceId);
        StockInternalBoxEntity entity = this.getOne(queryWrapper);
        if (entity == null) {
            throw new BusinessServiceException(StrUtil.format("仓库id:{}下找不到{}的撤货箱", spaceId, workspace));
        }
        return entity;
    }

    /**
     * 入库商品入箱
     *
     * @param stockInOrderNo
     */
    @Transactional
    public void skuToBox(List<StockinOrderItemEntity> orderItemEntityList, String stockInOrderNo) {
        LambdaQueryWrapper<StockInternalBoxItemEntity> queryWrapperByStockInOrderNo = StockInternalBoxItemQueryWrapper.buildWrapperByStockInOrderNo(stockInOrderNo);
        List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemMapper.selectList(queryWrapperByStockInOrderNo);
        orderItemEntityList.forEach(orderItemEntity -> {
            LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(orderItemEntity.getInternalBoxCode());
            StockInternalBoxEntity boxEntity = stockInternalBoxMapper.selectOne(queryWrapper);
            if (boxEntity == null) {
                throw new BusinessServiceException(String.format("未找到内部箱号为【%s】的内部箱!", orderItemEntity.getInternalBoxCode()));
            }
            Optional<StockInternalBoxItemEntity> any = boxItemEntityList.stream().filter(boxItemEntity ->
                    boxItemEntity.getInternalBoxCode().equals(orderItemEntity.getInternalBoxCode())
                            && boxItemEntity.getSku().equals(orderItemEntity.getSku())
                            && boxItemEntity.getPurchasePlanNo().equals(orderItemEntity.getPurchasePlanNo())).findAny();
            StockInternalBoxItemEntity boxItem = any.orElseGet(() -> createBoxItem(orderItemEntity, boxEntity, stockInOrderNo));
            // 内部箱明细 + 库存 +
            stockInternalBoxItemService.addStockInternalBoxItemQty(boxItem, orderItemEntity.getQty(), StockChangeLogTypeEnum.STOCKIN_RECEIVE, StockChangeLogTypeModuleEnum.STOCK_IN, null);
        });
    }

    @Transactional
    public StockInternalBoxItemEntity createBoxItem(StockinOrderItemEntity orderItemEntity, StockInternalBoxEntity boxEntity, String stockInOrderNo) {
        StockInternalBoxItemEntity entity = new StockInternalBoxItemEntity();
        entity.setBatchCode(orderItemEntity.getBatchCode());
        entity.setProductId(orderItemEntity.getProductId());
        entity.setPurchasePlanNo(orderItemEntity.getPurchasePlanNo());
        entity.setQty(0);
        entity.setSku(orderItemEntity.getSku());
        entity.setSellerBarcode(orderItemEntity.getSellerBarcode());
        entity.setSellerSku(orderItemEntity.getSellerSku());
        entity.setInternalBoxCode(boxEntity.getInternalBoxCode());
        entity.setInternalBoxId(boxEntity.getInternalBoxId());
        entity.setLocation(boxEntity.getLocation());
        entity.setSpaceId(boxEntity.getSpaceId());
        entity.setSpecId(orderItemEntity.getSpecId());
        entity.setStockInOrderNo(stockInOrderNo);
        entity.setCreateBy(loginInfoService.getName());
        entity.setStatus(orderItemEntity.getStatus());
        entity.setIsFbaQuick(orderItemEntity.getIsFbaQuick());
        entity.setWorkmanshipVersion(orderItemEntity.getWorkmanshipVersion());
        entity.setBrandName(orderItemEntity.getBrandName());
        StockinOrderTaskItemEntity taskItemEntity = stockinOrderTaskItemService.getById(orderItemEntity.getTaskItemId());
        if (Objects.nonNull(taskItemEntity)) {
            entity.setOrderNo(taskItemEntity.getOrderNo());
        }
        return entity;
    }

    /**
     * 查询内部箱
     *
     * @return
     */
    public StockInternalBox getInternalBox(String internalBoxCode) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxMapper.selectOne(queryWrapper);
        if (boxEntity == null)
            throw new BusinessServiceException(String.format("未找到内部箱号为【%s】的内部箱!", internalBoxCode));
        StockInternalBox stockInternalBox = new StockInternalBox();
        BeanUtilsEx.copyProperties(boxEntity, stockInternalBox);
        return stockInternalBox;
    }

    @Transactional
    public void fullInternalBox(String internalBoxCode, StockinInternalBoxFullRequest request) {
        if (!org.springframework.util.StringUtils.hasText(internalBoxCode))
            return;
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxMapper.selectOne(queryWrapper);
        if (boxEntity == null)
            return;
        //被删除直接返回
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted()))
            return;
        StockinOrderTaskEntity byId = stockinOrderTaskService.getById(request.getTaskId());
        if (Objects.isNull(byId))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(byId.getTaskId());
        if (Objects.isNull(stockinOrderEntity))
            throw new BusinessServiceException("未找到入库任务！");
        List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxId, boxEntity.getInternalBoxId())
                .eq(StockInternalBoxItemEntity::getStockInOrderNo, stockinOrderEntity.getStockinOrderNo()));
        // 更新内部箱状态
        updateInternalBoxStatusByItem(boxEntity.getInternalBoxCode());
        if (CollectionUtils.isEmpty(internalBoxItemEntityList))
            //先处理掉现场问题
            return;

        //回写更新质检数量 - 生成质检任务
        stockinQcService.generateQcTask(boxEntity, byId, internalBoxItemEntityList);
        // 满箱确认日志
        if (!CollectionUtils.isEmpty(internalBoxItemEntityList)) {
            Integer qty = internalBoxItemEntityList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
            fullBoxAddStockinOrderLog(stockinOrderEntity.getStockinOrderId(), internalBoxCode, qty);
        }
        //发送kafka消息
        String businessMark = KafkaConstant.BUSINESS_MARK_WMS_STOCK_IN_SHELVE;
        StockinShelveTaskMessage message = new StockinShelveTaskMessage();
        message.setInternalBoxCode(internalBoxCode);
        message.setStockinOrderId(Objects.nonNull(stockinOrderEntity) ? stockinOrderEntity.getStockinOrderId() : null);
        message.setInternalBoxItemIdList(internalBoxItemEntityList.stream().map(StockInternalBoxItemEntity::getInternalBoxItemId).collect(Collectors.toList()));
        message.setLocation(TenantContext.getTenant());
        messageProducer.sendMessage(businessMark, KafkaConstant.TOPIC_NAME_STOCKIN_SHELVE_TASK, message);
    }

    public void updateInternalBoxStatusByItem(String internalBoxCode) {
        StockInternalBoxEntity boxEntity = this.getStockInternalBoxByInternalBoxCode(internalBoxCode);
        List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemService.getByInternalBoxId(boxEntity.getInternalBoxId());
        if (CollectionUtils.isEmpty(boxItemEntityList)) {
            changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.EMPTY.name());
            return;
        }
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntities = boxItemEntityList.stream().filter(item -> Objects.nonNull(item.getStatus()) && item.getQty() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntities))
            return;
        Map<String, Long> collect = stockInternalBoxItemEntities.stream().collect(Collectors.groupingBy(StockInternalBoxItemEntity::getStatus, Collectors.counting()));
        Long qcProcessing = Objects.nonNull(collect.get(StockinOrderItemStatusEnum.QC_PROCESSING.name())) ? collect.get(StockinOrderItemStatusEnum.QC_PROCESSING.name()) : 0L;
        Long waitQc = Objects.nonNull(collect.get(StockinOrderItemStatusEnum.WAIT_QC.name())) ? collect.get(StockinOrderItemStatusEnum.WAIT_QC.name()) : 0L;
        Long waitShelve = Objects.nonNull(collect.get(StockinOrderItemStatusEnum.WAIT_SHELVE.name())) ? collect.get(StockinOrderItemStatusEnum.WAIT_SHELVE.name()) : 0L;
        Long waitReturn = Objects.nonNull(collect.get(StockinOrderItemStatusEnum.WAIT_RETURN.name())) ? collect.get(StockinOrderItemStatusEnum.WAIT_RETURN.name()) : 0L;
        Long waitDeal = Objects.nonNull(collect.get(StockinOrderItemStatusEnum.WAIT_DEAL.name())) ? collect.get(StockinOrderItemStatusEnum.WAIT_DEAL.name()) : 0L;
        if (qcProcessing > 0) {
            changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.QC_PROCESSING.name());
            return;
        }
        if (waitDeal > 0) {
            changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.QC_PROCESSING.name());
            return;
        }
        if (waitQc > 0) {
            //质检中的循环箱不修改状态
            if (StockInternalBoxMaterialSizeEnum.LOOP_BOX.name().equals(boxEntity.getInternalBoxMaterialSize())
                    && StockInternalBoxStatusEnum.QC_PROCESSING.name().equals(boxEntity.getStatus()))
                return;
            changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.WAIT_QC.name());
            return;
        }
        if (waitShelve > 0 && waitReturn > 0) {
            changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.QC_COMPLETED.name());
            return;
        }
        if (waitShelve > 0) {
            changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.WAIT_SHELVE.name());
            return;
        }
        if (waitReturn > 0) {
            changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.WAIT_RETURN.name());
        }
    }

    // 满箱确认入库单日志记录
    private void fullBoxAddStockinOrderLog(Integer stockinOrderId, String internalBoxCode, Integer qty) {
        String content = String.format("内部箱号【%s】，装入入库单内SKU共 %s 件，满箱确定", internalBoxCode, qty);
        stockinOrderLogService.addLog(stockinOrderId, StockinOrderLogTypeEnum.FULL_BOX.getName(), content);
    }

    /**
     * 修改内部箱明细和库存
     *
     * @param qty
     * @param orderEntity
     * @param stockinOrderItemEntity
     */
    @Transactional
    public void editQty(Integer qty, StockinOrderEntity orderEntity, StockinOrderItemEntity stockinOrderItemEntity) {
        LambdaQueryWrapper<StockInternalBoxItemEntity> eq = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, stockinOrderItemEntity.getInternalBoxCode())
                .eq(StockInternalBoxItemEntity::getStockInOrderNo, orderEntity.getStockinOrderNo())
                .eq(StockInternalBoxItemEntity::getSku, stockinOrderItemEntity.getSku());
        if (StringUtils.hasText(stockinOrderItemEntity.getPurchasePlanNo())) {
            eq.eq(StockInternalBoxItemEntity::getPurchasePlanNo, stockinOrderItemEntity.getPurchasePlanNo());
        }
        StockInternalBoxItemEntity byInternalBoxCodeAndSku = stockInternalBoxItemMapper.selectOne(eq);
        if (Objects.isNull(byInternalBoxCodeAndSku)) {
            StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getStockInternalBoxByInternalBoxCode(stockinOrderItemEntity.getInternalBoxCode());
            byInternalBoxCodeAndSku = createBoxItem(stockinOrderItemEntity, stockInternalBoxEntity, orderEntity.getStockinOrderNo());
        }
        if (qty == 0) {
            return;
        }
        // 库存变化 + 库存变动日志记录
        stockInternalBoxItemService.addStockInternalBoxItemQty(byInternalBoxCodeAndSku, qty, StockChangeLogTypeEnum.STOCKIN_RECEIVE, StockChangeLogTypeModuleEnum.STOCK_IN, null);
    }

    /*
     * 查询内部箱列表
     * */
    public PageResponse<StockInternalBoxResponse> getStockInternalBoxList(StockinInternalBoxListRequest request) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByStockinInternalBoxListRequest(request);
        Page<StockInternalBoxEntity> page = stockInternalBoxMapper.selectPage(new Page<>(request.getPageIndex(), request.getPageSize()), queryWrapper);
        PageResponse<StockInternalBoxResponse> pageResponse = PageResponse.of(page.getTotal());
        List<StockInternalBoxEntity> stockInternalBoxEntityList = page.getRecords();
        Map<String, String> internalBoxTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName());
        Map<String, String> materialSizeInternalBoxEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_MATERIAL_SIZE_INTERNAL_BOX.getName());
        Map<String, String> stockoutWorkLocationEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName());
        Map<Key, BdDictionaryItem> dictionaryItemMap = enumConversionChineseUtils.getAllDictionaryItemMap();
        List<StockInternalBoxResponse> stockInternalBoxResponseList = stockInternalBoxEntityList.stream().filter(Objects::nonNull).map(entity -> {
            StockInternalBoxResponse internalBoxResponse = new StockInternalBoxResponse();
            BeanUtilsEx.copyProperties(entity, internalBoxResponse);
            if (entity.getStatus() != null) {
                String statusStr = stockInternalBoxItemService.buildInternalBoxItemStatus(entity.getInternalBoxType(), entity.getStatus(), dictionaryItemMap);
                internalBoxResponse.setStatusStr(StringUtils.hasText(statusStr) ? statusStr : null);
            }
            if (entity.getInternalBoxType() != null) {
                internalBoxResponse.setInternalBoxTypeStr(internalBoxTypeEnumMap.get(entity.getInternalBoxType()));
            }
            if (entity.getSpaceId() != null) {
                BdSpaceEntity bdSpaceEntity = spaceService.getById(entity.getSpaceId());
                if (bdSpaceEntity != null && bdSpaceEntity.getSpaceName() != null) {
                    internalBoxResponse.setSpaceName(bdSpaceEntity.getSpaceName());
                }
            }
            if (entity.getInternalBoxMaterialSize() != null)
                internalBoxResponse.setInternalBoxMaterialSizeStr(materialSizeInternalBoxEnumMap.get(entity.getInternalBoxMaterialSize()));
            if (entity.getInternalBoxId() != null) {
                LambdaQueryWrapper<StockInternalBoxItemEntity> queryWrapper2 = StockInternalBoxItemQueryWrapper.buildWrapperInternalBoxItemByInternalBoxId(entity.getInternalBoxId());
                List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemMapper.selectList(queryWrapper2);
                if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList)) {
                    internalBoxResponse.setSkuQty(ZERO);
                } else {
                    Integer skuQty = stockInternalBoxItemEntityList.stream().filter(item -> item.getQty() != null).mapToInt(StockInternalBoxItemEntity::getQty).sum();
                    internalBoxResponse.setSkuQty(skuQty);
                }
            }
            internalBoxResponse.setWorkspaceStr(stockoutWorkLocationEnumMap.get(internalBoxResponse.getWorkspace()));
            return internalBoxResponse;
        }).collect(Collectors.toList());
        pageResponse.setContent(stockInternalBoxResponseList);
        return pageResponse;
    }

    public void setPositionCode(List<StockInternalBoxSupplierDeliveryNoInfo> groupList, StockInternalBoxItemPdaResponse stockInternalBoxItemPdaResponse) {
        StockInternalBoxSupplierDeliveryNoInfo deliveryNoInfo = groupList.get(0);
        if (!StringUtils.hasText(deliveryNoInfo.getAreaName()) || !StringUtils.hasText(deliveryNoInfo.getStockinType()))
            return;
        BdAreaEntity byAreaNameAndSpaceId = bdAreaService.findByAreaNameAndSpaceId(deliveryNoInfo.getAreaName(), deliveryNoInfo.getSpaceId());
        Integer areaId = byAreaNameAndSpaceId == null ? null : byAreaNameAndSpaceId.getAreaId();
        RecommendParamsInfo info = RecommendParamsInfo.builder().stockinType(deliveryNoInfo.getStockinType())
                .spaceId(deliveryNoInfo.getSpaceId()).specId(deliveryNoInfo.getSpecId())
                .areaId(areaId).recommendType(RecommendTypeEnum.POSITION).businessType(deliveryNoInfo.getBusinessType())
                .storeId(deliveryNoInfo.getStoreId()).isFbaQuick(deliveryNoInfo.getIsFbaQuick())
                .build();
        List<BdPositionInfo> bdPositionInfos = stockinShelveSearchPositionService.recommendSpaceAreaAndPosition(info);
        if (!CollectionUtils.isEmpty(bdPositionInfos))
            stockInternalBoxItemPdaResponse.setPositionCode(StringUtils.join(bdPositionInfos.stream().map(BdPositionInfo::getPositionCode).distinct().collect(Collectors.toList()), ','));
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCK_INTERNAL_BOX;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinInternalBoxListRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockinInternalBoxListRequest.class);
        // 设置每次的查询数量
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        PageResponse<StockInternalBoxResponse> pageResponse = getStockInternalBoxList(downloadRequest);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMAT_DATE4);
        List<StockInternalBoxExport> resultList = pageResponse.getContent().stream().map(item -> {
            StockInternalBoxExport export = new StockInternalBoxExport();
            BeanUtilsEx.copyProperties(item, export, new String[]{"createDate", "updateDate"});
            export.setCreateDate(item.getCreateDate() == null ? null : dateFormat.format(item.getCreateDate()));
            export.setUpdateDate(item.getUpdateDate() == null ? null : dateFormat.format(item.getUpdateDate()));
            return export;
        }).collect(Collectors.toList());
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        return response;
    }

    /**
     * 根据箱号，类型查找内部箱
     */
    public StockInternalBoxEntity getStockInternalBoxByInternalBoxCodeAndInternalBoxType(String internalBoxCode, String internalBoxType) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCodeAndInternalBoxType(internalBoxCode, internalBoxType);
        StockInternalBoxEntity entity = stockInternalBoxMapper.selectOne(queryWrapper);
        if (entity == null) {
            LOGGER.error("resource sync StockInternalBox  internalBoxCode={} is not found", internalBoxCode);
            throw new BusinessServiceException(String.format("找不到内部箱类型：%s，内部箱号: %s 的空箱",
                    enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName(), internalBoxType), internalBoxCode));
        }
        return entity;
    }

    /**
     * 根据箱号，类型,状态查找内部箱
     */
    public StockInternalBoxEntity getByInternalBoxCodeAndInternalBoxTypeAndStatusIsNotEmpty(String internalBoxCode, String internalBoxType) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCodeAndInternalBoxTypeAndStatusIsNotEmpty(internalBoxCode, internalBoxType);
        return stockInternalBoxMapper.selectOne(queryWrapper);
    }

    /**
     * 打印模板
     */
    @Transactional
    public PrintListResponse printStockinInternalBox(IdListRequest idListRequest) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKIN_INTERNAL_BOX.getTemplateName());

        List<StockInternalBoxEntity> stockInternalBoxEntities = this.listByIds(idListRequest.getIdList());
        List<String> list = new ArrayList<>();
        ArrayList<StockInternalBoxCodePrint> resList = new ArrayList<>();
        Map<String, String> internalBoxTypeMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName());
        for (StockInternalBoxEntity entity : stockInternalBoxEntities) {
            StockInternalBoxCodePrint printResponse = new StockInternalBoxCodePrint();
            BeanUtils.copyProperties(entity, printResponse);
            printResponse.setInternalBoxTypeStr(internalBoxTypeMap.get(entity.getInternalBoxType()));
            resList.add(printResponse);
        }
        if (resList.size() % 2 != 0) {
            for (int i = 0; i <= resList.size() - 2 - 1; i += 2) {
                List<StockInternalBoxCodePrint> subList = resList.subList(i, i + 2);
                list.add(PrintTransferUtils.doubleTransfer(templateEntity.getContent(), subList));
            }
            list.add(PrintTransferUtils.doubleTransfer(templateEntity.getContent(), resList.subList(resList.size() - 1, resList.size())));
        } else {
            for (int i = 0; i <= resList.size() - 2; i += 2) {
                List<StockInternalBoxCodePrint> subList = resList.subList(i, i + 2);
                list.add(PrintTransferUtils.doubleTransfer(templateEntity.getContent(), subList));
            }
        }
        // 更新一次性内部箱为已打印
        List<StockInternalBoxEntity> singleBoxList = stockInternalBoxEntities.stream().filter(box ->
                StockInternalBoxMaterialSizeEnum.SINGLE_USE_BOX.name().equals(box.getInternalBoxMaterialSize())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(singleBoxList)) {
            List<StockInternalBoxEntity> filterSingleBoxList = singleBoxList.stream().filter(item -> item.getIsPrint() != 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterSingleBoxList)) {
                singleBoxList.forEach(box -> {
                    box.setIsPrint(1);
                    box.setUpdateBy(loginInfoService.getName());
                });
                stockInternalBoxService.updateBatchById(singleBoxList);
            }
        }

        response.setHtmlList(list);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    // 上架完逻辑删除内部箱
    public void updateAfterShelved(String boxCode) {
        if (!StringUtils.hasText(boxCode)) {
            return;
        }
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(boxCode);
        StockInternalBoxEntity entity = stockInternalBoxMapper.selectOne(queryWrapper);
        if (IsDeletedConstant.DELETED.equals(entity.getIsDeleted())) {
            return;
        }
        if ((StockInternalBoxTypeEnum.RECEIVE_BOX.name().equalsIgnoreCase(entity.getInternalBoxType()) || StockInternalBoxTypeEnum.QA_BOX.name().equalsIgnoreCase(entity.getInternalBoxType()))
                && StockInternalBoxMaterialSizeEnum.SINGLE_USE_BOX.name().equalsIgnoreCase(entity.getInternalBoxMaterialSize())
                && StockInternalBoxStatusEnum.EMPTY.name().equalsIgnoreCase(entity.getStatus())) {
            entity.setIsDeleted(IsDeletedConstant.DELETED);
            updateById(entity);
        }
    }

    public StockInternalBoxQcResponse getStockInternalBoxCodeInfo(String internalBoxCode) {
        List<String> statusList = new ArrayList<>();
        statusList.add(StockInternalBoxStatusEnum.WAIT_QC.name());
        statusList.add(StockInternalBoxStatusEnum.QC_PROCESSING.name());
        StockInternalBoxEntity entity = getByInternalBoxCodeAndStatusList(internalBoxCode, statusList);
        StockInternalBoxQcResponse response = new StockInternalBoxQcResponse();
        if (Objects.nonNull(entity)) {
            BeanUtilsEx.copyProperties(entity, response);
            if (entity.getInternalBoxType() != null) {
                response.setInternalBoxTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName(), entity.getInternalBoxType()));
            }
            if (entity.getInternalBoxMaterialSize() != null) {
                response.setInternalBoxMaterialSizeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_MATERIAL_SIZE_INTERNAL_BOX.getName(), entity.getInternalBoxMaterialSize()));
            }
        }
        return response;
    }

    public StockInternalBoxQcResponse getStockInternalBoxCodeInfoByPurchase(String internalBoxCode) {
        StockInternalBoxEntity stockInternalBoxEntity = getByInternalBoxCodeAndStatusList(internalBoxCode, null);
        StockInternalBoxQcResponse response = new StockInternalBoxQcResponse();
        if (Objects.nonNull(stockInternalBoxEntity)) {
            BeanUtilsEx.copyProperties(stockInternalBoxEntity, response);
            if (stockInternalBoxEntity.getInternalBoxType() != null) {
                response.setInternalBoxTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName(), stockInternalBoxEntity.getInternalBoxType()));
            }
        }
        return response;
    }

    public StockInternalBoxTypeResponse getStockInternalBoxCodeType(StringListRequest request) {
        List<StockInternalBox> stockInternalBoxes = this.getBaseMapper().findInternalBoxTypeByCode(request.getStringList());
        Map<String, String> internalBoxTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName());
        List<StockInternalBoxTypeResponse.InternalBoxInfo> collect = stockInternalBoxes.stream().map(item -> {
            StockInternalBoxTypeResponse.InternalBoxInfo internalBoxInfo = new StockInternalBoxTypeResponse.InternalBoxInfo();
            internalBoxInfo.setInternalBoxCode(item.getInternalBoxCode());
            internalBoxInfo.setInternalBoxType(internalBoxTypeEnumMap.get(item.getInternalBoxType()));
            return internalBoxInfo;
        }).collect(Collectors.toList());
        StockInternalBoxTypeResponse stockInternalBoxTypeResponse = new StockInternalBoxTypeResponse();
        stockInternalBoxTypeResponse.setInternalBoxInfoList(collect);
        return stockInternalBoxTypeResponse;
    }

    public String getAreaNameByInternalBoxCode(String internalBoxCode) {
        return this.getBaseMapper().getAreaNameByInternalBoxCode(internalBoxCode);
    }

    public List<StockinVolumeWeightRecordWaitMeasureResponse> getWaitMeasureList(String internalBoxCode) {
        return this.getBaseMapper().getWaitMeasureList(internalBoxCode);
    }

    public String getWaitMeasureListSupplierDeliveryNoList(String internalBoxCode, String sku) {
        return this.getBaseMapper().getWaitMeasureListSupplierDeliveryNoList(internalBoxCode, sku);
    }

    /**
     * 批量删除箱子
     *
     * @param idListRequest
     */
    @Transactional
    public void deleteStockInternalBoxBatch(@Valid IdListRequest idListRequest) {

        idListRequest.getIdList().forEach(id -> {
            this.deleteStockInternalBox(id);
        });
    }
}
