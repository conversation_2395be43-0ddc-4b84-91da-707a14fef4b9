package com.nsy.wms.business.manage.user.upload;

import java.math.BigDecimal;


public class StockoutCustomsDeclareFormItemImport {


    private String declareDocumentNo;

    private String gNo;

    private Integer inputQty;

    private String supplierName;

    private String supplierCode;

    private String declareContractNo;

    private String inputInvoiceNo;

    private String inputInvoiceCode;

    private BigDecimal inputPrice;

    private BigDecimal taxPrice;

    private String invoiceDate;

    private String stockinDate;

    private String contractSignedDate;

    private String deliveryDate;

    private Integer isFinished;

    private String errorMsg;

    public Integer getInputQty() {
        return inputQty;
    }

    public void setInputQty(Integer inputQty) {
        this.inputQty = inputQty;
    }

    public String getDeclareDocumentNo() {
        return declareDocumentNo;
    }

    public void setDeclareDocumentNo(String declareDocumentNo) {
        this.declareDocumentNo = declareDocumentNo;
    }

    public String getgNo() {
        return gNo;
    }

    public void setgNo(String gNo) {
        this.gNo = gNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getDeclareContractNo() {
        return declareContractNo;
    }

    public void setDeclareContractNo(String declareContractNo) {
        this.declareContractNo = declareContractNo;
    }

    public String getInputInvoiceNo() {
        return inputInvoiceNo;
    }

    public void setInputInvoiceNo(String inputInvoiceNo) {
        this.inputInvoiceNo = inputInvoiceNo;
    }

    public String getInputInvoiceCode() {
        return inputInvoiceCode;
    }

    public void setInputInvoiceCode(String inputInvoiceCode) {
        this.inputInvoiceCode = inputInvoiceCode;
    }

    public BigDecimal getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(BigDecimal inputPrice) {
        this.inputPrice = inputPrice;
    }

    public BigDecimal getTaxPrice() {
        return taxPrice;
    }

    public void setTaxPrice(BigDecimal taxPrice) {
        this.taxPrice = taxPrice;
    }

    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(String stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getContractSignedDate() {
        return contractSignedDate;
    }

    public void setContractSignedDate(String contractSignedDate) {
        this.contractSignedDate = contractSignedDate;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getIsFinished() {
        return isFinished;
    }

    public void setIsFinished(Integer isFinished) {
        this.isFinished = isFinished;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }
}
