package com.nsy.wms.business.manage.transfer;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopCreateLogisticsShipmentRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopGetLogisticsShipmentResultRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopGetShipmentDocumentRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopGetShippingServicesRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopShippedPackageConfirmRequest;
import com.nsy.api.transfer.domain.request.platform.temuPop.TemuPopUpdateLogisticsShipmentRequest;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopBaseResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopCreateLogisticsShipmentResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopGetLogisticsShipmentResultResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopGetShipmentDocumentResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopGetShippingServicesResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopShippedPackageConfirmResponse;
import com.nsy.api.transfer.domain.response.platform.temuPop.TemuPopWarehouseListResponse;
import com.nsy.api.transfer.utils.TransferUtils;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;

@Service
public class TransferApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransferApiService.class);

    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.auth}")
    private String authServiceUrl;
    @Value("${auth.app-key}")
    private String appKey;
    @Value("${auth.secret}")
    private String secret;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ExternalApiLogService externalApiLogService;


    /**
     * temu半托管仓库列表
     */
    public TemuPopWarehouseListResponse getTemuPopWarehouseList(Integer storeId) {
        String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, url(authServiceUrl, "/temu-pop/warehouse-list"), storeId.toString(), null);
        try {
            TemuPopBaseResponse<TemuPopWarehouseListResponse> response = objectMapper.readValue(responseStr, new TemuPopWarehouseListResponseTypeReference());
            if (response == null || !response.getSuccess()) {
                String errorMsg = response != null ? response.getErrorMsg() : "获取仓库列表失败，响应为空";
                LOGGER.error("获取TemuPop仓库列表失败: {}", errorMsg);
                throw new BusinessServiceException("获取TemuPop仓库列表失败: " + errorMsg);
            }
            return response.getResult();
        } catch (JsonProcessingException e) {
            LOGGER.error("解析TemuPop仓库列表响应失败: {}", e.getMessage(), e);
            throw new BusinessServiceException("解析TemuPop仓库列表响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * temu半托管获取可用物流服务
     */
    public TemuPopGetShippingServicesResponse getTemuPopShippingServices(Integer storeId, TemuPopGetShippingServicesRequest request) {
        String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, url(authServiceUrl, "/temu-pop/shipping-services"), storeId.toString(), JSONUtil.toJsonStr(request));
        try {
            TemuPopBaseResponse<TemuPopGetShippingServicesResponse> response = objectMapper.readValue(responseStr, new TemuPopGetShippingServicesResponseTypeReference());
            if (response == null || !response.getSuccess()) {
                String errorMsg = response != null ? response.getErrorMsg() : "获取物流服务失败，响应为空";
                LOGGER.error("获取TemuPop物流服务失败: {}", errorMsg);
                throw new BusinessServiceException("获取TemuPop物流服务失败: " + errorMsg);
            }
            return response.getResult();
        } catch (JsonProcessingException e) {
            LOGGER.error("解析TemuPop物流服务响应失败: {}", e.getMessage(), e);
            throw new BusinessServiceException("解析TemuPop物流服务响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * temu半托管创建物流发货
     */
    public TemuPopCreateLogisticsShipmentResponse createTemuPopShipment(Integer storeId, TemuPopCreateLogisticsShipmentRequest request) {
        String apiUrl = url(authServiceUrl, "/temu-pop/shipment-create");
        String requestJson = JSONUtil.toJsonStr(request);
        // 从请求中获取第一个订单信息作为文档号
        String documentNo = "TEMU_POP_SHIPMENT";
        if (request.getSendRequestList() != null && !request.getSendRequestList().isEmpty()
                && request.getSendRequestList().get(0).getOrderSendInfoList() != null
                && !request.getSendRequestList().get(0).getOrderSendInfoList().isEmpty()) {
            documentNo = request.getSendRequestList().get(0).getOrderSendInfoList().get(0).getParentOrderSn();
        }

        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(
                ExternalApiInfoEnum.TEMU_POP_CREATE_SHIPMENT,
                apiUrl,
                requestJson,
                documentNo,
                String.format("TEMU半托管创建物流发货，订单号：%s", documentNo)
        );

        try {
            String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, apiUrl, storeId.toString(), requestJson);
            TemuPopBaseResponse<TemuPopCreateLogisticsShipmentResponse> response = objectMapper.readValue(responseStr, new TemuPopCreateLogisticsShipmentResponseTypeReference());

            if (response == null || !response.getSuccess()) {
                String errorMsg = response != null ? response.getErrorMsg() : "创建物流发货失败，响应为空";
                LOGGER.error("创建TemuPop物流发货失败: {}", errorMsg);
                externalApiLogService.updateLog(apiLogEntity, responseStr, ExternalApiLogStatusEnum.FAIL);
                throw new BusinessServiceException("创建TemuPop物流发货失败: " + errorMsg);
            }

            externalApiLogService.updateLog(apiLogEntity, responseStr, ExternalApiLogStatusEnum.SUCCESS);
            return response.getResult();
        } catch (JsonProcessingException e) {
            LOGGER.error("解析TemuPop创建物流发货响应失败: {}", e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("解析TemuPop创建物流发货响应失败: " + e.getMessage(), e);
        } catch (Exception e) {
            LOGGER.error("调用TemuPop创建物流发货接口失败: {}", e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("调用TemuPop创建物流发货接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * temu半托管获取发货结果
     */
    public TemuPopGetLogisticsShipmentResultResponse getTemuPopShipmentResult(Integer storeId, TemuPopGetLogisticsShipmentResultRequest request) {
        String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, url(authServiceUrl, "/temu-pop/shipment-result"), storeId.toString(), JSONUtil.toJsonStr(request));
        try {
            TemuPopBaseResponse<TemuPopGetLogisticsShipmentResultResponse> response = objectMapper.readValue(responseStr, new TemuPopGetLogisticsShipmentResultResponseTypeReference());
            if (response == null || !response.getSuccess()) {
                String errorMsg = response != null ? response.getErrorMsg() : "获取发货结果失败，响应为空";
                LOGGER.error("获取TemuPop发货结果失败: {}", errorMsg);
                throw new BusinessServiceException("获取TemuPop发货结果失败: " + errorMsg);
            }
            return response.getResult();
        } catch (JsonProcessingException e) {
            LOGGER.error("解析TemuPop发货结果响应失败: {}", e.getMessage(), e);
            throw new BusinessServiceException("解析TemuPop发货结果响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * temu半托管获取发货单文件
     */
    public TemuPopGetShipmentDocumentResponse getTemuPopShipmentDocument(Integer storeId, TemuPopGetShipmentDocumentRequest request) {
        String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, url(authServiceUrl, "/temu-pop/shipment-document"), storeId.toString(), JSONUtil.toJsonStr(request));
        try {
            TemuPopBaseResponse<TemuPopGetShipmentDocumentResponse> response = objectMapper.readValue(responseStr, new TemuPopGetShipmentDocumentResponseTypeReference());
            if (response == null || !response.getSuccess()) {
                String errorMsg = response != null ? response.getErrorMsg() : "获取发货单文件失败，响应为空";
                LOGGER.error("获取TemuPop发货单文件失败: {}", errorMsg);
                throw new BusinessServiceException("获取TemuPop发货单文件失败: " + errorMsg);
            }
            return response.getResult();
        } catch (JsonProcessingException e) {
            LOGGER.error("解析TemuPop发货单文件响应失败: {}", e.getMessage(), e);
            throw new BusinessServiceException("解析TemuPop发货单文件响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * temu半托管更新发货单
     */
    public Boolean updateTemuPopShipment(Integer storeId, TemuPopUpdateLogisticsShipmentRequest request) {
        String apiUrl = url(authServiceUrl, "/temu-pop/shipment-update");
        String requestJson = JSONUtil.toJsonStr(request);
        // 从请求中获取第一个parentOrderSn作为文档号
        String documentNo = "TEMU_POP_UPDATE_" + System.currentTimeMillis();
        if (request.getRetrySendPackageRequestList() != null && !request.getRetrySendPackageRequestList().isEmpty()) {
            String parentOrderSn = request.getRetrySendPackageRequestList().get(0).getParentOrderSn();
            if (parentOrderSn != null && !parentOrderSn.isEmpty()) {
                documentNo = parentOrderSn;
            }
        }

        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(
                ExternalApiInfoEnum.TEMU_POP_UPDATE_SHIPMENT,
                apiUrl,
                requestJson,
                documentNo,
                String.format("TEMU半托管更新发货单，文档号：%s", documentNo)
        );

        try {
            String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, apiUrl, storeId.toString(), requestJson);
            TemuPopBaseResponse<Boolean> response = objectMapper.readValue(responseStr, new TemuPopUpdateLogisticsShipmentResponseTypeReference());

            if (response == null || !response.getSuccess()) {
                String errorMsg = response != null ? response.getErrorMsg() : "更新发货单失败，响应为空";
                LOGGER.error("更新TemuPop发货单失败: {}", errorMsg);
                externalApiLogService.updateLog(apiLogEntity, responseStr, ExternalApiLogStatusEnum.FAIL);
                throw new BusinessServiceException("更新TemuPop发货单失败: " + errorMsg);
            }

            externalApiLogService.updateLog(apiLogEntity, responseStr, ExternalApiLogStatusEnum.SUCCESS);
            return response.getResult();
        } catch (JsonProcessingException e) {
            LOGGER.error("解析TemuPop更新发货单响应失败: {}", e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("解析TemuPop更新发货单响应失败: " + e.getMessage(), e);
        } catch (Exception e) {
            LOGGER.error("调用TemuPop更新发货单接口失败: {}", e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("调用TemuPop更新发货单接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * temu半托管包裹发货确认
     */
    public TemuPopShippedPackageConfirmResponse confirmTemuPopPackage(Integer storeId, TemuPopShippedPackageConfirmRequest request) {
        String apiUrl = url(authServiceUrl, "/temu-pop/package-confirm");
        String requestJson = JSONUtil.toJsonStr(request);
        // 从请求中获取第一个packageSn作为文档号
        String documentNo = "TEMU_POP_CONFIRM_" + System.currentTimeMillis();
        if (request.getPackageSendInfoList() != null && !request.getPackageSendInfoList().isEmpty()) {
            String packageSn = request.getPackageSendInfoList().get(0).getPackageSn();
            if (packageSn != null && !packageSn.isEmpty()) {
                documentNo = packageSn;
            }
        }

        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(
                ExternalApiInfoEnum.TEMU_POP_CONFIRM_PACKAGE,
                apiUrl,
                requestJson,
                documentNo,
                String.format("TEMU半托管包裹发货确认，包裹号：%s", documentNo)
        );

        try {
            String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, apiUrl, storeId.toString(), requestJson);
            TemuPopBaseResponse<TemuPopShippedPackageConfirmResponse> response = objectMapper.readValue(responseStr, new TemuPopShippedPackageConfirmResponseTypeReference());

            if (response == null || !response.getSuccess()) {
                String errorMsg = response != null ? response.getErrorMsg() : "包裹发货确认失败，响应为空";
                LOGGER.error("TemuPop包裹发货确认失败: {}", errorMsg);
                externalApiLogService.updateLog(apiLogEntity, responseStr, ExternalApiLogStatusEnum.FAIL);
                throw new BusinessServiceException("TemuPop包裹发货确认失败: " + errorMsg);
            }

            externalApiLogService.updateLog(apiLogEntity, responseStr, ExternalApiLogStatusEnum.SUCCESS);
            return response.getResult();
        } catch (JsonProcessingException e) {
            LOGGER.error("解析TemuPop包裹发货确认响应失败: {}", e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("解析TemuPop包裹发货确认响应失败: " + e.getMessage(), e);
        } catch (Exception e) {
            LOGGER.error("调用TemuPop包裹发货确认接口失败: {}", e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException("调用TemuPop包裹发货确认接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * temu半托管获取下载数据
     */
    public byte[] executeTemuPopShippingLabelUrl(String labelUrl) {
        String requestBody = JSONUtil.createObj().put("url", labelUrl).toString();
        String responseStr = TransferUtils.setPostRequest(restTemplate, appKey, secret, url(authServiceUrl, "/temu-pop/shipping-label-url"), null, requestBody);
        try {
            return responseStr.getBytes();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }


    public static String url(String url, String path) {
        return String.format("%s%s", url, path);
    }

    private static final class TemuPopWarehouseListResponseTypeReference extends TypeReference<TemuPopBaseResponse<TemuPopWarehouseListResponse>> {
    }

    private static final class TemuPopGetShippingServicesResponseTypeReference extends TypeReference<TemuPopBaseResponse<TemuPopGetShippingServicesResponse>> {
    }

    private static final class TemuPopCreateLogisticsShipmentResponseTypeReference extends TypeReference<TemuPopBaseResponse<TemuPopCreateLogisticsShipmentResponse>> {
    }

    private static final class TemuPopGetLogisticsShipmentResultResponseTypeReference extends TypeReference<TemuPopBaseResponse<TemuPopGetLogisticsShipmentResultResponse>> {
    }

    private static final class TemuPopGetShipmentDocumentResponseTypeReference extends TypeReference<TemuPopBaseResponse<TemuPopGetShipmentDocumentResponse>> {
    }

    private static final class TemuPopUpdateLogisticsShipmentResponseTypeReference extends TypeReference<TemuPopBaseResponse<Boolean>> {
    }

    private static final class TemuPopShippedPackageConfirmResponseTypeReference extends TypeReference<TemuPopBaseResponse<TemuPopShippedPackageConfirmResponse>> {
    }
}
