package com.nsy.wms.business.service.print.stockout;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.bd.PrintTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.print.PrintDistributionRequest;
import com.nsy.api.wms.response.stockout.PickingTaskPrintRequest;
import com.nsy.wms.business.service.print.IPrintService;
import com.nsy.wms.business.service.stockout.StockoutBatchMergeService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderService;
import com.nsy.wms.business.service.stockout.StockoutBatchPrintService;
import com.nsy.wms.business.service.stockout.StockoutBatchService;
import com.nsy.wms.business.service.stockout.StockoutPickingTaskService;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 透明计划融合标签打印服务
 * <AUTHOR>
 * @date 2024/11/8 13:43
 */
@Service
public class StockoutPickingTaskAllPrintService implements IPrintService {

    @Resource
    private StockoutBatchService batchService;
    @Resource
    private StockoutBatchOrderService stockoutBatchOrderService;
    @Resource
    private StockoutBatchMergeService stockoutBatchMergeService;
    @Resource
    private StockoutPickingTaskService taskService;
    @Autowired
    private StockoutBatchPrintService batchPrintService;

    @Override
    public String templateType() {
        return PrintTypeEnum.WMS_PICKING_TASK_ALL.name();
    }

    @Override
    public List<Map<String, Object>> dataAssembly(PrintDistributionRequest request) {
        List<Map<String, Object>> result = new ArrayList<>();
        PickingTaskPrintRequest printParams = JsonMapper.fromJson(request.getRequestParams(), PickingTaskPrintRequest.class);
        if (!CollectionUtils.isEmpty(printParams.getBatchIdList())) {
            // 根据波次号打印
            List<StockoutBatchEntity> stockoutBatchEntities = batchService.getBaseMapper().listByIdsOrderByIds(printParams.getBatchIdList());
            if (stockoutBatchEntities.stream().anyMatch(entity -> StockoutWaveTaskStatusEnum.NEW.name().equals(entity.getStatus()) || StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name().equals(entity.getStatus())))
                throw new BusinessServiceException("请先生成拣货任务！");
            for (StockoutBatchEntity batch : stockoutBatchEntities) {
                printBatchPickingTask(batch, result);
            }
        }
        return result;
    }

    private void printBatchPickingTask(StockoutBatchEntity batch, List<Map<String, Object>> result) {
        List<StockoutBatchOrderEntity> allByBatchId = stockoutBatchOrderService.findAllByBatchId(batch.getBatchId());
        if (CollectionUtils.isEmpty(allByBatchId))
            allByBatchId = stockoutBatchOrderService.findAllByBatchIds(stockoutBatchMergeService.findBatchWithMerge(batch));
        List<StockoutPickingTaskEntity> list = taskService.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().eq(StockoutPickingTaskEntity::getBatchId, batch.getBatchId()));
        List<StockoutPickingTaskEntity> processList = new ArrayList<>();
        if (batch.getIsNeedProcess() == 1) {
            processList = list.stream().filter(it -> it.getIsNeedProcess() == 1).collect(Collectors.toList());
            list = list.stream().filter(it -> it.getIsNeedProcess() != 1).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, Object> printNormalMap = batchPrintService.printBatchWhole(allByBatchId, batch, list);
            result.add(printNormalMap);
        }
        if (!CollectionUtils.isEmpty(processList)) {
            Map<String, Object> printProcessMap = batchPrintService.printBatchWhole(allByBatchId, batch, processList);
            result.add(printProcessMap);
        }
    }
}
