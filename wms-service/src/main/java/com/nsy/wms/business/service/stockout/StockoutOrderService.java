package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.TmsCommonConstant;
import com.nsy.api.wms.domain.bd.BdErpSpaceMapping;
import com.nsy.api.wms.domain.stockout.StockoutOrderList;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.domain.stockout.StockoutStatusCount;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.TagTypeEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.stockout.AmazonBuyShippingInfoEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutDeliveryNoticeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderMergeStateEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderAddRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemAddRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderListRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderLogisticsCompanyRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderNotifyShipStatusRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderPickingTypeRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderWorkspaceRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderDetailResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderListResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderStatusResponse;
import com.nsy.wms.business.domain.bo.bd.BdFindEnableTagMappingBo;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpGetTradeStatusRequest;
import com.nsy.wms.business.manage.erp.request.ErpUpdateLogisticsRequest;
import com.nsy.wms.business.manage.erp.response.ErpGetTradeStatusResponse;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.bd.BdPrematchRuleService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.bd.BdTagService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.logistics.base.PrintService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.valid.StockoutOrderValid;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockoutOrderService extends ServiceImpl<StockoutOrderMapper, StockoutOrderEntity> implements IDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderService.class);

    @Autowired
    BdSpaceService spaceService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    BdSystemParameterService parameterService;
    @Autowired
    StockoutOrderLogService logService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutOrderAddService stockoutOrderAddService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    PrintService printService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    ExternalApiLogService externalApiLogService;
    @Autowired
    BdPrematchRuleService bdPrematchRuleService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutOrderProcessInfoService processInfoService;
    @Autowired
    StockoutOrderItemProcessInfoService itemProcessInfoService;
    @Autowired
    StockoutShipmentErpPickingBoxService shipmentErpPickingBoxService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    StockoutOrderListService stockoutOrderListService;
    @Autowired
    TmsCacheService tmsCacheService;
    @Autowired
    StockoutShipmentAmazonRelationService amazonService;
    @Autowired
    MessageProducer producer;
    @Autowired
    BdTagMappingService tagMappingService;
    @Autowired
    BdTagService tagService;
    @Autowired
    StockoutOrderOttoExtendService ottoExtendService;
    @Autowired
    StockoutOrderTikTokLocalShippingExtendService tikTokDirectExtendService;
    @Autowired
    BrandCommonService brandCommonService;
    @Autowired
    StockoutOrderTemuPopExtendService temuPopExtendService;

    // 出库单列表
    public PageResponse<StockoutOrderListResponse> getOutOrderListByRequest(StockoutOrderListRequest request) {
        PageResponse<StockoutOrderListResponse> pageResponse = new PageResponse<>();
        Page<StockoutOrderList> page = new Page<>(request.getPageIndex(), request.getPageSize());
        //设置请求参数
        stockoutOrderListService.buildListRequest(request);

        IPage<StockoutOrderList> pageResult = this.baseMapper.pageSearchList(page, request);
        List<StockoutOrderItemEntity> filterItemEntityList = new ArrayList<>();
        List<StockoutOrderItemEntity> itemEntityList = null;
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> stockoutOrderIdList = pageResult.getRecords().stream().map(StockoutOrderList::getStockoutOrderId).distinct().collect(Collectors.toList());
            itemEntityList = stockoutOrderItemService.listByStockoutOrderIds(stockoutOrderIdList);
            if (!CollectionUtils.isEmpty(itemEntityList))
                filterItemEntityList = itemEntityList.stream().filter(item -> StringUtils.hasText(item.getSpaceAreaName())).collect(Collectors.toList());
        }
        List<StockoutOrderListResponse> orderList = buildStockoutOrderResponse(pageResult.getRecords(), itemEntityList, filterItemEntityList);
        //返回设置波次ID
        stockoutOrderListService.buildBatchOrderIdResponse(orderList);
        //返回订单号
        stockoutOrderListService.buildOrderNosResponse(orderList);
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(orderList);
        return pageResponse;
    }

    private List<StockoutOrderListResponse> buildStockoutOrderResponse(List<StockoutOrderList> records, List<StockoutOrderItemEntity> itemList, List<StockoutOrderItemEntity> filterItemEntityList) {
        Map<String, String> stockoutDocStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_DOC_STATUS.getName());
        Map<String, String> stockoutTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_TYPE.getName());
        Map<String, String> stockoutPickingTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName());
        Map<String, String> stockoutWorkLocationEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName());
        Map<String, String> stockoutDeliveryNoticeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_DELIVERY_NOTICE.getName());
        Map<String, String> stockoutPlatformNameEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_ORDER_PLATFORM_NAME.getName());
        return records.stream().map(projection -> {
            StockoutOrderListResponse response = new StockoutOrderListResponse();
            BeanUtils.copyProperties(projection, response);
            response.setPlatformNameCn(StrUtil.isEmpty(response.getPlatformName()) ? "" : stockoutPlatformNameEnumMap.get(response.getPlatformName()));
            response.setNeedProcess(projection.getNeedProcess());
            response.setUrgent(projection.getUrgent());
            if (projection.getDescSpaceId() != null) {
                BdSpaceEntity targetSpace = spaceService.getById(projection.getDescSpaceId());
                response.setDesSpaceName(targetSpace == null ? null : targetSpace.getSpaceName());
            }
            response.setLack(projection.getLack());
            response.setMerge(StringUtils.hasText(projection.getMergeState()));
            response.setExpectShipDate(projection.getLatestDeliveryDate());
            response.setLastSendDate(projection.getLatestDeliveryDate());
            response.setStatusStr(stockoutDocStatusEnumMap.get(projection.getStatus()));
            response.setStockoutTypeStr(stockoutTypeEnumMap.get(projection.getStockoutType()));
            response.setPickingTypeStr(stockoutPickingTypeEnumMap.get(projection.getPickingType()));
            response.setWorkspaceStr(stockoutWorkLocationEnumMap.get(projection.getWorkspace()));
            response.setNotifyShipStatusStr(stockoutDeliveryNoticeEnumMap.get(projection.getNotifyShipStatus()));
            response.setTagName(tagService.findTag(new BdFindEnableTagMappingBo(TagTypeEnum.ORDER.name(), response.getStockoutOrderNo())));
            List<StockoutOrderItemEntity> filterItemList = filterItemEntityList.stream().filter(item -> projection.getStockoutOrderId().equals(item.getStockoutOrderId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterItemList)) {
                int waitPickedQty = filterItemList.stream().mapToInt(StockoutOrderItemEntity::getQty).sum();
                int skuQty = (int) filterItemList.stream().map(StockoutOrderItemEntity::getSku).distinct().count();
                int scanQty = filterItemList.stream().mapToInt(StockoutOrderItemEntity::getScanQty).sum();
                int shipmentQty = filterItemList.stream().mapToInt(StockoutOrderItemEntity::getShipmentQty).sum();
                response.setWaitPickedQty(waitPickedQty);
                response.setSkuQty(skuQty);
                response.setScanQty(scanQty);
                response.setShipmentQty(shipmentQty);
                response.setSpaceMemo(filterItemList.stream().map(StockoutOrderItemEntity::getSpaceMemo).filter(StringUtils::hasText).distinct().collect(Collectors.joining(";")));
            }
            if (!CollectionUtils.isEmpty(itemList)) {
                List<StockoutOrderItemEntity> hasSpaceAreaNameItemList = itemList.stream().filter(item -> item.getStockoutOrderId().equals(projection.getStockoutOrderId()) && StringUtils.hasText(item.getSpaceAreaName())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(hasSpaceAreaNameItemList)) {
                    List<String> spaceAreaNameList = hasSpaceAreaNameItemList.stream().map(StockoutOrderItemEntity::getSpaceAreaName).distinct().collect(Collectors.toList());
                    response.setSpaceAreaName(String.join(",", spaceAreaNameList));
                } else {
                    response.setSpaceAreaName("");
                }
                response.setIsTransparency(itemList.stream().anyMatch(orderItems -> orderItems.getStockoutOrderId().equals(projection.getStockoutOrderId()) && orderItems.getIsTransparency()));
            }
            return response;
        }).collect(Collectors.toList());

    }

    // 出库单详情
    public StockoutOrderDetailResponse getOutOrderDetailById(Integer stockoutOrderId) {
        StockoutOrderEntity stockoutOrderEntity = getById(stockoutOrderId);
        if (Objects.isNull(stockoutOrderEntity)) throw new BusinessServiceException("出库单不存在");
        BdSpaceEntity spaceEntity = spaceService.getById(stockoutOrderEntity.getSpaceId());
        if (Objects.isNull(spaceEntity)) throw new BusinessServiceException("仓库不存在");
        StockoutOrderDetailResponse response = new StockoutOrderDetailResponse();
        List<StockoutOrderItemEntity> itemEntityList = stockoutOrderItemService.list(new LambdaQueryWrapper<StockoutOrderItemEntity>()
                .eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderId));
        if (!itemEntityList.isEmpty()) {
            List<String> orderNos = itemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).collect(Collectors.toList());
            response.setOrderNos(orderNos.stream().distinct().map(String::valueOf).collect(Collectors.joining(",")));
            response.setSpaceMemo(itemEntityList.stream().map(StockoutOrderItemEntity::getSpaceMemo).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        }
        StockoutBatchOrderEntity stockoutBatchOrderEntity = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderId);
        if (!Objects.isNull(stockoutBatchOrderEntity)) {
            response.setBatchId(stockoutBatchOrderEntity.getBatchId());
        }
        BeanUtils.copyProperties(stockoutOrderEntity, response);
        response.setSpaceName(spaceEntity.getSpaceName());
        response.setStockoutTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_TYPE.getName(), stockoutOrderEntity.getStockoutType()));
        response.setPickingTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), stockoutOrderEntity.getPickingType()));
        response.setWorkspaceStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), stockoutOrderEntity.getWorkspace()));
        response.setNotifyShipStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_DELIVERY_NOTICE.getName(), stockoutOrderEntity.getNotifyShipStatus()));
        response.setMerge(StringUtils.hasText(stockoutOrderEntity.getMergeState()));
        response.setExpectShipDate(stockoutOrderEntity.getLatestDeliveryDate());
        response.setReadyDate(stockoutOrderEntity.getReadyDate());
        response.setActualWeight(stockoutOrderItemService.getItemWeight(stockoutOrderEntity.getStockoutOrderNo()).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP) + "(kg)");
        StockoutReceiverInfo receiverInfo = stockoutReceiverInfoService.getReceiveInfoByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        BeanUtils.copyProperties(receiverInfo, response);
        List<StockoutShipmentEntity> shipments = shipmentItemService.getShipmentByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        shipments.forEach(shipment -> {
            if (shipment.getWeight() != null)
                response.setShipmentTotalWeight(response.getShipmentTotalWeight() == null ? shipment.getWeight() : response.getShipmentTotalWeight().add(shipment.getWeight()));
            if (shipment.getVolumeWeight() != null)
                response.setShipmentTotalVolumeWeight(response.getShipmentTotalVolumeWeight() == null ? shipment.getVolumeWeight() : response.getShipmentTotalVolumeWeight().add(shipment.getVolumeWeight()));
            response.setShipmentBoxCount(response.getShipmentBoxCount() == null ? 1 : response.getShipmentBoxCount() + 1);
        });
        if (StockoutOrderTypeEnum.FIRST_LEG_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType()))
            response.setReferenceId(amazonService.getReferenceIdByTid(itemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).collect(Collectors.toList())));
        return logService.buildOperateDate(response, stockoutOrderEntity.getStockoutOrderNo());
    }

    // 新增出库单
    @JLock(keyConstant = "addStockoutOrder", lockKey = "#addRequest.outOrderInfo.erpPickId")
    public void addOutOrder(StockoutOrderAddRequest addRequest) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.PARTIAL_PICK_PUSH, "/stockout-order",
                JsonMapper.toJson(addRequest), addRequest.getOutOrderInfo().getErpPickId().toString(), "OMS拣货单同步WMS");
        producer.sendMessage(KafkaConstant.STOCKOUT_ORDER_PUSH_TOPIC_MARK, KafkaConstant.STOCKOUT_ORDER_PUSH_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), apiLogEntity.getApiLogId()));
    }

    /**
     * 新增出库单 - 异步处理
     */
    @Transactional
    public void addOutOrderAsync(StockoutOrderAddRequest addRequest) {
        //校验出库单数据是否正常
        StockoutOrderValid.validStockoutOrderAddRequest(addRequest);
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderAddService.buildStockoutOrder(addRequest);
        List<StockoutOrderItemEntity> outOrderItemEntityList = new ArrayList<>(addRequest.getOutOrderItemList().size());
        BigDecimal totalWeight = BigDecimal.valueOf(0);
        List<String> skuList = addRequest.getOutOrderItemList().stream().map(StockoutOrderItemAddRequest::getSku).distinct().collect(Collectors.toList());
        List<ProductSpecInfoEntity> productSpecInfoEntityList = productSpecInfoService.list(new QueryWrapper<ProductSpecInfoEntity>().lambda().in(ProductSpecInfoEntity::getSku, skuList));
        BdSystemParameterEntity bdSystemParameterEntity = parameterService.getByKey(BdSystemParameterEnum.WMS_STORE_SPACE_MEMO.getKey());
        Map<String, String> storeMap = bdSystemParameterEntity != null && StringUtils.hasText(bdSystemParameterEntity.getConfigValue()) ? JsonMapper.fromJson(bdSystemParameterEntity.getConfigValue(), Map.class) : new HashMap<>();
        for (StockoutOrderItemAddRequest itemAddRequest : addRequest.getOutOrderItemList()) {
            ProductSpecInfoEntity specInfoEntity = productSpecInfoEntityList.stream().filter(o -> o.getSku().equals(itemAddRequest.getSku())).findFirst().orElse(null);
            if (specInfoEntity == null) throw new BusinessServiceException("未找到规格编码！");
            outOrderItemEntityList.add(stockoutOrderItemService.addOrderItem(stockoutOrderEntity, specInfoEntity, itemAddRequest, addRequest.getOutOrderInfo(), storeMap));
            if (specInfoEntity.getWeight() != null)
                totalWeight = totalWeight.add(specInfoEntity.getWeight().multiply(new BigDecimal(itemAddRequest.getQty())));
        }
        // 库存预配会设置库区，该逻辑去除
        //bdPrematchRuleService.buildSpaceArea(stockoutOrderEntity.getSpaceId(), stockoutOrderEntity.getStockoutType(), outOrderItemEntityList);
        stockoutOrderEntity.setEstimateWeight(totalWeight);
        updateById(stockoutOrderEntity);
        // 拣货模式，移至预配逻辑之后
        // stockoutOrderAddService.changePickingType(stockoutOrderEntity);
        // 合单操作
        Integer mergeParentId = stockoutOrderAddService.mergeStockoutOrder(stockoutOrderEntity, outOrderItemEntityList, addRequest.getReceiverInfo());
        // 添加面单
        Integer stockoutOrderId = mergeParentId != -1 ? mergeParentId : stockoutOrderEntity.getStockoutOrderId();
        stockoutOrderAddService.addStockoutOrderLabel(stockoutOrderId, addRequest);
        // 订单备注
        stockoutOrderAddService.addMemo(stockoutOrderId);
        // 加工
        if (stockoutOrderEntity.getNeedProcess()) {
            processInfoService.addByStockoutOrderInfo(addRequest.getOutOrderInfo(), stockoutOrderEntity);
            itemProcessInfoService.addByStockoutOrderItemInfo(addRequest.getOutOrderItemList(), stockoutOrderEntity);
        }
        // PACK商品
        stockoutOrderAddService.addPackInfo(stockoutOrderId, addRequest.getPackInfo(), stockoutOrderEntity.getCreateBy(), stockoutOrderEntity.getStockoutType());
        //判断是否品牌单，打品牌标签
        setBrandOrderTag(addRequest, stockoutOrderEntity);
        //处理扩展表信息
        handleExtend(stockoutOrderEntity, addRequest);
        // 预配出库单 - 异步处理
        producer.sendMessage(KafkaConstant.STOCKOUT_ORDER_PREMATCH_TOPIC_MARK, KafkaConstant.STOCKOUT_ORDER_PREMATCH_TOPIC, Key.of(stockoutOrderEntity.getStockoutOrderNo()), new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockoutOrderEntity.getStockoutOrderId()));
    }

    /**
     * 处理扩展表信息
     *
     * @param stockoutOrderEntity
     * @param addRequest
     */
    private void handleExtend(StockoutOrderEntity stockoutOrderEntity, StockoutOrderAddRequest addRequest) {
        // Temu
        if (StringUtils.hasText(stockoutOrderEntity.getPlatformName())
                && StockoutOrderPlatformEnum.PDD.getName().equals(stockoutOrderEntity.getPlatformName())
                && 2 == addRequest.getOutOrderInfo().getOriginType()) {
            stockoutOrderAddService.addTemuInfo(stockoutOrderEntity, addRequest.getOutOrderInfo().getSubWarehouseId());
            return;
        }

        // Shein
        if (StringUtils.hasText(stockoutOrderEntity.getPlatformName())
                && StockoutOrderPlatformEnum.SHEIN.getName().equals(stockoutOrderEntity.getPlatformName())
                && 2 == addRequest.getOutOrderInfo().getOriginType()) {
            stockoutOrderAddService.addSheinInfo(stockoutOrderEntity, addRequest.getOutOrderInfo().getSheinType());
            return;
        }

        // tiktok
        if (StringUtils.hasText(stockoutOrderEntity.getPlatformName())
                && StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(stockoutOrderEntity.getPlatformName())
                && 2 == addRequest.getOutOrderInfo().getOriginType()) {
            stockoutOrderAddService.addTikTokInfo(stockoutOrderEntity, addRequest.getOutOrderInfo().getSheinType());
            return;
        }

        //tk直邮
        if (StringUtils.hasText(stockoutOrderEntity.getPlatformName())
                && StockoutOrderPlatformEnum.TIKTOK_LOCAL_SHIPPING.getName().equals(stockoutOrderEntity.getPlatformName())
                && StringUtils.hasText(addRequest.getOutOrderInfo().getPackageId())) {
            tikTokDirectExtendService.addTikTokInfo(stockoutOrderEntity, addRequest.getOutOrderInfo().getPackageId());
        }
        // otto
        if (StringUtils.hasText(stockoutOrderEntity.getPlatformName())
                && StockoutOrderPlatformEnum.OTTO.getName().equals(stockoutOrderEntity.getPlatformName())) {
            ottoExtendService.createExtend(stockoutOrderEntity);
            return;
        }

        // TemuPop
        if (StringUtils.hasText(stockoutOrderEntity.getPlatformName())
                && StockoutOrderPlatformEnum.TEMU_POP.getName().equals(stockoutOrderEntity.getPlatformName())) {
            temuPopExtendService.createExtend(stockoutOrderEntity);
            return;
        }
    }

    /**
     * 出库单打品牌标签
     *
     * @param addRequest
     * @param stockoutOrderEntity
     */
    private void setBrandOrderTag(StockoutOrderAddRequest addRequest, StockoutOrderEntity stockoutOrderEntity) {
        Map<String, BdErpSpaceMapping> commonPositionCodeMap = brandCommonService.getCommonPositionCodeMap();

        BdErpSpaceMapping mapping = null;
        List<String> collect = addRequest.getOutOrderItemList().stream().map(StockoutOrderItemAddRequest::getPositionCode).distinct().collect(Collectors.toList());

        for (String positionCode : collect) {
            BdErpSpaceMapping bdErpSpaceMapping = commonPositionCodeMap.get(positionCode);
            if (bdErpSpaceMapping != null) {
                mapping = bdErpSpaceMapping;
                break;
            }
        }

        //todo 订单如果预配到TM09库区，则打上 "dokotoo仓" 标签
        if (collect.stream().anyMatch(position -> position.startsWith("TM09"))) {
            mapping = commonPositionCodeMap.get("QZ-DK");
        }
        //todo end

        //出库类型是内购出库的单  下来不需要打品牌标签
        if (StockoutOrderTypeEnum.INTERNAL_PURCHASE_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType()))
            return;

        if (Objects.nonNull(mapping)) {
            tagMappingService.saveOrderTag(mapping.getBrandTagName(), stockoutOrderEntity.getStockoutOrderNo());
        }
        if (addRequest.getOutOrderInfo().getCrossPosition()) {
            tagMappingService.saveOrderTag("有标单", stockoutOrderEntity.getStockoutOrderNo());
        }
    }

    // 设置物流公司
    @Transactional
    public void updateOutOrderLogisticsCompany(StockoutOrderLogisticsCompanyRequest request) {
        QueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(StockoutOrderEntity::getStockoutOrderId, request.getStockoutOrderIds());
        List<StockoutOrderEntity> stockoutOrderEntityList = list(queryWrapper);
        LOGGER.info("[cache] getAllLogisticsCompanyList, in updateOutOrderLogisticsCompany");
        List<TmsLogisticsCompany> allLogisticsCompanyList = tmsCacheService.getAllLogisticsCompanyList();
        for (StockoutOrderEntity stockoutOrderEntity : stockoutOrderEntityList) {
            if (StockoutOrderTypeEnum.OVERSEA_DELIVERY.name().equalsIgnoreCase(stockoutOrderEntity.getStockoutType()) && (stockoutOrderEntity.getSpaceId() == 65 || stockoutOrderEntity.getSpaceId() == 71)) {
                throw new BusinessServiceException("海外仓订单目前不允许修改物流！");
            }

            if (AmazonBuyShippingInfoEnum.ENABLE.getValue().equals(stockoutOrderEntity.getAmazonBuyShippingInfo())) {
                throw new BusinessServiceException(String.format("%s 已设置了购买配送，不允许修改物流", stockoutOrderEntity.getStockoutOrderNo()));
            }
            // 重新获取面单信息
            String oldLogisticsCompany = stockoutOrderEntity.getLogisticsCompany();
            stockoutOrderEntity.setLogisticsCompany(request.getLogisticsCompany());
            // 如果是小包，那么就立马获取面单，否则为空
            stockoutOrderEntity.setLogisticsNo("");
            if (StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name().equalsIgnoreCase(stockoutOrderEntity.getWorkspace())
                    && !TmsCommonConstant.FILTER_LOGISTICS.contains(request.getLogisticsCompany()) && request.isSyncErp()) {
                BaseGetLogisticsNoResponse response = printService.generateLabelByStockoutOrder(stockoutOrderEntity, loginInfoService.getUserName(), "修改物流重新");
                stockoutOrderEntity.setLogisticsNo(response.getLogisticsNo());
                stockoutOrderEntity.setSecondaryNumber(response.getSecondaryNumber());
                stockoutOrderEntity.setAmazonBuyShippingInfo(1 == response.getAmazonBuyShippingFlag() ? AmazonBuyShippingInfoEnum.ENABLE.getValue() : AmazonBuyShippingInfoEnum.DISABLE.getValue());
            }
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            allLogisticsCompanyList.stream().filter(item -> item.getLogisticsCompany().equalsIgnoreCase(request.getLogisticsCompany()))
                    .findFirst().ifPresent(item -> stockoutOrderEntity.setLogisticsLabelSize(item.getPrintSpec()));
            updateById(stockoutOrderEntity);
            // 如果有装箱清单，那么也更新装箱清单的值
            changeShipmentLogistics(stockoutOrderEntity);
            logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CHANGE_LOGISTICS, String.format("修改物流由【%s】修改为【%s】, 物流单号被改为【%s】",
                    oldLogisticsCompany, request.getLogisticsCompany(), stockoutOrderEntity.getLogisticsNo()));
            // 同步erp物流信息
            if (request.isSyncErp()) {
                syncErpLogistics(stockoutOrderEntity);
            }
        }
    }

    public void changeShipmentLogistics(StockoutOrderEntity stockoutOrderEntity) {
        List<StockoutShipmentEntity> shipmentList = shipmentItemService.findByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (CollectionUtils.isEmpty(shipmentList))
            return;
        for (StockoutShipmentEntity shipment : shipmentList) {
            if (StockoutShipmentStatusEnum.SHIPPED.name().equals(shipment.getStatus()))
                continue;
            shipment.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
            shipment.setLogisticsNo(stockoutOrderEntity.getLogisticsNo());
            shipmentService.updateById(shipment);
            if (StockoutShipmentStatusEnum.PACKING_END.name().equals(shipment.getStatus()))
                shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(shipment);
        }
    }

    public void syncErpLogistics(StockoutOrderEntity stockoutOrderEntity) {
        if (stockoutOrderEntity.getErpPickId() == null) {
            return;
        }
        ErpUpdateLogisticsRequest erpRequest = new ErpUpdateLogisticsRequest();
        String erpPickId = stockoutOrderEntity.getErpPickId().toString();
        erpRequest.setErpPickIds(erpPickId);
        erpRequest.setLogisticsNo(stockoutOrderEntity.getLogisticsNo());
        erpRequest.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
        erpRequest.setOperator(loginInfoService.getName());
        ErpUpdateLogisticsRequest.AmazonBuyShippingInfo amazonBuyShippingInfo = new ErpUpdateLogisticsRequest.AmazonBuyShippingInfo();
        amazonBuyShippingInfo.setErpPickId(erpPickId);
        amazonBuyShippingInfo.setAmazonBuyShippingFlag(AmazonBuyShippingInfoEnum.ENABLE.getValue().equals(stockoutOrderEntity.getAmazonBuyShippingInfo()) ? 1 : 0);
        erpRequest.setAmazonBuyShippingInfoList(Collections.singletonList(amazonBuyShippingInfo));
        erpApiService.updateLogistics(erpRequest);
    }

    // 设置拣货模式
    @Transactional
    public void updateOutOrderPickingType(StockoutOrderPickingTypeRequest request) {
        QueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(StockoutOrderEntity::getStockoutOrderId, request.getStockoutOrderIds());
        List<StockoutOrderEntity> stockoutOrderEntityList = list(queryWrapper);
        QueryWrapper<StockoutBatchOrderEntity> batchOrderEntityQueryWrapper = new QueryWrapper<>();
        batchOrderEntityQueryWrapper.lambda().ne(StockoutBatchOrderEntity::getStatus, StockoutWaveTaskStatusEnum.CANCELLED.name())
                .in(StockoutBatchOrderEntity::getStockoutOrderId, request.getStockoutOrderIds());
        List<StockoutBatchOrderEntity> stockoutBatchOrderEntityList = stockoutBatchOrderService.list(batchOrderEntityQueryWrapper);
        for (StockoutOrderEntity stockoutOrderEntity : stockoutOrderEntityList) {
            if (request.getPickingTypeEnum().name().equals(stockoutOrderEntity.getPickingType())) {
                continue;
            }
            if (stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.PICKING.name()))
                throw new BusinessServiceException(String.format("单号：%s，请选择未拣货的数据", stockoutOrderEntity.getStockoutOrderNo()));
            if (stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.READY_PICK.name()))
                throw new BusinessServiceException(String.format("单号：%s，当前出库单已经生成波次，更换拣货模式请先取消波次", stockoutOrderEntity.getStockoutOrderNo()));
            if (stockoutBatchOrderEntityList.stream().noneMatch(o -> o.getStockoutOrderId().equals(stockoutOrderEntity.getStockoutOrderId()))) {
                String oldPickingType = StockoutPickingTypeEnum.getNameBy(stockoutOrderEntity.getPickingType());
                stockoutOrderEntity.setPickingType(request.getPickingTypeEnum().name());
                stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
                updateById(stockoutOrderEntity);
                logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CHANGE_PICKING_TYPE, String.format("拣货模式由【%s】修改为【%s】", oldPickingType, request.getPickingTypeEnum().getStockoutPickingType()));
            }
        }
    }

    // 设置工作区域
    @Transactional
    public void updateOutOrderWorkspace(StockoutOrderWorkspaceRequest request) {
        List<StockoutOrderEntity> stockoutOrderEntityList = list(new LambdaQueryWrapper<StockoutOrderEntity>().in(StockoutOrderEntity::getStockoutOrderId, request.getStockoutOrderIds()));
        QueryWrapper<StockoutBatchOrderEntity> batchOrderEntityQueryWrapper = new QueryWrapper<>();
        batchOrderEntityQueryWrapper.lambda().in(StockoutBatchOrderEntity::getStockoutOrderId, request.getStockoutOrderIds());
        for (StockoutOrderEntity stockoutOrderEntity : stockoutOrderEntityList) {
            if (request.getWorkspace().equals(stockoutOrderEntity.getWorkspace()))
                continue;
            if (!StockoutOrderStatusEnum.READY_WAVE_GENERATED.name().equalsIgnoreCase(stockoutOrderEntity.getStatus())
                    && !StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equalsIgnoreCase(stockoutOrderEntity.getStatus())
                    && !StockoutOrderStatusEnum.WAIT_PRE_MATCH.name().equalsIgnoreCase(stockoutOrderEntity.getStatus())
                    && !StockoutOrderStatusEnum.UN_FULL_PRE_MATCH.name().equalsIgnoreCase(stockoutOrderEntity.getStatus())
                    && !StockoutOrderStatusEnum.READY.name().equalsIgnoreCase(stockoutOrderEntity.getStatus()))
                throw new BusinessServiceException(String.format("请选择待生成波次的数据，出库单号【%s】", stockoutOrderEntity.getStockoutOrderNo()));
            String oldWorkspace = StockoutOrderWorkSpaceEnum.getNameBy(stockoutOrderEntity.getWorkspace());
            stockoutOrderEntity.setWorkspace(request.getWorkspace());
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            updateById(stockoutOrderEntity);
            logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CHANGE_WORK_AREA, String.format("工作区域由【%s】修改为【%s】", oldWorkspace, StockoutOrderWorkSpaceEnum.getNameBy(request.getWorkspace())));
        }
    }

    // 暂不发货、设置发货
    @Transactional
    public void updateOutOrderNotifyShipStatus(StockoutOrderNotifyShipStatusRequest request) {
        QueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(StockoutOrderEntity::getStockoutOrderId, request.getStockoutOrderIds());
        List<StockoutOrderEntity> stockoutOrderEntityList = list(queryWrapper);
        for (StockoutOrderEntity stockoutOrderEntity : stockoutOrderEntityList) {
            // 正常发货
            if (request.getNoticeEnum() == StockoutDeliveryNoticeEnum.NORMAL_DELIVERY) {
                if (StockoutDeliveryNoticeEnum.NORMAL_DELIVERY.name().equals(stockoutOrderEntity.getNotifyShipStatus())
                        || StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrderEntity.getStatus())) {
                    throw new BusinessServiceException(String.format("单号：%s，请选择【等通知发货】且未发货的数据", stockoutOrderEntity.getStockoutOrderNo()));
                }
                checkDelivery(stockoutOrderEntity);
            } else {
                if (StockoutDeliveryNoticeEnum.WAIT_NOTIC_DELIVERY.name().equals(stockoutOrderEntity.getNotifyShipStatus())
                        || StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrderEntity.getStatus())) {
                    throw new BusinessServiceException(String.format("单号：%s，请选择【正常发货】且未发货的数据", stockoutOrderEntity.getStockoutOrderNo()));
                }
            }
            String oldNotifyShipStatus = StockoutDeliveryNoticeEnum.of(stockoutOrderEntity.getNotifyShipStatus());
            stockoutOrderEntity.setNotifyShipStatus(request.getNoticeEnum().name());
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            updateById(stockoutOrderEntity);
            String content = String.format("单号：%s，发货通知【%s】修改为【%s】", stockoutOrderEntity.getStockoutOrderNo(), oldNotifyShipStatus, request.getNoticeEnum().getName());
            logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.SHIPPING_NOTICE, content);
        }
    }

    /**
     * 检查商通是否可以发货
     *
     * @param stockoutOrder
     */
    private void checkDelivery(StockoutOrderEntity stockoutOrder) {
        List<StockoutOrderItemEntity> stockoutOrderItemList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        List<String> orderNoList = stockoutOrderItemList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList());
        List<ErpGetTradeStatusResponse> tradeStatusInfoList = erpApiService.getTradeStatus(new ErpGetTradeStatusRequest(orderNoList));
        tradeStatusInfoList.forEach(tradeStatusInfo -> {
            if (0 == tradeStatusInfo.getIsDeliveryAllowed())
                throw new BusinessServiceException(String.format("%s:%s", tradeStatusInfo.getTid(), tradeStatusInfo.getDeliveryMessage()));
        });
    }


    // 强制关单
    @Transactional
    public void forceDoneOutOrder(Integer stockoutOrderId) {
        StockoutOrderEntity stockoutOrderEntity = getById(stockoutOrderId);
        if (Objects.isNull(stockoutOrderEntity))
            throw new BusinessServiceException("出库单不存在");
        // 1）、若当前出库单缺货状态【是】且是【已发货】状态则进行出库单的强制完成完成，完成后反馈给至订单
        // 2）、若不是【是】缺货状态或不是【已发货】状态，则操作失败，同时提示出库单不是【已出库】状态且缺货单无法强制完成
        if (stockoutOrderEntity.getLack() && StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrderEntity.getStatus())) {
            // todo 通知订单系统
            throw new BusinessServiceException("订单系统还没有接口，等接口好了再改~");
        } else
            throw new BusinessServiceException(String.format("单号：%s，出库单不是【已发货】状态且非缺货单无法强制完成", stockoutOrderEntity.getStockoutOrderNo()));
    }

    // 状态数量
    public StockoutOrderStatusResponse getStatusCount() {
        List<String> statusList = StockoutBuilding.stockoutOrderStatus();
        List<StockoutStatusCount> statusCountList = this.baseMapper.statusCount(statusList);
        return StockoutBuilding.stockoutOrderStatusResponse(statusCountList);
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_ORDER_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockoutOrderListRequest stockoutOrderListRequest = JSONObject.parseObject(request.getRequestContent(), StockoutOrderListRequest.class);
        stockoutOrderListRequest.setPageIndex(request.getPageIndex());
        stockoutOrderListRequest.setPageSize(request.getPageSize());
        if (!CollectionUtils.isEmpty(stockoutOrderListRequest.getIdList()))
            stockoutOrderListRequest.setStockoutOrderIds(stockoutOrderListRequest.getIdList());
        PageResponse<StockoutOrderListResponse> outOrderListByRequest = this.getOutOrderListByRequest(stockoutOrderListRequest);
        response.setTotalCount(outOrderListByRequest.getTotalCount());
        response.setDataJsonStr(JSON.toJSONString(outOrderListByRequest.getContent()));
        return response;
    }

    public StockoutOrderEntity findByStockoutOrderNo(String stockoutOrderNo) {
        QueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockoutOrderEntity::getStockoutOrderNo, stockoutOrderNo).last("limit 1");
        return this.getOne(queryWrapper);
    }

    public StockoutOrderEntity getByStockoutOrderNo(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrderEntity = findByStockoutOrderNo(stockoutOrderNo);
        if (stockoutOrderEntity == null) throw new BusinessServiceException(stockoutOrderNo + "该出库单不存在");
        return stockoutOrderEntity;
    }

    public StockoutOrderEntity getByStockoutOrderId(Integer stockoutOrderId) {
        StockoutOrderEntity stockoutOrderEntity = getById(stockoutOrderId);
        if (stockoutOrderEntity == null) throw new BusinessServiceException(stockoutOrderId + "该出库单不存在");
        return stockoutOrderEntity;
    }

    public List<StockoutOrderEntity> getByStockoutOrderNoList(List<String> stockoutOrderNoList) {
        QueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<>();
        if (CollectionUtils.isEmpty(stockoutOrderNoList)) {
            LOGGER.info("入参的stockoutOrderNoList为空！");
            return Collections.emptyList();
        }
        queryWrapper.lambda().in(StockoutOrderEntity::getStockoutOrderNo, stockoutOrderNoList);
        return this.list(queryWrapper);
    }

    public void checkStockoutCancel(List<String> stockoutOrderNoList) {
        List<StockoutOrderEntity> stockoutOrderEntityList = this.getByStockoutOrderNoList(stockoutOrderNoList);
        if (stockoutOrderEntityList.stream().anyMatch(o -> StockoutOrderStatusEnum.CANCELLING.name().equals(o.getStatus()) || StockoutOrderStatusEnum.CANCELLED.name().equals(o.getStatus()))) {
            throw new BusinessServiceException("出库单已取消，请撤货！");
        }
    }

    public StockoutOrderDetailResponse getDetailByOutOrderNo(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrderEntity = this.getOne(new LambdaQueryWrapper<StockoutOrderEntity>()
                .eq(StockoutOrderEntity::getStockoutOrderNo, stockoutOrderNo));
        if (stockoutOrderEntity == null) throw new BusinessServiceException(stockoutOrderNo + "该出库单不存在");
        return getOutOrderDetailById(stockoutOrderEntity.getStockoutOrderId());
    }


    // 更新出库单状态 --不允许回退状态
    @Transactional
    public void updateStockoutOrderStatusByEntity(StockoutOrderEntity stockoutOrderEntity, String status) {
        StockoutOrderEntity byId = getById(stockoutOrderEntity.getStockoutOrderId());
        if (StockoutOrderStatusEnum.checkUpdateStatus(byId.getStatus(), status)) {
            String oriStatus = byId.getStatus();
            byId.setStatus(status);
            byId.setUpdateBy(loginInfoService.getName());
            if (StockoutOrderStatusEnum.DELIVERED.name().equals(status)) {
                stockoutOrderEntity.setDeliveryDate(new Date());
                byId.setDeliveryDate(new Date());
            }
            this.updateById(byId);
            //后续代码可能会更新该实体类，需要赋值
            stockoutOrderEntity.setStatus(status);
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            stockoutOrderEntity.setVersion(byId.getVersion());

            logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CHANGE_STATUS, StrUtil.format("出库单状态从{}变更为{}",
                    StockoutOrderStatusEnum.getCnNameByName(oriStatus), StockoutOrderStatusEnum.getCnNameByName(status)));
            if (StockoutOrderLogTypeEnum.READY_WAVE_GENERATED.name().equals(status)) {
                // 用于BI统计
                logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_WAVE_GENERATED, String.format("出库单【%s】待生成波次", stockoutOrderEntity.getStockoutOrderNo()));
            }
        } else {
            LOGGER.info("出库单:{}无法回退状态:{}到:{}", stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderEntity.getStatus(), status);
            logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CHANGE_STATUS_FAIL, StrUtil.format("出库单状态无法从{}变更为{}！",
                    StockoutOrderStatusEnum.getCnNameByName(byId.getStatus()), StockoutOrderStatusEnum.getCnNameByName(status)));
        }
    }

    // 获取erp拣货单id（合单情况）
    public Integer getErpPickId(StockoutOrderEntity stockoutOrderEntity, String orderItemId) {
        Integer result = stockoutOrderEntity.getErpPickId();
        if (StockoutOrderMergeStateEnum.PARENT.name().equals(stockoutOrderEntity.getMergeState())) {
            Integer stockoutChildId = stockoutOrderItemService.getOne(new QueryWrapper<StockoutOrderItemEntity>().lambda()
                    .eq(StockoutOrderItemEntity::getOrderItemId, orderItemId).ne(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId())
                    .last(MybatisQueryConstant.QUERY_FIRST)).getStockoutOrderId();
            result = this.getById(stockoutChildId).getErpPickId();
        }
        return result;
    }


    /**
     * 生成出库单号
     */
    public FormNoTypeEnum getFormNoTypeEnumByStockoutOrderType(StockoutOrderTypeEnum typeEnum) {
        FormNoTypeEnum formNoTypeEnum = null;
        switch (typeEnum) {
            case SALES_DELIVERY:
                formNoTypeEnum = FormNoTypeEnum.STOCKOUT_ORDER_NO_XSCK;
                break;
            case FIRST_LEG_DELIVERY:
                formNoTypeEnum = FormNoTypeEnum.STOCKOUT_ORDER_NO_TCCK;
                break;
            case OVERSEA_DELIVERY:
                formNoTypeEnum = FormNoTypeEnum.STOCKOUT_ORDER_NO_HWCK;
                break;
            case FBA_FACTORY_DELIVERY:
                formNoTypeEnum = FormNoTypeEnum.STOCKOUT_ORDER_NO_GCCK;
                break;
            case SPACE_TRANSFER_DELIVERY:
                formNoTypeEnum = FormNoTypeEnum.STOCKOUT_ORDER_NO_DBCK;
                break;
            case LIGHT_CUSTOMIZATION_DELIVERY:
                formNoTypeEnum = FormNoTypeEnum.STOCKOUT_ORDER_NO_BHCK;
                break;
            case INTERNAL_PURCHASE_DELIVERY:
                formNoTypeEnum = FormNoTypeEnum.STOCKOUT_ORDER_NO_NGCK;
                break;
            default:
                break;
        }
        return formNoTypeEnum;
    }

    public String getNotifyShipStatus(StringListRequest stockoutOrderNos) {
        List<StockoutOrderEntity> stockoutOrderNoList = this.getByStockoutOrderNoList(stockoutOrderNos.getStringList());
        if (CollectionUtils.isEmpty(stockoutOrderNoList))
            throw new BusinessServiceException("未找到出库单");
        List<StockoutOrderEntity> collect = stockoutOrderNoList.stream().filter(item -> StockoutDeliveryNoticeEnum.WAIT_NOTIC_DELIVERY.name().equals(item.getNotifyShipStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return "";
        }
        List<String> collect1 = collect.stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList());
        return String.format("出库单号%s，等通知发货", StringUtils.join(collect1, ','));
    }

    /**
     * 通过出库单状态和平台名称查找
     *
     * @param status
     * @param platformName
     * @return
     */
    public List<StockoutOrderEntity> getByStatusAndPlatformName(String status, String platformName) {
        return list(new LambdaQueryWrapper<StockoutOrderEntity>()
                .eq(StockoutOrderEntity::getStatus, status)
                .eq(StockoutOrderEntity::getPlatformName, platformName));
    }

    public List<StockoutOrderEntity> findReadyDeliveryBySpaceName(String spaceName) {
        BdSpaceEntity space = spaceService.getBySpaceName(spaceName);
        if (Objects.isNull(space))
            throw new BusinessServiceException(String.format("仓库 %s 不存在", spaceName));

        return list(new LambdaQueryWrapper<StockoutOrderEntity>()
                .eq(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.READY_DELIVERY.name())
                .eq(StockoutOrderEntity::getSpaceId, space.getSpaceId()));
    }

    public List<StockoutOrderEntity> findByErpPickIds(List<Integer> pickIdList) {
        return list(new LambdaQueryWrapper<StockoutOrderEntity>()
                .in(StockoutOrderEntity::getErpPickId, pickIdList)
                .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLED.name())
                .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLING.name()));
    }

    public List<StockoutOrderEntity> listBySpaceIdAndStatus(Integer spaceId, String status) {
        return list(new LambdaQueryWrapper<StockoutOrderEntity>()
                .eq(StockoutOrderEntity::getSpaceId, spaceId)
                .eq(StockoutOrderEntity::getStatus, status));
    }

    /**
     * 获取出库单数量
     *
     * @param stockoutOrderNoList
     * @return
     */
    public Integer getStockoutOrderQty(List<String> stockoutOrderNoList) {
        if (CollectionUtils.isEmpty(stockoutOrderNoList)) {
            return 0;
        }
        return this.getBaseMapper().getStockoutOrderQty(stockoutOrderNoList);
    }

    /**
     * 查询超过七天未发货的订单详情
     *
     * @param statusList      订单状态列表
     * @param createStartDate 开始日期
     * @param createEndDate   结束日期
     * @return 订单详情列表
     */
    public List<Map<String, Object>> findNoDeliveryOrderDetails(List<String> statusList, Date createStartDate, Date createEndDate) {
        return getBaseMapper().findNoDeliveryOrderDetails(statusList, createStartDate, createEndDate);
    }
}
