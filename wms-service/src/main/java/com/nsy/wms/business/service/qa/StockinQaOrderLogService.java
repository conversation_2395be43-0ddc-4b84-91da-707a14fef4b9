package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.enumeration.qa.QaLogTypeEnum;
import com.nsy.api.wms.request.base.LogListRequest;
import com.nsy.api.wms.response.base.LogListResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.StockinQaOrderDetailEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderLogEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderLogMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单日志业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderLogService extends ServiceImpl<StockinQaOrderLogMapper, StockinQaOrderLogEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaOrderLogService.class);

    @Resource
    LoginInfoService loginInfoService;
    @Autowired
    private StockinQaOrderDetailService qaOrderDetailService;

    @Transactional
    public void addLog(Integer stockinQaOrderId, QaLogTypeEnum qaLogTypeEnum, String content) {
        StockinQaOrderLogEntity logEntity = new StockinQaOrderLogEntity();
        logEntity.setStockinQaOrderId(stockinQaOrderId);
        logEntity.setContent(content);
        logEntity.setIpAddress(loginInfoService.getIpAddress());
        logEntity.setLocation(TenantContext.getTenant());
        logEntity.setType(qaLogTypeEnum.getValue());
        logEntity.setCreateBy(loginInfoService.getName());
        this.save(logEntity);
    }

    public PageResponse<LogListResponse> pageList(LogListRequest request) {
        if (CollectionUtils.isEmpty(request.getIdList()) && Objects.isNull(request.getId())) {
            throw new BusinessServiceException("参数错误");
        }
        PageResponse<LogListResponse> pageResponse = new PageResponse<>();
        Page<StockinQaOrderLogEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinQaOrderLogEntity> pageResult = this.getBaseMapper().pageSearchQaOrderLog(page, request);
        List<StockinQaOrderLogEntity> logEntityList = pageResult.getRecords();
        List<LogListResponse> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(logEntityList)) {
            resultList = logEntityList.stream().map(entity -> {
                LogListResponse log = new LogListResponse();
                BeanUtils.copyProperties(entity, log);
                log.setTypeStr(entity.getType());
                return log;
            }).collect(Collectors.toList());
        }
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(resultList);
        return pageResponse;
    }

    /**
     * 是否存在罚款任务
     *
     * @param stockinQaOrderId
     * @return
     */
    public boolean existPunishments(Integer stockinQaOrderId) {
        LambdaQueryWrapper<StockinQaOrderLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaOrderLogEntity::getStockinQaOrderId, stockinQaOrderId);
        queryWrapper.eq(StockinQaOrderLogEntity::getType, QaLogTypeEnum.PUNISHMENTS.getValue());
        return this.count(queryWrapper) > 0;
    }

    public void initAqlCount() {
        // 设置每次处理的数据量
        int batchSize = 200;
        LocalDate targetDate = LocalDate.of(2025, 6, 2);
        Date startDate = Date.from(targetDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        int totalProcessed = 0;
        int currentPage = 1;

        do {
            // 使用分页查询符合条件的日志记录
            Page<StockinQaOrderLogEntity> page = new Page<>(currentPage, batchSize);
            LambdaQueryWrapper<StockinQaOrderLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StockinQaOrderLogEntity::getType, QaLogTypeEnum.AUDIT.getValue());
            queryWrapper.gt(StockinQaOrderLogEntity::getCreateDate, startDate);
            queryWrapper.like(StockinQaOrderLogEntity::getContent, "轻微问题数量");
            queryWrapper.orderByAsc(StockinQaOrderLogEntity::getId);

            IPage<StockinQaOrderLogEntity> pageResult = this.page(page, queryWrapper);
            List<StockinQaOrderLogEntity> logList = pageResult.getRecords();
            if (logList.isEmpty()) {
                LOGGER.info("没有找到符合条件的日志记录，处理完成");
                break;
            }
            List<Integer> stockinQaOrderIdList = logList.stream().map(StockinQaOrderLogEntity::getStockinQaOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(stockinQaOrderIdList)) {
                break;
            }
            LambdaQueryWrapper<StockinQaOrderDetailEntity> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper.in(StockinQaOrderDetailEntity::getStockinQaOrderId, stockinQaOrderIdList);
            List<StockinQaOrderDetailEntity> detailEntityList = qaOrderDetailService.list(detailQueryWrapper);
            List<StockinQaOrderDetailEntity> updateDetailEntityList = new ArrayList<>();
            // 逐条处理数据
            for (StockinQaOrderLogEntity logEntity : logList) {
                try {
                    // 处理单条数据
                    StockinQaOrderDetailEntity detailEntity = processLogEntity(logEntity, detailEntityList);
                    if (Objects.isNull(detailEntity)) {
                        continue;
                    }
                    updateDetailEntityList.add(detailEntity);
                } catch (Exception e) {
                    // 异常处理：记录错误但不影响下一条数据处理
                    LOGGER.error("处理日志记录失败，ID: {}, 错误信息: {}", logEntity.getId(), e.getMessage(), e);
                }
            }
            if (!CollectionUtils.isEmpty(updateDetailEntityList)) {
                qaOrderDetailService.updateBatchById(updateDetailEntityList);
            }
            // 检查是否还有更多数据
            if (pageResult.getCurrent() >= pageResult.getPages()) {
                LOGGER.info("已处理完所有页数据，总共 {} 页", pageResult.getPages());
                break;
            }
            currentPage++;
        } while (true);

        LOGGER.info("初始化更新功能执行完成，总共处理了 {} 条记录", totalProcessed);
    }

    /**
     * 处理单条日志记录
     *
     * @param logEntity 日志实体
     */
    private StockinQaOrderDetailEntity processLogEntity(StockinQaOrderLogEntity logEntity, List<StockinQaOrderDetailEntity> qaOrderDetailEntityList) {
        try {
            if (CollectionUtils.isEmpty(qaOrderDetailEntityList)) {
                return null;
            }
            String originalContent = logEntity.getContent();
            if (originalContent != null && originalContent.contains("轻微问题数量")
                    && originalContent.contains("严重问题数量") && originalContent.contains("致命问题数量")) {
                // 解析问题数量
                int minorCount = extractProblemCount(originalContent, "轻微问题数量");
                int majorCount = extractProblemCount(originalContent, "严重问题数量");
                int criticalCount = extractProblemCount(originalContent, "致命问题数量");
                StockinQaOrderDetailEntity detailEntity = qaOrderDetailEntityList.stream().filter(item -> item.getStockinQaOrderId().equals(logEntity.getStockinQaOrderId())).findFirst().orElse(null);
                if (Objects.isNull(detailEntity)) {
                    return null;
                }
                detailEntity.setMinorDefectCount(minorCount);
                detailEntity.setMajorDefectCount(majorCount);
                detailEntity.setCriticalDefectCount(criticalCount);
                return detailEntity;
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("处理日志记录时发生异常，ID: {}", logEntity.getId(), e);
            return null;
        }
    }

    /**
     * 从日志内容中提取问题数量
     *
     * @param content     日志内容
     * @param problemType 问题类型（轻微问题数量、严重问题数量、致命问题数量）
     * @return 问题数量
     */
    private int extractProblemCount(String content, String problemType) {
        try {
            // 查找问题类型在内容中的位置
            int startIndex = content.indexOf(problemType);
            if (startIndex == -1) {
                return 0;
            }

            // 找到冒号的位置
            int colonIndex = content.indexOf('：', startIndex);
            if (colonIndex == -1) {
                return 0;
            }

            // 找到逗号的位置（下一个分隔符）
            int commaIndex = content.indexOf(',', colonIndex);
            if (commaIndex == -1) {
                // 如果没有逗号，可能是最后一个字段，查找句号
                commaIndex = content.indexOf('。', colonIndex);
                if (commaIndex == -1) {
                    // 如果都没有，取到字符串末尾
                    commaIndex = content.length();
                }
            }

            // 提取数字部分
            String numberStr = content.substring(colonIndex + 1, commaIndex).trim();
            return Integer.parseInt(numberStr);

        } catch (Exception e) {
            LOGGER.error("解析问题数量失败，内容: {}, 问题类型: {}, 错误: {}", content, problemType, e.getMessage());
            return 0;
        }
    }
}
