package com.nsy.wms.business.domain.bo.stock;

public class StockInternalBoxItemSourceAreaBo {


    /**
     * 来源区域ID
     */
    private Integer sourceAreaId;

    private Integer isCross;

    private Integer qty;

    /**
     * 来源库位
     */
    private String sourcePositionCode;

    public StockInternalBoxItemSourceAreaBo() {
    }

    public StockInternalBoxItemSourceAreaBo(Integer sourceAreaId, String sourcePositionCode, Integer isCross, Integer qty) {
        this.sourceAreaId = sourceAreaId;
        this.sourcePositionCode = sourcePositionCode;
        this.isCross = isCross;
        this.qty = qty;
    }

    public String getSourcePositionCode() {
        return sourcePositionCode;
    }

    public void setSourcePositionCode(String sourcePositionCode) {
        this.sourcePositionCode = sourcePositionCode;
    }

    public Integer getSourceAreaId() {
        return sourceAreaId;
    }

    public void setSourceAreaId(Integer sourceAreaId) {
        this.sourceAreaId = sourceAreaId;
    }

    public Integer getIsCross() {
        return isCross;
    }

    public void setIsCross(Integer isCross) {
        this.isCross = isCross;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }
}
