package com.nsy.wms.business.manage.user.upload;

import java.math.BigDecimal;

/**
 * 亚马逊装箱清单物流单号导入
 *
 * <AUTHOR>
 *         2024-03-21
 */
public class AmazonShipmentLogisticsNoImport {
    // 订单号（必填）
    private String orderNo;

    // 物流公司（必填）
    private String logisticsCompany;

    // 货代渠道（必填）
    private String forwarderChannel;

    // 物流单号（必填）
    private String logisticsNo;

    private BigDecimal unitPrice;

    // 错误信息
    private String errorMsg;

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getForwarderChannel() {
        return forwarderChannel;
    }

    public void setForwarderChannel(String forwarderChannel) {
        this.forwarderChannel = forwarderChannel;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
