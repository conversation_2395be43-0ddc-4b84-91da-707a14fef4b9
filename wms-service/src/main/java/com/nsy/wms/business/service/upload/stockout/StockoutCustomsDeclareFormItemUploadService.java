package com.nsy.wms.business.service.upload.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormSystemMarkEnum;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierInvoiceCompanyDropDownRequest;
import com.nsy.wms.business.manage.user.upload.StockoutCustomsDeclareFormItemImport;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormItemService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormLogService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormService;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareDocumentMapper;
import com.nsy.wms.utils.JsonMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockoutCustomsDeclareFormItemUploadService extends ServiceImpl<StockoutCustomsDeclareDocumentMapper, StockoutCustomsDeclareDocumentEntity> implements IProcessUploadDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareFormItemUploadService.class);

    @Resource
    StockoutCustomsDeclareFormService stockoutCustomsDeclareFormService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockoutCustomsDeclareFormItemService declareFormItemService;
    @Resource
    StockoutCustomsDeclareFormLogService logService;
    @Resource
    ScmApiService scmApiService;

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_CUSTOMS_DECLARE_FORM_ITEM;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (StringUtils.isBlank(request.getDataJsonStr())) {
            throw new BusinessServiceException("上传数据为空");
        }
        List<StockoutCustomsDeclareFormItemImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), StockoutCustomsDeclareFormItemImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            throw new BusinessServiceException("上传列表为空");
        }

        CollectionUtil.split(importList, 200).forEach(splitList -> {
            List<String> supplierCodeList = splitList.stream().map(StockoutCustomsDeclareFormItemImport::getSupplierCode)
                    .distinct().collect(Collectors.toList());
            Map<String, SelectModel> invoiceCompanyMap = scmApiService.getInvoiceCompanyDropDown(new SupplierInvoiceCompanyDropDownRequest("ALL", supplierCodeList))
                    .stream().collect(Collectors.toMap(SelectModel::getCode, Function.identity(), (v1, v2) -> v1));

            splitList.forEach(importRow -> {
                try {
                    SpringUtil.getBean(StockoutCustomsDeclareFormItemUploadService.class).importEachData(importRow, invoiceCompanyMap);
                } catch (Exception e) {
                    LOGGER.error(e.getMessage(), e);
                    importRow.setErrorMsg(e.getMessage());
                }
            });
        });


        response.setDataJsonStr(JsonMapper.toJson(importList.stream().filter(temp -> StrUtil.isNotEmpty(temp.getErrorMsg())).collect(Collectors.toList())));
        return response;
    }

    @Transactional
    public void importEachData(StockoutCustomsDeclareFormItemImport importRow, Map<String, SelectModel> invoiceCompanyMap) {
        LOGGER.info("关单冲库存 {} ", JSONUtil.toJsonStr(importRow));
        StockoutCustomsDeclareFormEntity formEntity = stockoutCustomsDeclareFormService.getByDeclareDocumentNoAndGNo(importRow.getDeclareDocumentNo(), importRow.getgNo());

        if (!Objects.isNull(formEntity.getSupplierId()))
            throw new BusinessServiceException(String.format("关单已匹配供应商 %s", formEntity.getDeclareFormId()));


        StockoutCustomsDeclareFormItemEntity itemEntity = new StockoutCustomsDeclareFormItemEntity();
        itemEntity.setDeclareFormId(formEntity.getDeclareFormId());
        itemEntity.setgNo(formEntity.getgNo());
        itemEntity.setInputQty(importRow.getInputQty());
        itemEntity.setTaxInclusiveUnitPrice(formEntity.getTaxInclusiveUnitPrice());
        itemEntity.setInputPrice(importRow.getInputPrice());
        itemEntity.setTaxInclusivePrice(itemEntity.getTaxInclusiveUnitPrice().multiply(BigDecimal.valueOf(itemEntity.getInputQty())));
        itemEntity.setTaxPrice(importRow.getTaxPrice());
        itemEntity.setInputInvoiceNo(importRow.getInputInvoiceNo());
        itemEntity.setInputInvoiceCode(importRow.getInputInvoiceCode());
        itemEntity.setInvoiceDate(DateUtil.parse(importRow.getInvoiceDate(), "yyyy-M-dd"));
        itemEntity.setDeclareContractNo(importRow.getDeclareContractNo());
        itemEntity.setIsManual(Boolean.TRUE);
        itemEntity.setCreateBy(loginInfoService.getName());
        itemEntity.setCreateDate(new Date());
        declareFormItemService.save(itemEntity);

        logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.INCREASE_ITEM, String.format("导入冲库存，进项数量%s 不含税单价%s 税额%s", itemEntity.getInputQty(), itemEntity.getInputPrice(), itemEntity.getTaxPrice()));

        if (importRow.getIsFinished() == 1) {
            SelectModel selectModel = invoiceCompanyMap.get(importRow.getSupplierCode());
            if (Objects.isNull(selectModel)) {
                throw new BusinessServiceException(String.format("查找不到供应商 %s", importRow.getSupplierName()));
            }
            formEntity.setDeclareContractNo(importRow.getDeclareContractNo());
            formEntity.setStockinDate(DateUtil.parse(importRow.getStockinDate(), "yyyy-M-dd"));
            formEntity.setContractSignedDate(DateUtil.parse(importRow.getContractSignedDate(), "yyyy-M-dd"));
            formEntity.setDeliveryDate(DateUtil.parse(importRow.getDeliveryDate(), "yyyy-M-dd"));
            formEntity.setStatus(StockoutCustomsDeclareFormStatusEnum.DEALT.name());
            formEntity.setSystemMark(StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name());
            formEntity.setMatchDate(new Date());
            formEntity.setSupplierId(Integer.valueOf(selectModel.getValue()));
            formEntity.setSupplierName(selectModel.getLabel());
            //出口明细 按照 进项明细 累加
            List<StockoutCustomsDeclareFormItemEntity> formItemList = declareFormItemService.itemList(formEntity.getDeclareFormId());
            formEntity.setTaxInclusivePrice(formItemList.stream().map(StockoutCustomsDeclareFormItemEntity::getTaxInclusivePrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            formEntity.setTaxInclusiveUnitPrice(itemEntity.getTaxInclusiveUnitPrice());
            stockoutCustomsDeclareFormService.updateById(formEntity);

            logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.MATCH_SUPPLIER, "导入冲库存，状态变更【已处理】");
        }
    }
}
