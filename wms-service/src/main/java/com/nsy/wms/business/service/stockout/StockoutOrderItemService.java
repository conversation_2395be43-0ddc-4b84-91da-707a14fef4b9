package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.domain.stockout.PrintSkuDTO;
import com.nsy.api.wms.domain.stockout.StockoutBatchScanTypeInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderItemList;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskSkuData;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSewTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskCreateEnum;
import com.nsy.api.wms.request.stockin.PrintItemSkuRequest;
import com.nsy.api.wms.request.stockout.ScanPrintRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemAddRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemListRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemQueryRequest;
import com.nsy.api.wms.request.stockout.SyncCustomerBarcodeRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.AreaResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderItemListResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.dto.stockout.StockoutVasTaskCreateDTO;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.bd.query.BdPositionQueryWrapper;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.product.ProductStoreSkuMappingEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemExtendEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.repository.jpa.mapper.product.ProductInfoMapper;
import com.nsy.wms.repository.jpa.mapper.product.ProductWmsCategorySpaceMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.mp.TenantContext;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockoutOrderItemService extends ServiceImpl<StockoutOrderItemMapper, StockoutOrderItemEntity> {

    @Autowired
    BdPositionMapper positionMapper;
    @Autowired
    StockoutOrderMapper stockoutOrderMapper;
    @Autowired
    StockoutOrderItemMapper stockoutOrderItemMapper;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    ProductStoreSkuMappingService storeSkuMappingService;
    @Autowired
    ProductWmsCategorySpaceMapper categorySpaceMapper;
    @Autowired
    ProductInfoMapper productInfoMapper;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    ExternalApiLogService externalApiLogService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutBatchScanTypeService scanTypeService;
    @Autowired
    BdSystemParameterService parameterService;
    @Autowired
    StockoutShipmentPrintService shipmentPrintService;
    @Autowired
    StockoutOrderItemExtendService stockoutOrderItemExtendService;
    @Autowired
    StockoutOrderLogService logService;
    @Autowired
    BdTagMappingService tagMappingService;
    @Autowired
    MessageProducer producer;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    BdAreaService bdAreaService;


    /**
     * 新增出库单明细
     */
    public StockoutOrderItemEntity addOrderItem(StockoutOrderEntity stockoutOrderEntity, ProductSpecInfoEntity specInfoEntity, StockoutOrderItemAddRequest itemAddRequest, StockoutOrderInfo orderInfo, Map<String, String> storeMap) {
        StockoutOrderItemQueryRequest queryRequest = new StockoutOrderItemQueryRequest();
        queryRequest.setOrderItemId(itemAddRequest.getOrderItemId());
        queryRequest.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        queryRequest.setSku(itemAddRequest.getSku());
        queryRequest.setIsNeedProcess(itemAddRequest.getIsProcess());
        queryRequest.setPositionCode(itemAddRequest.getPositionCode());
        queryRequest.setErpPickItemId(itemAddRequest.getErpPickItemId());
        List<StockoutOrderItemEntity> itemList = list(buildItemQuery(queryRequest));
        if (!itemList.isEmpty()) {
            StockoutOrderItemEntity existItem = itemList.get(0);
            existItem.setQty(existItem.getQty() + itemAddRequest.getQty());
            existItem.setUpdateBy(itemAddRequest.getCreateBy());
            updateById(existItem);
            return existItem;
        }
        StockoutOrderItemEntity itemEntity = buildBaseItemEntity(specInfoEntity, itemAddRequest, stockoutOrderEntity.getWorkspace());
        itemEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (StringUtils.hasText(orderInfo.getIossNum()))
            itemEntity.setIossNumber(orderInfo.getIossNum());
        if (StringUtils.hasText(itemEntity.getSellerMemo())) {
            itemEntity.setSellerMemo(processErpMemo(itemEntity.getSellerMemo(), 200));
        }
        if (StringUtils.hasText(itemEntity.getBuyerMemo())) {
            itemEntity.setBuyerMemo(processErpMemo(itemEntity.getBuyerMemo(), 200));
        }
        if (StringUtils.hasText(itemEntity.getSpaceMemo()) || StringUtils.hasText(itemEntity.getReplaceSku()) || StringUtils.hasText(itemEntity.getChangeType())) {
            itemEntity.setSpaceMemo(StockoutBuilding.setSpaceMemo(itemEntity.getSpaceMemo(), itemEntity.getSku(), itemEntity.getReplaceSku(), itemEntity.getChangeType()));
        }
        if (itemAddRequest.getPositionCode() != null && !itemAddRequest.getPositionCode().isEmpty()) {
            BdPositionEntity positionEntity = positionMapper.selectOne(BdPositionQueryWrapper.buildBdPositionByPositionCode(itemAddRequest.getPositionCode()));
            if (positionEntity != null) {
                itemEntity.setSpaceAreaName(positionEntity.getSpaceAreaName());
                itemEntity.setPositionId(positionEntity.getPositionId());
                itemEntity.setPositionCode(positionEntity.getPositionCode());
            }
        }
        // 增值服务类型
        if (StringUtils.hasText(orderInfo.getVas()) && !SpaceAreaMapConstant.WmsSpace.ZUOHAI_SPACE.equals(orderInfo.getSpaceName())) {
            itemEntity.setVasType(orderInfo.getVas());
        }
        // 特殊店铺配置 备注
        if (storeMap.containsKey(String.valueOf(stockoutOrderEntity.getStoreId()))) {
            String spaceMemo = itemEntity.getSpaceMemo() == null ? "" : itemEntity.getSpaceMemo();
            itemEntity.setSpaceMemo(storeMap.getOrDefault(String.valueOf(stockoutOrderEntity.getStoreId()), "") + spaceMemo);
        }
        save(itemEntity);
        this.saveExtendsInfo(itemAddRequest.getAsin(), itemEntity);
        return itemEntity;
    }

    public String processErpMemo(String memo, int length) {
        String result = memo;
        result = result.replaceAll("<br>", "");
        if (result.length() > length) {
            result = result.substring(0, length);
        }
        return result;
    }

    private StockoutOrderItemEntity buildBaseItemEntity(ProductSpecInfoEntity specInfoEntity, StockoutOrderItemAddRequest itemAddRequest, String workspace) {
        StockoutOrderItemEntity itemEntity = new StockoutOrderItemEntity();
        BeanUtils.copyProperties(itemAddRequest, itemEntity);
        StockoutBuilding.setBaseItem(specInfoEntity, itemAddRequest, itemEntity);
        // (location='QUANZHOU'&小包|FBA） 或 出库单仓库=厦门公司泉州仓
        if (StrUtil.equalsAnyIgnoreCase(TenantContext.getTenant(), LocationEnum.QUANZHOU.name(), LocationEnum.XIAMEN.name())
                && (StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name().equals(workspace) || StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(workspace))) {
            itemEntity.setChangeType(StockoutSewTaskTypeEnum.getNameByCode(itemAddRequest.getChangeType()));
        } else {
            itemEntity.setChangeType("");
            itemEntity.setChangeStoreName("");
        }
        return itemEntity;
    }

    private QueryWrapper<StockoutOrderItemEntity> buildItemQuery(StockoutOrderItemQueryRequest request) {
        QueryWrapper<StockoutOrderItemEntity> queryWrapper = new QueryWrapper<>();
        if (request.getStockoutOrderId() != null && request.getStockoutOrderId() > 0)
            queryWrapper.lambda().eq(StockoutOrderItemEntity::getStockoutOrderId, request.getStockoutOrderId());
        if (StringUtils.hasText(request.getOrderNo()))
            queryWrapper.lambda().eq(StockoutOrderItemEntity::getOrderNo, request.getOrderNo());
        if (StringUtils.hasText(request.getOrderItemId()))
            queryWrapper.lambda().eq(StockoutOrderItemEntity::getOrderItemId, request.getOrderItemId());
        if (StringUtils.hasText(request.getSku()))
            queryWrapper.lambda().eq(StockoutOrderItemEntity::getSku, request.getSku());
        if (StringUtils.hasText(request.getPositionCode()))
            queryWrapper.lambda().eq(StockoutOrderItemEntity::getPositionCode, request.getPositionCode());
        if (request.getIsNeedProcess() != null)
            queryWrapper.lambda().eq(StockoutOrderItemEntity::getIsNeedProcess, request.getIsNeedProcess());
        if (request.getErpPickItemId() != null)
            queryWrapper.lambda().eq(StockoutOrderItemEntity::getErpPickItemId, request.getErpPickItemId());
        return queryWrapper;
    }

    public List<StockoutOrderItemEntity> findByStockoutOrderEntityList(List<StockoutOrderEntity> orderEntityList) {
        return this.list(new QueryWrapper<StockoutOrderItemEntity>().lambda().in(StockoutOrderItemEntity::getStockoutOrderId,
                orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList())));
    }

    public PageResponse<StockoutOrderItemListResponse> getOrderItemListByRequest(StockoutOrderItemListRequest request) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(request.getStockoutOrderId());
        if (Objects.isNull(stockoutOrderEntity)) {
            throw new BusinessServiceException("出库单不存在");
        }
        PageResponse<StockoutOrderItemListResponse> pageResponse = new PageResponse<>();
        Page<StockoutOrderItemList> page = new Page<>(request.getPageIndex(), request.getPageSize());
        if (StringUtils.hasText(request.getBarcode())) {
            ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcode(request.getBarcode());
            if (productSpecInfoEntity == null)
                throw new BusinessServiceException("不存在该条码的商品信息");
            request.setBarcode(productSpecInfoEntity.getBarcode());
        }
        IPage<StockoutOrderItemList> pageResult = stockoutOrderItemMapper.pageSearchOutorderItem(page, request);
        pageResponse.setTotalCount(page.getTotal());
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return pageResponse;
        }
        List<String> skuList = pageResult.getRecords().stream().map(StockoutOrderItemList::getSku).collect(Collectors.toList());
        Map<String, ProductStoreSkuMappingEntity> productStoreSkuMappingMap = Collections.isEmpty(skuList) ? new HashMap<>() : storeSkuMappingService.list(new QueryWrapper<ProductStoreSkuMappingEntity>().lambda()
                        .in(ProductStoreSkuMappingEntity::getSku, skuList)
                        .eq(ProductStoreSkuMappingEntity::getStoreId, stockoutOrderEntity.getStoreId()))
                .stream().collect(Collectors.toMap(ProductStoreSkuMappingEntity::getSku, Function.identity(), (v1, v2) -> v1));
        List<Integer> productIdList = pageResult.getRecords().stream().map(StockoutOrderItemList::getProductId).collect(Collectors.toList());
        Map<Integer, ProductInfoEntity> productInfoMap = Collections.isEmpty(productIdList) ? new HashMap<>() : productInfoMapper.selectList(new LambdaQueryWrapper<ProductInfoEntity>()
                        .in(ProductInfoEntity::getProductId, productIdList))
                .stream().collect(Collectors.toMap(ProductInfoEntity::getProductId, Function.identity(), (v1, v2) -> v1));


        List<StockoutOrderItemListResponse> orderItemList = pageResult.getRecords().stream().map(projection -> {
            StockoutOrderItemListResponse response = new StockoutOrderItemListResponse();
            BeanUtils.copyProperties(projection, response);
            ProductStoreSkuMappingEntity storeSkuMappingEntity = productStoreSkuMappingMap.get(projection.getSku());
            if (!Objects.isNull(storeSkuMappingEntity))
                response.setStoreBarcode(storeSkuMappingEntity.getStoreBarcode());
            ProductInfoEntity productInfoEntity = productInfoMap.get(projection.getProductId());
            if (!Objects.isNull(productInfoEntity))
                response.setSpu(productInfoEntity.getSpu());
            return response;
        }).collect(Collectors.toList());
        pageResponse.setContent(orderItemList);
        return pageResponse;
    }

    /**
     * 根据出库单id查找出库单明细
     *
     * @param stockoutOrderId
     * @return
     */
    public List<StockoutOrderItemEntity> listByStockoutOrderId(Integer stockoutOrderId) {
        QueryWrapper<StockoutOrderItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderId);
        return list(queryWrapper);
    }

    public List<StockoutOrderItemEntity> listByStockoutOrderIds(List<Integer> stockoutOrderIds) {
        QueryWrapper<StockoutOrderItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderIds);
        return list(queryWrapper);
    }

    public StockoutOrderItemEntity listByStockoutOrderIdAndSkuAndOrderItemId(Integer stockoutOrderId, String sku, String orderItemNo) {
        LambdaQueryWrapper<StockoutOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderId);
        queryWrapper.eq(StockoutOrderItemEntity::getSku, sku);
        queryWrapper.eq(StockoutOrderItemEntity::getOrderItemId, orderItemNo).last("limit 1");
        return getOne(queryWrapper);
    }

    public StockoutOrderItemEntity getByStockoutOrderIdAndOrderItemId(Integer stockoutOrderId, String orderItemId) {
        LambdaQueryWrapper<StockoutOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderId);
        queryWrapper.eq(StockoutOrderItemEntity::getOrderItemId, orderItemId).last("limit 1");
        return getOne(queryWrapper);
    }

    public StockoutOrderItemEntity getByStockoutOrderItemId(Integer stockoutOrderItemId) {
        LambdaQueryWrapper<StockoutOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutOrderItemEntity::getStockoutOrderItemId, stockoutOrderItemId).last("limit 1");
        StockoutOrderItemEntity stockoutOrderItem = getOne(queryWrapper);
        if (Objects.isNull(stockoutOrderItem))
            throw new BusinessServiceException(String.format("出库单明细 %s 不存在", stockoutOrderItemId));
        return stockoutOrderItem;
    }

    public void pickingComplete(List<StockoutOrderEntity> orderEntityList, List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList) {

        //更新出库单
        List<StockoutOrderEntity> stockoutOrderEntityList = new ArrayList<>(8);
        orderEntityList.forEach(orderEntity -> {
            //出库单状态不可回退
            if (StockoutOrderStatusEnum.READY_OUTBOUND.getIndex() <= StockoutOrderStatusEnum.valueOf(orderEntity.getStatus()).getIndex()) {
                return;
            }
            StockoutOrderEntity stockoutOrderEntity = new StockoutOrderEntity();
            stockoutOrderEntity.setStockoutOrderId(orderEntity.getStockoutOrderId());
            stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.READY_OUTBOUND.name());
            stockoutOrderEntityList.add(stockoutOrderEntity);
        });
        if (!CollectionUtils.isEmpty(stockoutOrderEntityList)) {
            stockoutOrderService.updateBatchById(stockoutOrderEntityList);
            List<Integer> orderEntityIds = orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList());
            List<StockoutOrderItemEntity> list = this.list(new LambdaQueryWrapper<StockoutOrderItemEntity>().in(StockoutOrderItemEntity::getStockoutOrderId, orderEntityIds));
            pickingTaskItemEntityList.forEach(o -> {
                if (!StringUtils.hasText(o.getCustomerSku())) {
                    o.setCustomerSku(o.getSku());
                }
            });
            //增加出库单日志
            Map<String, StockoutPickingTaskSkuData> collect = pickingTaskItemEntityList.stream().collect(Collectors.groupingBy(StockoutPickingTaskItemEntity::getCustomerSku)).entrySet().stream()
                    .map(entry -> StockoutPickingTaskSkuDataService.stockoutPickingTaskSkuData(entry.getValue())).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(StockoutPickingTaskSkuData::getSku, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            List<StockoutOrderItemEntity> stockoutOrderItemEntityList = list.stream().map(entity -> {
                Integer qty = entity.getQty();
                StockoutOrderItemEntity stockoutOrderItemEntity = new StockoutOrderItemEntity();
                StockoutPickingTaskSkuData stockoutPickingTaskSkuData = collect.get(entity.getSku());
                stockoutOrderItemEntity.setQty(qty);
                stockoutOrderItemEntity.setScanQty(Objects.nonNull(stockoutPickingTaskSkuData) ? stockoutPickingTaskSkuData.takeOutQty(qty) : 0);
                stockoutOrderItemEntity.setLackQty(qty - stockoutOrderItemEntity.getScanQty());
                stockoutOrderItemEntity.setStockoutOrderItemId(entity.getStockoutOrderItemId());
                stockoutOrderItemEntity.setStockoutOrderId(entity.getStockoutOrderId());
                return stockoutOrderItemEntity;
            }).collect(Collectors.toList());
            orderEntityList.forEach(orderEntity -> {
                List<StockoutOrderItemEntity> itemEntityList = stockoutOrderItemEntityList.stream().filter(entity -> entity.getStockoutOrderId().equals(orderEntity.getStockoutOrderId())).collect(Collectors.toList());
                this.sendAddVasTaskInfo(itemEntityList, orderEntity);
                stockoutOrderLogService.addLog(orderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_OUTBOUND,
                        String.format("拣货完成，需拣%s件，共拣%s件，缺货%s件",
                                itemEntityList.stream().mapToInt(StockoutOrderItemEntity::getQty).sum(),
                                itemEntityList.stream().mapToInt(StockoutOrderItemEntity::getScanQty).sum(),
                                itemEntityList.stream().mapToInt(StockoutOrderItemEntity::getLackQty).sum()));
            });
        }

    }

    /**
     * 根据出库单明细id,扫描数，更新出库单明细
     */
    @Transactional
    public void updateStockoutItemScanQty(StockoutBatchEntity batchEntity, Integer stockoutOrderItemId, Integer scanQty) {
        StockoutOrderItemEntity itemEntity = this.getById(stockoutOrderItemId);
        if (Objects.isNull(itemEntity)) {
            throw new BusinessServiceException("找不到出库单明细");
        }
        Integer shipmentQty;
        if (scanQty >= itemEntity.getQty()) {
            itemEntity.setScanQty(itemEntity.getQty());
            shipmentQty = itemEntity.getQty();
            itemEntity.setLackQty(0);
            itemEntity.setLack(Boolean.FALSE);
        } else {
            itemEntity.setScanQty(scanQty);
            shipmentQty = scanQty;
            itemEntity.setLackQty(itemEntity.getQty() - scanQty);
        }
        StockoutBatchScanTypeInfo scanTypeInfo = scanTypeService.getScanTypeInfo(batchEntity);
        boolean hasScanTask = Objects.nonNull(scanTypeInfo) && scanTypeInfo.getCreateCheckTask() != null && scanTypeInfo.getCreateCheckTask();
        if (!hasScanTask) {
            itemEntity.setShipmentQty(shipmentQty);
        }
        itemEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(itemEntity);
    }

    /**
     * 根据出库单号，sku,扫描数，更新出库单明细
     */
    @Transactional
    public void updateStockoutItemSkuLackQty(Integer stockoutOrderItemId, Integer scanQty, Integer lackQty) {
        StockoutOrderItemEntity itemEntity = this.getById(stockoutOrderItemId);
        if (Objects.isNull(itemEntity))
            return;
        itemEntity.setLackQty(lackQty);
        itemEntity.setScanQty(scanQty);
        itemEntity.setUpdateBy(loginInfoService.getName());
        if (lackQty > 0) {
            itemEntity.setLack(Boolean.TRUE);
        } else {
            itemEntity.setLack(Boolean.FALSE);
        }
        this.updateById(itemEntity);
    }

    public List<StockoutOrderItemEntity> getByStockoutOrderIdAndSku(Integer stockoutOrderId, String sku, Integer stockoutOrderItemId) {
        LambdaQueryWrapper<StockoutOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(stockoutOrderId)) {
            queryWrapper.eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderId);
        }
        if (Objects.nonNull(stockoutOrderItemId)) {
            queryWrapper.eq(StockoutOrderItemEntity::getStockoutOrderItemId, stockoutOrderItemId);
        }
        if (StringUtils.hasText(sku)) {
            queryWrapper.eq(StockoutOrderItemEntity::getSku, sku);
        }
        return this.list(queryWrapper);
    }

    public List<StockoutOrderItemEntity> getByStockoutOrderIdAndSku(Integer stockoutOrderId, String sku) {
        LambdaQueryWrapper<StockoutOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderId);
        queryWrapper.eq(StockoutOrderItemEntity::getSku, sku);
        return this.list(queryWrapper);
    }

    public PrintListResponse itemSkuPrint(PrintItemSkuRequest request) {
        if (!StringUtils.hasText(request.getStockoutOrderNo())) {
            throw new BusinessServiceException("出库单号不能为空，请填写");
        }
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(request.getStockoutOrderNo());
        request.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        List<PrintSkuDTO> dtoList;
        if (CollectionUtils.isEmpty(request.getSkuList())) {
            // 如果是空，打印全部，先默认1000张
            request.setPageSize(1000);
            request.setPageIndex(1);
            StockoutOrderItemListRequest itemListRequest = new PrintItemSkuRequest();
            BeanUtils.copyProperties(request, itemListRequest);
            PageResponse<StockoutOrderItemListResponse> pageResponse = this.getOrderItemListByRequest(itemListRequest);
            if (CollectionUtils.isEmpty(pageResponse.getContent())) {
                throw new BusinessServiceException("该出库单暂无sku");
            }
            dtoList = pageResponse.getContent().stream().map(item -> {
                PrintSkuDTO dto = new PrintSkuDTO();
                dto.setSku(item.getSku());
                dto.setQty(1);
                return dto;
            }).collect(Collectors.toList());
        } else {
            dtoList = request.getSkuList().stream().map(item -> {
                PrintSkuDTO dto = new PrintSkuDTO();
                dto.setSku(item);
                dto.setQty(request.getQty());
                return dto;
            }).collect(Collectors.toList());
        }
        ScanPrintRequest scanPrintRequest = new ScanPrintRequest();
        scanPrintRequest.setStockoutOrderNo(request.getStockoutOrderNo());
        scanPrintRequest.setPrintSkuDTO(dtoList);
        scanPrintRequest.setTemplateName(PrintTemplateNameEnum.PRODUCT_CUSTOMER_SPEC_BARCODE.getTemplateName());
        PrintListResponse response = shipmentPrintService.printCustomer(scanPrintRequest);
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.PRODUCT_CUSTOMER_SPEC_BARCODE.getTemplateName());
        response.setHtmlList(PrintTransferUtils.doubleTransfer(request.getSinglePrint(), response.getHtmlList(), templateEntity));
        response.setSpec(templateEntity.getSpec());
        return response;
    }


    /**
     * 商通导入客户商品同步
     */
    @Transactional
    public void syncCustomerBarcode(SyncCustomerBarcodeRequest request) {
        if (CollectionUtils.isEmpty(request.getItemList())) throw new BusinessServiceException("同步集合不能为空");
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_RECEIVER_INFO, "/stockout-order/sync-customer-barcode",
                JsonMapper.toJson(request), request.getOrderNo(), "客户条码修改同步");
        apiLogEntity.setCreateBy(request.getOperator());
        try {
            List<StockoutOrderItemEntity> list = this.list(new LambdaQueryWrapper<StockoutOrderItemEntity>().eq(StockoutOrderItemEntity::getOrderNo, request.getOrderNo()));
            if (CollectionUtils.isEmpty(list))
                throw new BusinessServiceException(String.format("找不到订单【%s】的出库单", request.getOrderNo()));
            request.getItemList().forEach(item -> {
                this.updateBatchById(list.stream().filter(t -> t.getOrderItemId().equals(item.getOrderItemId())).peek(t -> {
                    t.setSellerBarcode(item.getSellerBarcode());
                    t.setSellerSku(item.getSellerSku());
                    t.setSellerText(item.getSellerText());
                    t.setSellerTitle(item.getSellerTitle());
                }).collect(Collectors.toList()));
            });
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (RuntimeException e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }


    public PageResponse<StockoutOrderItemListResponse> getStockoutOrderItemListByNo(StockoutOrderItemListRequest request) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(request.getStockoutOrderNo());
        request.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        return getOrderItemListByRequest(request);
    }


    public List<Integer> getStockoutOrderIdListByOrderNosAndSkuListAndCreateDate(List<String> orderNos, List<String> skuList, Date createDateBegin, Date createDateEnd) {
        LambdaQueryWrapper<StockoutOrderItemEntity> orderItemEntityQueryWrapper = new LambdaQueryWrapper<>();
        orderItemEntityQueryWrapper.in(CollectionUtil.isNotEmpty(orderNos), StockoutOrderItemEntity::getOrderNo, orderNos);
        orderItemEntityQueryWrapper.in(CollectionUtil.isNotEmpty(skuList), StockoutOrderItemEntity::getSku, skuList);
        orderItemEntityQueryWrapper.select(StockoutOrderItemEntity::getStockoutOrderId);
        orderItemEntityQueryWrapper.ge(ObjectUtil.isNotNull(createDateBegin), StockoutOrderItemEntity::getCreateDate, createDateBegin);
        orderItemEntityQueryWrapper.le(ObjectUtil.isNotNull(createDateEnd), StockoutOrderItemEntity::getCreateDate, createDateEnd);
        return this.list(orderItemEntityQueryWrapper).stream().map(StockoutOrderItemEntity::getStockoutOrderId).collect(Collectors.toList());
    }

    public List<StockoutOrderItemEntity> getListByOrderNo(String orderNo) {
        return this.list(new LambdaQueryWrapper<StockoutOrderItemEntity>().eq(StockoutOrderItemEntity::getOrderNo, orderNo));
    }

    public List<StockoutOrderItemEntity> getListByOrderNos(List<String> orderNos) {
        return this.list(new LambdaQueryWrapper<StockoutOrderItemEntity>().in(StockoutOrderItemEntity::getOrderNo, orderNos));
    }

    public List<String> getStockoutOrderByOrderNo(String orderNo) {
        List<StockoutOrderItemEntity> stockoutOrderItemList = getListByOrderNo(orderNo);
        List<Integer> stockoutOrderIdList = stockoutOrderItemList.stream().map(StockoutOrderItemEntity::getStockoutOrderId)
                .distinct().collect(Collectors.toList());
        if (stockoutOrderIdList.isEmpty()) return new ArrayList<>();
        return stockoutOrderService.listByIds(stockoutOrderIdList).stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList());
    }

    public List<StockoutOrderEntity> getStockoutOrderEntityByOrderNo(String orderNo) {
        List<StockoutOrderItemEntity> stockoutOrderItemList = getListByOrderNo(orderNo);
        List<Integer> stockoutOrderIdList = stockoutOrderItemList.stream().map(StockoutOrderItemEntity::getStockoutOrderId)
                .distinct().collect(Collectors.toList());
        if (stockoutOrderIdList.isEmpty()) return new ArrayList<>();
        return stockoutOrderService.listByIds(stockoutOrderIdList);
    }

    public BigDecimal getItemWeight(String stockoutOrderNo) {
        return this.getBaseMapper().getItemWeight(stockoutOrderNo);
    }

    public String getStockoutOrderNoById(Integer stokcoutOrderItemId) {
        StockoutOrderItemEntity stockoutOrderItemEntity = this.getById(stokcoutOrderItemId);
        if (stockoutOrderItemEntity == null) throw new BusinessServiceException("未找到出库单明细");
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(stockoutOrderItemEntity.getStockoutOrderId());
        if (stockoutOrderEntity == null) throw new BusinessServiceException("未找到出库单");
        return stockoutOrderEntity.getStockoutOrderNo();
    }

    public List<StockoutOrderItemEntity> getListByStockoutOrderNo(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.findByStockoutOrderNo(stockoutOrderNo);
        if (Objects.isNull(stockoutOrder)) return new ArrayList<>();
        return this.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
    }

    public List<StockoutOrderItemEntity> getListByStockoutOrderNoList(List<String> stockoutOrderNoList) {
        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(stockoutOrderList)) return new ArrayList<>();
        return this.listByStockoutOrderIds(stockoutOrderList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()));
    }

    /**
     * 保存扩展参数
     */
    private void saveExtendsInfo(String asin, StockoutOrderItemEntity entity) {
        if (!StringUtils.hasText(asin))
            //扩展参数为空不保存
            return;
        StockoutOrderItemExtendEntity extendEntity = new StockoutOrderItemExtendEntity();
        extendEntity.setAsin(asin);
        extendEntity.setStockoutOrderItemId(entity.getStockoutOrderItemId());
        extendEntity.setLocation(entity.getLocation());
        stockoutOrderItemExtendService.save(extendEntity);
    }

    public List<String> getOrderNoListByStockoutOrderId(Integer stockoutOrderId) {
        return listByStockoutOrderId(stockoutOrderId).stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList());
    }

    public String getSellerBarcode(String orderNo, String sku) {
        StockoutOrderItemEntity item = getOne(new LambdaQueryWrapper<StockoutOrderItemEntity>()
                .eq(StockoutOrderItemEntity::getOrderNo, orderNo)
                .eq(StockoutOrderItemEntity::getSku, sku)
                .last("limit 1"));

        return Objects.isNull(item) ? null : item.getSellerBarcode();
    }

    /**
     * 判断是否为透明计划，是否需要
     *
     * @return
     */
    public Boolean validTransparency(String stockoutOrderNo, List<StockoutOrderItemEntity> stockoutOrderItemList) {
        if (CollectionUtils.isEmpty(stockoutOrderItemList)) {
            return Boolean.FALSE;
        }
        if (!StringUtils.hasText(stockoutOrderNo)) {
            return stockoutOrderItemList.stream().anyMatch(StockoutOrderItemEntity::getIsTransparency);
        }
        return stockoutOrderItemList.stream().anyMatch(StockoutOrderItemEntity::getIsTransparency)
                && !tagMappingService.validWithLabelOrder(stockoutOrderNo);
    }

    private void sendAddVasTaskInfo(List<StockoutOrderItemEntity> itemEntityList, StockoutOrderEntity orderEntity) {
        //不存在增值服务则返回
        if (!itemEntityList.stream().anyMatch(o -> StringUtils.hasText(o.getVasType())) || !itemEntityList.stream().anyMatch(o -> 1 == o.getIsFirstOrderByStore())) {
            return;
        }
        StockoutBatchOrderEntity stockoutBatchOrderEntity = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(orderEntity.getStockoutOrderId());
        if (Objects.isNull(stockoutBatchOrderEntity)) {
            return;
        }
        //当前子波次
        List<StockoutPickingTaskEntity> pickingTaskList = stockoutPickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .eq(StockoutPickingTaskEntity::getBatchId, stockoutBatchOrderEntity.getBatchId())
                .notIn(StockoutPickingTaskEntity::getTaskType, CollUtil.newArrayList(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name(),
                        StockoutPickingTaskTypeEnum.REPLENISHMENT_PICKING.name())));
        //存在合并波次，则大波次也一起加入判断
        StockoutBatchEntity stockoutBatchEntity = stockoutBatchService.getStockoutBatchById(stockoutBatchOrderEntity.getBatchId());
        if (!ObjectUtils.isEmpty(stockoutBatchEntity) && !ObjectUtils.isEmpty(stockoutBatchEntity.getMergeBatchId())) {
            List<StockoutPickingTaskEntity> mergePickingTaskList = stockoutPickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                    .eq(StockoutPickingTaskEntity::getBatchId, stockoutBatchEntity.getMergeBatchId())
                    .notIn(StockoutPickingTaskEntity::getTaskType, CollUtil.newArrayList(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name(),
                            StockoutPickingTaskTypeEnum.REPLENISHMENT_PICKING.name())));
            if (!CollectionUtils.isEmpty(mergePickingTaskList)) {
                pickingTaskList.addAll(mergePickingTaskList);
            }
        }
        //大波次完成，则判断大波次下的子波次是否也完成
        if (!ObjectUtils.isEmpty(stockoutBatchEntity) && stockoutBatchEntity.getIsMergeBatch() == 1) {
            List<StockoutPickingTaskEntity> subPickingTaskList = new ArrayList<>();
            List<StockoutBatchEntity> subBatchList = stockoutBatchService.getSubBatchIdByBatchId(stockoutBatchEntity.getBatchId());
            if (!CollectionUtils.isEmpty(subBatchList)) {
                subPickingTaskList = stockoutPickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                        .in(StockoutPickingTaskEntity::getBatchId, subBatchList)
                        .notIn(StockoutPickingTaskEntity::getTaskType, CollUtil.newArrayList(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name(),
                                StockoutPickingTaskTypeEnum.REPLENISHMENT_PICKING.name())));
            }
            if (!CollectionUtils.isEmpty(subPickingTaskList)) {
                pickingTaskList.addAll(subPickingTaskList);
            }
        }
        if (CollectionUtils.isEmpty(pickingTaskList) || pickingTaskList.stream().anyMatch(taskEntity -> !StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())))
            return;
        StockoutVasTaskCreateDTO stockoutVasTaskCreateDTO = new StockoutVasTaskCreateDTO();
        stockoutVasTaskCreateDTO.setStockoutOrderId(orderEntity.getStockoutOrderId());
        stockoutVasTaskCreateDTO.setVasTaskCreateEnum(StockoutVasTaskCreateEnum.PICKING_END);
        producer.sendMessage(KafkaConstant.SYNC_VAS_TASK_CREATE_TOPIC_NAME, KafkaConstant.SYNC_VAS_TASK_CREATE_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutVasTaskCreateDTO));

    }

    /**
     * 根据订单号获取出库单信息，且出库状态不为已取消
     *
     * @return
     */
    public StockoutOrderEntity getStockoutOrderByOrderNoWithNoDelete(String orderNo) {
        return this.getBaseMapper().getStockoutOrderByOrderNo(orderNo);
    }

    /**
     * 通过出库单号和sku获取areaId列表
     *
     * @param stockoutOrderNo 出库单号
     * @param sku             SKU
     * @return 区域列表
     */
    public List<AreaResponse> getAreaIdsByStockoutOrderNoAndSku(String stockoutOrderNo, String sku) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (Objects.isNull(stockoutOrderEntity)) {
            throw new BusinessServiceException("未找到出库单！");
        }

        List<StockoutOrderItemEntity> stockoutOrderItemList = this.getByStockoutOrderIdAndSku(stockoutOrderEntity.getStockoutOrderId(), sku);
        if (CollectionUtils.isEmpty(stockoutOrderItemList)) {
            return new ArrayList<>(0);
        }

        // 获取所有库位编码
        Set<String> positionCodes = stockoutOrderItemList.stream()
                .map(StockoutOrderItemEntity::getPositionCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (positionCodes.isEmpty()) {
            return new ArrayList<>(0);
        }

        // 构建区域映射，避免重复查询，预设初始容量为库位编码数量
        Map<Integer, AreaResponse> areaMap = new HashMap<>(16);

        for (String positionCode : positionCodes) {
            BdPositionEntity position = bdPositionService.getPositionByCode(positionCode);
            if (position == null) {
                continue;
            }

            Integer areaId = position.getAreaId();
            if (areaId == null || areaMap.containsKey(areaId)) {
                continue;
            }

            BdAreaEntity area = bdAreaService.getById(areaId);
            if (area == null) {
                continue;
            }

            AreaResponse response = new AreaResponse();
            response.setAreaId(area.getAreaId());
            response.setAreaName(area.getAreaName());
            areaMap.put(areaId, response);
        }

        return new ArrayList<>(areaMap.values());
    }
}
