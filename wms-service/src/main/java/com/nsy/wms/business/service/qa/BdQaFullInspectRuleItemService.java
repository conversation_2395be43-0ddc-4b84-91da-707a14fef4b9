package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.enumeration.qa.BdQaInspectRuleEnum;
import com.nsy.api.wms.request.qa.BdQaFullInspectRuleItemSaveRequest;
import com.nsy.api.wms.response.qa.BdQaFullInspectRuleItemInfoResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.BdQaFullInspectRuleItemEntity;
import com.nsy.wms.repository.jpa.mapper.qa.BdQaFullInspectRuleItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 15:36
 */
@Service
public class BdQaFullInspectRuleItemService extends ServiceImpl<BdQaFullInspectRuleItemMapper, BdQaFullInspectRuleItemEntity> {

    @Autowired
    private LoginInfoService loginInfoService;

    @Autowired
    private BdQaFullInspectRuleProductItemService qaFullInspectRuleProductItemService;

    public BdQaFullInspectRuleItemInfoResponse getItemInfo(Integer ruleId) {
        LambdaQueryWrapper<BdQaFullInspectRuleItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdQaFullInspectRuleItemEntity::getRuleId, ruleId);
        List<BdQaFullInspectRuleItemEntity> ruleItemEntityList = this.list(wrapper);
        BdQaFullInspectRuleItemInfoResponse response = new BdQaFullInspectRuleItemInfoResponse();
        //赋值spu信息
        qaFullInspectRuleProductItemService.buildProductInfo(ruleId, response);
        if (CollectionUtils.isEmpty(ruleItemEntityList)) {
            return response;
        }
        Map<String, List<BdQaFullInspectRuleItemEntity>> itemMap = ruleItemEntityList.stream().collect(Collectors.groupingBy(BdQaFullInspectRuleItemEntity::getItemName));
        for (Map.Entry<String, List<BdQaFullInspectRuleItemEntity>> entry : itemMap.entrySet()) {
            BdQaInspectRuleEnum ruleEnum = BdQaInspectRuleEnum.getValueByName(entry.getKey());
            if (ObjectUtils.isEmpty(ruleEnum)) {
                continue;
            }
            this.buildDetailInfo(ruleEnum, entry.getValue(), response);
        }
        return response;
    }

    /**
     * 赋值明细信息
     *
     * @param ruleEnum
     * @param ruleItemEntityList
     * @param response
     */
    public void buildDetailInfo(BdQaInspectRuleEnum ruleEnum, List<BdQaFullInspectRuleItemEntity> ruleItemEntityList, BdQaFullInspectRuleItemInfoResponse response) {
        if (CollectionUtils.isEmpty(ruleItemEntityList)) {
            return;
        }
        List<String> paramList = ruleItemEntityList.stream().map(BdQaFullInspectRuleItemEntity::getItemValue).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        switch (ruleEnum) {
            case RETURN_APPLY:
                response.setIsReturnApply(paramList.get(0));
                break;
            case SKU_TYPE:
                response.setSkuTypeList(paramList);
                break;
            default:
                // 处理其他枚举值的情况
                break;
        }
    }

    public void saveItemInfo(BdQaFullInspectRuleItemSaveRequest itemInfo, Integer ruleId) {
        qaFullInspectRuleProductItemService.saveProductInfo(itemInfo.getProductInfoList(), ruleId);
        List<BdQaFullInspectRuleItemEntity> ruleItemEntityList = new ArrayList<>();
        //sku标签
        this.buildItemListInfo(BdQaInspectRuleEnum.SKU_TYPE, itemInfo.getSkuTypeList(), ruleId, ruleItemEntityList);
        //是否返工退货
        this.buildItemInfo(BdQaInspectRuleEnum.RETURN_APPLY, itemInfo.getIsReturnApply(), ruleId, ruleItemEntityList);
        this.saveBatch(ruleItemEntityList);
    }

    public void updateItemInfo(BdQaFullInspectRuleItemSaveRequest itemInfo, Integer ruleId) {
        LambdaQueryWrapper<BdQaFullInspectRuleItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdQaFullInspectRuleItemEntity::getRuleId, ruleId);
        // 执行删除操作
        this.remove(wrapper);
        qaFullInspectRuleProductItemService.removeAllProductInfoByRuleId(ruleId);
        this.saveItemInfo(itemInfo, ruleId);
    }

    private void buildItemListInfo(BdQaInspectRuleEnum ruleEnum, List<String> paramList, Integer ruleId, List<BdQaFullInspectRuleItemEntity> ruleItemEntityList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        for (String param : paramList) {
            this.buildItemInfo(ruleEnum, param, ruleId, ruleItemEntityList);
        }
    }

    private void buildItemInfo(BdQaInspectRuleEnum ruleEnum, String param, Integer ruleId, List<BdQaFullInspectRuleItemEntity> ruleItemEntityList) {
        if (!StringUtils.hasText(param) || BdQaInspectRuleEnum.RETURN_APPLY != ruleEnum && "0".equalsIgnoreCase(param)) {
            return;
        }
        BdQaFullInspectRuleItemEntity itemEntity = new BdQaFullInspectRuleItemEntity();
        itemEntity.setItemName(ruleEnum.name());
        itemEntity.setItemValue(param);
        itemEntity.setRuleId(ruleId);
        itemEntity.setCreateBy(loginInfoService.getName());
        itemEntity.setUpdateBy(loginInfoService.getName());
        ruleItemEntityList.add(itemEntity);
    }
}
