package com.nsy.wms.business.service.download.stockout;

import com.alibaba.fastjson.JSONObject;
import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockout.AmazonShipmentReferenceIdExport;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.ShipmentDownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutPackageBagListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutPackageBagListResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockout.StockoutPackageBagService;
import com.nsy.wms.business.service.stockout.StockoutShipmentDownloadService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PackageSkuDownloadService implements IDownloadService {

    @Autowired
    private StockoutPackageBagService stockoutPackageBagService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_PACKAGE_SKU_INFO;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(AmazonShipmentReferenceIdExport.class));
        StockoutPackageBagListRequest pageReq = JsonMapper.fromJson(request.getRequestContent(), StockoutPackageBagListRequest.class);
        if (CollectionUtils.isEmpty(pageReq.getBagIds())) {
            pageReq.setBagIds(new ArrayList<>());
            StockoutPackageBagListRequest pageReq1 = JsonMapper.fromJson(request.getRequestContent(), StockoutPackageBagListRequest.class);
            pageReq1.setPageSize(request.getPageSize());
            pageReq1.setPageIndex(request.getPageIndex());
            PageResponse<StockoutPackageBagListResponse> stockoutPackageBagList = stockoutPackageBagService.getStockoutPackageBagList(pageReq1);
            if (stockoutPackageBagList == null || stockoutPackageBagList.getTotalCount() == 0L) {
                response.setTotalCount(0L);
                excelResponse.setData(Collections.emptyList());
                response.setDataJsonStr(JsonMapper.toJson(excelResponse));
                return response;
            }
            stockoutPackageBagList.getContent().forEach(it -> pageReq.getBagIds().add(it.getBagId()));
        }
        if (CollectionUtils.isEmpty(pageReq.getBagIds())) {
            response.setTotalCount(0L);
            excelResponse.setData(Collections.emptyList());
            response.setDataJsonStr(JsonMapper.toJson(excelResponse));
            return response;
        }
        response.setDataJsonStr(JsonMapper.toJson(collect));
        return response;
    }
}
