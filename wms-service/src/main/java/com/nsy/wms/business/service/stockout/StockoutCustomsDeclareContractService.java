package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareConstractCompanyAuditNoPassRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareConstractCompanyAuditPassRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareConstractPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareConstractPageResponse;
import com.nsy.wms.business.domain.bo.esign.ESignCheckFileStatusResponse;
import com.nsy.wms.business.domain.bo.esign.ESignGetSignedFilesResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierTaxDetailSampleRequest;
import com.nsy.wms.business.manage.scm.response.SupplierTaxGetSampleDatailDto;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractSyncDataOneRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractWmsSignRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsRejectOneDetailRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsRejectOneRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsRejectRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.esign.ESignService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareContractMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 关单合同
 */
@Service
public class StockoutCustomsDeclareContractService extends ServiceImpl<StockoutCustomsDeclareContractMapper, StockoutCustomsDeclareContractEntity> {

//    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareContractService.class);

    @Resource
    ESignService eSignService;
    @Resource
    StockoutCustomsDeclareFormService formService;
    @Resource
    StockoutCustomsDeclareContractLogService contractLogService;
    @Resource
    ScmApiService scmApiService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    SupplierApiService supplierApiService;
    @Resource
    StockoutCustomsDeclareFormItemService formItemService;
    @Resource
    StockoutCustomsDeclareContractOpService contractOpService;
    @Resource
    StockoutCustomsDeclareFormRejectService formRejectService;
    @Resource
    StockoutCustomsDeclareContractCancelService contractCancelService;

    /**
     * @param request
     * @return
     */
    public PageResponse<StockoutCustomsDeclareConstractPageResponse> pageList(StockoutCustomsDeclareConstractPageRequest request) {
        PageResponse<StockoutCustomsDeclareConstractPageResponse> pageResponse = new PageResponse<>();
        IPage page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<StockoutCustomsDeclareConstractPageResponse> pageResult = this.baseMapper.pageList(page, request);

        pageResult.getRecords().forEach(entity -> {
            entity.setStatusCn(StockoutCustomsDeclareContractStatusEnum.getByName(entity.getStatus()));
            entity.setSignUrl(entity.getCompanySignUrl());
        });
        pageResponse.setContent(pageResult.getRecords());
        pageResponse.setTotalCount(pageResult.getTotal());

        return pageResponse;
    }

    /**
     * 状态统计
     *
     * @return
     */
    public List<StatusCountResponse> tabCount() {
        Map<String, List<StatusCountResponse>> collect = this.getBaseMapper().countByStatus().stream().collect(Collectors.groupingBy(StatusCountResponse::getStatus));
        List<StatusCountResponse> list = Arrays.stream(StockoutCustomsDeclareContractStatusEnum.values()).map(statusEnum -> {
            StatusCountResponse response = new StatusCountResponse();
            List<StatusCountResponse> responses = collect.get(statusEnum.name());
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getName());
            return response;
        }).collect(Collectors.toList());
        StatusCountResponse response = new StatusCountResponse();
        response.setQty(list.stream().mapToInt(StatusCountResponse::getQty).sum());
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }


    /**
     * 合同审核
     * <p>
     * 1. 判断是否【待审核】 回填审核时间
     * 2. 合同变成【待签署】 审核时间 供应商经办人 公司经办人
     * 3. esign生成合同，回填签署链接 预览链接 回填流程id
     * 4.同步供应商系统
     *
     * @param request
     */

    public void companyAuditPass(StockoutCustomsDeclareConstractCompanyAuditPassRequest request) {
        CollectionUtil.split(request.getDeclareContractIdList(), 200).stream()
                .map(this::listByIds).flatMap(Collection::stream)
                .peek(contractEntity -> {
                    if (!StockoutCustomsDeclareContractStatusEnum.WAIT_AUDIT.name().equals(contractEntity.getStatus())) {
                        throw new BusinessServiceException(String.format("合同 %s 非【待审核】状态", contractEntity.getDeclareContractNo()));
                    }

                    ESignCheckFileStatusResponse fileStatusResponse = eSignService.checkFileStatus(contractEntity.getFileId());
                    if (2 != fileStatusResponse.getFileStatus() && 5 != fileStatusResponse.getFileStatus()) {
                        throw new BusinessServiceException(String.format("%s 的文件还在上传中，请稍等", contractEntity.getDeclareContractNo()));
                    }
                })
                .collect(Collectors.groupingBy(StockoutCustomsDeclareContractEntity::getSupplierId))
                .values().forEach(groupContractList -> SpringUtil.getBean(StockoutCustomsDeclareContractService.class).companyAuditPass(groupContractList));
    }

    @Transactional
    @JLock(keyConstant = "companyAuditPass", lockKey = "#declareContractList[0].declareContractId")
    public void companyAuditPass(List<StockoutCustomsDeclareContractEntity> declareContractList) {
        if (CollectionUtil.isEmpty(declareContractList)) return;
        AtomicReference<Integer> supplierId = new AtomicReference<>(0);
        declareContractList.forEach(contractEntity -> {
            //1. 判断是否【待审核】 回填审核时间
            if (supplierId.get() == 0) {
                supplierId.set(contractEntity.getSupplierId());
            } else if (!supplierId.get().equals(contractEntity.getSupplierId())) {
                throw new BusinessServiceException("请选择同一个供应商的合同");
            }

            //  a.合同变成【待签署】 审核时间 供应商经办人 公司经办人
            contractEntity.setStatus(StockoutCustomsDeclareContractStatusEnum.WAIT_SIGN.name());
            contractEntity.setAuditDate(new Date());
            this.updateById(contractEntity);

            contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.AUDIT, "通过");
        });
        List<Integer> declareContractIdList = declareContractList.stream().map(StockoutCustomsDeclareContractEntity::getDeclareContractId).collect(Collectors.toList());
        //生成合同，回填流程id
        contractOpService.generateEsignContract(declareContractIdList);

        //  c.同步供应商系统
        List<StockoutCustomsDeclareContractEntity> contractList = listByIds(declareContractIdList);
        List<StockoutCustomsDeclareContractSyncDataOneRequest> syncSupplierRequestList = contractList.stream().map(contractEntity -> {
            StockoutCustomsDeclareContractSyncDataOneRequest syncDataRequest = new StockoutCustomsDeclareContractSyncDataOneRequest();
            BeanUtilsEx.copyProperties(contractEntity, syncDataRequest);
            List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractNo(contractEntity.getDeclareContractNo());
            StockoutCustomsDeclareFormEntity firstForm = formList.get(0);

            Set<Integer> formIdSet = formList.stream()
                    .map(StockoutCustomsDeclareFormEntity::getDeclareFormId).collect(Collectors.toSet());
            syncDataRequest.setDeclareFormIds(formIdSet);
            syncDataRequest.setContractGenerateDate(firstForm.getContractGenerateDate());
            return syncDataRequest;
        }).collect(Collectors.toList());
        //同步supplier
        supplierApiService.syncCustomsContract(syncSupplierRequestList);
        //同步scm
        scmApiService.syncCustomsContract(syncSupplierRequestList);
    }

    /**
     * 审核不通过
     * 不通过 合同状态改为【审核不通过】 走驳回流程
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "companyAuditNoPass", lockKey = "#request.declareContractIdList[0]")
    public void companyAuditNoPass(StockoutCustomsDeclareConstractCompanyAuditNoPassRequest request) {
        List<StockoutCustomsDeclareFormWmsRejectOneRequest> oneRequestList = new ArrayList<>();
        request.getDeclareContractIdList().forEach(declareFormId -> {
            //1. 判断是否【待审核】
            StockoutCustomsDeclareContractEntity contractEntity = findById(declareFormId);
            if (!StockoutCustomsDeclareContractStatusEnum.WAIT_AUDIT.name().equals(contractEntity.getStatus())) {
                throw new BusinessServiceException(String.format("合同 %s 非【待审核】状态", contractEntity.getDeclareContractNo()));
            }

            ESignCheckFileStatusResponse fileStatusResponse = eSignService.checkFileStatus(contractEntity.getFileId());
            if (2 != fileStatusResponse.getFileStatus() && 5 != fileStatusResponse.getFileStatus()) {
                throw new BusinessServiceException(String.format("%s 的文件还在上传中，请稍等", contractEntity.getDeclareContractNo()));
            }

            //2. 合同状态改为【审核不通过】 走驳回流程
            contractEntity.setStatus(StockoutCustomsDeclareContractStatusEnum.NO_PASS.name());
            contractEntity.setAuditDate(new Date());
            this.updateById(contractEntity);
            contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.AUDIT, String.format("不通过, 原因: %s", request.getReason()));
            //生成supplier请求数据
            oneRequestList.addAll(buildFormWmsRejectOneRequest(contractEntity.getDeclareContractId()));
            //驳回关单
            List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractNo(contractEntity.getDeclareContractNo());
            formList.forEach(temp -> formRejectService.rejectForm(temp.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.AUDIT_CONTRACT_NO_PASS, request.getReason()));

        });

        //关单驳回
        StockoutCustomsDeclareFormWmsRejectRequest formRejectRequest = new StockoutCustomsDeclareFormWmsRejectRequest();
        formRejectRequest.setAuditNoPassReason(request.getReason());
        formRejectRequest.setOperator(loginInfoService.getName());
        formRejectRequest.setDeclareForm(oneRequestList);
        supplierApiService.customsDeclareFormReject(formRejectRequest);

    }

    /**
     * 流程结束
     *
     * @param jObj
     */
    public void flowComplete(JSONObject jObj) {
        /**
         * 2 - 已完成（所有签署方完成签署）
         *
         * 3 - 已撤销（发起方撤销签署任务）
         *
         * 5 - 已过期（签署截止日到期后触发）
         *
         * 7 - 已拒签（签署方拒绝签署）
         */
        String signFlowStatus = jObj.getStr("signFlowStatus");

        if ("5".equals(signFlowStatus)) {
            String signFlowId = jObj.getStr("signFlowId");
            contractCancelService.overdue(signFlowId);
        }

    }

    /**
     * 通过id查找
     *
     * @param contractId
     * @return
     */
    public StockoutCustomsDeclareContractEntity findById(Integer contractId) {
        StockoutCustomsDeclareContractEntity contractEntity = this.getById(contractId);
        if (Objects.isNull(contractEntity)) {
            throw new BusinessServiceException(String.format("合同不存在 【%s】", contractId));
        }
        return contractEntity;
    }

    /**
     * 签署完成回调
     * <p>
     * 1.判断签署方 签署状态 。
     * * * a.签署成功 - 回填签署时间  回填关单  如果是供应商签署，则通知供应商
     * * * b.签署失败 - 日志记录，返回
     * 2.判断合同工是否已完成
     *
     * @param jObj
     */
    public void signFinish(JSONObject jObj) {
        String signFlowId = jObj.getStr("signFlowId");
        //2 - 签署完成，3 - 失败，4 - 拒签
        Integer signResult = jObj.getInt("signResult");
        //拒签或失败时，附加的原因描述
        String resultDescription = jObj.getStr("resultDescription");
        JSONObject operatorJobj = jObj.getJSONObject("operator");
        JSONObject psnAccountJobj = operatorJobj.getJSONObject("psnAccount");
        //手机号
        String phone = psnAccountJobj.getStr("accountMobile");

        List<StockoutCustomsDeclareContractEntity> contractList = getBySignFlowId(signFlowId);
        if (CollectionUtil.isEmpty(contractList))
            throw new BusinessServiceException(String.format("流程 %s 查到的合同列表为空", signFlowId));
        StockoutCustomsDeclareContractEntity firstContract = contractList.get(0);

        String operator = firstContract.getCompanyAgentPhone().equals(phone) ? "需方" : "供方";

        if (ContractSignResultEnum.REFUSE.getValue().equals(signResult)) {   //拒签
            contractCancelService.refuse(signFlowId);
            contractList.forEach(contractEntity ->
                    contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.SIGN,
                            String.format("%s 拒签，签署失败 %s", operator, resultDescription)));
        } else if (ContractSignResultEnum.FINISH.getValue().equals(signResult)) {   //完成
            if ("需方".equals(operator)) {   //公司签署
                contractList.forEach(contractEntity -> {
                    contractEntity.setHasCompanySignContract(Boolean.TRUE);
                    contractEntity.setCompanySignDate(new Date());
                    this.updateById(contractEntity);
                    contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.SIGN, operator + "已签署");
                    //回填关单
                    List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractNo(contractEntity.getDeclareContractNo());
                    formList.forEach(form -> formService.companySign(form.getDeclareFormId()));
                    //判断合同工是否已完成
                    contractOpService.checkContractComplete(contractEntity.getDeclareContractId());
                });
            } else {    //供应商签署
                contractList.forEach(contractEntity -> {
                    contractEntity.setHasSupplierSignContract(Boolean.TRUE);
                    contractEntity.setSupplierSignDate(new Date());
                    this.updateById(contractEntity);
                    contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.SIGN, operator + "已签署");
                    //回填关单
                    List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractNo(contractEntity.getDeclareContractNo());
                    formList.forEach(form -> formService.supplierSignAndCheckSupplierDone(form.getDeclareFormId()));
                    //同步supplier合同签订完毕
                    StockoutCustomsDeclareContractWmsSignRequest request = new StockoutCustomsDeclareContractWmsSignRequest();
                    request.setDeclareContractId(contractEntity.getDeclareContractId());
                    request.setSupplierSignDate(new Date());
                    supplierApiService.syncCustomsContractSign(request);
                    //判断合同工是否已完成
                    contractOpService.checkContractComplete(contractEntity.getDeclareContractId());
                });
            }
        } else {
            contractList.forEach(contractEntity ->
                    contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.SIGN, String.format("签署失败 %s", resultDescription)));

        }
    }


    /**
     * 获取供应商
     *
     * @param supplierId
     * @return
     */
    public SupplierTaxGetSampleDatailDto getSupplierInfo(Integer supplierId, String status) {
        List<SupplierTaxGetSampleDatailDto> dtoList = scmApiService.getSampleDetailBySupplierId(new SupplierTaxDetailSampleRequest(Collections.singletonList(supplierId), status));
        if (CollectionUtils.isEmpty(dtoList)) {
            throw new BusinessServiceException(String.format("供应商税务信息未维护 %s", supplierId));
        }
        return dtoList.get(0);
    }

    /**
     * 获取供应商
     *
     * @param supplierIdList
     * @return
     */
    public Map<Integer, SupplierTaxGetSampleDatailDto> getSupplierInfoMap(List<Integer> supplierIdList, String status) {
        return CollectionUtil.split(supplierIdList, 50).stream()
                .map(splitList -> scmApiService.getSampleDetailBySupplierId(new SupplierTaxDetailSampleRequest(splitList, status)))
                .flatMap(Collection::stream).collect(Collectors.toMap(SupplierTaxGetSampleDatailDto::getSupplierId, Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 构建请求参数
     *
     * @param contractId
     * @return
     */
    public List<StockoutCustomsDeclareFormWmsRejectOneRequest> buildFormWmsRejectOneRequest(Integer contractId) {
        List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractId(contractId);
        if (formList.isEmpty()) throw new BusinessServiceException(String.format("合同【%s】的关单为空", contractId));
        return formList.stream().map(form -> {
            StockoutCustomsDeclareFormWmsRejectOneRequest rejectOneRequest = new StockoutCustomsDeclareFormWmsRejectOneRequest();
            rejectOneRequest.setDeclareFormId(form.getDeclareFormId());
            List<StockoutCustomsDeclareFormItemEntity> formItemList = formItemService.itemListNoManual(form.getDeclareFormId());
            if (formItemList.isEmpty())
                throw new BusinessServiceException(String.format("关单【%s】明细为空", form.getDeclareFormId()));
            List<StockoutCustomsDeclareFormWmsRejectOneDetailRequest> detailList = formItemList.stream().map(formItem -> {
                StockoutCustomsDeclareFormWmsRejectOneDetailRequest detailRequest = new StockoutCustomsDeclareFormWmsRejectOneDetailRequest();
                detailRequest.setDeclareFormItemId(formItem.getDeclareFormItemId());
                detailRequest.setTaxItemId(formItem.getTaxItemId());
                return detailRequest;
            }).collect(Collectors.toList());
            rejectOneRequest.setDeclareFormItem(detailList);
            return rejectOneRequest;
        }).collect(Collectors.toList());
    }

    /**
     * 通过流程id查找合同
     *
     * @param signFlowId
     * @return
     */
    public List<StockoutCustomsDeclareContractEntity> getBySignFlowId(String signFlowId) {
        return this.list(new LambdaQueryWrapper<StockoutCustomsDeclareContractEntity>().eq(StockoutCustomsDeclareContractEntity::getSignFlowId, signFlowId));
    }


    /**
     * 获取预览链接
     *
     * @param declareContractId
     * @return
     */
    public String getFileUrl(Integer declareContractId) {
        StockoutCustomsDeclareContractEntity contractEntity = findById(declareContractId);
        if (StrUtil.isEmpty(contractEntity.getFileId()))
            throw new BusinessServiceException(String.format("合同 %s 未上传", declareContractId));
        if (StockoutCustomsDeclareContractStatusEnum.WAIT_SIGN.name().equals(contractEntity.getStatus())) {
            return eSignService.getSigningFiles(contractEntity.getSignFlowId(), contractEntity.getFileId()).getFileDownloadUrl();
        } else if (StockoutCustomsDeclareContractStatusEnum.COMPLETE.name().equals(contractEntity.getStatus())) {
            return getSignedFileUrl(contractEntity).getDownloadUrl();
        }
        return eSignService.getFiles(contractEntity.getFileId()).getFileDownloadUrl();
    }

    /**
     * 获取已签署文件链接
     *
     * @param contractEntity
     * @return
     */
    public ESignGetSignedFilesResponse.File getSignedFileUrl(StockoutCustomsDeclareContractEntity contractEntity) {
        List<ESignGetSignedFilesResponse.File> files = eSignService.getSignedFiles(contractEntity.getSignFlowId()).getFiles();
        Map<String, ESignGetSignedFilesResponse.File> fileMap = files.stream().collect(Collectors.toMap(ESignGetSignedFilesResponse.File::getFileId, Function.identity(), (v1, v2) -> v1));
        ESignGetSignedFilesResponse.File file = fileMap.get(contractEntity.getFileId());
        if (Objects.isNull(file)) {
            throw new BusinessServiceException("找不到已签署文件");
        }
        return file;
    }

    /**
     * 构建交货日期
     *
     * @param supplierInfo
     * @param stockinDate
     * @return
     */
    public Date buildDeliveryDate(SupplierTaxGetSampleDatailDto supplierInfo, Date stockinDate) {
        //交货日期：省外=入库日期-2天，省内=入库日期
        if (supplierInfo.getAddress().contains("福建")) {
            return stockinDate;
        } else {
            return DateUtil.offsetDay(stockinDate, -2);
        }
    }

    /**
     * 构建签订日期
     *
     * @param deliveryDate
     * @param total
     * @return
     */
    public Date buildSignDate(Date deliveryDate, Integer total) {
        //数量≤1万件，减30天、1万件＜数量≤2万件，减60天、2万件＜数量，减90天

        if (total <= 10000) {
            return DateUtil.offsetDay(deliveryDate, -30);
        } else if (total <= 20000) {
            return DateUtil.offsetDay(deliveryDate, -60);
        } else {
            return DateUtil.offsetDay(deliveryDate, -90);
        }
    }


    /**
     * 驳回合同
     * 1.判断是否待签署
     * 2.合同变成【XXX已驳回】
     * 3.合同解约
     * 4.关单驳回
     */
//    @Transactional
//    public void supplierRejectContract(List<SupplierRejectContractItemRequest> itemList) {
//        itemList.forEach(item -> {
//            //1.判断是否待签署
//            StockoutCustomsDeclareContractEntity contract = findById(item.getContractId());
//            if (!StockoutCustomsDeclareContractStatusEnum.WAIT_SIGN.name().equals(contract.getStatus())) {
//                throw new BusinessServiceException(String.format("合同【%s】不是【待签署】无法驳回", contract.getDeclareContractNo()));
//            }
//            contractLogService.addLog(item.getContractId(), StockoutCustomsDeclareContractLogTypeEnum.SUPPLIER_REJECT_CONTRACT, item.getReason());
//            // 2.合同变成【XXX已驳回】
//            contract.setStatus(StockoutCustomsDeclareContractStatusEnum.SUPPLIER_NO_PASS.name());
//            contract.setUpdateBy(loginInfoService.getName());
//            this.updateById(contract);
//            //3.合同解约
//            eSignService.revoke(contract.getSignFlowId());
//            //AEO单据删除
//            declareAEOService.removeAEO(contract);
//            //4.驳回关单
//            formRejectService.supplierRejectForm(contract.getDeclareContractNo(), item.getReason());
//        });
//
//    }
    public StockoutCustomsDeclareContractEntity getByDeclareContractNo(String declareContractNo) {
        StockoutCustomsDeclareContractEntity conrtact = getOne(new LambdaQueryWrapper<StockoutCustomsDeclareContractEntity>()
                .eq(StockoutCustomsDeclareContractEntity::getDeclareContractNo, declareContractNo)
                .last("limit 1"));
        if (Objects.isNull(conrtact))
            throw new BusinessServiceException(String.format("报关合同 %s 不存在", declareContractNo));
        return conrtact;
    }

    public StockoutCustomsDeclareContractEntity getByDeclareContractId(Integer declareContractId) {
        StockoutCustomsDeclareContractEntity contract = getById(declareContractId);
        if (Objects.isNull(contract))
            throw new BusinessServiceException(String.format("合同 %s 不存在", declareContractId));
        return contract;
    }

    public StockoutCustomsDeclareContractEntity getByFileId(String fileId) {
        StockoutCustomsDeclareContractEntity contract = getOne(new LambdaQueryWrapper<StockoutCustomsDeclareContractEntity>()
                .eq(StockoutCustomsDeclareContractEntity::getFileId, fileId));
        if (Objects.isNull(contract))
            throw new BusinessServiceException(String.format("找不到fileId是 %s 的合同", fileId));
        return contract;
    }

    public Integer countByDeclareContractNo(String declareContractNo) {
        return count(new LambdaQueryWrapper<StockoutCustomsDeclareContractEntity>()
                .likeRight(StockoutCustomsDeclareContractEntity::getDeclareContractNo, declareContractNo));
    }

    public StockoutCustomsDeclareContractEntity getCompleteManualContract(String declareContractNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclareContractEntity>()
                .eq(StockoutCustomsDeclareContractEntity::getStatus, StockoutCustomsDeclareContractStatusEnum.COMPLETE.name())
                .eq(StockoutCustomsDeclareContractEntity::getDeclareContractNo, declareContractNo)
                .eq(StockoutCustomsDeclareContractEntity::getIsManual, Boolean.TRUE));
    }

    public void updateManualSignInfo(Integer declareContractId, String signFlowId, String fileId) {
        // 4. 更新数据库
        LambdaUpdateWrapper<StockoutCustomsDeclareContractEntity> updateWrapper =
                new LambdaUpdateWrapper<>();
        updateWrapper.eq(StockoutCustomsDeclareContractEntity::getDeclareContractId, declareContractId)
                .set(StockoutCustomsDeclareContractEntity::getSignFlowId, signFlowId)
                .set(StockoutCustomsDeclareContractEntity::getFileId, fileId)
                .set(StockoutCustomsDeclareContractEntity::getUpdateBy, loginInfoService.getName());

        update(updateWrapper);
    }


    /**
     * 签署结果
     */
    public enum ContractSignResultEnum {
        FINISH(2, "签署完成"),
        FAIL(3, "失败"),
        REFUSE(4, "拒签");

        ContractSignResultEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private String name;
        private Integer value;

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }
    }
}
