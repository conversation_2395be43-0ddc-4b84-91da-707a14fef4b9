package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.manage.user.upload.AmazonShipmentLogisticsNoImport;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 亚马逊装箱清单物流单号导入服务
 *
 * <AUTHOR>
 *         2024-03-21
 */
@Service
public class AmazonShipmentUploadService implements IProcessUploadDataService {

    @Autowired
    private StockoutShipmentService stockoutShipmentService;

    @Autowired
    private StockoutShipmentItemService stockoutShipmentItemService;

    @Autowired
    private StockoutShipmentConfirmService stockoutShipmentConfirmService;

    @Autowired
    private TmsCacheService tmsCacheService;

    @Autowired
    private StockoutShipmentAmazonRelationService amazonRelationService;

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_AMAZON_SHIPMENT_LOGISTICS_NO_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!com.nsy.api.core.apicore.util.StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }

        List<AmazonShipmentLogisticsNoImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), AmazonShipmentLogisticsNoImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }

        List<AmazonShipmentLogisticsNoImport> errorList = new ArrayList<>();
        List<TmsLogisticsCompany> logisticsCompanyList = tmsCacheService.getAllLogisticsCompanyList();
        Map<String, String> companyMap = logisticsCompanyList.stream().collect(Collectors.toMap(TmsLogisticsCompany::getLogisticsCompany, TmsLogisticsCompany::getLogisticsMethod, (v1, v2) -> v1));

        importList.forEach(row -> {
            try {
                SpringUtil.getBean(AmazonShipmentUploadService.class).importShipmentLogisticsNo(row, request.getCreateBy(), companyMap);
            } catch (Exception e) {
                row.setErrorMsg(e.getMessage());
                errorList.add(row);
            }
        });

        if (!errorList.isEmpty()) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }

    /**
     * 导入亚马逊装箱清单物流单号
     * 
     * @param row 导入数据行
     * @param loginName 导入用户名
     * @param companyMap 物流公司映射
     */
    @Transactional
    @JLock(keyConstant = "importAmazonShipmentLogisticsNo", lockKey = "#row.orderNo")
    public void importShipmentLogisticsNo(AmazonShipmentLogisticsNoImport row, String loginName, Map<String, String> companyMap) {
        validUploadParams(row, companyMap);

        // 3. 查询并校验装箱清单
        List<StockoutShipmentItemEntity> shipmentItemEntities = stockoutShipmentItemService.list(new LambdaQueryWrapper<StockoutShipmentItemEntity>().eq(StockoutShipmentItemEntity::getOrderNo, row.getOrderNo()).eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(shipmentItemEntities)) {
            List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = amazonRelationService.listByFbaShipmentId(Collections.singletonList(row.getOrderNo()));
            if (CollectionUtils.isEmpty(amazonRelationEntities)) {
                throw new BusinessServiceException("找不到[" + row.getOrderNo() + "]对应的装箱清单记录");
            }
            shipmentItemEntities = stockoutShipmentItemService.findByShipmentIdList(amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getShipmentId).distinct().collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(shipmentItemEntities)) {
                throw new BusinessServiceException("找不到[" + row.getOrderNo() + "]对应的装箱清单记录");
            }
        }

        // 4. 获取并校验未发货的装箱清单
        List<Integer> shipmentIds = shipmentItemEntities.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentList = stockoutShipmentService.listByIds(shipmentIds).stream().filter(shipment -> !StockoutShipmentStatusEnum.SHIPPED.name().equals(shipment.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("找不到[" + row.getOrderNo() + "]对应的未发货装箱清单");
        }

        // 5. 校验装箱状态
        boolean hasPacking = shipmentList.stream().anyMatch(shipment -> shipment.getStatus().equalsIgnoreCase(StockoutShipmentStatusEnum.PACKING.name()));
        if (hasPacking) {
            throw new BusinessServiceException("单号[" + row.getOrderNo() + "]存在装箱中的箱子，无法发货");
        }
        List<Integer> shipmentIdList = new ArrayList<>(1);
        // 6. 更新装箱清单和发货确认
        for (StockoutShipmentEntity shipment : shipmentList) {
            // 更新装箱清单信息
            shipment.setLogisticsNo(row.getLogisticsNo());
            shipment.setLogisticsCompany(row.getLogisticsCompany());
            shipment.setForwarderChannel(row.getForwarderChannel());
            shipment.setUpdateBy(loginName);
            stockoutShipmentService.updateById(shipment);
            shipmentIdList.clear();
            shipmentIdList.add(shipment.getShipmentId());

            // 获取对应的出库单号
            List<String> sonList = stockoutShipmentItemService.getBaseMapper().getStocktoutOrderByShipment(Collections.singletonList(shipment.getShipmentBoxCode()));

            // 更新发货确认信息
            StockoutShipmentConfirmUpdateRequest shipRequest = new StockoutShipmentConfirmUpdateRequest();
            shipRequest.setLogisticsCompany(row.getLogisticsCompany());
            shipRequest.setLogisticsNo(row.getLogisticsNo());
            shipRequest.setShipmentIds(shipmentIdList);
            shipRequest.setStockoutOrderNos(sonList);
            shipRequest.setUnitPrice(row.getUnitPrice());
            stockoutShipmentConfirmService.updateShipmentConfirm(shipRequest);
        }
    }

    private void validUploadParams(AmazonShipmentLogisticsNoImport row, Map<String, String> companyMap) {
        // 1. 校验必填参数
        if (!StringUtils.hasText(row.getOrderNo())) {
            throw new BusinessServiceException("订单号不能为空");
        }
        if (!StringUtils.hasText(row.getLogisticsCompany())) {
            throw new BusinessServiceException("物流公司不能为空");
        }
        if (!StringUtils.hasText(row.getForwarderChannel())) {
            throw new BusinessServiceException("货代渠道不能为空");
        }
        if (!StringUtils.hasText(row.getLogisticsNo())) {
            throw new BusinessServiceException("物流单号不能为空");
        }

        // 2. 校验物流公司和货代渠道
        if (LogisticsCompanyConstant.FEDEX.equalsIgnoreCase(row.getLogisticsCompany())) {
            row.setLogisticsCompany(LogisticsCompanyConstant.FEDEX);
        }
        if (!companyMap.containsKey(row.getLogisticsCompany())) {
            throw new BusinessServiceException(row.getLogisticsCompany() + "此物流公司不正确");
        }
        if (StrUtil.isNotBlank(row.getForwarderChannel()) && !companyMap.containsKey(row.getForwarderChannel())) {
            throw new BusinessServiceException(row.getForwarderChannel() + "此货代渠道不正确");
        }
    }
}
