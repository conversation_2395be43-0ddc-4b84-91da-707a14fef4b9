package com.nsy.wms.business.manage.scm.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

public class PurchaseOrderRequest {
    private List<PurchaseOrderRequestItem> itemList;

    private String receivingNo;
    private Integer supplierId;
    //接收单完结时间 2024-11-11 17:03:15
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public List<PurchaseOrderRequestItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<PurchaseOrderRequestItem> itemList) {
        this.itemList = itemList;
    }

    public String getReceivingNo() {
        return receivingNo;
    }

    public void setReceivingNo(String receivingNo) {
        this.receivingNo = receivingNo;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

}