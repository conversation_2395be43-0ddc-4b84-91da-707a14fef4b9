package com.nsy.wms.business.service.internal.scm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.StatusTabConstants;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.domain.stock.StockDeliveryQtyBySupplierDeliveryBoxCodeInfo;
import com.nsy.api.wms.domain.stockin.ShelveTaskItemInfo;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.TabUrlEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderDeliveryConfirmStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskTypeEnum;
import com.nsy.api.wms.request.bd.DateRangeRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.QtyRecordRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptItemListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptRecordListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptSummaryRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.stockin.QueryReceiptAndDeliveryRecordResponse;
import com.nsy.api.wms.response.stockin.StatisticsByStockinOrderIdResponse;
import com.nsy.api.wms.response.stockin.StockReceiptItem;
import com.nsy.api.wms.response.stockin.StockReceiptItemListResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptListInfo;
import com.nsy.api.wms.response.stockin.StockinReceiptListResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptRecordListResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptStatisticsResponse;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.business.BusinessBaseService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.internal.common.SaleModuleService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stockin.StcokinOrderTimeService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinOrderLogService;
import com.nsy.wms.business.service.stockin.StockinOrderPostProcessingService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.business.service.stockin.StockinReturnProductService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskItemService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.business.service.stockin.StockinSpotInfoService;
import com.nsy.wms.business.service.tab.TabService;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.WmsDateUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinReceiptService implements IDownloadService, TabService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockinReceiptService.class);
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinOrderLogService stockinOrderLogService;
    @Autowired
    StockinSpotInfoService spotInfoService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    private BdSpaceService bdSpaceService;
    @Autowired
    private StockinReturnProductService stockinReturnProductService;
    @Autowired
    private StockPlatformScheduleService platformScheduleService;
    @Autowired
    private StockinReturnProductTaskItemService returnProductTaskItemService;
    @Autowired
    SaleModuleService saleModuleService;
    @Autowired
    StcokinOrderTimeService stcokinOrderTimeService;
    @Autowired
    StockinOrderPostProcessingService stockinOrderPostProcessingService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    BusinessBaseService businessBaseService;
    @Autowired
    private BdSystemParameterService bdSystemParameterService;

    /**
     * 收货记录列表
     *
     * @param request
     * @return
     */
    public PageResponse<StockinReceiptRecordListResponse> getReceiptRecordList(StockinReceiptRecordListRequest request) {
        request.setLocation(TenantContext.getTenant());
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }
        PageResponse<StockinReceiptRecordListResponse> pageResponse = new PageResponse<>();
        if (!com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo())) {
            throw new BusinessServiceException("采购单号不可为空");
        }

        Page<StockinReceiptRecordListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockinReceiptRecordListResponse> pageInfoList = stockinOrderService.getBaseMapper().pageSearchReceiptRecordList(page, request);
        if (CollectionUtils.isEmpty(pageInfoList)) {
            pageResponse.setContent(Lists.newArrayList());
            return pageResponse;
        }
        List<Integer> stockinOrderIdList = pageInfoList.stream().map(StockinReceiptRecordListResponse::getStockinOrderId).distinct().collect(Collectors.toList());
        List<Integer> realStockinOrderIdList = pageInfoList.stream().filter(o -> o.getRealStockinOrderId() != null && o.getRealStockinOrderId() > 0).map(StockinReceiptRecordListResponse::getRealStockinOrderId).distinct().collect(Collectors.toList());
        if (!realStockinOrderIdList.isEmpty()) {
            stockinOrderIdList = realStockinOrderIdList;
        }
        StockinReceiptSummaryRequest receiptSummaryRequest = new StockinReceiptSummaryRequest();
        BeanUtils.copyProperties(request, receiptSummaryRequest);
        receiptSummaryRequest.setStockinOrderIdList(stockinOrderIdList);
        List<StatisticsByStockinOrderIdResponse> statisticsList = stockinOrderItemService.getBaseMapper().statisticsByReceiptSummaryRequest(receiptSummaryRequest);
        Map<String, String> stockinOrderStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_ORDER_STATUS.getName());
        List<StockinReceiptRecordListResponse> responseList = pageInfoList.stream().peek(info -> {
            info.setShelvedQty(statisticsList.stream().filter(t -> (info.getStockinOrderId().equals(t.getStockinOrderId()) || info.getRealStockinOrderId().equals(t.getStockinOrderId())) && t.getShelvedQty() != null).mapToInt(StatisticsByStockinOrderIdResponse::getShelvedQty).sum());
            info.setReturnQty(statisticsList.stream().filter(t -> (info.getStockinOrderId().equals(t.getStockinOrderId()) || info.getRealStockinOrderId().equals(t.getStockinOrderId())) && t.getReturnQty() != null).mapToInt(StatisticsByStockinOrderIdResponse::getReturnQty).sum());
            info.setReceiveQty(statisticsList.stream().filter(t -> (info.getStockinOrderId().equals(t.getStockinOrderId()) || info.getRealStockinOrderId().equals(t.getStockinOrderId())) && t.getReceiveQty() != null).mapToInt(StatisticsByStockinOrderIdResponse::getReceiveQty).sum());
            if (StockinOrderStatusEnum.COMPLETED.name().equals(info.getStatus())) {
                // 少收 = 发货数 - 上架数 - 退货数
                int differenceQty = info.getShipmentQty() - info.getShelvedQty() - info.getReturnQty();
                info.setLessQty(Math.abs(Math.max(differenceQty, 0)));
                info.setOverchargeQty(Math.abs(Math.min(differenceQty, 0)));
            }
            // 结算数 = 上架数
            info.setSettlementQty(info.getShelvedQty());
            info.setStatusStr(stockinOrderStatusEnumMap.get(info.getStatus()));
        }).collect(Collectors.toList());
        pageResponse.setContent(responseList);
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    /**
     * 入库收货列表--scm
     *
     * @param request
     * @return
     */
    public PageResponse<StockinReceiptListResponse> getReceiptList(StockinReceiptListRequest request) {
        request.setLocation(TenantContext.getTenant());
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }
        PageResponse<StockinReceiptListResponse> pageResponse = new PageResponse<>();
        Page<StockinReceiptListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        LOGGER.info("获取用户信息");
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        if (Objects.nonNull(request.getModelMerchandiserEmpId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getModelMerchandiserEmpId());
            request.setModelMerchandiserEmpName(userInfo.getUserName());
        }
        //状态特殊处理
        if (StatusTabConstants.WAIT_DELIVERY_CONFIRM.equals(request.getStatus()))
            request.setStatus(null);
        if (!CollectionUtils.isEmpty(request.getStatusList()) && request.getStatusList().contains(StatusTabConstants.WAIT_DELIVERY_CONFIRM)) {
            request.setStatus(StockinOrderStatusEnum.COMPLETED.name());
            request.getStatusList().remove(StatusTabConstants.WAIT_DELIVERY_CONFIRM);
            request.setDeliveryConfirmStatus(StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name());
        }

        page.setSearchCount(false);
        LOGGER.info("获取列表");
        request.setLocation(TenantContext.getTenant());
        List<StockinReceiptListInfo> pageInfoList = stockinOrderService.getBaseMapper().pageSearchReceiptList(page, request);
        if (CollectionUtils.isEmpty(pageInfoList)) {
            return PageResponse.empty();
        }

        buildResponse(pageInfoList, pageResponse, request);
        return pageResponse;
    }

    /**
     * 入库收货列表统计--scm
     *
     * @param request 查询条件
     * @return 统计结果
     */
    public StockinReceiptStatisticsResponse getReceiptListStatistics(StockinReceiptListRequest request) {
        StockinReceiptStatisticsResponse response = new StockinReceiptStatisticsResponse();
        if (Objects.isNull(request.getDeliveryDateStart())
                || Objects.isNull(request.getDeliveryDateEnd()))
            return response;

        request.setLocation(TenantContext.getTenant());
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }

        // 处理用户信息
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        if (Objects.nonNull(request.getModelMerchandiserEmpId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getModelMerchandiserEmpId());
            request.setModelMerchandiserEmpName(userInfo.getUserName());
        }

        // 状态特殊处理
        if (StatusTabConstants.WAIT_DELIVERY_CONFIRM.equals(request.getStatus()))
            request.setStatus(null);
        if (!CollectionUtils.isEmpty(request.getStatusList()) && request.getStatusList().contains(StatusTabConstants.WAIT_DELIVERY_CONFIRM)) {
            request.setStatus(StockinOrderStatusEnum.COMPLETED.name());
            request.getStatusList().remove(StatusTabConstants.WAIT_DELIVERY_CONFIRM);
            request.setDeliveryConfirmStatus(StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name());
        }


        Integer shipmentQtyTotal = 0;
        Integer receiveQtyTotal = 0;
        Integer waitReturnQtyTotal = 0;
        Integer returnedQtyTotal = 0;
        Integer shelvedQtyTotal = 0;
        Integer returnQty = 0;

        // 将查询修改为7天一次
        List<DateRangeRequest> dateRangeRequests = WmsDateUtils.splitDateRange(request.getDeliveryDateStart(), request.getDeliveryDateEnd(), 7);
        for (DateRangeRequest dateRangeRequest : dateRangeRequests) {
            request.setDeliveryDateStart(dateRangeRequest.getStartDate());
            request.setDeliveryDateEnd(dateRangeRequest.getEndDate());
            shipmentQtyTotal += stockinOrderService.getBaseMapper().statisticsShipmentQty(request);

            Map<String, BigDecimal> shelveInfoMap = stockinOrderService.getBaseMapper().statisticsShelveInfo(request);
            receiveQtyTotal += shelveInfoMap.get("receiveQty").intValue();
            returnQty += shelveInfoMap.get("returnQty").intValue();
            shelvedQtyTotal += shelveInfoMap.get("shelvedQty").intValue();

            waitReturnQtyTotal += stockinOrderService.getBaseMapper().statisticsWaitReturnQtyTotal(request) + stockinOrderService.getBaseMapper().statisticsRealWaitReturnQtyTotal(request);
            returnedQtyTotal += stockinOrderService.getBaseMapper().statisticsReturnedQtyTotal(request) + stockinOrderService.getBaseMapper().statisticsRealReturnedQtyTotal(request);
        }

        response.setShipmentQtyTotal(shipmentQtyTotal);
        response.setReceiveQtyTotal(receiveQtyTotal);
        response.setWaitReturnQtyTotal(waitReturnQtyTotal);
        response.setReturnedQtyTotal(returnedQtyTotal);
        response.setShelvedQtyTotal(shelvedQtyTotal);


        int differenceQty = response.getShelvedQtyTotal() + returnQty - response.getShipmentQtyTotal();
        response.setLessQtyTotal(Math.min(differenceQty, 0));
        response.setOverchargeQtyTotal(Math.max(differenceQty, 0));
        response.setSettlementQtyTotal(Math.min(response.getShelvedQtyTotal(), response.getShipmentQtyTotal() - returnQty));

        return response;
    }

    private void buildResponse(List<StockinReceiptListInfo> pageInfoList, PageResponse<StockinReceiptListResponse> pageResponse, StockinReceiptListRequest request) {
        List<Integer> stockinOrderIdList = pageInfoList.stream().map(StockinReceiptListInfo::getStockinOrderId).distinct().collect(Collectors.toList());
        List<String> stockinOrderNoList = pageInfoList.stream().map(StockinReceiptListInfo::getStockinOrderNo).distinct().collect(Collectors.toList());
      /*  List<Integer> realStockinOrderIdList = pageInfoList.stream().filter(o -> o.getRealStockinOrderId() != null && o.getRealStockinOrderId() > 0).map(StockinReceiptListInfo::getRealStockinOrderId).distinct().collect(Collectors.toList());
        //LOGGER.info("查找出库单号");
        if (!realStockinOrderIdList.isEmpty()) {
            stockinOrderIdList = realStockinOrderIdList;
            stockinOrderNoList = stockinOrderService.getBaseMapper().searchByStockinOrderIdListIgnoreTenant(realStockinOrderIdList).stream().map(StockinOrderEntity::getStockinOrderNo).distinct().collect(Collectors.toList());
        }*/
        //LOGGER.info("查找退货商品");
        StockinReceiptSummaryRequest receiptSummaryRequest = new StockinReceiptSummaryRequest();
        BeanUtils.copyProperties(request, receiptSummaryRequest);
        receiptSummaryRequest.setStockinOrderIdList(stockinOrderIdList);
        receiptSummaryRequest.setStockinOrderNoList(stockinOrderNoList);
        Map<String, List<StockinReturnProductEntity>> returnProductEntityMap = stockinReturnProductService.getBaseMapper().findAllByReceiptSummaryRequestIgnoreTenant(receiptSummaryRequest)
                .stream().collect(Collectors.groupingBy(StockinReturnProductEntity::getStockinOrderNo));
        //LOGGER.info("查找入库单明细");
        List<StatisticsByStockinOrderIdResponse> statisticsList = stockinOrderItemService.getBaseMapper().statisticsByReceiptSummaryRequest(receiptSummaryRequest);
        Map<String, List<StockinReturnProductTaskItemEntity>> returnTaskItemMap = returnProductTaskItemService.getBaseMapper().findAllByReceiptSummaryRequest(receiptSummaryRequest)
                .stream().collect(Collectors.groupingBy(StockinReturnProductTaskItemEntity::getStockinOrderNo));
        //LOGGER.info("拼装数据返回");
        Map<String, String> stockinOrderStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_ORDER_STATUS.getName());
        List<StockinReceiptListResponse> responseList = pageInfoList.stream().map(pageInfo -> {
            StockinReceiptListResponse response = new StockinReceiptListResponse();
            BeanUtils.copyProperties(pageInfo, response);
            response.setStatusStr(stockinOrderStatusEnumMap.get(pageInfo.getStatus()));
            if (StockinOrderStatusEnum.COMPLETED.name().equals(pageInfo.getStatus())
                    && StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name().equals(pageInfo.getDeliveryConfirmStatus()))
                response.setStatusStr(String.format("%s(%s)", StockinOrderStatusEnum.COMPLETED.getName(), StatusTabConstants.CN_WAIT_DELIVERY_CONFIRM));
            //LOGGER.info("处理上架数据、接收、退货信息");
            this.processShelveInfo(pageInfo, response, statisticsList);
            //LOGGER.info("处理待退货、已退货信息");
            this.processReturnInfo(pageInfo, response, returnProductEntityMap, returnTaskItemMap);
            return response;
        }).collect(Collectors.toList());
        //LOGGER.info("拼装数据返回 --");
        buildStockinReceiptListResponse(responseList);
        pageResponse.setContent(responseList);
        //LOGGER.info("获取统计");
        pageResponse.setTotalCount(stockinOrderService.getBaseMapper().pageSearchReceiptListCount(request));
    }

    /**
     * 处理上架数据、接收、退货信息
     */
    private void processShelveInfo(StockinReceiptListInfo pageInfo, StockinReceiptListResponse response, List<StatisticsByStockinOrderIdResponse> statisticsList) {
        response.setShelvedQty(statisticsList.stream().filter(t -> (pageInfo.getStockinOrderId().equals(t.getStockinOrderId()) || pageInfo.getRealStockinOrderId().equals(t.getStockinOrderId())) && t.getShelvedQty() != null).mapToInt(StatisticsByStockinOrderIdResponse::getShelvedQty).sum());
        response.setReturnQty(statisticsList.stream().filter(t -> (pageInfo.getStockinOrderId().equals(t.getStockinOrderId()) || pageInfo.getRealStockinOrderId().equals(t.getStockinOrderId())) && t.getReturnQty() != null).mapToInt(StatisticsByStockinOrderIdResponse::getReturnQty).sum());
        response.setReceiveQty(statisticsList.stream().filter(t -> (pageInfo.getStockinOrderId().equals(t.getStockinOrderId()) || pageInfo.getRealStockinOrderId().equals(t.getStockinOrderId())) && t.getReceiveQty() != null).mapToInt(StatisticsByStockinOrderIdResponse::getReceiveQty).sum());

        response.setLessQty(Math.abs(Math.min(response.getDifferenceQty(), 0)));
        response.setOverchargeQty(Math.abs(Math.max(response.getDifferenceQty(), 0)));
        response.setSettlementQty(Math.min(response.getShelvedQty(), response.getShipmentQty() - response.getReturnQty()));
    }

    /**
     * 处理待退货、已退货信息
     */
    private void processReturnInfo(StockinReceiptListInfo pageInfo, StockinReceiptListResponse response, Map<String, List<StockinReturnProductEntity>> returnProductEntityMap, Map<String, List<StockinReturnProductTaskItemEntity>> returnTaskItemMap) {
        if (pageInfo.getRealStockinOrderId() != null && pageInfo.getRealStockinOrderId() > 0) {
            StockinOrderEntity realStockinOrder = stockinOrderService.getBaseMapper().findTopByStockinOrderIdIgnoreTenant(pageInfo.getRealStockinOrderId());
            pageInfo.setStockinOrderNo(realStockinOrder.getStockinOrderNo());
        }
        List<StockinReturnProductEntity> returnProductEntityList = returnProductEntityMap.getOrDefault(pageInfo.getStockinOrderNo(), Lists.newArrayList());
        response.setWaitReturnQty(returnProductEntityList.stream().mapToInt(StockinReturnProductEntity::getReturnQty).sum());
        int returnedQty = returnTaskItemMap.getOrDefault(pageInfo.getStockinOrderNo(), Lists.newArrayList()).stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum();
        response.setReturnedQty(returnedQty);
    }

    private void buildStockinReceiptListResponse(List<StockinReceiptListResponse> responseList) {
        if (responseList.isEmpty()) return;
        List<Integer> taskIdList = responseList.stream().map(StockinReceiptListResponse::getTaskId).collect(Collectors.toList());
        List<StockinOrderTaskItemEntity> taskItemList = stockinOrderTaskItemService.getBaseMapper().findAllByTaskIdListIgnoreTenant(taskIdList);
        List<Integer> spaceIdList = taskItemList.stream().map(StockinOrderTaskItemEntity::getSpaceId).distinct().collect(Collectors.toList());
        if (spaceIdList.isEmpty()) return;
        List<BdSpaceEntity> spaceList = bdSpaceService.getBaseMapper().findAllBySpaceIdListIgnoreTenant(spaceIdList);
        Map<Integer, List<StockinOrderTaskItemEntity>> taskItemMap = taskItemList.stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getTaskId));

        responseList.forEach(response -> {
            List<StockinOrderTaskItemEntity> tempTaskItemList = taskItemMap.get(response.getTaskId());
            if (Objects.isNull(tempTaskItemList) || tempTaskItemList.isEmpty()) return;

            List<Integer> tempSpaceIdList = tempTaskItemList.stream().map(StockinOrderTaskItemEntity::getSpaceId).distinct().collect(Collectors.toList());
            List<String> spaceNameList = spaceList.stream().filter(temp -> tempSpaceIdList.contains(temp.getSpaceId())).map(BdSpaceEntity::getSpaceName).collect(Collectors.toList());

            response.setSpaceName(String.join(",", spaceNameList));
        });
    }

    /**
     * 入库收货明细
     *
     * @param request
     * @return
     */
    public StockReceiptItemListResponse getReceiptItemListResponse(StockinReceiptItemListRequest request) {
        if (request.getStockinOrderId() == null) return new StockReceiptItemListResponse();
        StockinOrderEntity stockinOrderEntity = stockinOrderService.getById(request.getStockinOrderId());
        if (stockinOrderEntity.getOrderType().equals(1) && stockinOrderEntity.getRealStockinOrderId() != null && stockinOrderEntity.getRealStockinOrderId() > 0) {
            request.setStockinOrderId(stockinOrderEntity.getRealStockinOrderId());
            stockinOrderEntity = stockinOrderService.getBaseMapper().findTopByStockinOrderIdIgnoreTenant(stockinOrderEntity.getRealStockinOrderId());
        }
        StockReceiptItemListResponse response = getReceiptItemSummary(request, stockinOrderEntity);
        PageResponse<StockReceiptItem> pageList = getReceiptItemList(request, stockinOrderEntity);
        response.setTotalCount(pageList.getTotalCount());
        response.setContent(pageList.getContent());
        return response;
    }

    private StockReceiptItemListResponse getReceiptItemSummary(StockinReceiptItemListRequest request, StockinOrderEntity stockinOrderEntity) {
        List<StockReceiptItem> pageInfoList = stockinOrderItemService.getBaseMapper().pageSearchReceiptItemList(request);
        StockinReceiptSummaryRequest receiptSummaryRequest = new StockinReceiptSummaryRequest();
        BeanUtils.copyProperties(request, receiptSummaryRequest);
        receiptSummaryRequest.setStockinOrderNoList(Collections.singletonList(stockinOrderEntity.getStockinOrderNo()));
        pageInfoList.stream().peek(info -> {
            info.setDifferenceQty(info.getShelvedQty() + info.getReturnQty() - info.getShipmentQty());
            info.setSettlementQty(Math.min(info.getShelvedQty(), info.getShipmentQty() - info.getReturnQty()));
        }).collect(Collectors.toList());
        StockReceiptItemListResponse pageResponse = new StockReceiptItemListResponse();
        pageResponse.setReturnTotalQty(pageInfoList.stream().mapToInt(StockReceiptItem::getReturnQty).sum());
        pageResponse.setReceiveTotalQty(pageInfoList.stream().mapToInt(StockReceiptItem::getReceiveQty).sum());
        pageResponse.setShipmentTotalQty(pageInfoList.stream().mapToInt(StockReceiptItem::getShipmentQty).sum());
        pageResponse.setShelvedTotalQty(pageInfoList.stream().mapToInt(StockReceiptItem::getShelvedQty).sum());
        pageResponse.setDifferenceTotalQty(pageInfoList.stream().mapToInt(StockReceiptItem::getDifferenceQty).sum());
        pageResponse.setSettlementTotalQty(pageInfoList.stream().mapToInt(StockReceiptItem::getSettlementQty).sum());
        return pageResponse;
    }

    private PageResponse<StockReceiptItem> getReceiptItemList(StockinReceiptItemListRequest request, StockinOrderEntity stockinOrderEntity) {
        Page<StockReceiptItem> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockReceiptItem> pageInfoList = stockinOrderItemService.getBaseMapper().pageSearchReceiptItemList(page, request);
        if (CollectionUtils.isEmpty(pageInfoList)) {
            return PageResponse.empty();
        }
        StockinReceiptSummaryRequest receiptSummaryRequest = new StockinReceiptSummaryRequest();
        BeanUtils.copyProperties(request, receiptSummaryRequest);
        receiptSummaryRequest.setStockinOrderNoList(Collections.singletonList(stockinOrderEntity.getStockinOrderNo()));

        Map<String, List<StockinReturnProductEntity>> returnProductEntityMap = stockinReturnProductService.getBaseMapper().findAllByReceiptSummaryRequestIgnoreTenant(receiptSummaryRequest)
                .stream().collect(Collectors.groupingBy(e -> String.format("%s##%s", e.getPurchasePlanNo(), e.getSku())));
        Map<String, List<ShelveTaskItemInfo>> shelveTaskItemInfoMap = stockinShelveTaskItemService.getBaseMapper().searchShelveTaskItemList(Collections.singletonList(request.getStockinOrderId()), StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name())
                .stream().collect(Collectors.groupingBy(t -> String.format("%s##%s", t.getPurchasePlanNo(), t.getSku())));
        Map<String, List<StockinReturnProductTaskItemEntity>> returnTaskItemEntityMap = returnProductTaskItemService.getBaseMapper().findAllByReceiptSummaryRequest(receiptSummaryRequest)
                .stream().collect(Collectors.groupingBy(t -> String.format("%s##%s", t.getPurchasePlanNo(), t.getSku())));
        pageInfoList.stream().peek(info -> {
            List<ShelveTaskItemInfo> shelveItemList = shelveTaskItemInfoMap.getOrDefault(String.format("%s##%s", info.getPurchaseNo(), info.getSku()), Lists.newArrayList());
            List<ShelveTaskItemInfo> sortShelveTaskItemInfoList = shelveItemList.stream().sorted(Comparator.comparing(ShelveTaskItemInfo::getShelvedDate, Comparator.nullsFirst(Date::compareTo)).reversed()).collect(Collectors.toList());
            int waitReturnQty = returnProductEntityMap.getOrDefault(String.format("%s##%s", info.getPurchaseNo(), info.getSku()), Lists.newArrayList()).stream().mapToInt(StockinReturnProductEntity::getReturnQty).sum();
            int returnedQty = returnTaskItemEntityMap.getOrDefault(String.format("%s##%s", info.getPurchaseNo(), info.getSku()), Lists.newArrayList()).stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum();
            info.setWaitReturnQty(waitReturnQty);
            info.setReturnedQty(returnedQty);
            int differenceQty = info.getShelvedQty() + info.getReturnQty() - info.getShipmentQty();
            info.setLessQty(Math.abs(Math.max(differenceQty, 0)));
            info.setOverchargeQty(Math.abs(Math.min(differenceQty, 0)));
            info.setDifferenceQty(differenceQty);
            info.setSettlementQty(Math.min(info.getShelvedQty(), info.getShipmentQty() - info.getReturnQty()));
            String differenceInfo = differenceQty == 0 ? "0" : differenceQty > 0 ? "多收" : "少收";
            info.setDifferenceInfo(differenceQty == 0 ? "0" : String.format("%s%s件", differenceInfo, Math.abs(differenceQty)));
            info.setStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_SKU_STATUS.getName(), info.getStatusList()));
            info.setShelveDate(CollectionUtils.isEmpty(sortShelveTaskItemInfoList) ? null : sortShelveTaskItemInfoList.get(0).getShelvedDate());
        }).collect(Collectors.toList());
        return PageResponse.of(pageInfoList, page.getTotal());
    }

    /**
     * 核对按钮
     *
     * @param stockinOrderIdList
     */
    @Transactional(rollbackFor = Exception.class)
    public void check(List<Integer> stockinOrderIdList) {
        if (CollectionUtils.isEmpty(stockinOrderIdList))
            throw new BusinessServiceException("请至少选择一条数据进行确认");
        List<StockinOrderEntity> orderEntityList = stockinOrderService.list(new LambdaQueryWrapper<StockinOrderEntity>()
                .in(StockinOrderEntity::getStockinOrderId, stockinOrderIdList)
                .eq(StockinOrderEntity::getStatus, StockinOrderStatusEnum.CHECKING.name()));
        if (CollectionUtils.isEmpty(orderEntityList)) {
            throw new BusinessServiceException("只支持状态为待核对的入库单进行核对");
        }
        int count = stockinOrderTaskService.count(new LambdaQueryWrapper<StockinOrderTaskEntity>()
                .in(StockinOrderTaskEntity::getTaskId, orderEntityList.stream().map(StockinOrderEntity::getTaskId).collect(Collectors.toList()))
                .ne(StockinOrderTaskEntity::getStatus, StockinOrderTaskStatusEnum.RECEIVED.name()));
        if (count > 0)
            throw new BusinessServiceException("入库任务未完成，不允许核对！");

        List<StockinOrderEntity> stockinOrderEntityList = orderEntityList.stream().filter(o -> o.getRealStockinOrderId() == null || o.getRealStockinOrderId() == 0).collect(Collectors.toList());
        this.checkConfirm(stockinOrderEntityList);


        List<StockinOrderEntity> realStockinOrderEntityList = orderEntityList.stream().filter(o -> o.getRealStockinOrderId() != null && o.getRealStockinOrderId() > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(realStockinOrderEntityList)) {
            String wmsStockinDeliveryConfirm = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKIN_DELIVERY_CONFIRM.getKey());
            stockinOrderService.updateBatchById(realStockinOrderEntityList.stream().peek(orderEntity -> {
                orderEntity.setStatus(StockinOrderStatusEnum.COMPLETED.name());
                orderEntity.setDeliveryConfirmStatus(com.nsy.api.core.apicore.util.StringUtils.hasText(wmsStockinDeliveryConfirm) && "true".equals(wmsStockinDeliveryConfirm) ? StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name() : StockinOrderDeliveryConfirmStatusEnum.IGNORE.name());
                orderEntity.setUpdateBy(loginInfoService.getName());
            }).collect(Collectors.toList()));

            //处理分公司的单据
            List<Integer> realStockinOrderIdList = realStockinOrderEntityList.stream().map(StockinOrderEntity::getRealStockinOrderId).collect(Collectors.toList());
            List<StockinOrderEntity> stockinOrderEntities = stockinOrderService.getBaseMapper().searchByStockinOrderIdListIgnoreTenant(realStockinOrderIdList);
            Map<String, List<StockinOrderEntity>> collect = stockinOrderEntities.stream().collect(Collectors.groupingBy(StockinOrderEntity::getLocation));
            collect.forEach((location, orderList) -> {
                TenantContext.setTenant(location);
                this.checkConfirm(orderList);
            });
        }
    }


    private void checkConfirm(List<StockinOrderEntity> orderEntities) {
        if (CollectionUtils.isEmpty(orderEntities))
            return;

        List<StockinOrderEntity> stockinOrderEntityList = orderEntities;

        String wmsStockinDeliveryConfirm = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKIN_DELIVERY_CONFIRM.getKey());
        stockinOrderService.updateBatchById(stockinOrderEntityList.stream().peek(orderEntity -> {
            orderEntity.setStatus(StockinOrderStatusEnum.COMPLETED.name());
            orderEntity.setDeliveryConfirmStatus(com.nsy.api.core.apicore.util.StringUtils.hasText(wmsStockinDeliveryConfirm) && "true".equals(wmsStockinDeliveryConfirm) ? StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name() : StockinOrderDeliveryConfirmStatusEnum.IGNORE.name());
            orderEntity.setUpdateBy(loginInfoService.getName());
        }).collect(Collectors.toList()));
        List<StockinOrderLogEntity> stockinOrderLogEntityList = new ArrayList<>();
        stockinOrderEntityList.forEach(stockinOrderEntity -> {
            StockinOrderLogEntity logEntity = stockinOrderLogService.buildLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.CHECK.getName(), "入库单核对");
            StockinOrderLogEntity logEntity2 = stockinOrderLogService.buildLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.COMPLETE_STOCKIN_ORDER.getName(), "入库单完成");
            stockinOrderLogEntityList.add(logEntity);
            stockinOrderLogEntityList.add(logEntity2);
            //入库任务完成后置处理
            stockinOrderPostProcessingService.processingOrder(stockinOrderEntity);
        });
        List<Integer> stockinOrderIds = stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList());
        stcokinOrderTimeService.addTimeBatch(stockinOrderIds, StockinOrderLogTypeEnum.CHECK);
        stcokinOrderTimeService.addTimeBatch(stockinOrderIds, StockinOrderLogTypeEnum.COMPLETE_STOCKIN_ORDER);

        stockinOrderLogService.saveBatch(stockinOrderLogEntityList);
        stockinOrderService.feedbackShelvedToErp(stockinOrderEntityList, loginInfoService.getName());
    }


    /**
     * 工厂差异确认
     * 由于分公司找总公司申请的单有特殊处理逻辑，必须将正常单与分公司申请单分开
     *
     * @param request
     */
    @Transactional
    public void differenceConfirm(StringListRequest request) {
        if (CollectionUtils.isEmpty(request.getStringList()))
            throw new BusinessServiceException("请至少选择一条数据进行确认");
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.list(new LambdaQueryWrapper<StockinOrderEntity>()
                .in(StockinOrderEntity::getSupplierDeliveryBoxCode, request.getStringList())
                .eq(StockinOrderEntity::getDeliveryConfirmStatus, StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name()));
        if (CollectionUtils.isEmpty(stockinOrderEntityList))
            return;
        List<Integer> stockinOrderIdList = stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList());

        stockinOrderService.update(new LambdaUpdateWrapper<StockinOrderEntity>()
                .set(StockinOrderEntity::getDeliveryConfirmStatus, StockinOrderDeliveryConfirmStatusEnum.CONFIRM.name())
                .in(StockinOrderEntity::getStockinOrderId, stockinOrderIdList));

        List<StockinOrderLogEntity> stockinOrderLogEntityList = new ArrayList<>();
        stockinOrderEntityList.forEach(stockinOrderEntity -> {
            StockinOrderLogEntity logEntity = stockinOrderLogService.buildLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.DIFFERENCE_CONFIRM.getName(), "入库单工厂差异确认");
            stockinOrderLogEntityList.add(logEntity);
        });
        stcokinOrderTimeService.addTimeBatch(stockinOrderIdList, StockinOrderLogTypeEnum.DIFFERENCE_CONFIRM);

        stockinOrderLogService.saveBatch(stockinOrderLogEntityList);
    }

    /**
     * 入库收货日志类型
     *
     * @return
     */
    public List<SelectModel> getStocinOrderLogTypeSelect() {
        return Arrays.stream(StockinOrderLogTypeEnum.values()).map(type -> new SelectModel(type.getName(), type.getName())).collect(Collectors.toList());
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_ORDER_RECEIPT_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinReceiptListRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockinReceiptListRequest.class);
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.buildSkuAutoMatchList(downloadRequest.getSku());
        downloadRequest.buildSpuAutoMatchList(downloadRequest.getSpu());
        PageResponse<StockinReceiptListResponse> pageResponse = getReceiptList(downloadRequest);
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getContent()));
        return response;
    }

    @Override
    public List<StatusTabResponse> getTabs() {
        List<StatusTabResponse> tabs = stockinOrderService.getBaseMapper().getTabs();
        List<String> statusList = Arrays.stream(StockinOrderStatusEnum.values()).map(StockinOrderStatusEnum::name).collect(Collectors.toList());
        for (String status : statusList) {
            if (tabs.stream().noneMatch(tab -> status.equals(tab.getStatus()))) {
                StatusTabResponse response = new StatusTabResponse();
                response.setNum(0);
                response.setStatus(status);
                tabs.add(response);
            }
        }
        List<StatusTabResponse> newTabs = tabs.stream().filter(tab -> statusList.stream().anyMatch(status -> status.equals(tab.getStatus())))
                .sorted(Comparator.comparing(tab -> Enum.valueOf(StockinOrderStatusEnum.class, tab.getStatus()).ordinal())).collect(Collectors.toList());
        newTabs.forEach(tab -> {
            if (StockinOrderStatusEnum.COMPLETED.name().equals(tab.getStatus()))
                tab.setLabel("入库完成");
            else
                tab.setLabel(Enum.valueOf(StockinOrderStatusEnum.class, tab.getStatus()).getName());
            tab.setName(tab.getStatus());
        });

        //待工厂确认状态
        int count = stockinOrderService.count(new LambdaQueryWrapper<StockinOrderEntity>()
                .eq(StockinOrderEntity::getDeliveryConfirmStatus, StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name()));
        StatusTabResponse response = new StatusTabResponse();
        response.setNum(count);
        response.setLabel(StatusTabConstants.CN_WAIT_DELIVERY_CONFIRM);
        response.setName(StatusTabConstants.WAIT_DELIVERY_CONFIRM);
        response.setStatus(StatusTabConstants.WAIT_DELIVERY_CONFIRM);
        newTabs.add(response);

        StatusTabResponse responseAll = new StatusTabResponse();
        responseAll.setNum(0);
        responseAll.setLabel(StatusTabConstants.CN_ALL);
        responseAll.setName(StatusTabConstants.ALL);
        responseAll.setStatus(StatusTabConstants.ALL);
        newTabs.add(responseAll);
        return newTabs;
    }

    @Override
    public TabUrlEnum tabType() {
        return TabUrlEnum.STOCKIN_ORDER;
    }

    // 收发货记录
    public PageResponse<QueryReceiptAndDeliveryRecordResponse> receiptAndDeliveryRecord(QtyRecordRequest request) {
        request.setLocation(TenantContext.getTenant());
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchasePlanNo()) && request.getPurchasePlanNo().startsWith("V")) {
            request.setPurchasePlanNo(request.getPurchasePlanNo().substring(1));
        }
        PageResponse<QueryReceiptAndDeliveryRecordResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(Lists.newArrayList());
        pageResponse.setTotalCount(0);
        IPage<StockDeliveryQtyBySupplierDeliveryBoxCodeInfo> iPage = platformScheduleService.getBaseMapper().queryDeliveryQtyGroupBySupplierDeliveryBoxCode(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        List<StockDeliveryQtyBySupplierDeliveryBoxCodeInfo> infoList = iPage.getRecords();
        if (CollectionUtils.isEmpty(infoList)) {
            return pageResponse;
        }
        Map<String, StockinOrderTaskEntity> orderTaskEntityMap = new HashMap<>();
        Map<String, StockinOrderEntity> orderEntityMap = new HashMap<>();
        Map<Integer, List<StockinOrderItemEntity>> orderItemEntityListMap = new HashMap<>();
        List<Integer> platformScheduleIdList = infoList.stream().map(StockDeliveryQtyBySupplierDeliveryBoxCodeInfo::getPlatformScheduleId).distinct().collect(Collectors.toList());
        List<StockinOrderTaskEntity> orderTaskEntityList = stockinOrderTaskService.getBaseMapper().findAllByPlatformScheduleIdList(platformScheduleIdList); //入库任务
        if (CollectionUtils.isEmpty(orderTaskEntityList)) {
            List<QueryReceiptAndDeliveryRecordResponse> collect = buildReceiptAndDeliveryRecordResponse(infoList, orderTaskEntityMap, orderEntityMap, orderItemEntityListMap);
            pageResponse.setTotalCount(iPage.getTotal());
            pageResponse.setContent(collect);
            return pageResponse;
        }
        orderTaskEntityMap = orderTaskEntityList.stream().collect(Collectors.toMap(StockinOrderTaskEntity::getSupplierDeliveryBoxCode, Function.identity()));
        // 取实际收货入库单
        List<StockinOrderEntity> orderEntityList = stockinOrderService.getBaseMapper().searchByStockinTaskIdListIgnoreTenant(orderTaskEntityList.stream().map(StockinOrderTaskEntity::getTaskId).collect(Collectors.toList()))
                .stream().filter(o -> o.getRealStockinOrderId() == null || o.getRealStockinOrderId().equals(0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderEntityList)) {
            List<QueryReceiptAndDeliveryRecordResponse> collect = buildReceiptAndDeliveryRecordResponse(infoList, orderTaskEntityMap, orderEntityMap, orderItemEntityListMap);
            pageResponse.setTotalCount(iPage.getTotal());
            pageResponse.setContent(collect);
            return pageResponse;
        }
        orderEntityMap = orderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getSupplierDeliveryBoxCode, Function.identity()));
        List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.getBaseMapper().findAllByStockinOrderIdIgnoreTenant(orderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).distinct().collect(Collectors.toList()));
        orderItemEntityListMap = stockinOrderItemEntityList.stream().filter(o -> com.nsy.api.core.apicore.util.StringUtils.hasText(o.getPurchasePlanNo())
                        && o.getPurchasePlanNo().equals(request.getPurchasePlanNo())
                        && o.getSku().equals(request.getSku()))
                .collect(Collectors.groupingBy(StockinOrderItemEntity::getStockinOrderId));

        List<QueryReceiptAndDeliveryRecordResponse> collect = buildReceiptAndDeliveryRecordResponse(infoList, orderTaskEntityMap, orderEntityMap, orderItemEntityListMap);
        pageResponse.setTotalCount(iPage.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    private List<QueryReceiptAndDeliveryRecordResponse> buildReceiptAndDeliveryRecordResponse(List<StockDeliveryQtyBySupplierDeliveryBoxCodeInfo> infoList, Map<String, StockinOrderTaskEntity> orderTaskEntityMap,
                                                                                              Map<String, StockinOrderEntity> orderEntityMap, Map<Integer, List<StockinOrderItemEntity>> orderItemEntityListMap) {
        List<QueryReceiptAndDeliveryRecordResponse> collect = new ArrayList<>();
        if (CollectionUtils.isEmpty(infoList)) {
            return collect;
        }
        collect = infoList.stream().map(info -> {
            StockinOrderTaskEntity orderTaskEntity = orderTaskEntityMap.getOrDefault(info.getSupplierDeliveryBoxCode(), new StockinOrderTaskEntity());
            StockinOrderEntity orderEntity = orderEntityMap.getOrDefault(info.getSupplierDeliveryBoxCode(), new StockinOrderEntity());
            List<StockinOrderItemEntity> orderItemEntityList = Objects.isNull(orderEntity.getStockinOrderId()) ? new ArrayList<>() : orderItemEntityListMap.getOrDefault(orderEntity.getStockinOrderId(), Lists.newArrayList());
            QueryReceiptAndDeliveryRecordResponse response = new QueryReceiptAndDeliveryRecordResponse();
            response.setDeliveryDate(info.getDeliveryDate());
            response.setSupplierDeliveryBoxCode(info.getSupplierDeliveryBoxCode());
            response.setShipmentQty(info.getShipmentQty());
            response.setArrivalTime(info.getAuditDate());
            response.setReceiveDate(orderTaskEntity.getOperateStartDate());
            response.setOperator(orderTaskEntity.getOperator());
            response.setReceiveQty(orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum());
            return response;
        }).collect(Collectors.toList());
        return collect;
    }
}
