package com.nsy.wms.controller.qa;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.qc.QcInboundsProductSpecInfo;
import com.nsy.api.wms.request.qa.StockinCreateQaOrderRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchFirstAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchInspectRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchSecondAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateFirstAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateFullInspectRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateInspectRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateReturnRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateSecondAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOrderHistoryRequest;
import com.nsy.api.wms.request.qa.StockinQaPunishmentsRequest;
import com.nsy.api.wms.request.qa.StockinQaSpuReturnHistoryRequest;
import com.nsy.api.wms.request.stockin.ProductSpecInfoQueryRequest;
import com.nsy.api.wms.response.qa.StockinCreateQaOrderResponse;
import com.nsy.api.wms.response.qa.StockinQaHistoryResponse;
import com.nsy.api.wms.response.qa.StockinQaInfoResponse;
import com.nsy.api.wms.response.qa.StockinQaSopConfigResponse;
import com.nsy.api.wms.response.qa.StockinQaSpuReturnHistoryResponse;
import com.nsy.api.wms.response.qa.StockinWaitQaTaskPageResponse;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.qa.StockinQaFullInspectService;
import com.nsy.wms.business.service.qa.StockinQaInspectService;
import com.nsy.wms.business.service.qa.StockinQaOrderOperateService;
import com.nsy.wms.business.service.qa.StockinQaOrderSearchService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Locale;

/**
 * @description: 入库质检操作相关接口
 * @author: caishaohui
 * @time: 2024/11/21 17:18
 */
@Api(tags = "入库质检操作相关接口")
@RestController
@RequestMapping("/qa/operate")
public class StockinQaOperateController extends BaseController {

    @Autowired
    private StockinQaOrderOperateService stockinQaOrderOperateService;
    @Resource
    StockinQaOrderSearchService stockinQaOrderSearchService;
    @Resource
    ProductInfoService productInfoService;
    @Autowired
    private StockinQaInspectService stockinQaInspectService;
    @Autowired
    private StockinQaFullInspectService qaFullInspectService;

    @ApiOperation(value = "查询内部箱，返回待质检任务", notes = "查询内部箱，返回待质检任务", produces = "application/json")
    @GetMapping("/query-task/{internalBoxCode}")
    public List<StockinWaitQaTaskPageResponse> queryWaitQaTask(@PathVariable("internalBoxCode") String internalBoxCode) {
        return stockinQaOrderOperateService.queryWaitQaTask(internalBoxCode);
    }

    @ApiOperation(value = "根据箱码和商品条码，生成质检单，返回质检单ID", notes = "查询内部箱，返回待质检任务", produces = "application/json")
    @PostMapping("/generate-qa-order")
    public StockinCreateQaOrderResponse generateQaOrder(@RequestBody StockinCreateQaOrderRequest request) {
        if (!StringUtils.hasText(request.getBarcode()) || !StringUtils.hasText(request.getInternalBoxCode())) {
            throw new BusinessServiceException("传入参数不正确!");
        }
        return stockinQaOrderOperateService.generateQaOrder(request, request.getBarcode().toUpperCase(Locale.ROOT), request.getInternalBoxCode().toUpperCase(Locale.ROOT));
    }

    @ApiOperation(value = "操作质检页面-获取信息区数据", produces = "application/json")
    @GetMapping("/get-qa-info/{stockinQaOrderId}")
    public StockinQaInfoResponse getQaInfo(@PathVariable("stockinQaOrderId") Integer stockinQaOrderId) {
        return stockinQaOrderSearchService.getQaInfo(stockinQaOrderId);
    }

    @ApiOperation(value = "操作质检页面-历史同sku质检结果", produces = "application/json")
    @PostMapping("/get-qa-history")
    public PageResponse<StockinQaHistoryResponse> pageQaHistory(@RequestBody StockinQaOrderHistoryRequest request) {
        return stockinQaOrderSearchService.pageQaHistory(request);
    }

    @ApiOperation(value = "操作质检页面-历史同款退货信息", produces = "application/json")
    @PostMapping("/get-qa-spu-return-history")
    public PageResponse<StockinQaSpuReturnHistoryResponse> pageQaSpuReturnHistory(@RequestBody StockinQaSpuReturnHistoryRequest request) {
        return stockinQaOrderSearchService.pageQaSpuReturnHistory(request);
    }

    @ApiOperation(value = "操作质检页面-获取sop配置", produces = "application/json")
    @GetMapping("/get-qa-sop-config/{stockinQaOrderId}")
    public StockinQaSopConfigResponse getQaSopConfig(@PathVariable("stockinQaOrderId") Integer stockinQaOrderId) {
        return stockinQaOrderSearchService.getQaSopConfig(stockinQaOrderId);
    }

    @ApiOperation(value = "操作质检页面-查询对色图", produces = "application/json")
    @GetMapping("/get-qa-product-image/{sku}")
    public QcInboundsProductSpecInfo getQaSopConfig(@PathVariable("sku") String sku) {
        return productInfoService.findInboundsInfoFromProduct(sku);
    }

    @ApiOperation(value = "操作质检页面-查询对色图", produces = "application/json")
    @PostMapping("/get-qa-product-image-by-sku")
    public QcInboundsProductSpecInfo getQaSopConfig(@RequestBody ProductSpecInfoQueryRequest request) {
        return productInfoService.findInboundsInfoFromProduct(request.getSku());
    }


    @ApiOperation(value = "质检Sop操作接口", produces = "application/json")
    @PostMapping("/process")
    public void operate(@RequestBody StockinQaOperateRequest request) {
        stockinQaOrderOperateService.operate(request);
    }


    @ApiOperation(value = "质检初审", produces = "application/json")
    @PostMapping("/first-audit")
    public void firstAudit(@RequestBody StockinQaOperateFirstAuditRequest request) {
        stockinQaOrderOperateService.firstAudit(request);
    }

    @ApiOperation(value = "质检复审", produces = "application/json")
    @PostMapping("/second-audit")
    public void secondAudit(@RequestBody StockinQaOperateSecondAuditRequest request) {
        stockinQaOrderOperateService.secondAudit(request);
    }

    @ApiOperation(value = "质检初审", produces = "application/json")
    @PostMapping("batch/first-audit")
    public void batchFirstAudit(@RequestBody StockinQaBatchFirstAuditRequest request) {
        stockinQaOrderOperateService.batchFirstAudit(request);
    }

    @ApiOperation(value = "质检复审", produces = "application/json")
    @PostMapping("batch/second-audit")
    public void batchSecondAudit(@RequestBody StockinQaBatchSecondAuditRequest request) {
        stockinQaOrderOperateService.batchSecondAudit(request);
    }

    @ApiOperation(value = "增加退货", produces = "application/json")
    @PostMapping("/add-return-quantity")
    public void addReturnQuantity(@RequestBody StockinQaOperateReturnRequest request) {
        stockinQaOrderOperateService.addReturnQuantity(request);
    }

    @ApiOperation(value = "减少退货", produces = "application/json")
    @PostMapping("/reduce-return-quantity")
    public void reduceReturnQuantity(@RequestBody StockinQaOperateReturnRequest request) {
        stockinQaOrderOperateService.reduceReturnQuantity(request);
    }

    @ApiOperation(value = "取消质检单", produces = "application/json")
    @DeleteMapping("/delete-order/{stockinQaOrderId}")
    public void deleteOrder(@PathVariable("stockinQaOrderId") Integer stockinQaOrderId) {
        stockinQaOrderOperateService.deleteOrder(stockinQaOrderId);
    }

    @ApiOperation(value = "返回未完成状态", produces = "application/json")
    @PostMapping("/back-to-incomplete/{stockinQaOrderId}")
    public void backToIncomplete(@PathVariable("stockinQaOrderId") Integer stockinQaOrderId) {
        stockinQaOrderOperateService.backToIncomplete(stockinQaOrderId);
    }


    @ApiOperation(value = "质检稽查", produces = "application/json")
    @PostMapping("/inspect-commit-result")
    public void inspectCommitResult(@RequestBody @Valid StockinQaOperateInspectRequest request) {
        stockinQaInspectService.inspectCommitResult(request);
    }

    @ApiOperation(value = "质检全检", produces = "application/json")
    @PostMapping("/full-inspect-commit-result")
    public void fullInspectCommitResult(@RequestBody @Valid StockinQaOperateFullInspectRequest request) {
        qaFullInspectService.fullInspectCommitResult(request);
    }

    @ApiOperation(value = "批量质检稽查", produces = "application/json")
    @PostMapping("/batch/inspect-commit-result")
    public void batchInspectCommit(@RequestBody StockinQaBatchInspectRequest request) {
        stockinQaInspectService.batchInspectCommit(request);
    }

    @ApiOperation(value = "批量质检稽查", produces = "application/json")
    @PostMapping("/punishments")
    public void punishments(@RequestBody StockinQaPunishmentsRequest request) {
        stockinQaOrderOperateService.punishments(request);
    }
}
