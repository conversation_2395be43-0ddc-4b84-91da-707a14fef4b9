package com.nsy.wms.controller.qa;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.request.base.LogListRequest;
import com.nsy.api.wms.request.qa.PreviousStockinQaOrderRequest;
import com.nsy.api.wms.request.qa.QcInboundsConcessionApplyFileRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaFullInspectReportPageRequest;
import com.nsy.api.wms.request.qa.StockinQaFullInspectTaskPageRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectBatchRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectDetailRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectPageRequest;
import com.nsy.api.wms.request.qa.StockinQaOrderPageRequest;
import com.nsy.api.wms.response.base.LogListResponse;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.qa.PreviousStockinQaOrderResponse;
import com.nsy.api.wms.response.qa.QcInboundsConcessionApplyFileResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectReportPageResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectResultDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectTaskPageResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectBatchResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectCountQtyResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectPageResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectResultDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderCountQtyResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderPageResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderSkcResponse;
import com.nsy.wms.business.service.qa.StockinQaFullInspectService;
import com.nsy.wms.business.service.qa.StockinQaInspectService;
import com.nsy.wms.business.service.qa.StockinQaOrderLogService;
import com.nsy.wms.business.service.qa.StockinQaOrderSearchService;
import com.nsy.wms.business.service.qa.StockinQaOrderService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 14:54
 */
@Api(tags = "入库质检单相关接口")
@RestController
@RequestMapping("/qa/order")
public class StockinQaOrderController extends BaseController {

    @Autowired
    private StockinQaOrderService stockinQaOrderService;
    @Resource
    StockinQaOrderSearchService stockinQaOrderSearchService;
    @Resource
    StockinQaOrderLogService stockinQaOrderLogService;
    @Autowired
    private StockinQaInspectService stockinQaInspectService;
    @Autowired
    private StockinQaFullInspectService stockinQaFullInspectService;

    @ApiOperation(value = "上一次质检结果", notes = "上一次质检结果", produces = "application/json")
    @PostMapping("/previous/order")
    public PreviousStockinQaOrderResponse getPreviousOrder(@RequestBody PreviousStockinQaOrderRequest request) {
        return stockinQaOrderService.getPreviousOrder(request);
    }

    @ApiOperation(value = "流程tab查询", notes = "流程tab查询", produces = "application/json")
    @PostMapping("/process-tab/list")
    public List<StatusTabResponse> processTabList() {
        return stockinQaOrderSearchService.getProcessTabList();
    }

    @ApiOperation(value = "分页查询", notes = "分页查询", produces = "application/json")
    @PostMapping("/page/list")
    public PageResponse<StockinQaOrderPageResponse> pageList(@RequestBody StockinQaOrderPageRequest request) {
        return stockinQaOrderSearchService.pageList(request);
    }

    @ApiOperation(value = "统计数量", notes = "统计数量", produces = "application/json")
    @PostMapping("/count-qty")
    public StockinQaOrderCountQtyResponse countQty(@RequestBody StockinQaOrderPageRequest request) {
        return stockinQaOrderSearchService.countQty(request);
    }

    @ApiOperation(value = "批量初审列表查询", notes = "skc批量初审列表查询", produces = "application/json")
    @PostMapping("/first-audit/list")
    public List<StockinQaOrderSkcResponse> getFirstAuditSkcList(@RequestBody StockinQaBatchAuditRequest request) {
        return stockinQaOrderSearchService.getAuditList(request, StockinQaOrderProcessStatusEnum.FIRST_AUDIT);
    }

    @ApiOperation(value = "批量复审列表查询", notes = "skc批量复审列表查询", produces = "application/json")
    @PostMapping("/second-audit/list")
    public List<StockinQaOrderSkcResponse> getSecondAuditSkcList(@RequestBody StockinQaBatchAuditRequest request) {
        return stockinQaOrderSearchService.getAuditList(request, StockinQaOrderProcessStatusEnum.SECOND_AUDIT);
    }

    @ApiOperation(value = "查询详情", notes = "查询详情", produces = "application/json")
    @GetMapping("/get-info/{stockinQaOrderId}")
    public StockinQaOrderDetailResponse getDetail(@PathVariable("stockinQaOrderId") Integer stockinQaOrderId) {
        return stockinQaOrderSearchService.getDetail(stockinQaOrderId);
    }

    @ApiOperation(value = "日志分页查询", notes = "日志分页查询", produces = "application/json")
    @PostMapping("/page/log/list")
    public PageResponse<LogListResponse> pageLogList(@RequestBody LogListRequest request) {
        return stockinQaOrderLogService.pageList(request);
    }

    @ApiOperation(value = "查询让步接收文件", notes = "查询让步接收文件", produces = "application/json")
    @PostMapping("/get-concession-apply-file")
    public QcInboundsConcessionApplyFileResponse getConcessionApplyFile(@RequestBody QcInboundsConcessionApplyFileRequest request) {
        return stockinQaOrderSearchService.getConcessionApplyFile(request);
    }

    @ApiOperation(value = "分页查询稽查任务", notes = "分页查询稽查任务", produces = "application/json")
    @PostMapping("/page/inspect-task")
    public PageResponse<StockinQaInspectPageResponse> pageInspectTaskList(@RequestBody StockinQaInspectPageRequest request) {
        return stockinQaInspectService.pageInspectTaskList(request);
    }

    @ApiOperation(value = "分页查询稽查报告", notes = "分页查询稽查报告", produces = "application/json")
    @PostMapping("/page/inspect-result")
    public PageResponse<StockinQaInspectPageResponse> pageInspectResultList(@RequestBody StockinQaInspectPageRequest request) {
        return stockinQaInspectService.pageInspectResultList(request);
    }

    @ApiOperation(value = "稽查报告-统计数量", notes = "稽查报告-统计数量", produces = "application/json")
    @PostMapping("/count-inspect-qty")
    public StockinQaInspectCountQtyResponse countInspectQty(@RequestBody StockinQaInspectPageRequest request) {
        return stockinQaInspectService.countInspectQty(request);
    }

    @ApiOperation(value = "查询稽查报告详情", notes = "查询稽查报告详情", produces = "application/json")
    @PostMapping("/get-inspect-info")
    public StockinQaInspectResultDetailResponse getInspectDetail(@RequestBody StockinQaInspectDetailRequest request) {
        return stockinQaInspectService.getInspectDetail(request);
    }


    @ApiOperation(value = "批量稽查列表查询", notes = "批量稽查列表查询", produces = "application/json")
    @PostMapping("/batch-inspect/list")
    public List<StockinQaInspectBatchResponse> batchInspectList(@Valid @RequestBody StockinQaInspectBatchRequest request) {
        return stockinQaInspectService.batchInspectList(request);
    }

    @ApiOperation(value = "分页查询全检任务", notes = "分页查询稽查任务", produces = "application/json")
    @PostMapping("/page/full-inspect-task")
    public PageResponse<StockinQaFullInspectTaskPageResponse> pageFullInspectTaskList(@RequestBody StockinQaFullInspectTaskPageRequest request) {
        return stockinQaFullInspectService.pageFullInspectTaskList(request);
    }


    @ApiOperation(value = "分页查询全检报告", notes = "分页查询稽查报告", produces = "application/json")
    @PostMapping("/page/full-inspect-result")
    public PageResponse<StockinQaFullInspectReportPageResponse> pageFullInspectResultList(@RequestBody StockinQaFullInspectReportPageRequest request) {
        return stockinQaFullInspectService.pageFullInspectReportList(request);
    }

    @ApiOperation(value = "查询稽查报告详情", notes = "查询稽查报告详情", produces = "application/json")
    @PostMapping("/get-full-inspect-info")
    public StockinQaFullInspectResultDetailResponse getFullInspectDetail(@RequestBody StockinQaInspectDetailRequest request) {
        return stockinQaFullInspectService.getFullInspectDetail(request);
    }

    @ApiOperation(value = "全检报告-统计数量", notes = "全检报告-统计数量", produces = "application/json")
    @PostMapping("/count-full-inspect-qty")
    public StockinQaInspectCountQtyResponse countFullInspectQty(@RequestBody StockinQaFullInspectReportPageRequest request) {
        return stockinQaFullInspectService.countFullInspectQty(request);
    }

    @ApiOperation(value = "aql", notes = "aql数量错误初始化", produces = "application/json")
    @PostMapping("/init-aql-count")
    public void initAqlCount() {
        stockinQaOrderLogService.initAqlCount();
    }
}
