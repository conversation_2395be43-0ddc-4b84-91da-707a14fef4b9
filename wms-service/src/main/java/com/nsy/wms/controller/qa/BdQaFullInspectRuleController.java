package com.nsy.wms.controller.qa;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.request.qa.BdQaFullInspectRuleSaveRequest;
import com.nsy.api.wms.request.qa.BdQaInspectPageRequest;
import com.nsy.api.wms.request.qa.BdQaInspectRuleStatusChangeRequest;
import com.nsy.api.wms.response.qa.BdQaFullInspectRuleInfoResponse;
import com.nsy.api.wms.response.qa.BdQaFullInspectRulePageResponse;
import com.nsy.wms.business.service.qa.BdQaFullInspectRuleService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 13:52
 */
@Api(tags = "qa全检配置")
@RestController
@RequestMapping("/qa/full-inspect")
public class BdQaFullInspectRuleController extends BaseController {

    @Autowired
    private BdQaFullInspectRuleService fullInspectRuleService;

    @ApiOperation(value = "分页查询", notes = "分页查询", produces = "application/json")
    @PostMapping("/page/list")
    public PageResponse<BdQaFullInspectRulePageResponse> pageList(@RequestBody BdQaInspectPageRequest params) {
        return fullInspectRuleService.pageList(params);
    }

    @ApiOperation(value = "查询详情", notes = "查询详情", produces = "application/json")
    @GetMapping("/get-info/{id}")
    public BdQaFullInspectRuleInfoResponse getDetail(@PathVariable("id") Integer id) {
        return fullInspectRuleService.getInspectRuleInfo(id);
    }

    @ApiOperation(value = "新增", notes = "新增数据")
    @PostMapping("/save/info")
    public void saveInfo(@Valid @RequestBody BdQaFullInspectRuleSaveRequest request) {
        fullInspectRuleService.saveInspectRuleInfo(request);
    }

    @ApiOperation(value = "停用/启用全检规则", notes = "停用/启用全检规则")
    @PostMapping("/status/change")
    public void statusChange(@RequestBody BdQaInspectRuleStatusChangeRequest request) {
        fullInspectRuleService.changeStatus(request.getId(), request.getIsDeleted());
    }

    @ApiOperation(value = "修改信息", notes = "修改全检规则")
    @PostMapping("/edit/inspect")
    public void editProcess(@RequestBody BdQaFullInspectRuleSaveRequest request) {
        fullInspectRuleService.updateInspectRuleInfo(request);
    }

    @ApiOperation(value = "是否存在全检规则", notes = "检查是否存在全检规则")
    @GetMapping("/exist/inspect")
    public Boolean existInspect() {
        return fullInspectRuleService.existInspect();
    }
} 