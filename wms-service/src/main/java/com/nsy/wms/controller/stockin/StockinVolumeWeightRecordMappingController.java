package com.nsy.wms.controller.stockin;

import com.nsy.api.wms.request.bd.BdVolumeWeightRecordSkuHistoryListRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingDetailRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSaveRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSkuPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingValidRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingDetailResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingLogResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageDetailResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordWaitMeasureResponse;
import com.nsy.wms.business.service.bd.BdVolumeWeightRecordService;
import com.nsy.wms.business.service.stockin.StockinVolumeWeightRecordMappingLogService;
import com.nsy.wms.business.service.stockin.StockinVolumeWeightRecordMappingService;
import com.nsy.wms.business.service.stockin.StockinVolumeWeightStandardRecordService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-04 16:30
 */
@Api(tags = "质检包装测量尺寸")
@RestController
public class StockinVolumeWeightRecordMappingController extends BaseController {

    @Autowired
    private StockinVolumeWeightRecordMappingService stockinVolumeWeightRecordMappingService;
    @Autowired
    private BdVolumeWeightRecordService weightBdVolumeWeightRecordService;
    @Autowired
    private StockinVolumeWeightRecordMappingLogService stockinVolumeWeightRecordMappingLogService;
    @Autowired
    private StockinVolumeWeightStandardRecordService standardRecordService;

    /**
     * 转换sku信息
     *
     * @param request
     */
    @RequestMapping(value = "/volume-weight-record-mapping/convert/sku", method = RequestMethod.POST)
    @ApiOperation(value = "转换sku信息", produces = "application/json")
    public String convertSku(@RequestBody StockinVolumeWeightRecordMappingValidRequest request) {
        return stockinVolumeWeightRecordMappingService.convertSku(request);
    }

    /**
     * 保存信息--体积重，高度通用
     *
     * @param request
     */
    @RequestMapping(value = "/volume-weight-record-mapping/save/info", method = RequestMethod.POST)
    @ApiOperation(value = "保存信息", produces = "application/json")
    public void saveMappingInfo(@RequestBody StockinVolumeWeightRecordMappingSaveRequest request) {
        stockinVolumeWeightRecordMappingService.saveMappingInfo(request);
    }

    /**
     * 校验是否已经测量过--体积重，高度通用
     *
     * @param request
     */
    @RequestMapping(value = "/volume-weight-record-mapping/valid/exist", method = RequestMethod.POST)
    @ApiOperation(value = "校验是否已经测量过", produces = "application/json")
    public Boolean validExistRecordMapping(@RequestBody StockinVolumeWeightRecordMappingValidRequest request) {
        return stockinVolumeWeightRecordMappingService.validExistRecordMapping(request);
    }

    /**
     * 获取工艺测量历史 -- 质检使用
     *
     * @param request
     */
    @RequestMapping(value = "/volume-weight-record-mapping/record/history", method = RequestMethod.POST)
    @ApiOperation(value = "获取工艺测量历史", produces = "application/json")
    public List<StockinVolumeWeightRecordMappingResponse> getVolumeWeightRecordMappingList(@RequestBody StockinVolumeWeightRecordMappingRequest request) {
        return stockinVolumeWeightRecordMappingService.getVolumeWeightRecordMappingList(request);
    }

    /**
     * 获取待测量的列表--体积重，高度通用
     *
     * @param internalBoxCode
     * @return
     */
    @RequestMapping(value = "/volume-weight-record-mapping/get-wait-measure-list/{internalBoxCode}", method = RequestMethod.GET)
    @ApiOperation(value = "扫描商品信息", produces = "application/json")
    public List<StockinVolumeWeightRecordWaitMeasureResponse> getWaitMeasureList(@Valid @PathVariable String internalBoxCode) {
        return stockinVolumeWeightRecordMappingService.getWaitMeasureList(internalBoxCode);
    }

    @RequestMapping(value = "/volume-weight-record-mapping/page/list", method = RequestMethod.POST)
    @ApiOperation(value = "测量列表分页", produces = "application/json")
    public PageResponse<StockinVolumeWeightRecordMappingPageResponse> getPageList(@RequestBody StockinVolumeWeightRecordMappingPageRequest request) {
        return stockinVolumeWeightRecordMappingService.getPageList(request);
    }

    @RequestMapping(value = "/volume-weight-record-mapping/sku/page/list", method = RequestMethod.POST)
    @ApiOperation(value = "测量列表分页--根据sku信息分页", produces = "application/json")
    public PageResponse<StockinVolumeWeightRecordMappingSkuPageResponse> getPageSkuList(@RequestBody StockinVolumeWeightRecordMappingSkuPageRequest request) {
        return stockinVolumeWeightRecordMappingService.getPageSkuList(request);
    }

    @RequestMapping(value = "/volume-weight-record-mapping/detail/page/list", method = RequestMethod.POST)
    @ApiOperation(value = "获取详情信息", produces = "application/json")
    public PageResponse<StockinVolumeWeightRecordMappingPageDetailResponse> getDetailPage(@RequestBody StockinVolumeWeightRecordMappingDetailRequest request) {
        return stockinVolumeWeightRecordMappingService.getDetailPage(request);
    }

    @RequestMapping(value = "/volume-weight-record-mapping/page-log/list", method = RequestMethod.POST)
    @ApiOperation(value = "测量日志列表分页", produces = "application/json")
    public PageResponse<StockinVolumeWeightRecordMappingLogResponse> getLogPageList(@RequestBody StockinVolumeWeightRecordMappingDetailRequest request) {
        return stockinVolumeWeightRecordMappingLogService.pageList(request);
    }


    /**
     * 获取工艺测量历史 -- 近3天
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/volume-weight-record/sku/history", method = RequestMethod.POST)
    @ApiOperation(value = "获取对应sku当天测量数据", produces = "application/json")
    public List<StockinVolumeWeightRecordMappingResponse> getVolumeWeightSkuHistoryList(@RequestBody BdVolumeWeightRecordSkuHistoryListRequest request) {
        return weightBdVolumeWeightRecordService.getVolumeWeightSkuHistoryList(request);
    }

    @RequestMapping(value = "/volume-weight-record-mapping/save-scan-log", method = RequestMethod.POST)
    @ApiOperation(value = "保存扫描日志", produces = "application/json")
    public void saveScanLog(@RequestBody StockinVolumeWeightRecordMappingRequest request) {
        stockinVolumeWeightRecordMappingService.saveScanLog(request);
    }

    @RequestMapping(value = "/volume-weight-record-mapping/measure/standard", method = RequestMethod.POST)
    @ApiOperation(value = "获取工艺测量标准", produces = "application/json")
    public StockinVolumeWeightRecordMappingResponse getMeasureStandard(@RequestBody StockinVolumeWeightRecordMappingValidRequest request) {
        return stockinVolumeWeightRecordMappingService.getMeasureStandard(request);
    }

    @RequestMapping(value = "/volume-weight-record-mapping/measure/standard/{standardId}", method = RequestMethod.POST)
    @ApiOperation(value = "获取工艺测量标准", produces = "application/json")
    public StockinVolumeWeightRecordMappingResponse getMeasureStandardById(@PathVariable("standardId") Integer standardId) {
        return standardRecordService.getStandInfoById(standardId);
    }


    @RequestMapping(value = "/volume-weight-record-mapping/get-detail", method = RequestMethod.POST)
    @ApiOperation(value = "获取详情信息", produces = "application/json")
    public StockinVolumeWeightRecordMappingDetailResponse getDetail(@RequestBody StockinVolumeWeightRecordMappingDetailRequest request) {
        return stockinVolumeWeightRecordMappingService.getDetail(request);
    }

    @ApiOperation(value = "初始化包装方式", notes = "批量初始化包装方式数据")
    @PostMapping("/init-package-name")
    public void initPackageName() {
        stockinVolumeWeightRecordMappingService.initPackageName();
    }

}
