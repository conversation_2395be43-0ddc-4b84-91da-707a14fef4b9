package com.nsy.wms.controller.stockin;

import com.nsy.api.wms.request.bd.BdVolumeWeightRecordSkuHistoryListRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingDetailRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSkuPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingPageDetailResponse;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse;
import com.nsy.wms.business.service.stockin.StockinHeightRecordMappingService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-17 17:44
 */
@Api(tags = "质检高度测量尺寸")
@RestController
public class StockinHeightRecordMappingController extends BaseController {

    @Autowired
    private StockinHeightRecordMappingService stockinHeightRecordMappingService;

    /**
     * 获取质检高度测量历史 -- 质检使用
     *
     * @param request
     */
    @RequestMapping(value = "/height-measure/record/history", method = RequestMethod.POST)
    @ApiOperation(value = "获取工艺测量历史", produces = "application/json")
    public List<StockinHeightRecordMappingResponse> getVolumeWeightRecordMappingList(@RequestBody StockinVolumeWeightRecordMappingRequest request) {
        return stockinHeightRecordMappingService.getHeightRecordMappingList(request);
    }

    /**
     * 获取工艺测量历史 -- 当天
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/height-measure/record/sku/history", method = RequestMethod.POST)
    @ApiOperation(value = "获取对应sku当天测量数据", produces = "application/json")
    public List<StockinHeightRecordMappingResponse> getHeightMeasureSkuHistoryList(@RequestBody BdVolumeWeightRecordSkuHistoryListRequest request) {
        return stockinHeightRecordMappingService.getHeightMeasureSkuHistoryList(request);
    }

    @RequestMapping(value = "/height-measure/page/list", method = RequestMethod.POST)
    @ApiOperation(value = "测量列表分页", produces = "application/json")
    public PageResponse<StockinVolumeWeightRecordMappingPageResponse> getPageList(@RequestBody StockinVolumeWeightRecordMappingPageRequest request) {
        return stockinHeightRecordMappingService.getPageList(request);
    }

    @RequestMapping(value = "/height-measure/sku/page/list", method = RequestMethod.POST)
    @ApiOperation(value = "测量列表分页--根据sku信息分页", produces = "application/json")
    public PageResponse<StockinVolumeWeightRecordMappingSkuPageResponse> getPageSkuList(@RequestBody StockinVolumeWeightRecordMappingSkuPageRequest request) {
        return stockinHeightRecordMappingService.getPageSkuList(request);
    }

    @RequestMapping(value = "/height-measure/detail/page/list", method = RequestMethod.POST)
    @ApiOperation(value = "获取详情信息", produces = "application/json")
    public PageResponse<StockinHeightRecordMappingPageDetailResponse> getDetailPage(@RequestBody StockinVolumeWeightRecordMappingDetailRequest request) {
        return stockinHeightRecordMappingService.getDetailPage(request);
    }
}
