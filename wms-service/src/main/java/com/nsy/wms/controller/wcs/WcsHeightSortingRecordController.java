package com.nsy.wms.controller.wcs;

import com.nsy.api.wms.request.wcs.WcsHeightSortingRecordAddRequest;
import com.nsy.api.wms.request.wcs.WcsHeightSortingRecordPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.wcs.WcsHeightSortingRecordPageResponse;
import com.nsy.wms.business.service.wcs.WcsHeightSortingRecordService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import javax.validation.Valid;

/**
 * 测量高度分拣记录Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "测量高度分拣记录")
@RestController
@RequestMapping("/wcs/height-sorting-record")
public class WcsHeightSortingRecordController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WcsHeightSortingRecordController.class);

    @Inject
    private WcsHeightSortingRecordService wcsHeightSortingRecordService;

    /**
     * 新增测量高度分拣记录
     *
     * @param request 新增请求参数
     * @return 新增记录的ID
     */
    @ApiOperation("新增测量高度分拣记录")
    @PostMapping
    public Integer addRecord(@Valid @RequestBody WcsHeightSortingRecordAddRequest request) {
        LOGGER.info("新增测量高度分拣记录，请求参数：{}", request);

        Integer recordId = wcsHeightSortingRecordService.addRecord(request);

        LOGGER.info("新增测量高度分拣记录成功，记录ID：{}", recordId);

        return recordId;
    }

    /**
     * 分页查询测量高度分拣记录
     *
     * @param request 分页查询请求参数
     * @return 分页查询结果
     */
    @ApiOperation(value = "分页查询", produces = "application/json")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<WcsHeightSortingRecordPageResponse> page(@RequestBody WcsHeightSortingRecordPageRequest request) {

        return wcsHeightSortingRecordService.page(request);
    }
} 