package com.nsy.wms.controller.logistics;

import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsBaseInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsReloadShipmentInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsShipmentInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsShipmentSummary;
import com.nsy.api.wms.domain.logistics.documents.request.DhlRequestInfo;
import com.nsy.api.wms.domain.logistics.documents.request.FedexRequestInfo;
import com.nsy.api.wms.domain.logistics.documents.request.LogisticsDocumentsBaseRequestInfo;
import com.nsy.api.wms.domain.logistics.documents.request.UpsRequestInfo;
import com.nsy.api.wms.request.logistics.doucments.LogisticsDocumentsPrintInfoRequest;
import com.nsy.api.wms.request.logistics.doucments.LogisticsDocumentsSaveShipmentRequest;
import com.nsy.api.wms.request.logistics.doucments.LogisticsDocumentsShipmentInfoListRequest;
import com.nsy.api.wms.request.logistics.doucments.PrintInvoiceRequest;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.StockoutOrderDetailResponse;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.service.logistics.documents.LogisticsDocumentsInvoiceService;
import com.nsy.wms.business.service.logistics.documents.LogisticsDocumentsPrintService;
import com.nsy.wms.business.service.logistics.documents.LogisticsDocumentsService;
import com.nsy.wms.business.service.stockout.StockoutInvoiceInfoService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "物流单证相关接口")
@RestController
public class LogisticsDocumentsController extends BaseController {

    @Autowired
    LogisticsDocumentsService logisticsDocumentsService;
    @Autowired
    LogisticsDocumentsInvoiceService documentsInvoiceService;
    @Autowired
    LogisticsDocumentsPrintService documentsPrintService;
    @Autowired
    StockoutInvoiceInfoService invoiceInfoService;

    @ApiOperation("获取物流信息")
    @RequestMapping(value = "/logistics-documents/get-base-logistics-company", method = RequestMethod.GET)
    public StockoutOrderDetailResponse getBaseLogisticsCompany(@RequestParam String stockoutOrderNo) {
        return documentsPrintService.getBaseLogisticsCompany(stockoutOrderNo);
    }

    @ApiOperation("获取单证页面基本信息：收件人、账号信息、州二字码")
    @RequestMapping(value = "/logistics-documents/base-info", method = RequestMethod.GET)
    @ApiImplicitParams({@ApiImplicitParam(name = "documentsPage", value = "哪个物流单证页面标识，值为：FedEx，UPS，DHL", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "stockoutOrderNo", value = "出库单号", dataType = "String", paramType = "query")})
    public LogisticsDocumentsBaseInfo getLogisticsDocumentsBaseInfo(@RequestParam String documentsPage, @RequestParam String stockoutOrderNo) {
        return logisticsDocumentsService.getLogisticsDocumentsBaseInfo(documentsPage, stockoutOrderNo);
    }

    @ApiOperation("获取单证页面装箱清单信息,报关分类分组，用于无纸化发票和时颖发票")
    @RequestMapping(value = "/logistics-documents/print-info/category", method = RequestMethod.POST)
    public LogisticsDocumentsPrintInfo getPrintInfoByCategory(@RequestBody LogisticsDocumentsPrintInfoRequest request) {
        return logisticsDocumentsService.getPrintInfoByCategory(request);
    }

    @ApiOperation("获取单证页面装箱清单信息,sku级别，用于详细发票")
    @RequestMapping(value = "/logistics-documents/print-info/sku", method = RequestMethod.POST)
    public LogisticsDocumentsPrintInfo getPrintInfoBySku(@RequestBody LogisticsDocumentsPrintInfoRequest request) {
        return logisticsDocumentsService.getPrintInfoBySku(request);
    }

    @ApiOperation("单证页面装箱清单信息重载查询")
    @RequestMapping(value = "/logistics-documents/shipment-info/reload", method = RequestMethod.POST)
    public List<LogisticsDocumentsReloadShipmentInfo> getShipmentInfoReloadList(@RequestBody LogisticsDocumentsShipmentInfoListRequest request) {
        return logisticsDocumentsService.getShipmentInfoReloadList(request);
    }

    @ApiOperation("当天发货汇总运单总票数、商品总数、运单总重量(kg)")
    @RequestMapping(value = "/logistics-documents/shipment-summary", method = RequestMethod.GET)
    public LogisticsDocumentsShipmentSummary getShipmentSummary(@RequestParam String logisticsCompany, @RequestParam String logisticsAccount) {
        return logisticsDocumentsService.getShipmentSummary(logisticsCompany, logisticsAccount);
    }

    @ApiOperation("装箱清单列表展示，用来回填箱规和重量")
    @RequestMapping(value = "/logistics-documents/shipment-info", method = RequestMethod.POST)
    public List<LogisticsDocumentsShipmentInfo> getShipmentInfoList(@RequestBody LogisticsDocumentsPrintInfoRequest request) {
        return logisticsDocumentsService.getShipmentInfoList(request);
    }

    @ApiOperation("装箱清单列表保存回填箱规和重量")
    @RequestMapping(value = "/logistics-documents/shipment-info", method = RequestMethod.PUT)
    public void saveShipmentBoxSizeAndWeight(@RequestBody LogisticsDocumentsSaveShipmentRequest request) {
        logisticsDocumentsService.saveShipmentBoxSizeAndWeight(request);
    }

    @ApiOperation("装箱清单列表保存体积重")
    @RequestMapping(value = "/logistics-documents/shipment-info/volumn-weight", method = RequestMethod.PUT)
    public void saveShipmentVolumeWeight(@RequestBody LogisticsDocumentsSaveShipmentRequest request) {
        logisticsDocumentsService.saveShipmentVolumeWeight(request);
    }

    @ApiOperation("联邦打印")
    @RequestMapping(value = "/logistics-documents/print-fedex", method = RequestMethod.POST)
    public BaseGetLogisticsNoResponse printFedex(@RequestBody FedexRequestInfo fedexInfo) {
        return documentsPrintService.printFedex(fedexInfo);
    }

    @ApiOperation("异步Fedex打印")
    @RequestMapping(value = "/logistics-documents/async-print-fedex", method = RequestMethod.POST)
    public AsyncProcessFlowResult asyncPrintFedex(@RequestBody FedexRequestInfo fedexInfo) {
        return documentsPrintService.asyncPrintFedex(fedexInfo);
    }

    @ApiOperation("Fedex打印轮询")
    @RequestMapping(value = "/logistics-documents/print-fedex-pollling/{flowId}", method = RequestMethod.POST)
    public AsyncProcessFlowResult printFedexPolling(@PathVariable Integer flowId) {
        return documentsPrintService.printFedexPolling(flowId);
    }

    @ApiOperation("DHL打印")
    @RequestMapping(value = "/logistics-documents/print-dhl", method = RequestMethod.POST)
    public BaseGetLogisticsNoResponse printDhl(@RequestBody DhlRequestInfo dhlInfo) {
        return documentsPrintService.printDhl(dhlInfo);
    }

    @ApiOperation("异步DHL打印")
    @RequestMapping(value = "/logistics-documents/async-print-dhl", method = RequestMethod.POST)
    public AsyncProcessFlowResult asyncPrintDhl(@RequestBody DhlRequestInfo dhlInfo) {
        return documentsPrintService.asyncPrintDhl(dhlInfo);
    }

    @ApiOperation("DHL打印轮询")
    @RequestMapping(value = "/logistics-documents/print-dhl-pollling/{flowId}", method = RequestMethod.POST)
    public AsyncProcessFlowResult printDhlPolling(@PathVariable Integer flowId) {
        return documentsPrintService.printDhlPolling(flowId);
    }

    @ApiOperation("DHL打印其他人发票")
    @RequestMapping(value = "/logistics-documents/print-others-invoice", method = RequestMethod.POST)
    public Map<String, List<String>> printOthersInvoice(@RequestBody DhlRequestInfo dhlInfo) {
        return documentsInvoiceService.printOthersInvoice(dhlInfo);
    }

    @ApiOperation("UPS打印")
    @RequestMapping(value = "/logistics-documents/print-ups", method = RequestMethod.POST)
    public BaseGetLogisticsNoResponse printUps(@RequestBody UpsRequestInfo upsInfo) {
        return logisticsDocumentsService.printUps(upsInfo);
    }

    @ApiOperation("异步UPS打印")
    @RequestMapping(value = "/logistics-documents/async-print-ups", method = RequestMethod.POST)
    public AsyncProcessFlowResult asyncPrintUps(@RequestBody UpsRequestInfo upsInfo) {
        return documentsPrintService.asyncPrintUps(upsInfo);
    }

    @ApiOperation("UPS打印轮询")
    @RequestMapping(value = "/logistics-documents/print-ups-pollling/{flowId}", method = RequestMethod.POST)
    public AsyncProcessFlowResult printUpsPolling(@PathVariable Integer flowId) {
        return documentsPrintService.printUpsPolling(flowId);
    }

    @ApiOperation("发票打印")
    @RequestMapping(value = "/logistics-documents/print-invoice", method = RequestMethod.POST)
    public Map<String, List<String>> printInvoice(@RequestBody PrintInvoiceRequest request) {
        return logisticsDocumentsService.printInvoice(request.getLogisticsNoList());
    }

    @ApiOperation("ups发联打印")
    @RequestMapping(value = "/logistics-documents/print-falian", method = RequestMethod.POST)
    public List<String> printFaLianByUps(@RequestBody PrintInvoiceRequest request) {
        return logisticsDocumentsService.printFaLianByUps(request.getLogisticsNoList());
    }

    @ApiOperation("ups发票和发联上传")
    @RequestMapping(value = "/logistics-documents/upload-falian-invoice", method = RequestMethod.PUT)
    public void uploadFaLianAndInvoiceByUps(@RequestParam String logisticsNo) {
        logisticsDocumentsService.uploadFaLianAndInvoiceByUps(logisticsNo);
    }

    @RequestMapping(value = "/async/logistics-documents/upload-falian-invoice", method = RequestMethod.POST)
    @ApiOperation(value = "ups发票和发联上传(异步)", produces = "application/json")
    public AsyncProcessFlowResult asyncUploadFaLianAndInvoiceByUps(@RequestParam String logisticsNo) {
        return logisticsDocumentsService.asyncPrintUps(logisticsNo);
    }

    @ApiOperation("ups发票上传")
    @RequestMapping(value = "/logistics-documents/upload-invoice", method = RequestMethod.POST)
    public void uploadInvoiceByUps(@RequestBody PrintInvoiceRequest request) {
        logisticsDocumentsService.uploadInvoiceByUps(request.getLogisticsNoList());
    }

    @ApiOperation("ups发联上传")
    @RequestMapping(value = "/logistics-documents/upload-falian", method = RequestMethod.POST)
    public void uploadFaLianByUps(@RequestBody PrintInvoiceRequest request) {
        logisticsDocumentsService.uploadFaLianByUps(request.getLogisticsNoList());
    }

    @ApiOperation("获取制单条目数")
    @RequestMapping(value = "/logistics-documents/invoice-item/{logisticsNo}", method = RequestMethod.GET)
    public LogisticsDocumentsBaseRequestInfo getItemListSizeByLogisticsNo(@PathVariable String logisticsNo) {
        return invoiceInfoService.getItemListSizeByLogisticsNo(logisticsNo);
    }
}
