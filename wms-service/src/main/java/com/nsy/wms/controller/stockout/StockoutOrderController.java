package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.domain.stockout.StockoutOrderListCount;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.PrintItemSkuRequest;
import com.nsy.api.wms.request.stockin.SyncMemoRequest;
import com.nsy.api.wms.request.stockout.ErpRetryPrintLabelRequest;
import com.nsy.api.wms.request.stockout.ErpSyncLogisticsRequest;
import com.nsy.api.wms.request.stockout.FactoryDirectShippingRequest;
import com.nsy.api.wms.request.stockout.PackBarcodePrintRequest;
import com.nsy.api.wms.request.stockout.PrematchCancelRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderAddRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderAreaQueryRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderCancelRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderGenerateWaveRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemCancelRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemListRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderListRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderLogisticsCompanyRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderNotifyShipStatusRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderOperateRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderPickingTypeRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderWorkspaceRequest;
import com.nsy.api.wms.request.stockout.StockoutUnFullPrematchRequest;
import com.nsy.api.wms.request.stockout.StockoutUpdateLogisticsNoRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.request.stockout.SyncCancelPickRequest;
import com.nsy.api.wms.request.stockout.SyncCustomerBarcodeRequest;
import com.nsy.api.wms.request.stockout.SyncIsUrgentRequest;
import com.nsy.api.wms.request.stockout.SyncReceiverInfoRequest;
import com.nsy.api.wms.request.stockout.SyncShipmentNoticeRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.AreaResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.GetSecondaryNumResponse;
import com.nsy.api.wms.response.stockout.StockoutGeneratedWaveResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderDetailResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderItemListResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderListResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderStatusResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentTransparencyCodeResponse;
import com.nsy.wms.business.manage.erp.request.ErpSyncIossRequest;
import com.nsy.wms.business.manage.erp.response.ErpBasePrintLabelResponse;
import com.nsy.wms.business.manage.erp.response.TmsWaybillCloudPrintResponse;
import com.nsy.wms.business.service.logistics.base.PrintService;
import com.nsy.wms.business.service.stock.StockPrematchRemoveService;
import com.nsy.wms.business.service.stock.StockPrematchService;
import com.nsy.wms.business.service.stockout.StockoutGenerateBatchService;
import com.nsy.wms.business.service.stockout.StockoutOrderCancelService;
import com.nsy.wms.business.service.stockout.StockoutOrderCountService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderMemoService;
import com.nsy.wms.business.service.stockout.StockoutOrderOperateService;
import com.nsy.wms.business.service.stockout.StockoutOrderPrintService;
import com.nsy.wms.business.service.stockout.StockoutOrderScanTaskService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutOrderShipService;
import com.nsy.wms.business.service.stockout.StockoutReceiverInfoService;
import com.nsy.wms.business.service.stockout.StockoutTransparencyCodeService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "出库单相关接口")
@RestController
public class StockoutOrderController extends BaseController {

    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService itemService;
    @Autowired
    StockoutGenerateBatchService stockoutGenerateBatchService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    PrintService printService;
    @Autowired
    StockoutOrderScanTaskService scanTaskService;
    @Autowired
    StockoutOrderOperateService stockoutOrderOperateService;
    @Autowired
    StockoutOrderCountService stockoutOrderCountService;
    @Autowired
    StockoutOrderPrintService stockoutOrderPrintService;
    @Autowired
    StockoutOrderShipService stockoutOrderShipService;
    @Autowired
    StockoutTransparencyCodeService transparencyCodeService;
    @Autowired
    StockoutOrderCancelService stockoutOrderCancelService;
    @Resource
    StockPrematchService stockPrematchService;
    @Autowired
    StockPrematchRemoveService stockPrematchRemoveService;
    @Resource
    StockoutOrderMemoService stockoutOrderMemoService;

    @ApiOperation(value = "出库单新增", produces = "application/json")
    @RequestMapping(value = "/stockout-order", method = RequestMethod.POST)
    @ResponseBody
    public void addStockoutOrderList(@RequestBody StockoutOrderAddRequest request) {
        stockoutOrderService.addOutOrder(request);
    }

    @ApiOperation(value = "出库单新增-同步", produces = "application/json")
    @RequestMapping(value = "/stockout-order-sync", method = RequestMethod.POST)
    @ResponseBody
    public void addStockoutOrderListSync(@RequestBody StockoutOrderAddRequest request) {
        stockoutOrderService.addOutOrderAsync(request);
    }

    @ApiOperation(value = "出库单列表", produces = "application/json")
    @RequestMapping(value = "/stockout-order/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutOrderListResponse> getStockoutOrderList(@RequestBody StockoutOrderListRequest request) {
        return stockoutOrderService.getOutOrderListByRequest(request);
    }

    @ApiOperation(value = "出库单统计数量", produces = "application/json")
    @RequestMapping(value = "/stockout-order/page-search-count", method = RequestMethod.POST)
    @ResponseBody
    public StockoutOrderListCount pageSearchCount(@RequestBody StockoutOrderListRequest request) {
        return stockoutOrderCountService.pageSearchCount(request);
    }

    @ApiOperation(value = "出库单状态对应数量", produces = "application/json")
    @RequestMapping(value = "/stockout-order/status-count", method = RequestMethod.GET)
    public StockoutOrderStatusResponse getStockoutOrderStatusCount() {
        return stockoutOrderService.getStatusCount();
    }

    @ApiOperation(value = "出库单详情", produces = "application/json")
    @RequestMapping(value = "/stockout-order/{id}", method = RequestMethod.GET)
    @ResponseBody
    public StockoutOrderDetailResponse getStockoutOrder(@PathVariable Integer id) {
        return stockoutOrderService.getOutOrderDetailById(id);
    }

    @ApiOperation(value = "出库单详情,根据stockoutOrderNo查找", produces = "application/json")
    @RequestMapping(value = "/stockout-order/no/{stockoutOrderNo}", method = RequestMethod.GET)
    @ResponseBody
    public StockoutOrderDetailResponse getDetailByOutOrderNo(@PathVariable String stockoutOrderNo) {
        return stockoutOrderService.getDetailByOutOrderNo(stockoutOrderNo);
    }

    @ApiOperation(value = "出库单明细列表", produces = "application/json")
    @RequestMapping(value = "/stockout-order-item/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutOrderItemListResponse> getStockoutOrderItemList(@RequestBody StockoutOrderItemListRequest request) {
        return itemService.getOrderItemListByRequest(request);
    }

    @ApiOperation(value = "出库单明细列表sku重量统计", produces = "application/json")
    @GetMapping("/stockout-order-item/sku-weight/{stockoutOrderNo}")
    public BigDecimal getItemWeight(@PathVariable String stockoutOrderNo) {
        return itemService.getItemWeight(stockoutOrderNo);
    }

    @ApiOperation(value = "出库单明细列表", produces = "application/json")
    @RequestMapping(value = "/stockout-order-item/list/no", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutOrderItemListResponse> getStockoutOrderItemListByNo(@RequestBody StockoutOrderItemListRequest request) {
        return itemService.getStockoutOrderItemListByNo(request);
    }

    @ApiOperation(value = "出库单明细列表-打印店铺商品", produces = "application/json")
    @RequestMapping(value = "/stockout-order-item/list/no/sku-print", method = RequestMethod.POST)
    public PrintListResponse itemSkuPrint(@RequestBody PrintItemSkuRequest request) {
        return itemService.itemSkuPrint(request);
    }

    @ApiOperation(value = "取消出库单", produces = "application/json")
    @RequestMapping(value = "/stockout-order/cancel-order", method = RequestMethod.POST)
    public List<String> cancelStockoutOrder(@RequestBody StockoutOrderCancelRequest request) {
        return request.getIdList().stream().map(stockoutOrderId -> stockoutOrderCancelService.cancelOutOrder(stockoutOrderId, request.getCancelType())).filter(StringUtils::hasText).collect(Collectors.toList());
    }

    @ApiOperation(value = "出库单列表设置物流", produces = "application/json")
    @RequestMapping(value = "/stockout-order-logistics-company", method = RequestMethod.POST)
    @ResponseBody
    public void updateStockoutOrderLogisticsCompany(@RequestBody StockoutOrderLogisticsCompanyRequest request) {
        stockoutOrderService.updateOutOrderLogisticsCompany(request);
    }

    @ApiOperation(value = "出库单列表设置拣货模式", produces = "application/json")
    @RequestMapping(value = "/stockout-order-picking-type", method = RequestMethod.POST)
    @ResponseBody
    public void updateStockoutOrderPickingType(@RequestBody StockoutOrderPickingTypeRequest request) {
        stockoutOrderService.updateOutOrderPickingType(request);
    }

    @ApiOperation(value = "出库单列表设置工作区域", produces = "application/json")
    @RequestMapping(value = "/stockout-order-workspace", method = RequestMethod.POST)
    @ResponseBody
    public void updateStockoutOrderWorkspace(@RequestBody StockoutOrderWorkspaceRequest request) {
        stockoutOrderService.updateOutOrderWorkspace(request);
    }

    @ApiOperation(value = "出库单列表设置发货通知", produces = "application/json")
    @RequestMapping(value = "/stockout-order-notify-ship-status", method = RequestMethod.POST)
    @ResponseBody
    public void updateStockoutOrderNotifyShipStatus(@RequestBody StockoutOrderNotifyShipStatusRequest request) {
        stockoutOrderService.updateOutOrderNotifyShipStatus(request);
    }

    @ApiOperation(value = "出库单列表生成复核任务", produces = "application/json")
    @RequestMapping(value = "/stockout-order-scan-task", method = RequestMethod.POST)
    @ResponseBody
    public void addStockoutOrderScanTask(@RequestBody StockoutOrderOperateRequest request) {
        scanTaskService.createScanTaskByStockoutOrderOperateRequest(request);
    }

    @ApiOperation(value = "强制完成出库单", produces = "application/json")
    @RequestMapping(value = "/stockout-order/force-done/{stockoutOrderId}", method = RequestMethod.PUT)
    public void forceDoneStockoutOrder(@PathVariable Integer stockoutOrderId) {
        stockoutOrderService.forceDoneOutOrder(stockoutOrderId);
    }

    @ApiOperation(value = "生成波次", produces = "application/json")
    @RequestMapping(value = "/stockout-order/generate-wave", method = RequestMethod.POST)
    public StockoutGeneratedWaveResponse generateWave(@RequestBody StockoutOrderGenerateWaveRequest request) {
        return stockoutGenerateBatchService.generateWaveFromStockoutOrder(request);
    }

    @ApiOperation(value = "设置急单同步", produces = "application/json")
    @RequestMapping(value = "/stockout-order/sync-is-urgent", method = RequestMethod.POST)
    public void syncIsUrgent(@RequestBody SyncIsUrgentRequest request) {
        stockoutOrderOperateService.syncIsUrgent(request);
    }

    @ApiOperation(value = "设置备注同步", produces = "application/json")
    @RequestMapping(value = "/stockout-order/sync-memo", method = RequestMethod.POST)
    public void syncMemo(@RequestBody SyncMemoRequest request) {
        stockoutOrderMemoService.syncMemo(request);
    }

    @ApiOperation(value = "同步修改收货人信息", produces = "application/json")
    @RequestMapping(value = "/stockout-order/sync-receiver-info", method = RequestMethod.POST)
    public void syncReceiverInfo(@RequestBody SyncReceiverInfoRequest request) {
        stockoutReceiverInfoService.syncReceiverInfo(request);
    }

    @ApiOperation(value = "同步修改客户条码信息", produces = "application/json")
    @RequestMapping(value = "/stockout-order/sync-customer-barcode", method = RequestMethod.POST)
    public void syncCustomerBarcode(@RequestBody SyncCustomerBarcodeRequest request) {
        itemService.syncCustomerBarcode(request);
    }

    @ApiOperation(value = "同步发货通知修改", produces = "application/json")
    @RequestMapping(value = "/stockout-order/sync-shipment-notice", method = RequestMethod.POST)
    public void syncShipmentNotice(@RequestBody SyncShipmentNoticeRequest request) {
        stockoutOrderOperateService.syncShipmentNotice(request);
    }

    @ApiOperation(value = "【ERP】打印异常重新获取面单", produces = "application/json")
    @RequestMapping(value = "/stockout-order/erp-retry-print-label", method = RequestMethod.POST)
    public String erpRetryPrintLabel(@RequestBody ErpRetryPrintLabelRequest request) {
        return printService.erpRetryPrintLabel(request);
    }

    @ApiOperation(value = "【ERP】获取面单", produces = "application/json")
    @RequestMapping(value = "/stockout-order/erp-re-print-label", method = RequestMethod.POST)
    public ErpBasePrintLabelResponse erpRePrintLabel(@RequestBody ErpRetryPrintLabelRequest request) {
        return printService.erpRePrintLabel(request);
    }

    @ApiOperation(value = "【ERP】同步物流公司", produces = "application/json")
    @RequestMapping(value = "/stockout-order/erp-change-logistics", method = RequestMethod.POST)
    public void erpChangeLogistics(@RequestBody ErpSyncLogisticsRequest request) {
        stockoutOrderOperateService.erpChangeLogistics(request);
    }

    @ApiOperation(value = "【ERP】erp进行菜鸟打印", produces = "application/json")
    @RequestMapping(value = "/stockout-order/erp-re-print-label-cn", method = RequestMethod.POST)
    public TmsWaybillCloudPrintResponse erpRePrintLabelCN(@RequestBody ErpRetryPrintLabelRequest request) {
        return printService.erpRePrintLabelCN(request);
    }

    @ApiOperation(value = "校验是否能取消拣货单", produces = "application/json")
    @RequestMapping(value = "/stockout-order/check-cancel-pick", method = RequestMethod.POST)
    public boolean checkCancelPick(@RequestBody SyncCancelPickRequest request) {
        return stockoutOrderOperateService.checkCancelPick(request);
    }

    @ApiOperation(value = "erp拣货单取消同步", produces = "application/json")
    @RequestMapping(value = "/stockout-order/sync-cancel-pick", method = RequestMethod.POST)
    public void syncCancelPick(@RequestBody SyncCancelPickRequest request) {
        stockoutOrderCancelService.syncCancelPick(request);
    }

    @ApiOperation(value = "校验是否能回退订单", produces = "application/json")
    @RequestMapping(value = "/stockout-order/check-back-trade", method = RequestMethod.POST)
    public boolean checkBackTrade(@RequestBody SyncCancelPickRequest request) {
        return stockoutOrderOperateService.checkBackTrade(request);
    }

    @ApiOperation(value = "手动获取面单", produces = "application/json")
    @RequestMapping(value = "/stockout-order/get-order-label", method = RequestMethod.POST)
    public void getTmsLabelList(@RequestBody StringListRequest request) {
        printService.getTmsLabelList(request);
    }

    @ApiOperation(value = "出库单打印", produces = "application/json")
    @RequestMapping(value = "/stockout-order/print-order-a4", method = RequestMethod.POST)
    public PrintListResponse printPickingBoxCode(@Valid @RequestBody IdListRequest request) {
        return stockoutOrderPrintService.printStockoutOrderA4(request);
    }

    /**
     * 打印有备注的配货单，如果没有就返回空
     *
     * <AUTHOR>
     * 2022-06-13
     */
    @ApiOperation(value = "出库单打印换码", produces = "application/json")
    @RequestMapping(value = "/stockout-order/print-replace-sku", method = RequestMethod.POST)
    public PrintListResponse printStockoutOrderReplaceSku(@Valid @RequestBody StringListRequest orderNos) {
        return stockoutOrderPrintService.printStockoutOrderReplaceSku(orderNos);
    }

    /**
     * 查询出库单备注，如果没有就返回空
     *
     * <AUTHOR>
     * 2022-06-13
     */
    @ApiOperation(value = "弹窗出库单-仓库备注", produces = "application/json")
    @RequestMapping(value = "/stockout-order/order-space-memo/{stockOrderNo}", method = RequestMethod.GET)
    public List<String> printStockoutOrderReplaceSku(@PathVariable String stockOrderNo) {
        return stockoutOrderPrintService.getOrderSpaceMemoShow(stockOrderNo);
    }

    /**
     * 查询出库单备注，如果没有就返回空
     */
    @ApiOperation(value = "弹窗出库单-业务备注", produces = "application/json")
    @RequestMapping(value = "/stockout-order/order-seller-memo", method = RequestMethod.POST)
    public String getStockoutOrderBusinessMark(@RequestBody @Valid StringListRequest request) {
        return stockoutOrderPrintService.getStockoutOrderBusinessMark(request.getStringList());
    }

    // 海外发货预留接口
    @ApiOperation(value = "海外发货预留", produces = "application/json")
    @RequestMapping(value = "/stockout-order/ship-oversea/{stockOrderNo}", method = RequestMethod.POST)
    @Deprecated
    public void shipOversea(@PathVariable String stockOrderNo, @RequestBody GetSecondaryNumResponse secondaryNumberResponse) {
        StockoutOrderEntity stockoutOrderNo = stockoutOrderService.getByStockoutOrderNo(stockOrderNo);
        if (StockoutOrderTypeEnum.OVERSEA_DELIVERY.name().equals(stockoutOrderNo.getStockoutType())
                && StockoutOrderStatusEnum.READY_OUTBOUND.name().equals(stockoutOrderNo.getStatus()))
            stockoutOrderShipService.shipOversea(stockoutOrderNo, secondaryNumberResponse);
    }

    @ApiOperation(value = "出库单打印pack关系(返回html)", produces = "application/json")
    @RequestMapping(value = "/stockout-order/print-pack-sku", method = RequestMethod.POST)
    public PrintListResponse printPackInfo(@Valid @RequestBody StringListRequest orderNos) {
        return stockoutOrderPrintService.printPackInfo(orderNos);
    }

    @ApiOperation(value = "出库单打印pack条码(返回pdf)", produces = "application/json")
    @RequestMapping(value = "/stockout-order/print-pack-barcode", method = RequestMethod.POST)
    public PrintListResponse printPackBarcode(@Valid @RequestBody PackBarcodePrintRequest orderNos) {
        return stockoutOrderPrintService.printPackBarcode(orderNos);
    }

    @ApiOperation(value = "根据stockoutOrderNos查找通知发货状态", produces = "application/json")
    @RequestMapping(value = "/stockout-order/getNotifyShipStatus", method = RequestMethod.POST)
    @ResponseBody
    public String isWaitNoticDelivery(@Valid @RequestBody StringListRequest stockoutOrderNos) {
        return stockoutOrderService.getNotifyShipStatus(stockoutOrderNos);
    }

    @ApiOperation(value = "出库单批量修改物流单号", produces = "application/json")
    @RequestMapping(value = "/stockout-order/change-logistics-no-batch", method = RequestMethod.POST)
    public void changeLogisticsNoBatch(@Valid @RequestBody StockoutUpdateLogisticsNoRequest request) {
        stockoutOrderOperateService.changeLogisticsNoBatch(request);
    }

    @ApiOperation(value = "获取拣货单对应的t code使用情况", notes = "t code使用情况", produces = "application/json")
    @RequestMapping(value = "/stockout-order/transparency-code/use-info", method = RequestMethod.POST)
    public StockoutShipmentTransparencyCodeResponse getTransparencyCodeInfo(@RequestBody IdListRequest request) {
        return transparencyCodeService.getTransparencyCodeInfo(request);
    }

    @ApiOperation(value = "同步IOSS", notes = "同步IOSS", produces = "application/json")
    @RequestMapping(value = "/stockout-order/sync-ioss", method = RequestMethod.POST)
    public void syncIOSS(@Valid @RequestBody ErpSyncIossRequest request) {
        stockoutOrderOperateService.syncIOSS(request);
    }

    @ApiOperation(value = "出库单-预配", produces = "application/json")
    @RequestMapping(value = "/stockout-order-prematch", method = RequestMethod.POST)
    public void prematchStockoutOrder(@RequestBody IdListRequest request) {
        stockPrematchService.prematchStockoutOrder(request);
    }

    @ApiOperation(value = "出库单-取消预配", produces = "application/json")
    @RequestMapping(value = "/stockout-order-prematch-cancel", method = RequestMethod.POST)
    public void prematchCancel(@RequestBody IdListRequest request) {
        stockPrematchRemoveService.cancelPrematch(request.getIdList());
    }

    @ApiOperation(value = "出库单-不完全预配", produces = "application/json")
    @RequestMapping(value = "/stockout-order-un-full-prematch", method = RequestMethod.POST)
    public String unFullPrematch(@RequestBody StockoutUnFullPrematchRequest request) {
        return stockPrematchService.unFullPrematch(request);
    }

    @ApiOperation(value = "出库单-打印出库单TCode", produces = "application/json")
    @RequestMapping(value = "/stockout-order/print-all-t-code", method = RequestMethod.POST)
    public PrintListResponse printTCodeByStockoutOrder(@RequestBody IdListRequest request) {
        return transparencyCodeService.printTCodeByStockoutOrder(request.getIdList());
    }

    @ApiOperation(value = "出库单-取消明细数量-通过出库单明细ID", produces = "application/json")
    @RequestMapping(value = "/stockout-order-prematch-cancel/by-stockout-order-item-id", method = RequestMethod.POST)
    public void stockoutOrderItemCancel(@RequestBody StockoutOrderItemCancelRequest request) {
        stockPrematchRemoveService.stockoutOrderItemCancel(request);
    }

    @ApiOperation(value = "出库单-取消明细数量-通过预配主键ID", produces = "application/json")
    @RequestMapping(value = "/stockout-order-prematch-cancel/by-stock-prematch-id", method = RequestMethod.POST)
    public void stockoutOrderItemCancelByPrematchId(@RequestBody PrematchCancelRequest request) {
        request.getCancelItemList().stream().filter(item -> Objects.nonNull(item.getCancelQty())).forEach(item -> {
            stockPrematchRemoveService.stockoutOrderItemCancelByPrematchId(item);
        });
    }

    @ApiOperation(value = "工厂直发wms数据生成", produces = "application/json")
    @RequestMapping(value = "/stockout-order/factory-shipping-data", method = RequestMethod.POST)
    public void generateFactoryShippingData(@RequestBody FactoryDirectShippingRequest request) {
        stockoutOrderOperateService.generateFactoryShippingData(request);
    }

    @ApiOperation(value = "通过出库单号和sku获取areaId列表", produces = "application/json")
    @RequestMapping(value = "/stockout-order/area-ids", method = RequestMethod.POST)
    @ResponseBody
    public List<AreaResponse> getAreaIdsByStockoutOrderNoAndSku(@Valid @RequestBody StockoutOrderAreaQueryRequest request) {
        return itemService.getAreaIdsByStockoutOrderNoAndSku(request.getStockoutOrderNo(), request.getSku());
    }
}
