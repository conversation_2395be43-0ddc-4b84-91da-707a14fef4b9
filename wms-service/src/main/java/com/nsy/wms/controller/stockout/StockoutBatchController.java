package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.domain.stockout.StockoutBatchLog;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.StockoutBatchLogListRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchLabelPrintRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchListRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchOperateRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchOperateScanTypeRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchOperateSplictTypeRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchOrderRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchPickingTaskRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchCountResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchListCountResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchOrderResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchPickingTaskResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchResponse;
import com.nsy.wms.business.service.stockout.StockoutBatchCountService;
import com.nsy.wms.business.service.stockout.StockoutBatchListService;
import com.nsy.wms.business.service.stockout.StockoutBatchLogService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderService;
import com.nsy.wms.business.service.stockout.StockoutBatchPrintService;
import com.nsy.wms.business.service.stockout.StockoutBatchService;
import com.nsy.wms.business.service.stockout.StockoutBatchSplitTaskService;
import com.nsy.wms.business.service.stockout.StockoutPickingTaskPrintService;
import com.nsy.wms.business.service.stockout.StockoutPickingTaskService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "波次列表接口")
@RestController
public class StockoutBatchController extends BaseController {

    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutBatchOrderItemService bdBatchGenerateRuleItemService;
    @Autowired
    StockoutBatchLogService stockoutBatchLogService;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockoutBatchSplitTaskService batchSplitTaskService;
    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;
    @Autowired
    StockoutBatchPrintService batchPrintService;
    @Autowired
    StockoutBatchCountService stockoutBatchCountService;
    @Autowired
    StockoutBatchListService stockoutBatchListService;
    @Autowired
    StockoutPickingTaskPrintService taskPrintService;

    @ApiOperation(value = "查询波次状态数量", notes = "查询波次状态数量", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/count", method = RequestMethod.GET)
    public List<StockoutBatchCountResponse> getStockoutBatchCount() {
        return stockoutBatchService.getStockoutBatchCount();
    }

    @ApiOperation(value = "查询波次列表", notes = "查询波次列表", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/list", method = RequestMethod.POST)
    public PageResponse<StockoutBatchResponse> getStockoutBatchList(@Valid @RequestBody StockoutBatchListRequest request) {
        return stockoutBatchListService.getStockoutBatchList(request);
    }

    @ApiOperation(value = "查询波次列表统计", notes = "查询波次列表统计", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/page-search-count", method = RequestMethod.POST)
    public StockoutBatchListCountResponse pageSearchCount(@Valid @RequestBody StockoutBatchListRequest request) {
        return stockoutBatchCountService.pageSearchCount(request);
    }

    // 查询波次详情
    @RequestMapping(value = "/stockout-batch/{batchId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询波次详情", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "batchId", value = "波次号", dataType = "Integer", paramType = "path")})
    public StockoutBatchResponse getStockoutBatch(@Valid @PathVariable Integer batchId) {
        return stockoutBatchService.getStockoutBatch(batchId);
    }

    // 查询波次出库单信息
    @RequestMapping(value = "/stockout-batch-order", method = RequestMethod.POST)
    @ApiOperation(value = "查询波次出库单信息", produces = "application/json")
    public PageResponse<StockoutBatchOrderResponse> getStockoutBatchOrder(@Valid @RequestBody StockoutBatchOrderRequest request) {
        return stockoutBatchService.getStockoutBatchOrder(request);
    }

    // 查询波次拣货任务
    @RequestMapping(value = "/stockout-batch-picking-task", method = RequestMethod.POST)
    @ApiOperation(value = "查询波次拣货任务", produces = "application/json")
    public List<StockoutBatchPickingTaskResponse> getStockoutBatchPickingTask(@Valid @RequestBody StockoutBatchPickingTaskRequest request) {
        return stockoutBatchService.getStockoutBatchPickingTask(request);
    }


    @ApiOperation(value = "生成拣货任务", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/picking-task", method = RequestMethod.POST)
    public void generatePickingTask(@Valid @RequestBody StockoutBatchOperateRequest request) {
        request.getBatchIdList().forEach(batchId -> {
            stockoutBatchService.generatePickingTask(batchId);
        });
    }

    @ApiOperation(value = "设置分拣类型", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/split-type", method = RequestMethod.PUT)
    public void setBatchSplitType(@Valid @RequestBody StockoutBatchOperateSplictTypeRequest request) {
        stockoutBatchService.setBatchSplitType(request);
    }

    @ApiOperation(value = "设置扫描台", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/scan-type", method = RequestMethod.PUT)
    public void setScanType(@Valid @RequestBody StockoutBatchOperateScanTypeRequest request) {
        stockoutBatchService.setScanType(request);
    }

    @ApiOperation(value = "合并波次", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/merge-batch", method = RequestMethod.PUT)
    public void mergeBatch(@Valid @RequestBody StockoutBatchOperateRequest request) {
        stockoutBatchService.mergeBatch(request);
    }


    @ApiOperation(value = "撤销合并波次", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/undo-merge-batch", method = RequestMethod.PUT)
    public void undoMergeBatch(@Valid @RequestBody StockoutBatchOperateRequest request) {
        stockoutBatchService.undoMergeBatch(request);
    }

    @ApiOperation(value = "取消波次", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/cancel-batch", method = RequestMethod.PUT)
    public void cancelBatch(@Valid @RequestBody StockoutBatchOperateRequest request) {
        stockoutBatchService.cancelBatch(request);
    }

    @ApiOperation(value = "取消拣货任务", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/cancel-picking-task", method = RequestMethod.PUT)
    public void cancelPickingTask(@Valid @RequestBody StockoutBatchOperateRequest request) {
        stockoutBatchService.cancelPickingTask(request);
    }


    @ApiOperation(value = "生成分拣任务", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/split-task", method = RequestMethod.PUT)
    public void generateSplitTask(@Valid @RequestBody StockoutBatchOperateRequest request) {
        stockoutBatchService.generateSplitTask(request);
    }

    // 波次日志信息
    @RequestMapping(value = "/stockout-batch/log/list", method = RequestMethod.POST)
    @ApiOperation(value = "波次日志信息", produces = "application/json")
    public PageResponse<StockoutBatchLog> logList(@RequestBody StockoutBatchLogListRequest request) {
        return stockoutBatchLogService.logList(request);
    }

    @ApiOperation(value = "拣货箱号打印", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/print-picking-box", method = RequestMethod.POST)
    public PrintListResponse printPickingBoxCode(@Valid @RequestBody IdListRequest request) {
        return batchPrintService.printPickingBoxCode(request);
    }

    @ApiOperation(value = "已单找货（小包打印）", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/print-label", method = RequestMethod.POST)
    public PrintListResponse batchPrintLabel(@Valid @RequestBody IdListRequest request) {
        return batchOrderService.batchPrintLabel(request);
    }

    @ApiOperation(value = "已单找货（未生成波次打印-直接发货）", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/print-label-before-batch", method = RequestMethod.POST)
    public PrintListResponse printLabelBeforeBatch(@Valid @RequestBody StockoutBatchLabelPrintRequest request) {
        return batchOrderService.printLabelBeforeBatch(request);
    }

    @ApiOperation(value = "拣货任务A4打印", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/print-picking-task-a4", method = RequestMethod.POST)
    public PrintListResponse printPickingTaskA4(@Valid @RequestBody IdListRequest request) {
        return taskPrintService.printPickingTaskA4ByBatchId(request);
    }

    @ApiOperation(value = "加工拣货任务A4打印", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/print-process-picking-task-a4", method = RequestMethod.POST)
    public PrintListResponse printProcessPickingTaskA4ByBatchId(@Valid @RequestBody IdListRequest request) {
        return batchPrintService.printProcessPickingTaskA4ByBatchId(request);
    }

    @ApiOperation(value = "热敏拣货任务打印", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/print-picking-task", method = RequestMethod.POST)
    public PrintListResponse printPickingTasks(@Valid @RequestBody IdListRequest request) {
        return batchPrintService.printPickingTaskInBatch(request);
    }

    @ApiOperation(value = "出库单打印", produces = "application/json")
    @RequestMapping(value = "/stockout-batch/print-order-a4", method = RequestMethod.POST)
    public PrintListResponse printStockoutOrderA4(@Valid @RequestBody IdListRequest request) {
        return batchPrintService.printStockoutOrderA4(request);
    }
}
