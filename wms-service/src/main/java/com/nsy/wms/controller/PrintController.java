package com.nsy.wms.controller;

import com.nsy.api.wms.request.print.PrintDistributionRequest;
import com.nsy.wms.business.service.print.PrintFactory;
import com.nsy.wms.utils.mp.TenantContext;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "打印分发接口")
@RestController
public class PrintController extends BaseController {

    @Autowired
    private PrintFactory printFactory;

    /**
     * 分发下载数据的请求并返回结果
     */
    @RequestMapping(value = "/print/distribution", method = RequestMethod.POST)
    public List<Map<String, Object>> downloadDistribution(@RequestBody PrintDistributionRequest request) {
        TenantContext.setTenant(request.getLocation());
        return printFactory.handle(request, request.getTypeService());
    }
}
