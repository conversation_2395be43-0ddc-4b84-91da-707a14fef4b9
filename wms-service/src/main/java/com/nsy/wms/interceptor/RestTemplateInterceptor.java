package com.nsy.wms.interceptor;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <h3>RestTemplate 拦截器</h3>
 *
 * <AUTHOR>
 * @since 2024/07/17 10:59
 */
@Component
public class RestTemplateInterceptor implements ClientHttpRequestInterceptor {

    public static final String AUTH_TYPE_HEADER = "X-Auth-Type";
    public static final String QYF_AUTH_TYPE = "1";

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        /// 获取请求头
        HttpHeaders headers = request.getHeaders();
        // 加入请求头信息
        headers.add(AUTH_TYPE_HEADER, QYF_AUTH_TYPE);
        // 执行请求
        return execution.execute(request, body);
    }
}