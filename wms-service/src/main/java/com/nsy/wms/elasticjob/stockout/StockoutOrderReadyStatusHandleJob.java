package com.nsy.wms.elasticjob.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.constants.TmsCommonConstant;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.LogisticsTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderReadyStatusHandleEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderGetLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.ready.IStockoutOrderReadyStatusHandleService;
import com.nsy.wms.business.service.stockout.ready.StockoutOrderReadyStatusHandlerService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/4 9:04
 */
@Component
public class StockoutOrderReadyStatusHandleJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderReadyStatusHandleJob.class);

    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    private StockoutOrderReadyStatusHandlerService handlerService;
    @Autowired
    TmsCacheService tmsCacheService;
    @Autowired
    private BdSpaceService bdSpaceService;
    @Autowired
    private StockoutOrderGetLabelService orderGetLabelService;
    @Autowired
    private StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    private LoginInfoService loginInfoService;


    /**
     * 准备中处理job根据更新时间正序。每次处理100条
     * 1.平台=Shein,调用shein平台获取条码，如果遇到失败则记录错误信息状态仍为准备中，成功则为待生成波次
     * 2.平台=TikTokShop，调用tk发货以及获取条码颜色，成功则修改为待生成波次，失败修改为获取面单失败
     * 3.平台=TTITOK_LOCAL_SHIPPING（tk直邮）调用tk发货以及获取面单，成功则修改为待生成波次，失败修改为获取面单失败
     * 4.平台=OTTO 调用otto平台创建订单后调用tms创建订单，如果都成功则修改为待出库，失败则仍为准备中
     * 5.需要透明计划且工作区域为FAB则获取T-CODE,如果tcode不足则自动补充状态修改为Tcode申请中，数量足够则修改为待生成波次
     * 6.如果出库类型为第三方出库，则推送对应订单到对应的海外仓，状态修改为待出库
     * 7.如果物流不为"海运货代", "陆运货代", "铁运货代", "空运货代", "货代" 且物流类型则国际小包或者货代，则推送到对应的物流商获取物流面单，状态修改为待生成波次
     * 8.如以上都不满足则直接到待生成波次
     *
     * @param jobDataMap
     * @throws Exception
     */
    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockoutOrderReadyStatusHandleJob start");
        QueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.READY)
                .ne(StockoutOrderEntity::getPlatformName, StockoutOrderPlatformEnum.PDD.getName())
                .ne(StockoutOrderEntity::getPlatformName, StockoutOrderPlatformEnum.TEMU_POP.getName())
                .orderByAsc(StockoutOrderEntity::getUpdateDate);
        IPage<StockoutOrderEntity> page = stockoutOrderService.page(new Page<>(1, 100), queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) return;
        Map<String, TmsLogisticsCompany> allLogisticsCompanyMap = tmsCacheService.getAllLogisticsCompanyWithoutStatus(TenantContext.getTenant());
        BdSpaceEntity spaceEntity = bdSpaceService.getBySpaceName(SpaceAreaMapConstant.WmsSpace.OTTO_SPACE);
        LoginInfoService.setName("readyStatusHandleJob");
        page.getRecords().forEach(detail -> {
            try {
                TmsLogisticsCompany tmsLogisticsCompany = allLogisticsCompanyMap.get(detail.getLogisticsCompany());
                // 前置校验，如果报错，直接到失败面单
                handlerService.validBefore(detail);
                this.getHandlerService(detail, spaceEntity, tmsLogisticsCompany).handleOrder(detail);
            } catch (Exception e) {
                StockoutOrderEntity order = stockoutOrderService.getByStockoutOrderId(detail.getStockoutOrderId());
                if (StockoutOrderPlatformEnum.SHEIN.getName().equals(order.getPlatformName())
                        || StockoutOrderPlatformEnum.OTTO.getName().equals(order.getPlatformName())) { // shein 只记录错误信息
                    String msg = org.springframework.util.StringUtils.hasText(e.getMessage()) && !Objects.equals(e.getMessage(), "null") ? e.getMessage() : org.apache.commons.lang3.StringUtils.substring(e.toString(), 0, 300);
                    detail.setUpdateDate(new Date());
                    detail.setUpdateBy(loginInfoService.getName());
                    stockoutOrderService.updateById(detail);
                    stockoutOrderLogService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.GET_LOGISTICS_FAIL_RECORD, String.format("job获取物流失败：%s，更改出库单状态:获取面单失败", msg));
                } else if (StrUtil.equalsIgnoreCase(order.getStatus(), StockoutOrderStatusEnum.READY.name())) {
                    orderGetLabelService.doException(order, e);
                    // 更新出库单失败
                    orderGetLabelService.buildErrorStatus(order);
                }
                LOGGER.error(String.format("准备中订单处理失败,出库单号为:%s", detail.getStockoutOrderNo()), e);
            }
        });
        LoginInfoService.removeName();
        LOGGER.info("StockoutOrderReadyStatusHandleJob end");
    }

    /**
     * 获取处理实现类
     *
     * @param stockoutOrderEntity
     * @return
     */
    private IStockoutOrderReadyStatusHandleService getHandlerService(StockoutOrderEntity stockoutOrderEntity, BdSpaceEntity spaceEntity, TmsLogisticsCompany company) {
        StockoutOrderReadyStatusHandleEnum handleEnum;
        if (StringUtils.hasText(stockoutOrderEntity.getPlatformName()) && StockoutOrderPlatformEnum.SHEIN.getName().equals(stockoutOrderEntity.getPlatformName())) {
            handleEnum = StockoutOrderReadyStatusHandleEnum.SHEIN;
        } else if (StringUtils.hasText(stockoutOrderEntity.getPlatformName()) && StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(stockoutOrderEntity.getPlatformName())) {
            handleEnum = StockoutOrderReadyStatusHandleEnum.TIKTOKSHOP;
        } else if (StringUtils.hasText(stockoutOrderEntity.getPlatformName()) && StockoutOrderPlatformEnum.TIKTOK_LOCAL_SHIPPING.getName().equals(stockoutOrderEntity.getPlatformName())) {
            handleEnum = StockoutOrderReadyStatusHandleEnum.TTITOK_LOCAL_SHIPPING;
        } else if (!Objects.isNull(stockoutOrderEntity.getSpaceId()) && !Objects.isNull(spaceEntity) && spaceEntity.getSpaceId().equals(stockoutOrderEntity.getSpaceId())) {
            handleEnum = StockoutOrderReadyStatusHandleEnum.OTTO;
        } else if (stockoutOrderItemService.validTransparency(stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderItemService.getListByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo()))
                && StockoutOrderWorkSpaceEnum.FBA_AREA.name().equalsIgnoreCase(stockoutOrderEntity.getWorkspace())) {
            handleEnum = StockoutOrderReadyStatusHandleEnum.TRANSPARENCY;
        } else if (StockoutOrderTypeEnum.OVERSEA_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())) {
            handleEnum = StockoutOrderReadyStatusHandleEnum.OVERSEA;
        } else if (company != null && !TmsCommonConstant.NO_GET_LABEL_LOGISTICS.contains(company.getLogisticsMethod())
                && !StrUtil.equalsAnyIgnoreCase(company.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name(), LogisticsTypeEnum.DOMESTIC_EXPRESS.name())) {
            handleEnum = StockoutOrderReadyStatusHandleEnum.NORMAL;
        } else {
            // 如果以上的方法都不能处理，那么直接到待生成波次
            handleEnum = StockoutOrderReadyStatusHandleEnum.PASS;
        }
        return handlerService.getSupportService(handleEnum, stockoutOrderEntity.getStockoutOrderNo());
    }

}
