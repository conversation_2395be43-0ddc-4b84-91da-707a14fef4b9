package com.nsy.wms.elasticjob.stockout;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderTemuPopExtendService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuPopExtendEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * TemuPop创建物流发货任务
 * 获取所有平台是TemuPop的准备中的出库单并且packageSn为空的创建物流发货
 */
@Component
public class TemuPopCreateShipmentJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(TemuPopCreateShipmentJob.class);

    public static final String JOB_NAME = "TemuPopCreateShipmentJob";

    @Resource
    private StockoutOrderTemuPopExtendService temuPopExtendService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("TemuPopCreateShipmentJob start");
        LoginInfoService.setName(JOB_NAME);
        try {
            processCreateShipment();
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        } finally {
            LOGGER.info(JOB_NAME);
            LoginInfoService.removeName();
            LOGGER.info("TemuPopCreateShipmentJob end");
        }
    }

    /**
     * 处理创建物流发货
     */
    private void processCreateShipment() {
        // 查询需要创建物流发货的出库单
        List<StockoutOrderTemuPopExtendEntity> orderList =
                temuPopExtendService.listOrdersForCreateShipment();

        if (CollectionUtils.isEmpty(orderList)) {
            LOGGER.info("没有需要创建物流发货的TemuPop出库单");
            return;
        }

        LOGGER.info("找到{}个需要创建物流发货的TemuPop出库单", orderList.size());

        // 逐个处理创建物流发货
        for (StockoutOrderTemuPopExtendEntity extendEntity : orderList) {
            try {
                temuPopExtendService.createShipment(extendEntity);
            } catch (Exception e) {
                LOGGER.error("创建物流发货失败，出库单号: {}, 错误: {}",
                        extendEntity.getStockoutOrderNo(), e.getMessage(), e);
            }
        }

        LOGGER.info("TemuPop创建物流发货处理完成，共处理{}个订单", orderList.size());
    }
} 