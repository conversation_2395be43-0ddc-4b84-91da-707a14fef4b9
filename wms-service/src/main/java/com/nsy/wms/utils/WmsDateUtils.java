package com.nsy.wms.utils;

import com.nsy.api.wms.request.bd.DateRangeRequest;
import com.nsy.api.wms.request.bd.DateWithoutTimeRangeRequest;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Month;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * HXD
 * 2021/9/3
 **/

public class WmsDateUtils {

    public static final long ONE_DAY = 24 * 60 * 60 * 1000;

    // 获得某天零点
    public static Date getDateStart(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    // 获得某天23:59:59秒
    public static Date getDateEnd(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    // 获得yyyy-MM-dd
    public static String shortDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat shortDate = new SimpleDateFormat("yyyy-MM-dd");
        return shortDate.format(date);
    }

    public static String longDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat shortDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return shortDate.format(date);
    }

    public static Date longDateConvert(String longDateStr) throws ParseException {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(longDateStr);
    }

    //单据专用日期格式
    public static String dateToEnglishStr(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        String originMonth = Month.of(calendar.get(Calendar.MONTH) + 1).name();
        String month = originMonth.charAt(0) + originMonth.substring(1).toLowerCase(Locale.ENGLISH);
        return month + " " + calendar.get(Calendar.DAY_OF_MONTH) + ", " + calendar.get(Calendar.YEAR);
    }

    public static Date nextDay() {
        return DateUtils.addDays(new Date(), 1);
    }

    public static Date lastWorkDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 7 || dayOfWeek == 1) {
            return lastWorkDay(calendar.getTime());
        }
        return calendar.getTime();
    }

    public static boolean isFirstHalfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH) < 16;
    }

    public static long getDiffDay(Date startDate, Date endDate) {
        long diff = endDate.getTime() - startDate.getTime();
        return diff / (1000 * 24 * 60 * 60);
    }

    public static long getDiffHour(Date startDate, Date endDate) {
        long diff = endDate.getTime() - startDate.getTime();
        return diff / (1000 * 60 * 60);
    }

    public static Date getDateBefore(DateTime now, Integer before) {
        DateTime date = now.minusDays(before).dayOfWeek().roundFloorCopy();
        return date.toDate();
    }

    /**
     * 获得当月起始时间
     *
     * @return
     */
    public static Date getStartMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获得当月末尾时间
     *
     * @return
     */
    public static Date getEndMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public static Date getCurrentDateAfter(Integer offset) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        return calendar.getTime();
    }

    public static char convertYearToLetter(int year) {
        int baseYear = 2023;
        char baseLetter = 'A';
        int diff = year - baseYear;
        int letterIndex = (diff % 26 + 26) % 26;
        return (char) (baseLetter + letterIndex);
    }

    //获取两个时间戳之间的相差时间，输出： “24小时35分钟”
    public static String dateDiffMinutesAndSeconds(long timestampBefore, long timestampAfter) {
        long timestamp = timestampAfter - timestampBefore;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(timestamp);
        long hours = TimeUnit.SECONDS.toHours(seconds);
        long remainder = TimeUnit.SECONDS.toMinutes(seconds) - TimeUnit.HOURS.toMinutes(hours);

        return String.format("%s小时%s分钟", hours, remainder);
    }


    /**
     * 入参初始时间和结束时间，按天参数拆分时间
     *
     * @param startDate
     * @param endDate
     * @param day
     * @return
     */
    public static List<DateRangeRequest> splitDateRange(Date startDate, Date endDate, int day) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        List<DateRangeRequest> list = new LinkedList<>();
        while (endTime > startTime) {
            long endTimeNew = Math.min(endTime, startTime + ONE_DAY * day);
            list.add(new DateRangeRequest(new Date(startTime), new Date(endTimeNew)));
            startTime += ONE_DAY * day;
        }
        return list;
    }

    /**
     * 入参初始日期和结束日期，按天参数拆分日期
     *
     * @param startDate
     * @param endDate
     * @param splitDays
     * @return
     */
    public static List<DateWithoutTimeRangeRequest> splitDateWithoutTimeRange(Date startDate, Date endDate, int splitDays) {
        List<DateWithoutTimeRangeRequest> list = new LinkedList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        while (startTime <= endTime) {
            long currentEndTime = Math.min(
                    startTime + (splitDays * 24L * 60L * 60L * 1000L) - 1,
                    endTime
            );

            list.add(new DateWithoutTimeRangeRequest(new Date(startTime), new Date(currentEndTime)));

            startTime = currentEndTime + 1;
        }

        return list;
    }

}
