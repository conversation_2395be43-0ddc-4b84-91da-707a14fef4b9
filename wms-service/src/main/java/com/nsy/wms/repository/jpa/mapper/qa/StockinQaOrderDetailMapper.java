package com.nsy.wms.repository.jpa.mapper.qa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.wms.repository.entity.qa.StockinQaOrderDetailEntity;

/**
 * @author: ca<PERSON><PERSON><PERSON>
 * @version: v1.0
 * @description: 质检单子表Mapper
 * @date: 2024-11-18 15:58
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinQaOrderDetailMapper extends BaseMapper<StockinQaOrderDetailEntity> {

    /**
     * 清除让步接收数据
     * @param stockinQaOrderId
     */
    void clearConcessions(Integer stockinQaOrderId);
}
