package com.nsy.wms.repository.entity.stockout;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 出库单TemuPop半托管扩展表
 */
@Entity
@Table(name = "stockout_order_temu_pop_extend")
@TableName("stockout_order_temu_pop_extend")
public class StockoutOrderTemuPopExtendEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    String location;
    //出库单ID
    private Integer stockoutOrderId;
    //出库单号
    private String stockoutOrderNo;
    //订单号
    private String orderNo;
    //包裹序列号（创建物流发货后返回）
    private String packageSn;
    //发货标签URL
    private String shippingLabelUrl;
    //物流服务商ID
    private String shippingServiceId;
    //物流服务商名称
    private String shippingServiceName;
    //物流公司名称
    private String logisticsCompanyName;
    //物流公司ID
    private String logisticsCompanyId;
    //仓库ID
    private String warehouseId;
    //仓库名称
    private String warehouseName;
    //物流单号
    private String logisticsNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPackageSn() {
        return packageSn;
    }

    public void setPackageSn(String packageSn) {
        this.packageSn = packageSn;
    }


    public String getShippingLabelUrl() {
        return shippingLabelUrl;
    }

    public void setShippingLabelUrl(String shippingLabelUrl) {
        this.shippingLabelUrl = shippingLabelUrl;
    }

    public String getShippingServiceId() {
        return shippingServiceId;
    }

    public void setShippingServiceId(String shippingServiceId) {
        this.shippingServiceId = shippingServiceId;
    }

    public String getShippingServiceName() {
        return shippingServiceName;
    }

    public void setShippingServiceName(String shippingServiceName) {
        this.shippingServiceName = shippingServiceName;
    }

    public String getLogisticsCompanyName() {
        return logisticsCompanyName;
    }

    public void setLogisticsCompanyName(String logisticsCompanyName) {
        this.logisticsCompanyName = logisticsCompanyName;
    }

    public String getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(String logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }
}
