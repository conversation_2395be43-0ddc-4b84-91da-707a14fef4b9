package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@TableName("stock_take_stock_task")
@Entity
@Table(name = "stock_take_stock_task")
public class StockTakeStockTaskEntity extends BaseMpEntity implements Serializable {
    private static final long serialVersionUID = 3135416250287665212L;

    /**
     * 唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer taskId;
    /**
     * 地区
     */
    private String location;
    /**
     * 计划ID
     */
    private Integer planId;
    /**
     * 盘点类型
     */
    private String planType;
    /**
     * 盘点任务生成方式
     */
    private String taskGenerateMode;
    /**
     * 盘点库区数
     */
    private Integer spaceAreaQty;
    /**
     * 盘点库位数
     */
    private Integer positionQty;
    /**
     * 监盘人
     */
    private String supervisor;
    /**
     * 盘点人
     */
    private String operator;
    /**
     * 盘点时间
     */
    private Date operateDate;
    /**
     * 盘点任务状态
     */
    private String status;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getPlanId() {
        return planId;
    }

    public void setPlanId(Integer planId) {
        this.planId = planId;
    }

    public String getPlanType() {
        return planType;
    }

    public void setPlanType(String planType) {
        this.planType = planType;
    }

    public String getTaskGenerateMode() {
        return taskGenerateMode;
    }

    public void setTaskGenerateMode(String taskGenerateMode) {
        this.taskGenerateMode = taskGenerateMode;
    }

    public Integer getSpaceAreaQty() {
        return spaceAreaQty;
    }

    public void setSpaceAreaQty(Integer spaceAreaQty) {
        this.spaceAreaQty = spaceAreaQty;
    }

    public Integer getPositionQty() {
        return positionQty;
    }

    public void setPositionQty(Integer positionQty) {
        this.positionQty = positionQty;
    }

    public String getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(String supervisor) {
        this.supervisor = supervisor;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
