package com.nsy.wms.repository.entity.qa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 15:25
 */
@Entity
@TableName("bd_qa_full_inspect_rule_product_item")
@Table(name = "bd_qa_full_inspect_rule_product_item")
public class BdQaFullInspectRuleProductItemEntity extends BaseMpEntity {

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 区域
     */
    private String location;
    /**
     * 质检规则ID
     */
    private Integer ruleId;
    /**
     * 供应商id
     */
    private Integer supplierId;
    /**
     * 商品id
     */
    private Integer productId;
    /**
     * 商品编码
     */
    private String spu;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }
}