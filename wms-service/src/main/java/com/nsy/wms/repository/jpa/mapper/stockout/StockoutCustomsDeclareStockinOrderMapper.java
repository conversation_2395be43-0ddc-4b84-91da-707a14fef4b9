package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareStockinOrderPageRequest;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareStockinOrderResponse;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareStockinOrderEntity;
import org.apache.ibatis.annotations.Param;

@org.apache.ibatis.annotations.Mapper
public interface StockoutCustomsDeclareStockinOrderMapper extends BaseMapper<StockoutCustomsDeclareStockinOrderEntity> {
    /**
     * 分页
     *
     * @param request
     * @return
     */
    Page<StockoutCustomsDeclareStockinOrderResponse> findPage(IPage page, @Param("request") StockoutCustomsDeclareStockinOrderPageRequest request);

    Integer countPage(@Param("request") StockoutCustomsDeclareStockinOrderPageRequest request);
}
