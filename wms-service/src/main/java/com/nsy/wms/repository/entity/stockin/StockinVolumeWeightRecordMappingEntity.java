package com.nsy.wms.repository.entity.stockin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-04 9:43
 */
@Entity
@Table(name = "stockin_volume_weight_record_mapping")
@TableName("stockin_volume_weight_record_mapping")
public class StockinVolumeWeightRecordMappingEntity extends BaseMpEntity {

    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    /**
     * 记录id
     */
    private Integer recordId;

    /**
     * 测量类型 VOLUME_WEIGHT:体积重,HEIGHT:高度
     */
    private String measureType;

    /**
     * 测量标准id
     */
    private Integer standardId;

    /**
     * 工厂出库单号
     */
    private String supplierDeliveryNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品系统的product.id
     */
    private Integer productId;

    /**
     * 商品系统product_spec.id
     */
    private Integer specId;

    /**
     * barcode
     */
    private String barcode;

    /**
     * 内部箱号
     */
    private String internalBoxCode;

    /**
     * 箱内数
     */
    private Integer boxQty;

    /**
     * 是否跳档 1-是，0-否
     */
    private Integer isOverStandard;

    /**
     * 包装类型
     */
    private String packageName;

    /**
     * 是否合格 1-是，0-否
     */
    private Integer isQualified;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public Integer getIsOverStandard() {
        return isOverStandard;
    }

    public void setIsOverStandard(Integer isOverStandard) {
        this.isOverStandard = isOverStandard;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public Integer getIsQualified() {
        return isQualified;
    }

    public void setIsQualified(Integer isQualified) {
        this.isQualified = isQualified;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }
}
