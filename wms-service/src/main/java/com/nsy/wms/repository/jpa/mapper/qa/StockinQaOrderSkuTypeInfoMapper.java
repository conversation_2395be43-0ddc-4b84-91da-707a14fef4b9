package com.nsy.wms.repository.jpa.mapper.qa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.response.qa.StockinQaOrderPageExport;
import com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity;

import java.util.List;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检任务标签内容Mapper
 * @date: 2024-11-18 15:58
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinQaOrderSkuTypeInfoMapper extends BaseMapper<StockinQaOrderSkuTypeInfoEntity> {

    List<StockinQaOrderPageExport> listSkuType(List<Integer> stockinQaOrderIds);

    IPage<StockinQaOrderSkuTypeInfoEntity> pageList(Page page);
}
