package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentSearchRequest;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentExportList;
import com.nsy.wms.repository.entity.stockout.StockoutFaireShipmentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface StockoutFaireShipmentMapper extends BaseMapper<StockoutFaireShipmentEntity> {

    IPage<Integer> pageSearchShipmentIds(IPage page, @Param("query") StockoutFaireShipmentSearchRequest request);

    Integer pageSearchShipmentIdsCount(@Param("query") StockoutFaireShipmentSearchRequest request);

    List<Integer> getSearchShipmentIds(@Param("query") StockoutFaireShipmentSearchRequest request);

    List<StockoutFaireShipmentExportList> getExportListByIds(@Param("idList") List<Integer> idList);

    StockoutFaireShipmentEntity getStockoutFaireShipmentByMax(@Param("batchId") String batchId);

    BigDecimal getProductInvoicePrice(@Param("stockoutOrderNo") String stockoutOrderNo);

    BigDecimal getProductInvoicePriceByShipmentId(@Param("shipmentId") Integer shipmentId);
}
