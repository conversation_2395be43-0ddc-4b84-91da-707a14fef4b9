package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareCustomerOrderPageRequest;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareCustomerOrderResponse;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareCustomerOrderEntity;
import org.apache.ibatis.annotations.Param;

@org.apache.ibatis.annotations.Mapper
public interface StockoutCustomsDeclareCustomerOrderMapper extends BaseMapper<StockoutCustomsDeclareCustomerOrderEntity> {
    Page<StockoutCustomsDeclareCustomerOrderResponse> findPage(IPage page, @Param("request") StockoutCustomsDeclareCustomerOrderPageRequest request);

    Integer countPage(@Param("request") StockoutCustomsDeclareCustomerOrderPageRequest request);
}
