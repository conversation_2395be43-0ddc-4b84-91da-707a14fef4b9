package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderBoxListBo;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderLogListBo;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderPositionCodeByInternalBoxCodeDTO;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderQcDTO;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderShelvedNumDTO;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderSpaceAndStockinQtyDTO;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderStatusDTO;
import com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryPrintDTO;
import com.nsy.api.wms.request.stockin.StockinSupplierDeliveryOrderItemListRequest;
import com.nsy.api.wms.request.stockin.StockinSupplierDeliveryOrderListRequest;
import com.nsy.api.wms.response.stockin.StockinQtySumResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryEfficientStatusDTO;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderBoxListResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderInfoResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderItemSkuListResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderListResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderQcListResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderSkuDetail;
import com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: chenzheqi
 * @description: 入库任务表Mapper
 * @date: 2022-04-22 9:00
 */
@Mapper
public interface StockinSupplierDeliveryOrderMapper {

    /**
     * 查找列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinSupplierDeliveryOrderListResponse> pageSearchSupplierDeliveryOrderList(Page page, @Param("query") StockinSupplierDeliveryOrderListRequest request);


    Integer countSearchSupplierDeliveryOrderList(@Param("query") StockinSupplierDeliveryOrderListRequest request);

    /**
     * 查找包含该sku的工厂出库单号
     */
    List<String> findFactorySupplierDeliveryNoBySku(String sku);

    /**
     * 统计状态
     *
     * @return
     */
    List<StockinSupplierDeliveryOrderStatusDTO> statusCountSupplierDeliveryOrder();

    /**
     * 通过工厂出库单号统计上架数
     *
     * @param supplierDeliveryNoList
     * @return
     */
    List<StockinSupplierDeliveryOrderSkuDetail> countShelvedNumBySupplierDeliveryNo(List<String> supplierDeliveryNoList);

    /**
     * 工厂出库单信息
     *
     * @param supplierDeliveryNo
     * @return
     */
    StockinSupplierDeliveryOrderInfoResponse getStockinSupplierDeliveryOrderInfoBySupplierDeliveryNo(String supplierDeliveryNo);


    /**
     * 查找工厂出库单明细
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinSupplierDeliveryOrderItemSkuListResponse> pageSearchStockinSupplierDeliveryOrderItemList(IPage page, @Param("query") StockinSupplierDeliveryOrderItemListRequest request);


    IPage<StockinSupplierDeliveryOrderItemSkuListResponse> pageSearchStockinSupplierDeliveryOrderItemListForDownload(IPage page, @Param("query") StockinSupplierDeliveryOrderItemListRequest request);

    /**
     * 通过工厂出库箱号和sku查找上架数
     *
     * @param supplierDeliveryBoxCodeList
     * @param skuList
     * @return
     */
    List<StockinSupplierDeliveryOrderShelvedNumDTO> countShelvedQtyBySupplierDeliveryBoxCodeAndSku(List<String> supplierDeliveryBoxCodeList, List<String> skuList);

    /**
     * 内部箱信息
     *
     * @param page
     * @param request
     * @return
     */
    Page<StockinSupplierDeliveryOrderBoxListResponse> stockinSupplierDeliveryOrderDetailList(Page page, @Param("request") StockinSupplierDeliveryOrderBoxListBo request);

    /**
     * 日志信息
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinOrderLogEntity> pageSearchStockinOrderLog(IPage page, @Param("request") StockinSupplierDeliveryOrderLogListBo request);

    /**
     * 通过内部箱号查找上架库位
     *
     * @param internalBoxCodeList
     * @return
     */
    List<StockinSupplierDeliveryOrderPositionCodeByInternalBoxCodeDTO> selectDirectShelvePositionCodeByInternalBoxCode(List<String> internalBoxCodeList);

    /**
     * 通过工厂出库单号查找
     *
     * @param supplierDeliveryNoList
     * @return
     */
    List<StockinSupplierDeliveryOrderSpaceAndStockinQtyDTO> findSpaceAndStockinQty(List<String> supplierDeliveryNoList);

    /**
     * 根据出库单号获取计划入库数量
     *
     * @param request
     * @return
     */
    Integer sumExpectedQtyBySupplierDeliveryOrderNo(@Param("query") StockinSupplierDeliveryOrderListRequest request);

    /**
     * 根据出库单号获取计划上架数、入库数量
     *
     * @param request
     * @return
     */
    StockinQtySumResponse sumQtyBySupplierDeliveryOrderNo(@Param("query") StockinSupplierDeliveryOrderListRequest request);

    /**
     * 获取打印信息
     *
     * @param supplierDeliveryNo
     * @return
     */
    List<StockinSupplierDeliveryPrintDTO> getPrintInfo(String supplierDeliveryNo);

    /**
     * 根据入库单ID和SKU获取 该入库单的上架库位
     *
     * @param stockinOrderId
     * @param sku
     * @return
     */
    List<String> findShelvePositionByStockOrderIdAndSku(Integer stockinOrderId, String sku);

    IPage<StockinSupplierDeliveryOrderQcListResponse> pageSearchDeliveryNos(Page page, @Param("query") StockinSupplierDeliveryOrderListRequest request);

    /**
     * 工厂出库单时效报表
     *
     * @param supplierDeliveryNos
     * @return
     */
    List<StockinSupplierDeliveryEfficientStatusDTO> efficientPageList(@Param("supplierDeliveryNos") List<String> supplierDeliveryNos);

    int countSearchDeliveryNos(@Param("query") StockinSupplierDeliveryOrderListRequest request);

    double calculateDeliveryNos(@Param("query") StockinSupplierDeliveryOrderListRequest request);

    List<StockinSupplierDeliveryOrderQcDTO> searchSupplierDeliveryOrderItem(@Param("supplierDeliveryNos") List<String> supplierDeliveryNos);
}
