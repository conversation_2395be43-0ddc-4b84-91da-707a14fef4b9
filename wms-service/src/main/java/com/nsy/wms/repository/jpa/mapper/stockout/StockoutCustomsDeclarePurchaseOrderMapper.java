package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclarePurchaseOrderPageRequest;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclarePurchaseOrderResponse;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclarePurchaseOrderEntity;
import org.apache.ibatis.annotations.Param;

@org.apache.ibatis.annotations.Mapper
public interface StockoutCustomsDeclarePurchaseOrderMapper extends BaseMapper<StockoutCustomsDeclarePurchaseOrderEntity> {
    /**
     * 分页
     *
     * @param request
     * @return
     */
    Page<StockoutCustomsDeclarePurchaseOrderResponse> findPage(IPage page, @Param("request") StockoutCustomsDeclarePurchaseOrderPageRequest request);

    /**
     * 分页统计
     *
     * @param request
     * @return
     */
    Integer countPage(@Param("request") StockoutCustomsDeclarePurchaseOrderPageRequest request);

}
