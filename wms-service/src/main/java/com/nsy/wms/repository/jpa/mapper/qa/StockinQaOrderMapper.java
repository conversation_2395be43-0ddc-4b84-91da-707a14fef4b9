package com.nsy.wms.repository.jpa.mapper.qa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.qa.PreviousStockinQaOrderRequest;
import com.nsy.api.wms.request.qa.StockinQaFullInspectReportPageRequest;
import com.nsy.api.wms.request.qa.StockinQaFullInspectTaskPageRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectBatchRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectPageRequest;
import com.nsy.api.wms.request.qa.StockinQaOrderHistoryRequest;
import com.nsy.api.wms.request.qa.StockinQaOrderPageRequest;
import com.nsy.api.wms.request.qa.StockinQaSpuReturnHistoryRequest;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.qa.PreviousStockinQaOrderResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectReportPageResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectTaskPageResponse;
import com.nsy.api.wms.response.qa.StockinQaHistoryResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectBatchResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectCountQtyResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectPageResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderCountQtyResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderPageResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderSkcResponse;
import com.nsy.api.wms.response.qa.StockinQaSpuReturnHistoryResponse;
import com.nsy.permission.annatation.Permission;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单Mapper
 * @date: 2024-11-18 15:58
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinQaOrderMapper extends BaseMapper<StockinQaOrderEntity> {

    /**
     * 统计不允许巡检状态
     *
     * @param internalBoxCode
     * @param sku
     * @return
     */
    int countProcessingByInfo(@Param("internalBoxCode") String internalBoxCode, @Param("sku") String sku);

    /**
     * 获取上一次质检单
     *
     * @param request
     * @return
     */
    PreviousStockinQaOrderResponse getPreviousOrder(@Param("request") PreviousStockinQaOrderRequest request);

    IPage<StockinQaHistoryResponse> pageQaHistory(Page page, @Param("request") StockinQaOrderHistoryRequest request);

    IPage<StockinQaSpuReturnHistoryResponse> pageQaSpuReturnHistory(Page page, @Param("request") StockinQaSpuReturnHistoryRequest request);

    @Permission
    IPage<StockinQaOrderPageResponse> pageList(Page<StockinQaOrderPageResponse> page, @Param("request") StockinQaOrderPageRequest request);

    @Permission
    long pageCount(@Param("request") StockinQaOrderPageRequest request);

    @Permission
    List<StatusTabResponse> countByProcessStatus();

    @Permission
    StockinQaOrderEntity getByIdPermission(Integer stockinQaOrderId);

    @Permission
    List<StockinQaOrderSkcResponse> getAuditList(@Param("spu") String spu, @Param("skc") String skc, @Param("supplierDeliveryNoList") List<String> supplierDeliveryNoList,
                                                 @Param("processStatus") String processStatus, @Param("appointor") String appointor);

    @Permission
    StockinQaOrderCountQtyResponse countQty(@Param("request") StockinQaOrderPageRequest request);

    Integer findPreQaOrder(@Param("stockinQaOrderId") Integer stockinQaOrderId, @Param("sku") String sku, @Param("purchasePlanNos") List<String> purchasePlanNos);

    void clearAppointor(@Param("stockinQaOrderId") Integer stockinQaOrderId);

    int countQcColorOrderBySupplierDeliveryNoAndSkc(@Param("supplierDeliveryNoList") List<String> supplierDeliveryNoList, @Param("skc") String skc);

    /**
     * 待稽查分页
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinQaInspectPageResponse> pageWaitInspectTaskList(Page page, @Param("request") StockinQaInspectPageRequest request);

    /**
     * 稽查报告分页
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinQaInspectPageResponse> pageInspectTaskList(Page page, @Param("request") StockinQaInspectPageRequest request);

    long pageWaitInspectCount(@Param("request") StockinQaInspectPageRequest request);

    long pageInspectCount(@Param("request") StockinQaInspectPageRequest request);

    void updateInspectStatusNull(Integer stockinQaOrderId);

    void updateWaitInspectStatus(Integer stockinQaOrderId);

    StockinQaInspectCountQtyResponse countInspectQty(@Param("request") StockinQaInspectPageRequest request);

    /**
     * 全检统计
     * @param request
     * @return
     */
    StockinQaInspectCountQtyResponse countFullInspectQty(@Param("request") StockinQaFullInspectReportPageRequest request);

    List<StockinQaInspectBatchResponse> batchInspectList(@Param("request") StockinQaInspectBatchRequest request);

    /**
     * 全检任务
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinQaFullInspectTaskPageResponse> pageFullInspectTaskList(Page page, @Param("request") StockinQaFullInspectTaskPageRequest request);

    long pageFullInspectTaskCount(@Param("request") StockinQaFullInspectTaskPageRequest request);

    /**
     * 全检报告
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinQaFullInspectReportPageResponse> pageFullInspectReportList(Page page, @Param("request") StockinQaFullInspectReportPageRequest request);

    long pageFullInspectReportCount(@Param("request") StockinQaFullInspectReportPageRequest request);
}
