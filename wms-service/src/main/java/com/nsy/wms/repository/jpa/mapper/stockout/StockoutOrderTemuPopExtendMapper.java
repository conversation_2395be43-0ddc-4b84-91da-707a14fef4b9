package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuPopExtendEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StockoutOrderTemuPopExtendMapper extends BaseMapper<StockoutOrderTemuPopExtendEntity> {

    /**
     * 查询需要创建物流发货的出库单（packageSn为空）
     */
    List<StockoutOrderTemuPopExtendEntity> listOrdersForCreateShipment();

    /**
     * 查询需要获取发货结果的出库单（packageSn不为空，但发货结果未完成）
     */
    List<StockoutOrderTemuPopExtendEntity> listOrdersForGetShipmentResult();
} 