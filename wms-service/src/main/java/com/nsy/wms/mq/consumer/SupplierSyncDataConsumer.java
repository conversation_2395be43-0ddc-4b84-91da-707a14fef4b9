package com.nsy.wms.mq.consumer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.mq.TableListenerMessage;
import com.nsy.wms.mq.consumer.base.TableListenerConsumer;
import com.nsy.wms.utils.JsonMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class SupplierSyncDataConsumer extends TableListenerConsumer<SupplierSyncDataConsumer.SupplierIdDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierSyncDataConsumer.class);

    @Autowired
    SupplierService supplierService;

    @KafkaListener(topics = KafkaConstant.NSY_SCM_SUPPLIER)
    public void newSupplierConsumer(ConsumerRecord<?, ?> record) {
        LOGGER.info("SupplierSyncDataConsumer receive message: {}", record.value());
        TableListenerMessage<SupplierSyncDataConsumer.SupplierIdDto> receiveMessage = JsonMapper.fromJson(record.value().toString(), SupplierDtoMessage.class);
        if (CollectionUtils.isEmpty(receiveMessage.getData()))
            return;
        processMessage(receiveMessage, receiveMessage.getData().get(0).getSupplierId().toString());
    }

    @Override
    protected void doProcessMessage(TableListenerMessage<SupplierSyncDataConsumer.SupplierIdDto> receiveMessage) {
        LoginInfoService.setName("SupplierSyncDataConsumer");
        try {
            List<Integer> supplierIdList = receiveMessage.getData().stream().map(SupplierIdDto::getSupplierId).collect(Collectors.toList());
            supplierService.syncSupplier(supplierIdList, receiveMessage.getType());
        } finally {
            LoginInfoService.removeName();
        }
    }

    @Override
    public String getTableName() {
        return KafkaConstant.NSY_SCM_SUPPLIER;
    }

    @Override
    public Class getBeanClass() {
        return SupplierDtoMessage.class;
    }


    static class SupplierDtoMessage extends TableListenerMessage<SupplierSyncDataConsumer.SupplierIdDto> {

    }

    public static class SupplierIdDto {
        @JsonProperty("supplier_id")
        Integer supplierId;

        public Integer getSupplierId() {
            return supplierId;
        }

        public void setSupplierId(Integer supplierId) {
            this.supplierId = supplierId;
        }
    }

}
