<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinVolumeWeightRecordMappingMapper">
    
    <select id="countRecordMapping" resultType="java.lang.Integer">
        select count(1) from stockin_volume_weight_record_mapping tvm
        where tvm.sku =  #{request.sku} and tvm.internal_box_code =#{request.internalBoxCode} 
    </select>
    
    <select id="getVolumeWeightRecordMappingList" resultType="com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingResponse">
        select
        bvw.sku,
        bvw.barcode,
        bvw.product_id,
        bvw.length,
        bvw.width,
        bvw.height,
        bvw.weight,
        bvw.length_inch,
        bvw.width_inch,
        bvw.height_inch,
        bvw.weight_pound,
        bvw.volume_weight_pound,
        bvw.standard_type,
        bvw.charged_weight,
        bvw.fba_cost,
        bvw.machine_no,
        tvm.id,
        tvm.create_date,
        tvm.is_over_standard,
        tvm.is_qualified,
        tvm.create_by
        from  stockin_volume_weight_record_mapping tvm
        inner join bd_volume_weight_record bvw on bvw.id = tvm.record_id
        where tvm.supplier_delivery_no in
        <foreach collection="request.supplierDeliveryNoList" item="supplierDeliveryNo" open="(" separator="," close=")">
            #{supplierDeliveryNo}
        </foreach>
        and bvw.sku = #{request.sku}
        and tvm.measure_type = 'VOLUME_WEIGHT'
    </select>
    
    <select id="getdetail" resultType="com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingDetailResponse">
        select soi.internal_box_code,
               soi.sku,
               so.supplier_id,
               so.supplier_name,
               svwrm.supplier_delivery_no,
               p.image_url as imageUrl,
               p.thumbnail_image_url as thumbnailImageUrl,
               p.preview_image_url as previewImageUrl,
               GROUP_CONCAT(DISTINCT soi.purchase_plan_no) as purchasePlanNo,
               min(soi.create_date) as stockinDate,
               svwrm.create_by,
               svwrm.create_date,
               svwrm.box_qty as boxQty
        from  stockin_volume_weight_record_mapping  svwrm
        inner join stockin_order_item soi on soi.internal_box_code = svwrm.internal_box_code and svwrm.sku = soi.sku
        inner join stockin_order so on so.stockin_order_id= soi.stockin_order_id
        inner join product_spec_info p on p.sku = soi.sku
        where  
            svwrm.supplier_delivery_no = #{request.supplierDeliveryNo}
        and svwrm.sku = #{request.sku}
        and svwrm.internal_box_code = #{request.internalBoxCode}
        group by svwrm.internal_box_code,svwrm.supplier_delivery_no,svwrm.sku
    </select>
    
    <select id="getPageExportList" resultType="com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingExportResponse">
        select t.internal_box_code as internalBoxCode,
               t.sku as sku,
               t.package_name as packageName,
               max(so.supplier_id) as supplierId,
               max(so.supplier_name) as supplierName,
               t.supplier_delivery_no as supplierDeliveryNo,
               soi.purchase_plan_no as purchasePlanNo,
               min(soi.create_date) as stockinDate,
               bvw.product_id as productId,
               bvw.length as length,
               bvw.width as width,
               bvw.height as height,
               bvw.weight as weight,
               bvw.length_inch as lengthInch,
               bvw.width_inch as widthInch,
               bvw.height_inch as heightInch,
               bvw.weight_pound as weightPound,
               bvw.volume_weight_pound as volumeWeightPound,
               bvw.standard_type as standardType,
               bvw.charged_weight as chargedWeight,
               bvw.fba_cost as fbaCost,
               bvw.machine_no as machineNo,
               standard.length as lengthStandard,
               standard.width as widthStandard,
               standard.height as heightStandard,
               standard.weight as weightStandard,
               standard.length_inch as lengthInchStandard,
               standard.width_inch as widthInchStandard,
               standard.height_inch as heightInchStandard,
               standard.weight_pound as weightPoundStandard,
               standard.volume_weight_pound as volumeWeightPoundStandard,
               standard.standard_type as standardTypeStandard,
               standard.charged_weight as chargedWeightStandard,
               standard.fba_cost as fbaCostStandard,
                t.create_by as createBy,
                t.create_date as createDate,
                t.is_over_standard as isOverStandard,
               sum(soi.qty) as arrivalCount,
                t.box_qty as boxQty
        from  stockin_volume_weight_record_mapping  t
                  inner join bd_volume_weight_record bvw on bvw.id = t.record_id
                  left join stockin_volume_weight_standrd_record standard on standard.id = t.standard_id
                  left join stockin_order_item soi on soi.internal_box_code = t.internal_box_code and t.sku = soi.sku
                  left join stockin_order so on so.stockin_order_id= soi.stockin_order_id
                  <if test="request.spuList != null and request.spuList.size() > 0 ">
                    inner join product_info p on p.product_id = t.product_id
                  </if>
        <include refid="pageCondition"></include>
        group by t.internal_box_code,t.supplier_delivery_no,t.sku,t.id
        order by t.create_date desc
    </select>
    
    <select id="countScanInfo" resultType="java.lang.Integer">
        select count(1)  
        from  stockin_volume_weight_record_mapping  svwrm 
        where svwrm.sku =  #{request.sku}
        and svwrm.supplier_delivery_no in
        <foreach collection="request.supplierDeliveryNoList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>
    
    <select id="getExistRecordInfo" resultType="java.lang.Integer">
        select svwrm.id
        from  stockin_volume_weight_record_mapping  svwrm
        where svwrm.sku =  #{sku}
        and svwrm.measure_type =  #{measureType}
        and svwrm.internal_box_code =  #{internalBoxCode}
        and svwrm.supplier_delivery_no in
        <foreach collection="supplierDeliveryNoList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>
    
    <select id="pageList" resultType="com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageResponse">
        select t.sku,
               t.package_name,
               count(distinct supplier_delivery_no) as measureBatchCount,
               count(DISTINCT CASE WHEN t.is_qualified = 0 THEN t.supplier_delivery_no END) AS measureBatchUnqualifiedCount,
               count(distinct t.id) as measureTotalCount,
               count(DISTINCT CASE WHEN t.is_over_standard = 1 THEN t.id END) AS overStandardCount,
               MAX(t.create_date) AS lastMeasureDate,
               MAX(t.create_by) AS lastMeasureOperator
        from
            stockin_volume_weight_record_mapping t
        <if test="request.spuList != null and request.spuList.size() > 0 ">
            inner join product_info p on p.product_id = t.product_id
        </if>
        <include refid="pageCondition"></include>
        group by t.sku,t.package_name
        order by lastMeasureDate desc
    </select>
    
    <select id="getPageCount" resultType="java.lang.Integer">
        select count(1) from (
            select 1
            from stockin_volume_weight_record_mapping t
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                inner join product_info p on p.product_id = t.product_id
            </if>
            <include refid="pageCondition"></include>
            group by t.sku,t.package_name
        ) temp
    </select>
    
    <select id="pageSkuList" resultType="com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse">
        select 	t.sku,
        t.package_name,
        t.supplier_delivery_no,
        t.internal_box_code,
        count(distinct t.id) as measureTotalCount,
        COUNT(DISTINCT CASE WHEN t.is_over_standard = 1 THEN t.id END) AS overStandardCount,
        t.is_qualified,
        t.create_by as createBy,
        t.create_date as createDate,
        min(soi.create_date) as stockinDate,
        max(so.supplier_name) as supplierName,
        bvw.standard_type as standardType
        from
        stockin_volume_weight_record_mapping t
        inner join bd_volume_weight_record bvw on bvw.id = t.record_id
        left join stockin_order_item soi on soi.internal_box_code = t.internal_box_code and t.sku = soi.sku
        left join stockin_order so on so.stockin_order_id= soi.stockin_order_id
        inner join product_info p on p.product_id = t.product_id
        <include refid="pageSkuCondition"></include>
        group by t.supplier_delivery_no,t.sku,t.package_name
        order by createDate desc
    </select>
    
    <select id="getPageSkuCount" resultType="java.lang.Integer">
        select count(1) from (
            select t.supplier_delivery_no, t.internal_box_code
            from stockin_volume_weight_record_mapping t
            left join stockin_order_item soi on soi.internal_box_code = t.internal_box_code and t.sku = soi.sku
            left join stockin_order so on so.stockin_order_id= soi.stockin_order_id
            <include refid="pageSkuCondition"></include>
            group by t.supplier_delivery_no, t.internal_box_code
        ) temp
    </select>
    
    <select id="pageDetailList" resultType="com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageDetailResponse">
        select t.internal_box_code as internalBoxCode,
        t.sku as sku,
        max(so.supplier_id) as supplierId,
        max(so.supplier_name) as supplierName,
        t.supplier_delivery_no as supplierDeliveryNo,
        soi.purchase_plan_no as purchasePlanNo,
        min(soi.create_date) as stockinDate,
        bvw.product_id as productId,
        bvw.length as length,
        bvw.width as width,
        bvw.height as height,
        bvw.weight as weight,
        bvw.length_inch as lengthInch,
        bvw.width_inch as widthInch,
        bvw.height_inch as heightInch,
        bvw.weight_pound as weightPound,
        bvw.volume_weight_pound as volumeWeightPound,
        bvw.standard_type as standardType,
        bvw.charged_weight as chargedWeight,
        bvw.fba_cost as fbaCost,
        bvw.machine_no as machineNo,
        t.create_by as createBy,
        t.create_date as createDate,
        t.is_over_standard as isOverStandard,
        sum(soi.qty) as arrivalCount,
        t.box_qty as boxQty,
        t.standard_id as standardId
        from  stockin_volume_weight_record_mapping t
        inner join bd_volume_weight_record bvw on bvw.id = t.record_id
        left join stockin_order_item soi on soi.internal_box_code = t.internal_box_code and t.sku = soi.sku
        left join stockin_order so on so.stockin_order_id= soi.stockin_order_id
        <include refid="pageDetailCondition"></include>
        group by soi.internal_box_code,t.supplier_delivery_no,soi.sku,t.id
        order by createDate desc
    </select>

    <sql id="pageCondition">
        <where>
            <if test="request.createDateStart != null">
                and t.create_date &gt;= #{request.createDateStart}
            </if>
            <if test="request.createDateEnd != null">
                and t.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.measureType != null and request.measureType != ''">
                and t.measure_type = #{request.measureType}
            </if>
            <if test="request.skuList != null and request.skuList.size() > 0 ">
                and t.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                and p.spu in
                <foreach collection="request.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.supplierDeliveryNo != null and request.supplierDeliveryNo != ''">
                and t.supplier_delivery_no = #{request.supplierDeliveryNo}
            </if>
            <if test="request.createBy != null and request.createBy != ''">
                and t.create_by = #{request.createBy}
            </if>
            <if test="request.internalBoxCode != null and request.internalBoxCode != ''">
                and t.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.isOverStandard != null">
                and t.is_over_standard = #{request.isOverStandard}
            </if>
        </where>
    </sql>


    <sql id="pageSkuCondition">
        <where>
            <if test="request.createDateStart != null">
                and t.create_date &gt;= #{request.createDateStart}
            </if>
            <if test="request.createDateEnd != null">
                and t.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.measureType != null and request.measureType != ''">
                and t.measure_type = #{request.measureType}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and t.sku = #{request.sku}
            </if>
            <if test="request.packageName != null and request.packageName != ''">
                and t.package_name = #{request.packageName}
            </if>
            <if test="request.supplierDeliveryNo != null and request.supplierDeliveryNo != ''">
                and t.supplier_delivery_no = #{request.supplierDeliveryNo}
            </if>
            <if test="request.createBy != null and request.createBy != ''">
                and t.create_by = #{request.createBy}
            </if>
            <if test="request.internalBoxCode != null and request.internalBoxCode != ''">
                and t.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.supplierId != null">
                and so.supplier_id = #{request.supplierId}
            </if>
            <if test="request.isOverStandard != null">
                and t.is_over_standard = #{request.isOverStandard}
            </if>
            <if test="request.isQualified != null">
                and t.is_qualified = #{request.isQualified}
            </if>
            <if test="request.stockinDateStart != null">
                and soi.create_date &gt;= #{request.stockinDateStart}
            </if>
            <if test="request.stockinDateEnd != null">
                and soi.create_date &lt;= #{request.stockinDateEnd}
            </if>
        </where>
    </sql>


    <sql id="pageDetailCondition">
        <where>
            <if test="request.createDateStart != null">
                and t.create_date &gt;= #{request.createDateStart}
            </if>
            <if test="request.createDateEnd != null">
                and t.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.measureType != null and request.measureType != ''">
                and t.measure_type = #{request.measureType}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and t.sku = #{request.sku}
            </if>
            <if test="request.packageName != null and request.packageName != ''">
                and t.package_name = #{request.packageName}
            </if>
            <if test="request.supplierDeliveryNo != null and request.supplierDeliveryNo != ''">
                and t.supplier_delivery_no = #{request.supplierDeliveryNo}
            </if>
            <if test="request.createBy != null and request.createBy != ''">
                and t.create_by = #{request.createBy}
            </if>
            <if test="request.internalBoxCode != null and request.internalBoxCode != ''">
                and t.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.supplierId != null">
                and so.supplier_id = #{request.supplierId}
            </if>
            <if test="request.isOverStandard != null">
                and t.is_over_standard = #{request.isOverStandard}
            </if>
            <if test="request.isQualified != null">
                and t.is_qualified = #{request.isQualified}
            </if>
            <if test="request.stockinDateStart != null">
                and soi.create_date &gt;= #{request.stockinDateStart}
            </if>
            <if test="request.stockinDateEnd != null">
                and soi.create_date &lt;= #{request.stockinDateEnd}
            </if>
        </where>
    </sql>

    <select id="getHeightRecordMappingList" resultType="com.nsy.api.wms.response.stockin.StockinHeightRecordMappingResponse">
        select
        wcs.sku,
        wcs.barcode,
        wcs.length,
        wcs.width,
        wcs.height,
        wcs.weight,
        wcs.measure_type as heightMeasureType,
        wcs.volume_weight,
        wcs.fba_cost,
        wcs.measure_height,
        wcs.measure_volume_weight,
        wcs.measure_fba_cost,
        wcs.sorting_port,
        tvm.id,
        tvm.create_date,
        tvm.is_over_standard,
        tvm.is_qualified,
        tvm.create_by
        from  stockin_volume_weight_record_mapping tvm
        inner join wcs_height_sorting_record wcs on wcs.id = tvm.record_id
        where tvm.supplier_delivery_no in
        <foreach collection="request.supplierDeliveryNoList" item="supplierDeliveryNo" open="(" separator="," close=")">
            #{supplierDeliveryNo}
        </foreach>
        and tvm.sku = #{request.sku}
        and tvm.measure_type = 'HEIGHT'
    </select>
    
    <select id="heightPageSkuList" resultType="com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse">
        select 	t.sku,
        t.package_name,
        t.supplier_delivery_no,
        t.internal_box_code,
        count(distinct t.id) as measureTotalCount,
        COUNT(DISTINCT CASE WHEN t.is_over_standard = 1 THEN t.id END) AS overStandardCount,
        t.is_qualified,
        t.create_by as createBy,
        t.create_date as createDate,
        min(soi.create_date) as stockinDate,
        max(so.supplier_name) as supplierName
        from
        stockin_volume_weight_record_mapping t
        left join stockin_order_item soi on soi.internal_box_code = t.internal_box_code and t.sku = soi.sku
        left join stockin_order so on so.stockin_order_id= soi.stockin_order_id
        inner join product_info p on p.product_id = t.product_id
        <include refid="pageSkuCondition"></include>
        group by t.supplier_delivery_no,t.sku,t.package_name
        order by createDate desc
    </select>
    
    <select id="heightPageDetailList" resultType="com.nsy.api.wms.response.stockin.StockinHeightRecordMappingPageDetailResponse">
        select t.internal_box_code as internalBoxCode,
        t.sku as sku,
        max(so.supplier_id) as supplierId,
        max(so.supplier_name) as supplierName,
        t.supplier_delivery_no as supplierDeliveryNo,
        soi.purchase_plan_no as purchasePlanNo,
        min(soi.create_date) as stockinDate,
        t.product_id as productId,
        wsc.length as length,
        wsc.width as width,
        wsc.height as height,
        wsc.weight as weight,
        wsc.measure_type as heightMeasureType,
        wsc.volume_weight as volumeWeight,
        wsc.fba_cost as fbaCost,
        wsc.measure_height as measureHeight,
        wsc.measure_volume_weight as measureVolumeWeight,
        wsc.measure_fba_cost as measureFbaCost,
        wsc.sorting_port as sortingPort,
        t.create_by as createBy,
        t.create_date as createDate,
        t.is_over_standard as isOverStandard,
        sum(soi.qty) as arrivalCount,
        t.box_qty as boxQty,
        t.standard_id as standardId
        from  stockin_volume_weight_record_mapping t
        inner join wcs_height_sorting_record wsc on wsc.id = t.record_id
        left join stockin_order_item soi on soi.internal_box_code = t.internal_box_code and t.sku = soi.sku
        left join stockin_order so on so.stockin_order_id= soi.stockin_order_id
        <include refid="pageDetailCondition"></include>
        group by t.internal_box_code,t.supplier_delivery_no,t.sku,t.id
        order by createDate desc
    </select>
    
    <select id="getHeightPageExportList" resultType="com.nsy.api.wms.response.stockin.StockinHeightRecordMappingExportResponse">
        select t.internal_box_code as internalBoxCode,
        t.sku as sku,
        t.package_name as packageName,
        max(so.supplier_id) as supplierId,
        max(so.supplier_name) as supplierName,
        t.supplier_delivery_no as supplierDeliveryNo,
        soi.purchase_plan_no as purchasePlanNo,
        min(soi.create_date) as stockinDate,
        t.product_id as productId,
        wsc.length as length,
        wsc.width as width,
        wsc.height as height,
        wsc.weight as weight,
        wsc.measure_type as heightMeasureType,
        wsc.volume_weight as volumeWeight,
        wsc.fba_cost as fbaCost,
        wsc.measure_height as measureHeight,
        wsc.measure_volume_weight as measureVolumeWeight,
        wsc.measure_fba_cost as measureFbaCost,
        wsc.sorting_port as sortingPort,
        standard.length as lengthStandard,
        standard.width as widthStandard,
        standard.height as heightStandard,
        standard.weight as weightStandard,
        standard.length_inch as lengthInchStandard,
        standard.width_inch as widthInchStandard,
        standard.height_inch as heightInchStandard,
        standard.weight_pound as weightPoundStandard,
        standard.volume_weight_pound as volumeWeightPoundStandard,
        standard.standard_type as standardTypeStandard,
        standard.charged_weight as chargedWeightStandard,
        standard.fba_cost as fbaCostStandard,
        t.create_by as createBy,
        t.create_date as createDate,
        t.is_over_standard as isOverStandard,
        sum(soi.qty) as arrivalCount,
        t.box_qty as boxQty
        from  stockin_volume_weight_record_mapping  t
        inner join wcs_height_sorting_record wsc on wsc.id = t.record_id
        left join stockin_volume_weight_standrd_record standard on standard.id = t.standard_id
        left join stockin_order_item soi on soi.internal_box_code = t.internal_box_code and t.sku = soi.sku
        left join stockin_order so on so.stockin_order_id= soi.stockin_order_id
        <if test="request.spuList != null and request.spuList.size() > 0 ">
            inner join product_info p on p.product_id = t.product_id
        </if>
        <include refid="pageCondition"></include>
        group by t.internal_box_code,t.supplier_delivery_no,t.sku,t.id
        order by t.create_date desc
    </select>
</mapper>
