<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinSupplierDeliveryOrderMapper">

    <select id="pageSearchSupplierDeliveryOrderList"
            resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderListResponse">
        SELECT sps.supplier_delivery_no AS supplierDeliveryNo,
            sps.receipt_place as receiptPlace,
            sps.remarks as remarks,
            sps.platform_schedule_type AS stockinType,
            sps.supplier_name AS supplierName,
            sps.supplier_id AS supplierId,
            min(sps.audit_date) AS createDate,
            case sps.stockin_status when 'WART_INBOUND' then 'PENDING'
            when 'INBOUNDING' then 'RECEIVING'
            when 'INBOUNDED' then 'RECEIVING'
            when 'COMPLETED' then 'SHELVED'
            end as status,
            (select max(oi.update_date) from stockin_order_task t
            INNER JOIN stockin_order o on o.task_id = t.task_id
            INNER JOIN stockin_order_item oi on o.stockin_order_id = oi.stockin_order_id
            where t.supplier_delivery_no = sps.supplier_delivery_no) as updateDate

        FROM stock_platform_schedule sps
        <if test="query.spaceIds != null and query.spaceIds.size() > 0
        or query.sku != null and query.sku !=''
        or query.brandName != null and query.brandName !=''
        or  query.isFbaQuick != null">
            inner join stock_platform_schedule_item spsi on sps.platform_schedule_id = spsi.platform_schedule_id
        </if>
        <include refid="searchSupplierDeliveryOrderCondition"></include>
        group by sps.supplier_delivery_no
        <trim prefix="having" prefixOverrides="and">
            <if test="query.updateStartDate != null">
                AND updateDate &gt;= #{query.updateStartDate}
            </if>
            <if test="query.updateEndDate != null">
                AND updateDate &lt;= #{query.updateEndDate}
            </if>
        </trim>
        order by createDate desc

    </select>

    <select id="countSearchSupplierDeliveryOrderList" resultType="java.lang.Integer">
        SELECT count(*) from
        (SELECT sps.supplier_delivery_no,
        (select max(oi.update_date) from stockin_order_task t
        INNER JOIN stockin_order o on o.task_id = t.task_id
        INNER JOIN stockin_order_item oi on o.stockin_order_id = oi.stockin_order_id
        where t.supplier_delivery_no = sps.supplier_delivery_no) as updateDate
        FROM stock_platform_schedule sps
        <if test="query.spaceIds != null and query.spaceIds.size() > 0 or query.sku != null and query.sku !='' or query.brandName != null and query.brandName !='' or  query.isFbaQuick != null">
            inner join stock_platform_schedule_item spsi on sps.platform_schedule_id = spsi.platform_schedule_id
        </if>
        <include refid="searchSupplierDeliveryOrderCondition"></include>
        group by sps.supplier_delivery_no
        <trim prefix="having" prefixOverrides="and">
            <if test="query.updateStartDate != null">
                AND updateDate &gt;= #{query.updateStartDate}
            </if>
            <if test="query.updateEndDate != null">
                AND updateDate &lt;= #{query.updateEndDate}
            </if>
        </trim>
        ) t

    </select>

    <select id="statusCountSupplierDeliveryOrder"
            resultType="com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderStatusDTO">
        SELECT t.STATUS,
               count(*) as num
        FROM (SELECT IF(sum(IF(ANY_VALUE(so.`status`) IN ('COMPLETED', 'RECEIVED'), 2,
                               IF(ANY_VALUE(sot.`status`) = 'PENDING', 1, 0))) = 2 * count(*),
                        'SHELVED', IF(sum(IF(ANY_VALUE(so.`status`) IN ('COMPLETED', 'RECEIVED'), 2,
                                             IF(ANY_VALUE(sot.`status`) = 'PENDING', 1, 0))) = count(*),
                                      'PENDING', 'RECEIVING')) AS STATUS
              FROM stockin_order_task sot
                       LEFT JOIN stockin_order so
                                 ON sot.task_id = so.task_id
              WHERE sot.stockin_type = 'FACTORY'
              GROUP BY sot.supplier_delivery_no) t
        GROUP BY t.STATUS
    </select>

    <select id="findFactorySupplierDeliveryNoBySku" resultType="java.lang.String">
        SELECT DISTINCT sot.supplier_delivery_no
        FROM stockin_order_task sot
                 LEFT JOIN stockin_order_task_item soti ON sot.task_id = soti.task_id
        WHERE sot.stockin_type = "FACTORY"
          AND soti.sku = #{sku}
    </select>


    <select id="countShelvedNumBySupplierDeliveryNo"
            resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderSkuDetail">
        SELECT
        sot.supplier_delivery_no as supplierDeliveryOrderNo,
        ssti.sku as sku,
        any_value(p.skc) as skc,
        any_value(p.size) as size,
        sum( ssti.shelved_qty ) as shelveQty
        FROM
        stockin_shelve_task_item ssti
        LEFT JOIN stockin_order so ON ssti.source_id = so.stockin_order_id
        LEFT JOIN stockin_order_task sot ON so.task_id = sot.task_id
        LEFT JOIN product_spec_info p on p.spec_id =  ssti.spec_id
        WHERE
        sot.supplier_delivery_no IN
        <foreach collection="supplierDeliveryNoList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        GROUP BY
        sot.supplier_delivery_no,ssti.sku
    </select>

    <select id="getStockinSupplierDeliveryOrderInfoBySupplierDeliveryNo"
            resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderInfoResponse">
        SELECT sot.supplier_delivery_no,
               ANY_VALUE(bs.space_name)                                          AS spaceName,
               ANY_VALUE(sps.plan_arrive_date)                                   AS planArriveDate,
               IFNULL(ANY_VALUE(sps.supplier_name), ANY_VALUE(so.supplier_name)) AS supplierName,
               min(sps.audit_date)                                              AS createDate,
               sum(soti.expected_qty)                                            AS expectedQty,
               case sps.stockin_status when 'WART_INBOUND' then 'PENDING'
                                       when 'INBOUNDING' then 'RECEIVING'
                                       when 'INBOUNDED' then 'RECEIVING'
                                       when 'COMPLETED' then 'SHELVED'
                   end as status
        FROM stockin_order_task sot
                 LEFT JOIN stockin_order so ON so.task_id = sot.task_id
                 LEFT JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
                 LEFT JOIN bd_space bs ON soti.space_id = bs.space_id
                 LEFT JOIN stock_platform_schedule sps ON sot.platform_schedule_id = sps.platform_schedule_id
        WHERE sot.supplier_delivery_no = #{supplierDeliveryNo}
        GROUP BY sot.supplier_delivery_no
    </select>


    <select id="pageSearchStockinSupplierDeliveryOrderItemList"
            resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderItemSkuListResponse">
        SELECT
            t.sku,
            t.is_fba_quick,
            t.barcode,
            t.seller_barcode,
            t.supplier_delivery_box_code,
            ifnull(sum( t.expected_qty ), 0) AS expected_qty,
            ifnull(sum( t.shelved_qty ), 0) AS shelved_qty,
            ifnull(sum( t.return_qty ), 0) AS returnQty,
            t.task_item_id,
            t.stockin_type,
            t.space_id,
            t.spec_id,
            t.area_name,
            t.store_id,
            t.business_type,
            t.vacuum_flag,
            t.brand_name,
            psi.thumbnail_image_url AS thumbnailImageUrl,
            psi.preview_image_url AS previewImageUrl,
            psi.color AS color,
            psi.size AS size,
            psi.skc as skc,
            pi.package_vacuum as packageVacuum,
            min(t.canEdit) as canEdit
        FROM
        (
            SELECT
                soti.sku,
                soti.is_fba_quick,
                soti.barcode,
                soti.seller_barcode,
                sot.supplier_delivery_box_code,
                soti.expected_qty,
                soti.task_item_id,
                sot.stockin_type,
                soti.space_id,
                soti.spec_id,
                soti.area_name,
                soti.store_id,
                soti.business_type,
                soti.vacuum_flag,
                soti.brand_name,
                min(if(so.stockin_order_id is null
                           or soi.stockin_order_item_id is null
                           or so.status in ('CHECKING','CONFIRMING','COMPLETED')
                        or so.stockin_type = 'OVER',0,1)) as canEdit,
                sum( soi.return_qty ) AS return_qty,
                sum( soi.shelved_qty ) AS shelved_qty
            FROM
                stockin_order_task sot
            INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
            LEFT JOIN stockin_order so on so.task_id = sot.task_id
            LEFT JOIN stockin_order_item soi ON soi.task_item_id = soti.task_item_id
            <where>
                sot.supplier_delivery_no = #{query.supplierDeliveryNo}
                <if test="query.sku != null and query.sku !=''">
                    and soti.sku like concat(#{query.sku}, '%')
                </if>
                <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                    and sot.supplier_delivery_box_code = #{query.supplierDeliveryBoxCode}
                </if>
            </where>
            GROUP BY
            soti.task_item_id
        ) t
        LEFT JOIN product_spec_info psi ON t.spec_id = psi.spec_id
        LEFT JOIN product_info pi on psi.product_id = pi.product_id
        GROUP BY
            supplier_delivery_box_code,
            sku
        <if test="query.isComplete != null">
        HAVING
            <if test="query.isComplete != null and query.isComplete ">
                expected_qty &lt;= shelved_qty
            </if>
            <if test="query.isComplete != null and !query.isComplete ">
                expected_qty &gt; shelved_qty
            </if>
        </if>
        order by skc
    </select>

    <select id="countShelvedQtyBySupplierDeliveryBoxCodeAndSku"
            resultType="com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderShelvedNumDTO">
        SELECT
        so.supplier_delivery_box_code AS supplierDeliveryBoxCode,
        ssti.sku,
        sum( ssti.shelved_qty ) AS shelvedQty
        FROM
        stockin_shelve_task_item ssti
        LEFT JOIN stockin_order so ON ssti.source_id = so.stockin_order_id
        LEFT JOIN stockin_order_task sot ON so.task_id = sot.task_id
        WHERE
        so.supplier_delivery_box_code IN
        <foreach collection="supplierDeliveryBoxCodeList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        AND ssti.sku IN
        <foreach collection="skuList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        GROUP BY
        so.supplier_delivery_box_code,
        ssti.sku
    </select>


    <select id="stockinSupplierDeliveryOrderDetailList"
            resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderBoxListResponse">
        SELECT
        stock_internal_box.internal_box_code AS internalBoxCode,
        ANY_VALUE ( stock_internal_box.STATUS ) AS STATUS,
        ANY_VALUE ( stock_internal_box.space_area_name ) AS spaceAreaName,
        sum( soi.qty ) AS qty,
        ANY_VALUE ( stock_internal_box.update_by ) AS updateBy,
        ANY_VALUE ( stock_internal_box.update_date ) AS updateDate,
        soi.sku,
        pi.spu
        FROM
        stock_internal_box stock_internal_box
        LEFT JOIN stockin_order_item soi ON soi.internal_box_code = stock_internal_box.internal_box_code
        LEFT JOIN stockin_order so ON soi.stockin_order_id = so.stockin_order_id
        LEFT JOIN product_info pi ON pi.product_id = soi.product_id
        <where>
            <if test="request.stockinOrderNoList!=null and request.stockinOrderNoList.size() > 0">
                and so.stockin_order_no in
                <foreach collection="request.stockinOrderNoList" separator="," index="index" item="stockinOrderNo"
                         open="(" close=")">
                    #{stockinOrderNo}
                </foreach>
            </if>
        </where>
        GROUP BY stock_internal_box.internal_box_code, soi.sku, pi.spu
    </select>

    <select id="pageSearchStockinOrderLog" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity">
        SELECT
        sol.*
        FROM
        stockin_order_task sot
        RIGHT JOIN stockin_order so ON so.task_id = sot.task_id
        RIGHT JOIN stockin_order_log sol ON so.stockin_order_id = sol.stockin_order_id
        where
            sot.supplier_delivery_no = #{request.supplierDeliveryNo}
        order by sol.stockin_order_log_id
    </select>

    <select id="selectDirectShelvePositionCodeByInternalBoxCode"
            resultType="com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderPositionCodeByInternalBoxCodeDTO">
        SELECT
        sib. internal_box_code as internalBoxCode,
        ANY_VALUE(ssti.position_code) as positionCode
        FROM
        stock_internal_box sib
        LEFT JOIN stockin_shelve_task sst ON sib.internal_box_id = sst.internal_box_id
        LEFT JOIN stockin_shelve_task_item ssti ON sst.shelve_task_id = ssti.shelve_task_id
        <where>
            <if test="internalBoxCodeList!=null and internalBoxCodeList.size() > 0">
                and sib.internal_box_code in
                <foreach collection="internalBoxCodeList" separator="," index="index" item="internalBoxCode"
                         open="(" close=")">
                    #{internalBoxCode}
                </foreach>
            </if>
        </where>
        group by sib.internal_box_code
    </select>

    <select id="findSpaceAndStockinQty"
            resultType="com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderSpaceAndStockinQtyDTO">
        SELECT
        sot.supplier_delivery_no,
        any_value(soti.space_id) as space_id,
        any_value(soti.purchase_plan_no) as purchasePlanNo,
        any_value(bs.space_name) as space_name,
        SUM(soti.stockin_qty) as stockin_qty,
        SUM(soti.expected_qty) as expected_qty,
        max(soti.is_fba_quick) as isFbaQuick,
        soti.sku as sku,
        any_value(p.skc) as skc,
        any_value(p.size) as size,
        count(DISTINCT sot.task_id) AS boxNum,
        max(sot.operate_end_date) AS operateEndDate
        FROM
        stockin_order_task sot
        LEFT JOIN stockin_order_task_item soti ON sot.task_id = soti.task_id
        left join product_spec_info p on p.spec_id =  soti.spec_id
        left join bd_space bs on soti.space_id = bs.space_id
        WHERE
        sot.supplier_delivery_no IN
        <foreach collection="supplierDeliveryNoList" separator="," index="index" item="supplierDeliveryNo"
                 open="(" close=")">
            #{supplierDeliveryNo}
        </foreach>
        GROUP BY supplier_delivery_no,soti.sku
    </select>

    <select id="sumExpectedQtyBySupplierDeliveryOrderNo" resultType="java.lang.Integer">
        <choose>
            <when test="query.updateStartDate != null or query.updateEndDate != null">
                SELECT ifnull(sum(t.expected_qty),0) from (
                SELECT ifnull(sum(spsi.qty),0) as expected_qty,
                (select max(oi.update_date) from stockin_order_task t
                INNER JOIN stockin_order o on o.task_id = t.task_id
                INNER JOIN stockin_order_item oi on o.stockin_order_id = oi.stockin_order_id
                where t.supplier_delivery_no = sps.supplier_delivery_no) as updateDate
                FROM stock_platform_schedule sps
                inner join stock_platform_schedule_item spsi on sps.platform_schedule_id = spsi.platform_schedule_id
                <include refid="searchSupplierDeliveryOrderCondition"></include>
                group by sps.supplier_delivery_no
                <trim prefix="having" prefixOverrides="and">
                    <if test="query.updateStartDate != null">
                        AND updateDate &gt;= #{query.updateStartDate}
                    </if>
                    <if test="query.updateEndDate != null">
                        AND updateDate &lt;= #{query.updateEndDate}
                    </if>
                </trim>
                ) t
            </when>
            <otherwise>
                SELECT ifnull(sum(spsi.qty),0) as expected_qty
                FROM stock_platform_schedule sps
                inner join stock_platform_schedule_item spsi on sps.platform_schedule_id = spsi.platform_schedule_id
                <include refid="searchSupplierDeliveryOrderCondition"></include>
            </otherwise>
        </choose>


    </select>

    <select id="sumQtyBySupplierDeliveryOrderNo" resultType="com.nsy.api.wms.response.stockin.StockinQtySumResponse">
        SELECT
        ifnull(SUM(soi.shelved_qty),0) as shelvedQtyTotal,ifnull(sum(soi.qty),0) as stockinQtyTotal
        FROM
        (
            SELECT sps.supplier_delivery_no AS supplierDeliveryNo
                 <if test="query.updateStartDate != null or query.updateEndDate != null">
                    ,(select max(oi.update_date) from stockin_order_task t
                    INNER JOIN stockin_order o on o.task_id = t.task_id
                    INNER JOIN stockin_order_item oi on o.stockin_order_id = oi.stockin_order_id
                    where t.supplier_delivery_no = sps.supplier_delivery_no) as updateDate
                 </if>
            FROM stock_platform_schedule sps
            <if test="query.spaceIds != null and query.spaceIds.size() > 0 or query.sku != null and query.sku !='' or query.brandName != null and query.brandName !='' or  query.isFbaQuick != null">
                inner join stock_platform_schedule_item spsi on sps.platform_schedule_id = spsi.platform_schedule_id
            </if>
            <include refid="searchSupplierDeliveryOrderCondition"></include>
            group by sps.supplier_delivery_no
            <trim prefix="having" prefixOverrides="and">
                <if test="query.updateStartDate != null">
                    AND updateDate &gt;= #{query.updateStartDate}
                </if>
                <if test="query.updateEndDate != null">
                    AND updateDate &lt;= #{query.updateEndDate}
                </if>
            </trim>
        ) as t
        INNER JOIN stockin_order_task sot on t.supplierDeliveryNo = sot.supplier_delivery_no
        INNER JOIN stockin_order_task_item soti ON sot.task_id = soti.task_id
        left join stockin_order_item soi on soi.task_item_id = soti.task_item_id

    </select>

    <select id="getPrintInfo" resultType="com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryPrintDTO">
        select distinct sot.task_id,sot.supplier_delivery_box_code supplierDeliveryBoxCode,sot.supplier_delivery_barcode as supplierDeliveryBarcode ,
        sps.supplier_name as supplierName, spsi.store_id as storeId,sps.purchase_user_real_name as purchaseUserRealName
        from stockin_order_task sot
        left join stock_platform_schedule sps on sps.platform_schedule_id = sot.platform_schedule_id
        left join stock_platform_schedule_item spsi on spsi.platform_schedule_id = sot.platform_schedule_id
         WHERE
        sot.supplier_delivery_no  = #{supplierDeliveryNo}
        order by sot.box_index
    </select>
    <select id="findShelvePositionByStockOrderIdAndSku" resultType="java.lang.String">

        select DISTINCT sti.position_code
        from stockin_order_item oi
                 INNER JOIN stockin_shelve_task st
                            on st.internal_box_code = oi.internal_box_code
                 INNER JOIN stockin_shelve_task_item sti
                            on sti.source_id = oi.stockin_order_id
                                and sti.sku = #{sku}
                                and sti.purchase_plan_no = oi.purchase_plan_no
        where oi.stockin_order_id = #{stockinOrderId}
          and oi.sku = #{sku}
          and sti.shelved_qty > 0

    </select>
    <select id="pageSearchDeliveryNos" resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderQcListResponse">
        select t.supplier_delivery_no,
        t.supplierName,
        t.create_date,
        t.delivery_date,
        t.audit_date,
        t.expectedTotalQty,
        CASE WHEN t.pendingCount = t.allCount then 'PENDING'
        when t.shelveCount = t.allCount then 'SHELVED'
        else 'RECEIVING' end as `status`
        from (
        select sot.supplier_delivery_no,
        count(if(sot.`status` = 'PENDING',1,null)) as pendingCount,
        count(if(so.`status` = 'COMPLETED',1,null)) as shelveCount,
        count(sot.task_id) as allCount,
        ifnull(so.supplier_name, sps.supplier_name) AS supplierName,
        sot.create_date,
        sps.delivery_date,
        sps.audit_date,
        sum(sot.expected_qty) as expectedTotalQty
        from stockin_order_task sot
        left join stockin_order so on sot.task_id = so.task_id
        LEFT JOIN stock_platform_schedule sps ON sot.platform_schedule_id = sps.platform_schedule_id
        <if test="(query.skc != null and query.skc !='') or (query.sku != null and query.sku !='') or (query.spaceIds != null and query.spaceIds.size() > 0)">
        inner join stockin_order_task_item soti on sot.task_id = soti.task_id
        LEFT JOIN product_spec_info psi on psi.spec_id = soti.spec_id
        </if>
        <where>
            <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                AND sot.supplier_delivery_box_code = #{query.supplierDeliveryBoxCode}
            </if>
            <if test="query.supplierDeliveryNoSet != null and query.supplierDeliveryNoSet.size() > 0">
                AND sot.supplier_delivery_no in
                <foreach collection="query.supplierDeliveryNoSet" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                AND sot.supplier_delivery_no =#{query.supplierDeliveryNo}
            </if>
            <if test="query.supplierId != null">
                AND sps.supplier_id = #{query.supplierId}
            </if>
            <if test="query.createStartDate != null">
                and sot.create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query.createEndDate != null">
                and sot.create_date &lt;= #{query.createEndDate}
            </if>
            <if test="query.sku != null and query.sku !=''">
                and soti.sku like concat(#{query.sku}, '%')
            </if>
            <if test="query.skc != null and query.skc !=''">
                and psi.skc = #{query.skc}
            </if>
            <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                AND soti.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY sot.supplier_delivery_no
        ) t
        <if test="(query.statusList != null and query.statusList.size()>0)">
            <trim prefix="having" prefixOverrides="and">
                <if test="query.statusList != null and query.statusList.size()>0">
                    and status in
                    <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                             close=")">
                        #{status}
                    </foreach>
                </if>
            </trim>
        </if>
    </select>

    <select id="countSearchDeliveryNos" resultType="java.lang.Integer">
        select count(*) from(
        select t.supplier_delivery_no,
        t.supplierName,
        CASE WHEN t.pendingCount = t.allCount then 'PENDING'
        when t.shelveCount = t.allCount then 'SHELVED'
        else 'RECEIVING' end as `status`
        from (
        select sot.supplier_delivery_no,
        count(if(sot.`status` = 'PENDING',1,null)) as pendingCount,
        count(if(so.`status` = 'COMPLETED',1,null)) as shelveCount,
        count(sot.task_id) as allCount,
        ifnull(so.supplier_name, sps.supplier_name) AS supplierName
        from stockin_order_task sot
        left join stockin_order so
        on sot.task_id = so.task_id
        LEFT JOIN stock_platform_schedule sps
        ON sot.platform_schedule_id = sps.platform_schedule_id
        <if test="(query.skc != null and query.skc !='') or (query.sku != null and query.sku !='') or (query.spaceIds != null and query.spaceIds.size() > 0)">
            inner join stockin_order_task_item soti on sot.task_id = soti.task_id
            LEFT JOIN product_spec_info psi on psi.spec_id = soti.spec_id
        </if>
        <where>
            <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                AND sot.supplier_delivery_box_code = #{query.supplierDeliveryBoxCode}
            </if>
            <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                AND sot.supplier_delivery_no =#{query.supplierDeliveryNo}
            </if>
            <if test="query.supplierId != null">
                AND sps.supplier_id = #{query.supplierId}
            </if>
            <if test="query.createStartDate != null">
                and sot.create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query.createEndDate != null">
                and sot.create_date &lt;= #{query.createEndDate}
            </if>
            <if test="query.sku != null and query.sku !=''">
                and soti.sku = #{query.sku}
            </if>
            <if test="query.skc != null and query.skc !=''">
                and psi.skc = #{query.skc}
            </if>
            <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                AND soti.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY sot.supplier_delivery_no
        ) t
        <if test="(query.statusList != null and query.statusList.size()>0)">
            <trim prefix="having" prefixOverrides="and">
                <if test="query.statusList != null and query.statusList.size()>0">
                    and status in
                    <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                             close=")">
                        #{status}
                    </foreach>
                </if>
            </trim>
        </if>
        ) T
    </select>
    <select id="searchSupplierDeliveryOrderItem" resultType="com.nsy.api.wms.domain.stockin.StockinSupplierDeliveryOrderQcDTO">
        select t.supplier_delivery_no,
               t.supplier_delivery_box_code,
               bs.space_name,
               p.skc,
               p.size,
               ti.task_item_id,
               ti.sku,
               ti.package_name,
               ti.expected_qty,
               ti.qa_qty,
               oi.status as subStatus,
               ifnull(oi.qty,0) as stockinQty,
               oi.internal_box_code,
                ifnull(oi.shelved_qty,0) as shelvedQty,
            ifnull(oi.return_qty,0) as returnQty,
               if(b.internal_box_type = 'QA_BOX','质检箱','收货箱') as internalBoxType
        from stockin_order_task t
                 INNER JOIN stockin_order_task_item ti
                            on t.task_id = ti.task_id
                 LEFT JOIN stockin_order_item oi
                           on oi.task_item_id = ti.task_item_id
                 left join product_spec_info p on p.spec_id = ti.spec_id
                 left join bd_space bs on ti.space_id = bs.space_id
                left join stock_internal_box b on b.internal_box_code = oi.internal_box_code
        where t.supplier_delivery_no in
        <foreach collection="supplierDeliveryNos" separator="," index="index" item="supplierDeliveryNo" open="("
                 close=")">
            #{supplierDeliveryNo}
        </foreach>

    </select>
    <select id="pageSearchStockinSupplierDeliveryOrderItemListForDownload"
            resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderItemSkuListResponse">
        SELECT
        t.sku,
        t.is_fba_quick,
        t.barcode,
        t.seller_barcode,
        t.supplier_delivery_box_code,
        ifnull(sum( t.expected_qty ), 0) AS expected_qty,
        ifnull(sum( t.shelved_qty ), 0) AS shelved_qty,
        ifnull(sum( t.return_qty ), 0) AS returnQty,
        t.task_item_id,
        t.stockin_type,
        t.space_id,
        t.spec_id,
        t.area_name,
        t.store_id,
        t.business_type,
        t.vacuum_flag,
        t.brand_name,
        psi.thumbnail_image_url AS thumbnailImageUrl,
        psi.preview_image_url AS previewImageUrl,
        psi.color AS color,
        psi.size AS size,
        psi.skc as skc,
        pi.package_vacuum as packageVacuum,
        min(t.canEdit) as canEdit
        FROM
        (
        SELECT
        soti.sku,
        soti.is_fba_quick,
        soti.barcode,
        soti.seller_barcode,
        sot.supplier_delivery_box_code,
        soti.expected_qty,
        soti.task_item_id,
        sot.stockin_type,
        soti.space_id,
        soti.spec_id,
        soti.area_name,
        soti.store_id,
        soti.business_type,
        soti.vacuum_flag,
        soti.brand_name,
        min(if(so.stockin_order_id is null
        or soi.stockin_order_item_id is null
        or so.status in ('CHECKING','CONFIRMING','COMPLETED')
        or so.stockin_type = 'OVER',0,1)) as canEdit,
        sum( soi.return_qty ) AS return_qty,
        sum( soi.shelved_qty ) AS shelved_qty
        FROM
        stockin_order_task sot
        INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
        LEFT JOIN stockin_order so on so.task_id = sot.task_id
        LEFT JOIN stockin_order_item soi ON soi.task_item_id = soti.task_item_id
        <where>
            sot.supplier_delivery_no = #{query.supplierDeliveryNo}
        </where>
        GROUP BY
        soti.task_item_id
        ) t
        LEFT JOIN product_spec_info psi ON t.spec_id = psi.spec_id
        LEFT JOIN product_info pi on psi.product_id = pi.product_id
        GROUP BY
        supplier_delivery_box_code,
        sku
        order by skc
    </select>

    <select id="efficientPageList" resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryEfficientStatusDTO">
        SELECT
            DISTINCT sot.supplier_delivery_no,
        CASE
            WHEN COUNT(CASE WHEN sotime.complete_date IS NOT NULL THEN 1 END) = COUNT(*)  THEN '已完成'
            WHEN COUNT(CASE WHEN sotime.complete_shelved_date IS NOT NULL THEN 1 END) = COUNT(*)
                AND COUNT(CASE WHEN sotime.complete_date IS NULL THEN 1 END) > 0 THEN '待核对'
            WHEN COUNT(CASE WHEN sotime.complete_qc_date IS NOT NULL THEN 1 END) = COUNT(*) THEN '上架中'
            WHEN COUNT(CASE WHEN sotime.generate_date IS NOT NULL THEN 1 END) = COUNT(*)
                AND COUNT(CASE WHEN sotime.complete_qc_date IS NULL THEN 1 END) > 0 THEN '质检中'
            WHEN COUNT(CASE WHEN sotime.generate_date IS NULL THEN 1 END) > 0 THEN '收货中'
            ELSE '待收货'
            END AS status,
        min(sps.audit_date) as auditDate,
        max(sotime.received_date) as receivedEndDate,
        min(sotime.start_qc_date) as completeQcStartDate,
        max(sotime.complete_qc_date) as completeQcEndDate,
        min(sotime.shelve_date) as completeShelvedStartDate,
        max(sotime.complete_shelved_date) as completeShelvedEndDate,
        max(sotime.checked_date) as checkedDate,
        max(sotime.complete_date) as completeDate
        FROM
            stockin_order_task sot
        INNER JOIN stock_platform_schedule sps ON sps.platform_schedule_id = sot.platform_schedule_id
        INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
        LEFT JOIN stockin_order so ON so.task_id = sot.task_id
        LEFT JOIN stockin_order_time sotime ON sotime.stockin_order_id = so.stockin_order_id
        where
        sot.supplier_delivery_no in
        <foreach collection="supplierDeliveryNos" separator="," index="index" item="supplierDeliveryNo" open="("
                 close=")">
            #{supplierDeliveryNo}
        </foreach>
        GROUP BY sot.supplier_delivery_no
        order by sps.audit_date desc
    </select>

    <select id="calculateDeliveryNos" resultType="java.lang.Double">
        select ifnull(ROUND(AVG(TIMESTAMPDIFF(MINUTE, t.auditDate, t.completeDate) / 60.0), 1),0) from (
            SELECT
                min(sps.audit_date) as auditDate,
                min(CASE WHEN sotime.complete_date IS NULL THEN 0 ELSE sotime.complete_date END) as completeStartDate,
                max(sotime.complete_date) as completeDate
            FROM
            stockin_order_task sot
            INNER JOIN stock_platform_schedule sps ON sps.platform_schedule_id = sot.platform_schedule_id
            LEFT JOIN stockin_order so ON so.task_id = sot.task_id
            LEFT JOIN stockin_order_time sotime ON sotime.stockin_order_id = so.stockin_order_id
            <if test="(query.skc != null and query.skc !='') or (query.sku != null and query.sku !='') or (query.spaceIds != null and query.spaceIds.size() > 0)">
            inner join stockin_order_task_item soti on sot.task_id = soti.task_id
            LEFT JOIN product_spec_info psi on psi.spec_id = soti.spec_id
            </if>
            <where>
                <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                    AND sot.supplier_delivery_box_code = #{query.supplierDeliveryBoxCode}
                </if>
                <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                    AND sot.supplier_delivery_no =#{query.supplierDeliveryNo}
                </if>
                <if test="query.supplierId != null">
                    AND sps.supplier_id = #{query.supplierId}
                </if>
                <if test="query.createStartDate != null">
                    and sot.create_date &gt;= #{query.createStartDate}
                </if>
                <if test="query.createEndDate != null">
                    and sot.create_date &lt;= #{query.createEndDate}
                </if>
                <if test="query.sku != null and query.sku !=''">
                    and soti.sku = #{query.sku}
                </if>
                <if test="query.skc != null and query.skc !=''">
                    and psi.skc = #{query.skc}
                </if>
                <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                    AND soti.space_id in
                    <foreach collection="query.spaceIds" separator="," index="index" item="item" open="("
                             close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
        GROUP BY sot.supplier_delivery_no
        HAVING completeStartDate > 0
        AND completeDate IS NOT NULL
        ) t
    </select>

    <sql id="searchSupplierDeliveryOrderCondition">
        where sps.status = 'AUDITED'
        <if test="query.spaceIds != null and query.spaceIds.size() > 0 or query.sku != null and query.sku !='' or query.brandName != null and query.brandName !='' or  query.isFbaQuick != null">
            <if test="query.sku != null and query.sku !=''">
                and spsi.sku like concat(#{query.sku}, '%')
            </if>
            <if test="query.brandName != null and query.brandName !=''">
                and spsi.brand_name = #{query.brandName}
            </if>
            <if test="query.isFbaQuick != null">
                and spsi.is_fba_quick = #{query.isFbaQuick}
            </if>
            <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                and spsi.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                         close=")">
                    #{spaceId}
                </foreach>
            </if>
        </if>
        <if test="query.statusList != null and query.statusList.size()>0">
            and sps.stockin_status in
            <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                     close=")">
                #{status}
            </foreach>
        </if>


        <if test="query.containAllotCount != null and query.containAllotCount == true">
            and sps.platform_schedule_type in ('FACTORY','ALLOT')
        </if>
        <if test="query.containAllotCount != null and query.containAllotCount == false">
            and sps.platform_schedule_type = 'FACTORY'
        </if>
        <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
            AND sps.supplier_delivery_no =#{query.supplierDeliveryNo}
        </if>
        <if test="query.supplierDeliveryNoSet != null and query.supplierDeliveryNoSet.size() > 0">
            AND sps.supplier_delivery_no in
            <foreach collection="query.supplierDeliveryNoSet" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.selectNoList != null and query.selectNoList.size() > 0">
            AND sps.supplier_delivery_no in
            <foreach collection="query.selectNoList" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.supplierId != null">
            AND sps.supplier_id = #{query.supplierId}
        </if>
        <if test="query.createStartDate != null">
            and sps.audit_date &gt;= #{query.createStartDate}
        </if>
        <if test="query.createEndDate != null">
            and sps.audit_date &lt;= #{query.createEndDate}
        </if>
        <if test="query.remarks != null and query.remarks !=''">
            and sps.remarks like concat(#{query.remarks},'%')
        </if>
        <if test="query.receiptPlace != null and query.receiptPlace !=''">
            and sps.receipt_place like concat(#{query.receiptPlace},'%')
        </if>
    </sql>
</mapper>
