<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderTaskMapper">
    <select id="statusStatistics" resultType="com.nsy.api.wms.domain.stockin.StatusStatistics">
        select s.status, count(*) count from stockin_order_task s group by s.status
    </select>

    <select id="pageList" resultType="com.nsy.api.wms.response.stockin.StockinOrderTaskListResponse">
        SELECT
        t.task_id,
        t.supplier_delivery_no,
        ps.platform_name,
        ps.platform_schedule_type AS stockin_type,
        ps.supplier_name,
        ps.plan_arrive_date,
        o.stockin_order_no,
        t.box_index,
        t.supplier_delivery_box_code,
        t.supplier_delivery_barcode,
        t.expected_qty,
        t.description,
        t.operator,
        t.`status`,
        i.is_fba_quick,
        i.vacuum_flag,
        i.space_name,
        if(max(i.isOEM)>0,1,0) as isOEM,
        if(max(i.isODM)>0,1,0) as isODM,
        ps.receipt_place as receiptPlace,
        ps.remarks as remarks,
        ps.audit_date as auditDate,
        t.operate_start_date,
        t.operate_end_date
        FROM
        stockin_order_task t
        LEFT JOIN stock_platform_schedule ps ON t.platform_schedule_id = ps.platform_schedule_id
        LEFT JOIN (select is_fba_quick,sku,task_id,vacuum_flag,brand_name,s.space_name
        ,FIND_IN_SET('OEM首单',label_attribute_names) as isOEM
        ,FIND_IN_SET('ODM首单',label_attribute_names) as isODM
        from stockin_order_task_item t 
        left join bd_space s on s.space_id = t.space_id
        <where>
            <if test="request.sku != null and request.sku !=''">and sku like  concat(#{request.sku}, '%')</if>
            <if test="request.brandName != null and request.brandName !=''">and brand_name = #{request.brandName}</if>
            <if test="request.isFbaQuick != null">and is_fba_quick = #{request.isFbaQuick}</if>
            <if test="request.vacuumFlag != null">and vacuum_flag = #{request.vacuumFlag}</if>
        </where>
        ) i on t.task_id = i.task_id
        LEFT JOIN stockin_order o ON o.task_id = t.task_id
        <if test="request.purchasePlanNo != null and request.purchasePlanNo !=''">
            left join stockin_order_task_item ti on t.task_id = ti.task_id
        </if>
        <include refid="pageCondition"></include>
        GROUP BY t.task_id
        ORDER BY
        t.task_id desc
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT
        count(distinct t.task_id)
        FROM
        stockin_order_task t
        LEFT JOIN stock_platform_schedule ps ON t.platform_schedule_id = ps.platform_schedule_id
        LEFT JOIN (select is_fba_quick,sku,task_id,vacuum_flag,brand_name
        ,FIND_IN_SET('OEM首单',label_attribute_names) as isOEM
        ,FIND_IN_SET('ODM首单',label_attribute_names) as isODM
        from stockin_order_task_item
        <where>
            <if test="request.sku != null and request.sku !=''">and sku like  concat(#{request.sku}, '%')</if>
            <if test="request.brandName != null and request.brandName !=''">and brand_name = #{request.brandName}</if>
            <if test="request.isFbaQuick != null">and is_fba_quick = #{request.isFbaQuick}</if>
            <if test="request.vacuumFlag != null">and vacuum_flag = #{request.vacuumFlag}</if>
        </where>
        ) i on t.task_id = i.task_id
        LEFT JOIN stockin_order o ON o.task_id = t.task_id
        <if test="request.purchasePlanNo != null and request.purchasePlanNo !=''">
            left join stockin_order_task_item ti on t.task_id = ti.task_id
        </if>
        <include refid="pageCondition"></include>
    </select>

    <sql id="pageCondition">
        <where>
            <if test="request.taskId != null">
                AND t.task_id = #{request.taskId}
            </if>
            <if test="request.stockinOrderNo != null and request.stockinOrderNo !=''">
                AND o.stockin_order_no = #{request.stockinOrderNo}
            </if>
            <if test="request.supplierId != null">
                AND ps.supplier_id= #{request.supplierId}
            </if>
            <if test="request.supplierDeliveryNo != null and request.supplierDeliveryNo !=''">
                AND t.supplier_delivery_no = #{request.supplierDeliveryNo}
            </if>
            <if test="request.supplierDeliveryBoxCode != null and request.supplierDeliveryBoxCode !=''">
                AND t.supplier_delivery_box_code = #{request.supplierDeliveryBoxCode}
            </if>
            <if test="request.supplierDeliveryBarcode != null and request.supplierDeliveryBarcode !=''">
                AND t.supplier_delivery_barcode = #{request.supplierDeliveryBarcode}
            </if>
            <if test="request.platformId != null">
                AND ps.platform_id = #{request.platformId}
            </if>
            <if test="request.stockinType != null and request.stockinType !=''">
                AND t.stockin_type = #{request.stockinType}
            </if>
            <if test="request.planArriveStartDate != null">
                and ps.plan_arrive_date &gt;= #{request.planArriveStartDate}
            </if>
            <if test="request.planArriveEndDate != null">
                and ps.plan_arrive_date &lt;= #{request.planArriveEndDate}
            </if>
            <if test="request.description != null and request.description !=''">
                AND t.description like concat('%',#{request.description},'%')
            </if>
            <if test="request.status != null and request.status !=''">
                AND t.status = #{request.status}
            </if>
            <if test="request.sku != null and request.sku !=''">
                AND i.sku like  concat(#{request.sku}, '%')
            </if>
            <if test="request.isFbaQuick != null">
                AND i.is_fba_quick = #{request.isFbaQuick}
            </if>
            <if test="request.vacuumFlag != null">
                AND i.vacuum_flag = #{request.vacuumFlag}
            </if>
            <if test="request.hasBrand != null">
                AND t.has_brand = #{request.hasBrand}
            </if>
            <if test="request.brandName != null and request.brandName !=''">
                AND i.brand_name = #{request.brandName}
            </if>
            <if test="request.remarks != null and request.remarks !=''">
                AND ps.remarks like concat(#{request.remarks},'%')
            </if>
            <if test="request.receiptPlace != null and request.receiptPlace !=''">
                AND ps.receipt_place like concat(#{request.receiptPlace},'%')
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                and t.status in
                <foreach collection="request.statusList" separator="," index="index" item="status" open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.createStartDate != null">
                and t.create_date &gt;= #{request.createStartDate}
            </if>
            <if test="request.createEndDate != null">
                and t.create_date &lt;= #{request.createEndDate}
            </if>
            <if test="request.purchasePlanNo != null and request.purchasePlanNo !=''">
                and ti.purchase_plan_no = #{request.purchasePlanNo}
            </if>
            <if test="request.logisticsNo != null and request.logisticsNo !=''">
                AND o.logistics_no = #{request.logisticsNo}
            </if>
        </where>
    </sql>

    <select id="baseInfo" resultType="com.nsy.api.wms.response.stockin.StockinOrderTaskBaseInfoResponse">
        SELECT
            t.task_id,
            t.supplier_delivery_no,
            t.box_index,
            t.supplier_delivery_box_code,
            ps.supplier_name,
            ps.platform_name,
            ps.platform_schedule_type AS stockin_type,
            t.stockin_method,
            t.qc_shelve,
            t.weight_process,
            t.expected_qty,
            ps.plan_arrive_date,
            t.operator,
            t.operate_start_date,
            t.operate_end_date,
            t.`status`,
            t.create_date
        FROM
            stockin_order_task t
            LEFT JOIN stock_platform_schedule ps ON t.platform_schedule_id = ps.platform_schedule_id
        where t.task_id = #{taskId}
    </select>
    <select id="statisticsByPlatformScheduleId"
            resultType="com.nsy.api.wms.response.stockin.StatisticsByPlatformScheduleIdResponse">
        SELECT sot.platform_schedule_id,
               soi.stockin_order_id,
               IFNULL(soi.return_qty,0) as returnQty,
                IFNULL(soi.shelved_qty,0) as shelvedQty,
               soi.qty as receiveQty
        FROM stockin_order_task sot
        INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
        INNER JOIN stockin_order_item soi ON soi.task_item_id = soti.task_item_id and soi.real_stockin_order_item_id = 0
        <where>
            <if test="platformScheduleIdList != null and platformScheduleIdList.size() > 0">
                and sot.platform_schedule_id in
                <foreach collection="platformScheduleIdList" separator="," index="index" item="platformScheduleId"
                         open="(" close=")">
                    #{platformScheduleId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findNoReceivedTask" resultType="com.nsy.api.wms.response.stockin.StockinOrderTaskListResponse">
        SELECT t.task_id,
               (select sum(expected_qty) from stockin_order_task_item where task_id = t.task_id) expected_qty,
               sum(oi.qty + oi.stockin_return_qty)                                               qty,
               t.stockin_type,
               t.supplier_delivery_no as                                                         supplierDeliveryNo
        FROM stockin_order_task t
                 LEFT JOIN stockin_order o ON t.task_id = o.task_id
                 LEFT JOIN stockin_order_item oi ON o.stockin_order_id = oi.stockin_order_id
        WHERE t.`status` = 'RECEIVING'
        GROUP BY t.task_id
        HAVING expected_qty = qty
    </select>

    <select id="findNoReceivedTaskBySupplierDeliveryNo"
            resultType="com.nsy.api.wms.response.stockin.StockinOrderTaskListResponse">
        SELECT t.task_id,
               (select sum(expected_qty) from stockin_order_task_item where task_id = t.task_id) as expectedQty,
               ifnull(sum(oi.qty + oi.stockin_return_qty), 0)                                    as stockinQty,
               t.stockin_type,
               t.supplier_delivery_no                                                            as supplierDeliveryNo
        FROM stockin_order_task t
                 LEFT JOIN stockin_order o ON t.task_id = o.task_id
                 LEFT JOIN stockin_order_item oi ON o.stockin_order_id = oi.stockin_order_id
        WHERE t.`status` != 'RECEIVED'
          and t.supplier_delivery_no = #{supplierDeliveryNo}
        GROUP BY t.task_id
    </select>

    <select id="pageSupplierDeliveryNoList"
            resultType="com.nsy.api.wms.response.stockin.StockinSupplierDeliveryListResponse">
        SELECT
        t.supplier_delivery_no,
        s.plan_arrive_date,
        s.supplier_name,
        sum(t.expected_qty) expected_qty,
        ifnull(sum(oi.qty),0) stockin_qty,
        GROUP_CONCAT(t.`status`) STATUS
        FROM
            stockin_order_task t
        LEFT JOIN stock_platform_schedule s ON s.supplier_delivery_no = t.supplier_delivery_no
        LEFT JOIN stockin_order o ON t.task_id = o.task_id
        LEFT JOIN stockin_order_item oi ON oi.stockin_order_id = o.stockin_order_id
        WHERE
            t.supplier_delivery_no IN (
                SELECT
                    supplier_delivery_no
                FROM
                    stockin_order_task
                WHERE
                    `status` != 'RECEIVED'
            )
        AND t.stockin_type = 'FACTORY'
        <if test="request.planArriveStartDate != null">
            and s.plan_arrive_date &gt;= #{request.planArriveStartDate}
        </if>
        <if test="request.planArriveEndDate != null">
            and s.plan_arrive_date &lt;= #{request.planArriveEndDate}
        </if>
        GROUP BY
            t.supplier_delivery_no;
    </select>

    <select id="getSupplierDeliveryNoFromInternalBox" resultType="java.lang.String">
        select DISTINCT sot.supplier_delivery_no from stock_internal_box_item  bi
        left join stockin_order o on bi.stock_in_order_no = o.stockin_order_no
        left join stockin_order_task sot on sot.task_id = o.task_id
        where internal_box_code = #{internalBoxCode}
        and sot.supplier_delivery_no is not null
    </select>

    <select id="findAllByPlatformScheduleIdList" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity">
        select * from stockin_order_task
        <where>
            <if test="platformScheduleIdList != null and platformScheduleIdList.size() > 0">
                and platform_schedule_id in
                <foreach collection="platformScheduleIdList" separator="," index="index" item="platformScheduleId"
                         open="(" close=")">
                    #{platformScheduleId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findAllByIds" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity">
        select * from stockin_order_task
        where task_id in
        <foreach collection="taskIds" separator="," index="index" item="taskId"
                 open="(" close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="findByIdIgnore" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity">
        select * from stockin_order_task
        where task_id = #{taskId}
    </select>
    <select id="countByPlatformScheduleIdIgnoreTenant" resultType="java.lang.Integer">

        select count(1) from stockin_order_task
        where platform_schedule_id = #{platformScheduleId}
    </select>
    <select id="sumExpectedQty" resultType="java.lang.Integer">
        select sum(expected_qty) from stockin_order_task_item
        where task_id = #{taskId}
    </select>

    <select id="getSupplierDeliveryNoFromPurchasePlanNo" resultType="java.lang.String">
        select DISTINCT t.supplier_delivery_no from stockin_order_task_item  ti
        inner join stockin_order_task t on t.task_id = ti.task_id
        where ti.purchase_plan_no = #{purchasePlanNo}
    </select>
    <select id="getSupplierDeliveryBoxCodeFromSupplierDeliveryNo" resultType="java.lang.String">
        select DISTINCT t.supplier_delivery_box_code from  stockin_order_task t
        where t.supplier_delivery_no = #{supplierDeliveryNo}
    </select>
</mapper>
