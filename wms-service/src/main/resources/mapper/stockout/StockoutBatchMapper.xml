<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchMapper">
    <select id="pageSearchCount" resultType="com.nsy.api.wms.response.stockout.StockoutBatchListCountResponse">
        SELECT
        ifnull(sboi.qty, 0) as qty,
        sboi.sku as sku,
        soi.order_no as orderNo,
        soi.is_first_order_by_store as isFirstOrderByStore
        FROM
        stockout_batch sb
        LEFT JOIN stockout_batch_order sbo ON sb.batch_id = sbo.batch_id
        LEFT JOIN stockout_batch_order_item sboi ON sbo.batch_order_id = sboi.batch_order_id
        left join stockout_order_item soi ON soi.stockout_order_item_id = sboi.stockout_order_item_id
        <where>
            (sb.is_merge_batch = 0 or sb.batch_id in (select sb.batch_id from stockout_batch sb where sb.is_merge_batch
            = 1))
            <if test="request.batchId!=null and request.batchId!=''">
                and sb.batch_id = #{request.batchId}
            </if>
            <if test="request.batchIds!=null and request.batchIds.size() > 0">
                and sb.batch_id in
                <foreach collection="request.batchIds" separator="," index="index" item="batchId" open="("
                         close=")">
                    #{batchId}
                </foreach>
            </if>
            <if test="request.sku!=null and request.sku!=''">
                and sboi.sku like concat(#{request.sku}, '%')
            </if>
            <if test="request.isLack!=null">
                and sbo.is_lack = #{request.isLack}
            </if>
            <if test="request.mergeBatchId!=null and request.mergeBatchId!=''">
                and sb.merge_batch_id = #{request.mergeBatchId}
            </if>
            <if test="request.isMergeBatch!=null and request.isMergeBatch!=''">
                and sb.is_merge_batch = #{request.isMergeBatch}
            </if>
            <if test="request!=null and request.logisticsCompanys != null and request.logisticsCompanys.size() > 0">
                and sb.logistics_company in
                <foreach collection="request.logisticsCompanys" separator="," index="index" item="logisticsCompany"
                         open="("
                         close=")">
                    #{logisticsCompany}
                </foreach>
            </if>
            <if test="request.declareType!=null and request.declareType!=''">
                and sb.customs_declare_type = #{request.declareType}
            </if>
            <if test="request.scanType!=null and request.scanType!=''">
                and sb.scan_type = #{request.scanType}
            </if>
            <if test="request.batchSplitType!=null and request.batchSplitType!=''">
                and sb.batch_split_type = #{request.batchSplitType}
            </if>
            <if test="request.pickingType!=null and request.pickingType!=''">
                and sb.picking_type = #{request.pickingType}
            </if>
            <if test="request.workspace!=null and request.workspace!=''">
                and sb.workspace = #{request.workspace}
            </if>
            <if test="request.workspaceList != null and request.workspaceList.size() > 0">
                and sb.workspace in
                <foreach collection="request.workspaceList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.stockoutType!=null and request.stockoutType.size() > 0">
                and sb.stockout_type in
                <foreach collection="request.stockoutType" separator="," index="index" item="stockoutType" open="("
                         close=")">
                    #{stockoutType}
                </foreach>
            </if>
            <if test="request.operator!=null and request.operator!=''">
                and sb.create_by = #{request.operator}
            </if>
            <if test="request.isNeedProcess!=null">
                and sb.is_need_process = #{request.isNeedProcess}
            </if>
            <if test="request.isPrint!=null">
                and sb.is_print = #{request.isPrint}
            </if>
            <if test="request.multipleSpace!=null">
                and sb.multiple_space = #{request.multipleSpace}
            </if>
            <if test="request.status!=null and request.status.size() > 0">
                and sb.status in
                <foreach collection="request.status" separator="," index="index" item="status" open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.status.size()==0">
                and sb.`status` in('NEW',
                'WAIT_TO_GENERATE_PICK','WAIT_PICK','PICKING','WAIT_SORT','SORTING','COMPLETED')
            </if>
            <if test="request.startDate!=null">
                and sb.create_date &gt;= #{request.startDate}
            </if>
            <if test="request.endDate!=null">
                and sb.create_date &lt;= #{request.endDate}
            </if>
            <if test="request.spaceId != null">
                and sb.space_id = #{request.spaceId}
            </if>
        </where>

    </select>

    <select id="findStockoutBatchOrderQty" resultType="com.nsy.api.wms.domain.stockout.StockoutBatchOrderQtyInfo">
       SELECT so.stockout_order_no AS stockoutOrderNo, SUM(i.qty) AS qty
        FROM stockout_batch_order_item i
        LEFT JOIN stockout_batch_order o ON o.batch_order_id = i.batch_order_id
		LEFT JOIN stockout_order so on so.stockout_order_id = o.stockout_order_id
        WHERE o.batch_id = #{batchId}
        GROUP BY so.stockout_order_no
        ORDER BY qty DESC, stockout_order_no ASC
    </select>

    <select id="findStockoutBatchQty" resultType="com.nsy.api.wms.domain.stockout.StockoutBatchOrderQtyInfo">
        SELECT o.batch_id AS id, SUM(i.qty) AS qty
        FROM stockout_batch_order_item i
        LEFT JOIN stockout_batch_order o ON o.batch_order_id = i.batch_order_id
        LEFT JOIN stockout_batch b ON o.batch_id = b.batch_id
        WHERE b.merge_batch_id = #{batchId}
        GROUP BY o.batch_id
        ORDER BY qty DESC
    </select>

    <select id="findStockoutOrderNoByBatchIds" resultType="java.lang.String">
        SELECT
        o.stockout_order_no AS stockoutOrderNo
        FROM
        stockout_order o
        LEFT JOIN stockout_batch_order bo ON bo.stockout_order_id = o.stockout_order_id
        <where>
            <if test="batchIds!=null and batchIds.size() > 0">
                and bo.batch_id in
                <foreach collection="batchIds" separator="," index="index" item="batchId" open="("
                         close=")">
                    #{batchId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getSplitBatchType" resultType="com.nsy.api.wms.domain.stockout.StockoutSplitBatchType">
        select t.task_id taskId,t.batch_id batchId,b.is_merge_batch isMergeBatch,
        b.picking_type as pickingType,b.workspace,b.logistics_company as logisticsCompany
        from stockout_batch_split_task t
        left join stockout_batch b
        on t.batch_id = b.batch_id
        where task_id in
        <foreach collection="taskIds" separator="," index="index" item="taskId" open="("
                 close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="getPickingAllBatch" resultType="com.nsy.api.wms.response.stockout.StockoutAllBatchPickingTaskResponse">
        SELECT
        b.batch_id,
        sum(t.expected_qty) expectedQty,
        sum(ti.picked_qty) pickedQty,
        group_concat(ifnull(ti.space_area_name, '')) as spaceAreaNameString,
        group_concat(ifnull(ti.space_area_id, '')) as spaceAreaIdString,
        b.status,
        case when sum(ti.lack_qty) > 0 then 1 else 0 end isLack
        FROM stockout_picking_task_item ti
        left join stockout_picking_task t
        on t.task_id = ti.task_id
        left join stockout_batch b
        on b.batch_id = t.batch_id

        where b.status in ('WAIT_PICK','PICKING')

        GROUP BY b.batch_id
        order by batch_id desc
    </select>

    <select id="listStockoutBatchMergeList"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutBatchEntity">
        select * from stockout_batch
        where status = 'NEW'
        and is_merge_batch = 0
        and is_need_process = 0
        and merge_batch_id is null
        and (picking_type in ('FIND_DOC_BY_GOODS','SECOND_SORT') or (picking_type = 'WHOLE_PICK' and workspace = 'FBA_AREA'))

    </select>

    <select id="pageStockoutBatchList" resultType="com.nsy.wms.repository.entity.stockout.StockoutBatchEntity">
        select b.* from stockout_batch b
        <include refid="stockoutBatchListWhere"></include>
        group by b.batch_id
        order by b.batch_id
    </select>

    <select id="countStockoutBatchList" resultType="java.lang.Long">
        select count(distinct b.batch_id) from stockout_batch b
        <include refid="stockoutBatchListWhere"></include>
    </select>

    <sql id="stockoutBatchListWhere">
        <if test="(request.tagName != null and request.tagName != '')
            or request.isLack != null
            or (request.storeIdList != null and request.storeIdList.size() > 0)">
            left join stockout_batch_order bo on b.batch_id = bo.batch_id
        </if>

        <if test="(request.sku != null and request.sku != '')
        or (request.stockoutOrderIdList != null and request.stockoutOrderIdList.size() > 0)  or request.qtyMin!=null
        or request.qtyMax!=null or request.skuQtyMin!=null or request.skuQtyMax != null
        or (request.storeIdList != null and request.storeIdList.size() > 0) or request.isFirstOrderByStore != null
        or (request.spaceAreaIds != null and request.spaceAreaIds.size() > 0)">
            left join
            (
            select
                bo.batch_id,
                sum(boi.qty) as expectedQty,
                count(distinct boi.sku) as skuQty,
                sum(boi.is_first_order_by_store) as isFirstOrderByStore
            from stockout_batch_order_item boi
            inner join stockout_batch_order bo on bo.batch_order_id = boi.batch_order_id
            where bo.status != 'CANCELLED'
            <if test="request.batchId != null">
                and bo.batch_id = #{request.batchId}
            </if>
            <if test="request.batchIds != null and request.batchIds.size() > 0">
                and bo.batch_id in
                <foreach collection="request.batchIds" separator="," index="index" item="batchId" open="("
                         close=")">
                    #{batchId}
                </foreach>
            </if>
            <if test="request.spaceAreaIds != null and request.spaceAreaIds.size() > 0">
                and boi.space_area_Id in
                <foreach collection="request.spaceAreaIds" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="request.startDate != null">
                and boi.create_date &gt;= #{request.startDate}
            </if>
            <if test="request.endDate != null">
                and boi.create_date &lt;= #{request.endDate}
            </if>
            <if test="request.stockoutOrderIdList != null and request.stockoutOrderIdList.size() > 0">
                AND bo.stockout_order_id in
                <foreach collection="request.stockoutOrderIdList" separator="," index="index" item="stockoutOrderId"
                         open="(" close=")">
                    #{stockoutOrderId}
                </foreach>
            </if>
            <if test="request.stockoutBatchIdList != null and request.stockoutBatchIdList.size() > 0">
                and bo.batch_id in
                <foreach collection="request.stockoutBatchIdList" separator="," index="index" item="stockoutBatchId" open="("
                         close=")">
                    #{stockoutBatchId}
                </foreach>
            </if>
            <if test="request.storeIdList != null and request.storeIdList.size() > 0">
                and bo.store_id in
                <foreach collection="request.storeIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            group by bo.batch_id
            <trim prefix="having" suffixOverrides="and">
                <if test="request.qtyMin != null">
                    expectedQty &gt;= #{request.qtyMin} and
                </if>
                <if test="request.qtyMax != null">
                    expectedQty &lt;= #{request.qtyMax} and
                </if>
                <if test="request.skuQtyMin != null">
                    skuQty &gt;= #{request.skuQtyMin} and
                </if>
                <if test="request.skuQtyMax != null">
                    skuQty &lt;= #{request.skuQtyMax} and
                </if>
            </trim>
            ) t on t.batch_id = b.batch_id
        </if>
        <where>
            <if test="(request.sku != null and request.sku != '')
            or (request.stockoutOrderIdList != null and request.stockoutOrderIdList.size() > 0)
            or request.qtyMin!=null or request.qtyMax!=null
            or request.skuQtyMin!=null or request.skuQtyMax != null
              or (request.spaceAreaIds != null and request.spaceAreaIds.size() > 0)">
                and t.batch_id is not null
            </if>
            <if test="request.batchId != null">
                and b.batch_id = #{request.batchId}
            </if>
            <if test="request.batchIds != null and request.batchIds.size() > 0">
                and b.batch_id in
                <foreach collection="request.batchIds" separator="," index="index" item="batchId" open="("
                         close=")">
                    #{batchId}
                </foreach>
            </if>
            <if test="request.mergeBatchId != null">
                and b.merge_batch_id = #{request.mergeBatchId}
            </if>
            <if test="request.isMergeBatch != null">
                and b.is_merge_batch = #{request.isMergeBatch}
            </if>
            <if test="request.stockoutWavePlanType != null and request.stockoutWavePlanType != ''">
                and b.batch_type = #{request.stockoutWavePlanType}
            </if>
            <if test="request.tagName != null and request.tagName != ''">
                and EXISTS (select o.stockout_order_id from stockout_order o
                INNER JOIN bd_tag_mapping m on o.stockout_order_no = m.reference_no
                where o.stockout_order_id = bo.stockout_order_id
                and m.tag_name = #{request.tagName}
                limit 1)
            </if>
            <if test="request.logisticsCompanys != null and request.logisticsCompanys.size() > 0">
                and b.logistics_company in
                <foreach collection="request.logisticsCompanys" separator="," index="index" item="logisticsCompany"
                         open="("
                         close=")">
                    #{logisticsCompany}
                </foreach>
            </if>
            <if test="request.stockoutType != null and request.stockoutType.size() > 0">
                and b.stockout_type in
                <foreach collection="request.stockoutType" separator="," index="index" item="item"
                         open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.declareType != null and request.declareType !=''">
                and b.customs_declare_type = #{request.declareType}
            </if>
            <if test="request.scanType != null and request.scanType !=''">
                and b.scan_type = #{request.scanType}
            </if>
            <if test="request.batchSplitType != null and request.batchSplitType !=''">
                and b.batch_split_type = #{request.batchSplitType}
            </if>
            <if test="request.pickingType != null and request.pickingType !=''">
                and b.picking_type = #{request.pickingType}
            </if>
            <if test="request.workspace != null and request.workspace !=''">
                and b.workspace = #{request.workspace}
            </if>
            <if test="request.workspaceList != null and request.workspaceList.size() > 0">
                and b.workspace in
                <foreach collection="request.workspaceList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.operator != null and request.operator !=''">
                and b.create_by = #{request.operator}
            </if>
            <if test="request.externalBatchId != null">
                and b.external_batch_id = #{request.externalBatchId}
            </if>
            <if test="request.isNeedProcess != null">
                and b.is_need_process = #{request.isNeedProcess}
            </if>
            <if test="request.isPrint != null">
                and b.is_print = #{request.isPrint}
            </if>
            <if test="request.multipleSpace != null">
                and b.multiple_space = #{request.multipleSpace}
            </if>
            <if test="request.status != null and request.status.size() > 0">
                and b.status in
                <foreach collection="request.status" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.startDate != null">
                and b.create_date &gt;= #{request.startDate}
            </if>
            <if test="request.endDate != null">
                and b.create_date &lt;= #{request.endDate}
            </if>
            <if test="request.isLack != null">
                and bo.is_lack = #{request.isLack}
            </if>
            <if test="request.spaceId != null">
                and b.space_id = #{request.spaceId}
            </if>
            <if test="request.storeIdList != null and request.storeIdList.size() > 0">
                and bo.store_id in
                <foreach collection="request.storeIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.isFirstOrderByStore == 1">
                and t.isFirstOrderByStore > 0
            </if>
            <if test="request.isFirstOrderByStore == 0">
                and t.isFirstOrderByStore = 0
            </if>
        </where>
    </sql>

    <select id="findStockoutItemByMergeBatch" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderItemInfoByBatch">
        SELECT
		oi.stockout_order_item_id,
        pi.base_sku as sku,
        pi.customer_sku as customerSku,
        oi.qty as qty,
        b.batch_id as batchId
        FROM `stockout_batch` b
        INNER JOIN stockout_batch_order bo on b.batch_id = bo.batch_id
		INNER JOIN stockout_order_item oi on bo.stockout_order_id = oi.stockout_order_id
        INNER JOIN stockout_order_item_process_info pi on oi.stockout_order_item_id = pi.stockout_order_item_id
        where b.merge_batch_id = #{batchId};

    </select>

    <select id="waitCompleteBatchIdList" resultType="java.lang.Integer">
        SELECT
            b.batch_id
        FROM
            stockout_batch b
        WHERE
            b.status != 'COMPLETED'
            AND b.status != 'CANCELLING'
            AND b.status != 'CANCELLED'
            AND (b.is_merge_batch = 1 or b.merge_batch_id is null)
    </select>
    <select id="listByIdsOrderByIds" resultType="com.nsy.wms.repository.entity.stockout.StockoutBatchEntity">
        SELECT
        b.*
        FROM
        stockout_batch b
        WHERE b.batch_id in
        <foreach collection="idList" separator="," index="index" item="item" open="("
                 close=")">
            #{item}
        </foreach>
        ORDER BY FIELD(batch_id,
        <foreach collection="idList" separator="," index="index" item="item" open=""
                 close="">
            #{item}
        </foreach>
        )
    </select>

    <select id="getStockoutBatchIdListBySku" resultType="java.lang.Integer">
        select distinct bo.batch_id FROM stockout_batch_order_item boi
        inner join stockout_batch_order bo on bo.batch_order_id = boi.batch_order_id
        where boi.sku like concat(#{sku}, '%')
        <if test="startDate != null">
            and boi.create_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and boi.create_date &lt;= #{endDate}
        </if>
    </select>
    <select id="countByStockoutOrderStatus" resultType="java.lang.Integer">
        select count(o.stockout_order_id) from stockout_batch_order bo
        INNER JOIN stockout_order o on bo.stockout_order_id = o.stockout_order_id
        where bo.batch_id = #{batchId}
        and o.`status` not in ('DELIVERED','READY_DELIVERY','CANCELLED','CANCELLING')
    </select>
    <select id="countMergeBatchByStockoutOrderStatus" resultType="java.lang.Integer">
    select count(o.stockout_order_id) from stockout_batch b
    INNER JOIN stockout_batch_order bo on b.batch_id = bo.batch_id
    INNER JOIN stockout_order o on bo.stockout_order_id = o.stockout_order_id
    where b.merge_batch_id = #{batchId}
    and o.`status` not in ('DELIVERED','READY_DELIVERY','CANCELLED','CANCELLING')
    </select>
</mapper>
