<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareStockoutOrderMapper">

    <select id="findPage" resultType="com.nsy.api.wms.response.stockout.StockoutCustomsDeclareStockoutOrderResponse">
        SELECT
        scdso.id,
        scdso.stockout_order_no,
        scdco.id declare_customer_order_id,
        scdso.order_no,
        scdso.space_id,
        scdso.space_name,
        scdso.store_name,
        scdso.logistics_no,
        scdso.logistics_company,
        scdso.`status`,
        scdso.delivery_date,
        scdso.customer_name,
        scdso.customer_phone,
        scdso.customer_area,
        scdso.customer_address,
        scdso.customer_mail,
        scdso.create_by,
        scdso.create_date,
        sum( scdsoi.pending_qty ) pending_qty,
        sum( scdsoi.qty ) qty
        FROM
        stockout_customs_declare_stockout_order scdso
        LEFT JOIN stockout_customs_declare_stockout_order_item scdsoi ON scdso.id = scdsoi.declare_stockout_order_id
        LEFT JOIN stockout_customs_declare_customer_order scdco ON scdco.order_no = scdso.order_no
        <where>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdso.order_no = #{request.orderNo}
            </if>

            <if test="request.stockoutOrderNo != null and request.stockoutOrderNo != ''">
                AND scdso.stockout_order_no = #{request.stockoutOrderNo}
            </if>
            <if test="request.storeName != null and request.storeName != ''">
                and scdso.store_name = #{request.storeName}
            </if>
            <if test="request.logisticCompany != null and request.logisticCompany != ''">
                and scdso.logistics_company = #{request.logisticCompany}
            </if>
            <if test="request.logisticNo != null and request.logisticNo != ''">
                and scdso.logistics_no = #{request.logisticNo}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdso.status = #{request.status}
            </if>
            <if test="request.spaceName != null and request.spaceName != ''">
                and scdso.space_name = #{request.spaceName}
            </if>
            <if test="request.createDateBegin != null">
                and scdso.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdso.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdsoi.sku = #{request.sku}
            </if>
            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdso.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        scdso.id
    </select>

    <select id="countPage" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        stockout_customs_declare_stockout_order scdso
        <if test="request.sku != null and request.sku != ''">
        LEFT JOIN stockout_customs_declare_stockout_order_item scdsoi ON scdso.id = scdsoi.declare_stockout_order_id
        </if>
        <where>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdso.order_no = #{request.orderNo}
            </if>

            <if test="request.stockoutOrderNo != null and request.stockoutOrderNo != ''">
                AND scdso.stockout_order_no = #{request.stockoutOrderNo}
            </if>
            <if test="request.storeName != null and request.storeName != ''">
                and scdso.store_name = #{request.storeName}
            </if>
            <if test="request.logisticCompany != null and request.logisticCompany != ''">
                and scdso.logistics_company = #{request.logisticCompany}
            </if>
            <if test="request.logisticNo != null and request.logisticNo != ''">
                and scdso.logistics_no = #{request.logisticNo}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdso.status = #{request.status}
            </if>
            <if test="request.spaceName != null and request.spaceName != ''">
                and scdso.space_name = #{request.spaceName}
            </if>
            <if test="request.createDateBegin != null">
                and scdso.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdso.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdsoi.sku = #{request.sku}
            </if>
            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdso.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="request.sku != null and request.sku != ''">
        GROUP BY
        scdso.id
        </if>
    </select>
</mapper>
