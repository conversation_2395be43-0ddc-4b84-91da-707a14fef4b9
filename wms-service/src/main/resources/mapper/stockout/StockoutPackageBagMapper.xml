<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutPackageBagMapper">
    <select id="pageSearchList" resultType="com.nsy.api.wms.domain.stockout.StockoutPackageBagList">
        SELECT
        DISTINCT bag.bag_id as bagId,
        bag.bag_code as bagCode,
        bag.bag_index as bagIndex,
        bag.logistics_company as logisticsCompany,
        bag.customs_declare_type as customsDeclareType,
        bag.package_qty as packageQty,
        bag.weight as weight,
        bag.status as status,
        bag.create_date as createDate,
        bag.create_by as createBy,
        bag.update_date as updateDate,
        bag.update_by as updateBy
        FROM
        stockout_package_bag AS bag
        JOIN stockout_package_bag_item AS bagItem ON bag.bag_id = bagItem.bag_id
        JOIN stockout_shipment_item AS sItem ON sItem.shipment_id = bagItem.shipment_id
        <where>
            <if test="query!=null and query.bagCode != null and query.bagCode !=''">
                bag.`bag_code` = #{query.bagCode}
            </if>
            <if test="query!=null and query.logisticsNo != null and query.logisticsNo != ''">
                and bagItem.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query!=null and query.orderNo != null and query.orderNo !=''">
                and sItem.order_no = #{query.orderNo}
            </if>
            <if test="query!=null and query.logisticsCompany != null and query.logisticsCompany !=''">
                and bag.logistics_company = #{query.logisticsCompany}
            </if>
            <if test="query!=null and query.customsDeclareType != null and query.customsDeclareType !=''">
                and bag.`customs_declare_type` = #{query.customsDeclareType}
            </if>
            <if test="query.statusList != null and query.statusList.size() > 0">
                and bag.`status` in
                <foreach collection="query.statusList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query!=null and query.status != null and query.status !=''">
                and bag.`status` = #{query.status}
            </if>
            <if test="query!=null and query.createDateStartDate != null">
                and bag.`create_date` &gt;= #{query.createDateStartDate}
            </if>
            <if test="query!=null and query.createDateEndDate != null">
                and bag.`create_date` &lt;= #{query.createDateEndDate}
            </if>
            and sItem.is_deleted = 0
        </where>
        order by bag.bag_id desc
    </select>

    <select id="getToDayCount" resultType="java.lang.Integer">
        SELECT count(bag_id) FROM stockout_package_bag
        WHERE TO_DAYS(create_date) = TO_DAYS(NOW())
        and logistics_company = #{logisticsCompany}

    </select>


    <select id="countByStatus" resultType="com.nsy.api.wms.domain.stockout.StockoutPackageListCountInfo">
        SELECT
            bag.status,count(*) as qty
        FROM
            stockout_package_bag AS bag FORCE INDEX (idx_create_date)
        JOIN stockout_package_bag_item AS bagItem ON bag.bag_id = bagItem.bag_id
        JOIN stockout_shipment_item AS sItem ON sItem.shipment_id = bagItem.shipment_id
        where
            sItem.is_deleted = 0
            <if test="query.createStartDate!=null">
                and bag.create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query.createEndDate!=null">
                and bag.create_date &lt;= #{query.createEndDate}
            </if>
        GROUP BY bag.status
    </select>
    <select id="searchListStatistics"
            resultType="com.nsy.api.wms.domain.stockout.StockoutPackageBagStatistics">
        SELECT IFNULL(sum(bag.package_qty), 0) as packageQty,
               IFNULL(sum(bag.weight), 0)      as weight
        FROM stockout_package_bag AS bag
        where bag.bag_id in (
        SELECT
        DISTINCT bag.bag_id as bagId
        FROM
        stockout_package_bag AS bag
        JOIN stockout_package_bag_item AS bagItem ON bag.bag_id = bagItem.bag_id
        JOIN stockout_shipment_item AS sItem ON sItem.shipment_id = bagItem.shipment_id
        <where>
            <if test="query!=null and query.bagCode != null and query.bagCode !=''">
                bag.`bag_code` = #{query.bagCode}
            </if>
            <if test="query!=null and query.logisticsNo != null and query.logisticsNo != ''">
                and bagItem.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query!=null and query.orderNo != null and query.orderNo !=''">
                and sItem.order_no = #{query.orderNo}
            </if>
            <if test="query!=null and query.logisticsCompany != null and query.logisticsCompany !=''">
                and bag.logistics_company = #{query.logisticsCompany}
            </if>
            <if test="query!=null and query.customsDeclareType != null and query.customsDeclareType !=''">
                and bag.`customs_declare_type` = #{query.customsDeclareType}
            </if>
            <if test="query.statusList != null and query.statusList.size() > 0">
                and bag.`status` in
                <foreach collection="query.statusList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query!=null and query.status != null and query.status !=''">
                and bag.`status` = #{query.status}
            </if>
            <if test="query!=null and query.createDateStartDate != null">
                and bag.`create_date` &gt;= #{query.createDateStartDate}
            </if>
            <if test="query!=null and query.createDateEndDate != null">
                and bag.`create_date` &lt;= #{query.createDateEndDate}
            </if>
            and sItem.is_deleted = 0
        </where>
            )
    </select>
    <select id="packageSkuInfoExport" resultType="com.nsy.api.wms.domain.stockout.PackageSkuInfoExport">
        SELECT
            bag.bag_code AS packageNo,
            sItem.order_no AS orderNo,
            sItem.sku AS skuCode,
            CONCAT(psi.color, '/', psi.size) AS colorSize,
            psi.barcode AS barcode,
            psi.store_sku AS customerSku,
            psi.store_barcode AS fnsku,
            SUM(sItem.qty) AS quantity,
            psi.unit_price AS unitPrice,
            so.receiver_name AS receiverName,
            bag.bag_index AS boxNo,
            bag.shipment_date AS shipmentTime,
            bag.logistics_company AS logisticsCompany
        FROM
            stockout_package_bag AS bag
            JOIN stockout_package_bag_item AS bagItem ON bag.bag_id = bagItem.bag_id
            JOIN stockout_shipment_item AS sItem ON sItem.shipment_id = bagItem.shipment_id
            LEFT JOIN product_spec_info psi ON psi.sku = sItem.sku
            LEFT JOIN stockout_order so ON so.stockout_order_no = sItem.stockout_order_no
        <where>
            <if test="bagIds != null and bagIds.size() > 0">
                bag.bag_id IN
                <foreach collection="bagIds" separator="," index="index" item="bagId" open="(" close=")">
                    #{bagId}
                </foreach>
            </if>
            AND sItem.is_deleted = 0
        </where>
        GROUP BY bag.bag_code, sItem.order_no, sItem.sku
        ORDER BY bag.bag_code, sItem.order_no, sItem.sku
    </select>
</mapper>
