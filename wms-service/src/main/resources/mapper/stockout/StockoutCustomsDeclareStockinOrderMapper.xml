<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareStockinOrderMapper">
    <select id="findPage" resultType="com.nsy.api.wms.response.stockout.StockoutCustomsDeclareStockinOrderResponse">
        SELECT
            scdso.id,
            scdso.stockin_order_no,
            scdpo.id AS declare_purchase_order_id,
            scdso.purchase_order_no,
            scdso.space_id,
            scdso.space_name,
            scdso.supplier_id,
            scdso.supplier_name,
            scdso.`status`,
            scdso.received_date,
            scdso.shelved_date,
            sum( scdsoi.purchase_qty ) purchase_qty,
            sum( scdsoi.received_qty ) received_qty,
            sum( scdsoi.shelved_qty ) shelved_qty,
            scdso.create_date,
            scdso.create_by
        FROM
            stockout_customs_declare_stockin_order scdso
                LEFT JOIN stockout_customs_declare_stockin_order_item scdsoi ON scdso.id = scdsoi.declare_stockin_order_id
                LEFT JOIN stockout_customs_declare_purchase_order scdpo ON scdso.purchase_order_no = scdpo.purchase_order_no
        <where>
            <if test="request.stockinOrderNo != null and request.stockinOrderNo != ''">
                AND scdso.stockin_order_no = #{request.stockinOrderNo}
            </if>
            <if test="request.purchaseOrderNo != null and request.purchaseOrderNo != ''">
                and scdso.purchase_order_no = #{request.purchaseOrderNo}
            </if>
            <if test="request.supplierName != null and request.supplierName != ''">
                and scdso.supplier_name = #{request.supplierName}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdso.status = #{request.status}
            </if>
            <if test="request.spaceName != null and request.spaceName != ''">
                and scdso.space_name = #{request.spaceName}
            </if>
            <if test="request.createDateBegin != null">
                and scdso.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdso.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdso.order_no = #{request.orderNo}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdsoi.sku = #{request.sku}
            </if>
            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdso.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            scdso.id
    </select>


    <select id="countPage" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        stockout_customs_declare_stockin_order scdso
        <if test="request.sku != null and request.sku != ''">
        LEFT JOIN stockout_customs_declare_stockin_order_item scdsoi ON scdso.id = scdsoi.declare_stockin_order_id
        </if>
        <where>
            <if test="request.stockinOrderNo != null and request.stockinOrderNo != ''">
                AND scdso.stockin_order_no = #{request.stockinOrderNo}
            </if>
            <if test="request.purchaseOrderNo != null and request.purchaseOrderNo != ''">
                and scdso.purchase_order_no = #{request.purchaseOrderNo}
            </if>
            <if test="request.supplierName != null and request.supplierName != ''">
                and scdso.supplier_name = #{request.supplierName}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdso.status = #{request.status}
            </if>
            <if test="request.spaceName != null and request.spaceName != ''">
                and scdso.space_name = #{request.spaceName}
            </if>
            <if test="request.createDateBegin != null">
                and scdso.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdso.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdso.order_no = #{request.orderNo}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdsoi.sku = #{request.sku}
            </if>
            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdso.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="request.sku != null and request.sku != ''">
        GROUP BY
        scdso.id
        </if>
    </select>
</mapper>
