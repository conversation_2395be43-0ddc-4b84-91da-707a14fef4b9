<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderTemuPopExtendMapper">

    <select id="listOrdersForCreateShipment"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderTemuPopExtendEntity">
        SELECT sote.*
        FROM stockout_order_temu_pop_extend sote
                 INNER JOIN stockout_order so ON sote.stockout_order_id = so.stockout_order_id
        WHERE so.status = 'READY'
          AND so.platform_name = 'TemuPop'
          AND (sote.package_sn IS NULL OR sote.package_sn = '')
    </select>

    <select id="listOrdersForGetShipmentResult"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderTemuPopExtendEntity">
        SELECT sote.*
        FROM stockout_order_temu_pop_extend sote
                 INNER JOIN stockout_order so ON sote.stockout_order_id = so.stockout_order_id
        WHERE so.status = 'READY'
          AND so.platform_name = 'TemuPop'
          AND sote.package_sn IS NOT NULL
          AND sote.package_sn != ''
    </select>

</mapper> 