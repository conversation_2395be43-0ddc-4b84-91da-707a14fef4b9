<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutFaireShipmentMapper">

    <sql id="shipmentPageWhere">
        <if test="query.boxCode != null and query.boxCode != ''">
            and stockout_faire_shipment.box_code = #{query.boxCode}
        </if>
        <if test="query.boxCodes != null and query.boxCodes.size() > 0">
            and stockout_faire_shipment.box_code in
            <foreach collection="query.boxCodes" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.idList != null and query.idList.size() > 0">
            and stockout_faire_shipment.id in
            <foreach collection="query.idList" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.status != null and query.status.size() > 0">
            and stockout_faire_shipment.status in
            <foreach collection="query.status" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.statusSingle != null and query.statusSingle != ''">
            and stockout_faire_shipment.status = #{query.statusSingle}
        </if>
        <if test="query.storeName != null and query.storeName != ''">
            and stockout_faire_shipment.store_name = #{query.storeName}
        </if>
        <if test="query.logisticsNo != null and query.logisticsNo != ''">
            and stockout_faire_shipment.logistics_no = #{query.logisticsNo}
        </if>
        <if test="query.shipBatchId != null and query.shipBatchId != ''">
            and stockout_faire_shipment.ship_batch_id = #{query.shipBatchId}
        </if>
        <if test="query.boxSize != null and query.boxSize != ''">
            and stockout_faire_shipment.box_size = #{query.boxSize}
        </if>
        <if test="query.isMerge != null">
            and stockout_faire_shipment.is_merge = #{query.isMerge}
        </if>
        <if test="query.shipmentDateBegin!=null">
            and stockout_faire_shipment.shipment_date &gt;= #{query.shipmentDateBegin}
        </if>
        <if test="query.shipmentDateEnd!=null">
            and stockout_faire_shipment.shipment_date &lt;= #{query.shipmentDateEnd}
        </if>
        <if test="query.deliveryDate!=null">
            and stockout_faire_shipment.delivery_date &gt;= #{query.deliveryDate}
        </if>
        <if test="query.deliveryDateEnd!=null">
            and stockout_faire_shipment.delivery_date &lt;= #{query.deliveryDateEnd}
        </if>
        <if test="query.isContainSub != null and !query.isContainSub">
            and stockout_faire_shipment.merge_faire_shipment_id = 0
        </if>
        and stockout_faire_shipment.is_delete = 0
        and stockout_faire_shipment.box_sku_qty > 0
    </sql>

    <select id="pageSearchShipmentIds" resultType="java.lang.Integer">
        select
        DISTINCT (stockout_faire_shipment.id) as id
        from stockout_faire_shipment stockout_faire_shipment
        <where>
            <include refid="shipmentPageWhere"></include>
        </where>
        order by stockout_faire_shipment.shipment_date asc
    </select>

    <select id="pageSearchShipmentIdsCount" resultType="java.lang.Integer">
        select
        count(DISTINCT stockout_faire_shipment.id)
        from stockout_faire_shipment stockout_faire_shipment
        <where>
            <include refid="shipmentPageWhere"></include>
        </where>
    </select>

    <select id="getSearchShipmentIds" resultType="java.lang.Integer">
        select
        DISTINCT (stockout_faire_shipment.id) as id
        from stockout_faire_shipment stockout_faire_shipment
        <where>
            <include refid="shipmentPageWhere"></include>
        </where>
        order by stockout_faire_shipment.id desc
    </select>

    <select id="getExportListByIds" resultType="com.nsy.api.wms.response.stockout.StockoutFaireShipmentExportList">
        SELECT
        fi.order_no,
        fi.faire_shipment_id,
        fi.stockout_order_no,
        f.box_size,
        f.weight
        FROM
        stockout_faire_shipment_item fi
        LEFT JOIN stockout_faire_shipment f ON f.id = fi.faire_shipment_id
        <where>
            fi.is_delete = 0
            AND f.is_delete = 0
            <if test="idList != null and idList.size() > 0">
                AND fi.faire_shipment_id in
                <foreach collection="idList" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
        </where>
        GROUP BY
        fi.stockout_order_no,
        fi.faire_shipment_id
    </select>

    <select id="getStockoutFaireShipmentByMax"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutFaireShipmentEntity">
        select * from
        stockout_faire_shipment t
        where t.ship_batch_id = #{batchId}
        order by print_box_index desc limit 1
    </select>
    <select id="getProductInvoicePrice" resultType="java.math.BigDecimal">
        SELECT
            SUM(COALESCE(soi.invoice_price, 0)*ssi.qty)
        FROM stockout_shipment_item ssi
        JOIN stockout_order_item soi ON ssi.stockout_order_item_id = soi.stockout_order_item_id
        WHERE ssi.stockout_order_no = #{stockoutOrderNo} and ssi.is_deleted = 0
        GROUP BY ssi.stockout_order_no;
    </select>
    <select id="getProductInvoicePriceByShipmentId" resultType="java.math.BigDecimal">
        SELECT
            SUM(COALESCE(soi.invoice_price, 0)*ssi.qty)
        FROM stockout_shipment_item ssi
        JOIN stockout_order_item soi ON ssi.stockout_order_item_id = soi.stockout_order_item_id
        WHERE ssi.shipment_id = #{shipmentId} and ssi.is_deleted = 0
        GROUP BY ssi.shipment_id;
    </select>

</mapper>
