<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclarePurchaseOrderMapper">
    <select id="findPage" resultType="com.nsy.api.wms.response.stockout.StockoutCustomsDeclarePurchaseOrderResponse">
        SELECT
        scdpo.id,
        scdco.id declare_customer_order_id,
        scdc.declare_contract_id,
        scdpo.declare_contract_no,
        scdpo.purchase_order_no,
        scdpo.order_no,
        scdpo.space_id,
        scdpo.space_name,
        scdpo.supplier_id,
        scdpo.supplier_name,
        scdpo.purchase_user_real_name,
        scdpo.`status`,
        sum( scdpoi.qty ) qty,
        scdpo.create_date,
        scdpo.create_by
        FROM
        stockout_customs_declare_purchase_order scdpo
        LEFT JOIN stockout_customs_declare_purchase_order_item scdpoi ON scdpo.id = scdpoi.declare_purchase_order_id
        LEFT JOIN stockout_customs_declare_customer_order scdco ON scdco.order_no = scdpo.order_no
        LEFT JOIN stockout_customs_declare_contract scdc ON scdc.declare_contract_no = scdpo.declare_contract_no
        <where>
            <if test="request.purchaseOrderNo != null and request.purchaseOrderNo != ''">
                and scdpo.purchase_order_no = #{request.purchaseOrderNo}
            </if>
            <if test="request.spaceName != null and request.spaceName != ''">
                and scdpo.space_name = #{request.spaceName}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdpo.status = #{request.status}
            </if>
            <if test="request.purchaseUserRealName != null and request.purchaseUserRealName != ''">
                and scdpo.purchase_user_real_name = #{request.purchaseUserRealName}
            </if>
            <if test="request.createDateBegin != null">
                and scdpo.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdpo.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdpo.order_no = #{request.orderNo}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdpoi.sku = #{request.sku}
            </if>
            <if test="request.declareContractNo != null and request.declareContractNo != ''">
                and scdpo.declare_contract_no = #{request.declareContractNo}
            </if>

            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdpo.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        scdpo.id
    </select>

    <select id="countPage" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        stockout_customs_declare_purchase_order scdpo
        <if test="request.sku != null and request.sku != ''">
        LEFT JOIN stockout_customs_declare_purchase_order_item scdpoi ON scdpo.id = scdpoi.declare_purchase_order_id
        </if>
        <where>
            <if test="request.purchaseOrderNo != null and request.purchaseOrderNo != ''">
                and scdpo.purchase_order_no = #{request.purchaseOrderNo}
            </if>
            <if test="request.spaceName != null and request.spaceName != ''">
                and scdpo.space_name = #{request.spaceName}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdpo.status = #{request.status}
            </if>
            <if test="request.purchaseUserRealName != null and request.purchaseUserRealName != ''">
                and scdpo.purchase_user_real_name = #{request.purchaseUserRealName}
            </if>
            <if test="request.createDateBegin != null">
                and scdpo.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdpo.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdpo.order_no = #{request.orderNo}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdpoi.sku = #{request.sku}
            </if>
            <if test="request.declareContractNo != null and request.declareContractNo != ''">
                and scdpo.declare_contract_no = #{request.declareContractNo}
            </if>

            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdpo.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="request.sku != null and request.sku != ''">
        GROUP BY
        scdpo.id
        </if>
    </select>
</mapper>
