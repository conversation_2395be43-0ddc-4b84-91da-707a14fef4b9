<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareCustomerOrderMapper">

    <select id="findPage" resultType="com.nsy.api.wms.response.stockout.StockoutCustomsDeclareCustomerOrderResponse">
        SELECT
        scdco.id,
        scdco.order_no,
        scdco.space_id,
        scdco.space_name,
        scdco.store_name,
        scdco.logistics_company,
        scdco.logistics_no,
        scdco.`status`,
        scdco.delivery_date,
        scdco.customer_name,
        scdco.customer_phone,
        scdco.customer_area,
        scdco.customer_address,
        scdco.customer_mail,
        scdco.create_date,
        scdco.create_by,
        sum( scdcoi.qty ) qty
        FROM
        stockout_customs_declare_customer_order scdco
        LEFT JOIN stockout_customs_declare_customer_order_item scdcoi ON scdco.id = scdcoi.declare_customer_order_id
        <where>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdco.order_no = #{request.orderNo}
            </if>
            <if test="request.storeName != null and request.storeName != ''">
                and scdco.store_name = #{request.storeName}
            </if>
            <if test="request.logisticCompany != null and request.logisticCompany != ''">
                and scdco.logistics_company = #{request.logisticCompany}
            </if>
            <if test="request.logisticNo != null and request.logisticNo != ''">
                and scdco.logistics_no = #{request.logisticNo}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdco.status = #{request.status}
            </if>

            <if test="request.createDateBegin != null">
                and scdco.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdco.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.deliveryDateBegin != null">
                and scdco.delivery_date &gt;= #{request.deliveryDateBegin}
            </if>
            <if test="request.deliveryDateEnd != null">
                and scdco.delivery_date &lt;= #{request.deliveryDateEnd}
            </if>

            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdco.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdcoi.sku = #{request.sku}
            </if>
        </where>
        GROUP BY
        scdco.id
    </select>


    <select id="countPage" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        stockout_customs_declare_customer_order scdco
        <if test="request.sku != null and request.sku != ''">
        LEFT JOIN stockout_customs_declare_customer_order_item scdcoi ON scdco.id = scdcoi.declare_customer_order_id
        </if>
        <where>
            <if test="request.orderNo != null and request.orderNo != ''">
                and scdco.order_no = #{request.orderNo}
            </if>
            <if test="request.storeName != null and request.storeName != ''">
                and scdco.store_name = #{request.storeName}
            </if>
            <if test="request.logisticCompany != null and request.logisticCompany != ''">
                and scdco.logistics_company = #{request.logisticCompany}
            </if>
            <if test="request.logisticNo != null and request.logisticNo != ''">
                and scdco.logistics_no = #{request.logisticNo}
            </if>
            <if test="request.status != null and request.status != ''">
                and scdco.status = #{request.status}
            </if>

            <if test="request.createDateBegin != null">
                and scdco.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd != null">
                and scdco.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.deliveryDateBegin != null">
                and scdco.delivery_date &gt;= #{request.deliveryDateBegin}
            </if>
            <if test="request.deliveryDateEnd != null">
                and scdco.delivery_date &lt;= #{request.deliveryDateEnd}
            </if>

            <if test="request.orderNoList !=null and request.orderNoList.size() > 0">
                and scdco.order_no in
                <foreach collection="request.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.sku != null and request.sku != ''">
                and scdcoi.sku = #{request.sku}
            </if>
        </where>
        <if test="request.sku != null and request.sku != ''">
        GROUP BY
        scdco.id
        </if>
    </select>
</mapper>
