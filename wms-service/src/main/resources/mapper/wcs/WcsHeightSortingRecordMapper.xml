<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.wcs.WcsHeightSortingRecordMapper">
    
    <!-- 分页查询测量高度分拣记录，MyBatis Plus自动处理count -->
    <select id="selectPageWithConditions" resultType="com.nsy.wms.repository.entity.wcs.WcsHeightSortingRecordEntity">
        SELECT 
            whsr.*
        FROM 
            wcs_height_sorting_record whsr
        <if test="request.spuList != null and request.spuList.size() > 0">
            INNER JOIN product_spec_info psi ON whsr.sku = psi.sku
            INNER JOIN product_info pi ON psi.product_id = pi.product_id AND pi.is_delete = 0
        </if>
        <where>
            <if test="request.skuList != null and request.skuList.size() > 0">
                AND whsr.sku IN
                <foreach collection="request.skuList" separator="," index="index" item="sku" open="(" close=")">
                    #{sku}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0">
                AND pi.spu IN
                <foreach collection="request.spuList" separator="," index="index" item="spu" open="(" close=")">
                    #{spu}
                </foreach>
            </if>
            <if test="request.barcode != null and request.barcode != ''">
                AND whsr.barcode = #{request.barcode}
            </if>
            <if test="request.measureType != null and request.measureType != ''">
                AND whsr.measure_type = #{request.measureType}
            </if>
            <if test="request.sortingPort != null and request.sortingPort != ''">
                AND whsr.sorting_port = #{request.sortingPort}
            </if>
            <if test="request.createBeginDate != null">
                AND whsr.create_date &gt;= #{request.createBeginDate}
            </if>
            <if test="request.createEndDate != null">
                AND whsr.create_date &lt;= #{request.createEndDate}
            </if>
        </where>
        ORDER BY whsr.create_date DESC
    </select>
    
</mapper> 