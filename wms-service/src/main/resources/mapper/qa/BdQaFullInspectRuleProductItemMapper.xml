<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.BdQaFullInspectRuleProductItemMapper">
    
    <select id="getProductInfoList" resultType="com.nsy.api.wms.response.qa.BdQaFullInspectRuleProductItemInfoResponse">
        select t.*, s.supplier_name as supplierName from
            bd_qa_full_inspect_rule_product_item t
        left join supplier s on t.supplier_id = s.supplier_id
        where t.rule_id = #{ruleId}
    </select>
</mapper>