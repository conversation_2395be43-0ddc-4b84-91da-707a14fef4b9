<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaTaskMapper">


    <select id="queryBoxItemInfo" resultType="com.nsy.api.wms.domain.qa.StockinQaBoxItemInfo">

        select bi.internal_box_code,
        bi.space_id,
        bi.product_id,
        pi.spu,
        pi.category_id,
        pi.category_name,
        si.skc,
        bi.sku,
        bi.seller_sku,
        bi.seller_barcode,
        bi.qty,
        ti.qa_qty,
        o.supplier_id,
        o.supplier_name,
        ti.first_order_label,
        ti.is_need_qa,
        ti.is_fba_quick,
        ti.purchasing_apply_type,
        ti.purchase_skc_first_order,
        ti.package_name,
        ti.label_attribute_names,
        ti.business_type,
        o.stockin_type,
        oi.stockin_order_item_id,
        oi.purchase_plan_no,
        o.supplier_delivery_box_code,
        o.stockin_order_no,
        t.supplier_delivery_no,
        ti.brand_name,
        bs.space_name,
        ti.workmanship_version

        from stock_internal_box_item bi
        INNER JOIN stockin_order o
        on o.stockin_order_no = bi.stock_in_order_no
        INNER JOIN stockin_order_task t
        on t.task_id = o.task_id
        INNER JOIN stockin_order_item oi
        on oi.stockin_order_id = o.stockin_order_id
        and oi.sku = bi.sku
        and oi.purchase_plan_no = bi.purchase_plan_no
        and oi.internal_box_code = bi.internal_box_code
        INNER JOIN stockin_order_task_item ti
        on ti.task_item_id = oi.task_item_id
        INNER JOIN product_spec_info si
        on si.sku = bi.sku
        INNER JOIN product_info pi
        on pi.product_id = bi.product_id
        inner join bd_space bs
        on bs.space_id = ti.space_id
        where bi.internal_box_item_id in
        <foreach collection="internalBoxItemIds" separator="," item="id" open="(" close=")">
            #{id}
        </foreach>
        and bi.qty > 0
    </select>

    <select id="isUpShelved" resultType="java.lang.Boolean">
        select sum(ifnull(oi.shelved_qty, 0)) > 0
        from stockin_qa_task_item ti
                 left join stockin_order_item oi
                           on ti.stockin_order_item_id = oi.stockin_order_item_id
        where ti.task_id = #{taskId}
    </select>

    <select id="pageList" resultType="com.nsy.api.wms.response.qa.StockinQaTaskPageResponse">
        select
        t.task_id,
        t.internal_box_code,
        b.internal_box_type,
        t.product_id,
        t.sku,
        t.skc,
        t.spu,
        t.category_id,
        t.category_name,
        t.arrival_count,
        t.qa_qty,
        t.supplier_name,
        t.is_new,
        t.need_product_sample,
        t.check_status,
        t.stockin_date,
        t.operator,
        s.affiliate_dept_name as department,
        bs.space_name
        from stockin_qa_task t
        left join bd_space bs on t.space_id = bs.space_id
        left join stock_internal_box b on b.internal_box_code = t.internal_box_code
        left join supplier s on s.supplier_id = t.supplier_id

        <if test="(request.supplierDeliveryNo != null and request.supplierDeliveryNo !='')
                    or (request.supplierDeliveryBoxCode != null and request.supplierDeliveryBoxCode !='')
                    or (request.purchasePlanNo != null and request.purchasePlanNo !='')
                    or (request.isReturnApply != null)">
            inner join stockin_qa_task_item ti
            on t.task_id = ti.task_id
        </if>
        <include refid="page_where"></include>
        group by t.task_id
        order by t.task_id desc
    </select>
    <select id="pageCount" resultType="java.lang.Long">
        select
        count(distinct t.task_id)
        from stockin_qa_task t
        <if test="request.department != null and request.department !=''">
            left join supplier s on s.supplier_id = t.supplier_id
        </if>
        <if test="(request.supplierDeliveryNo != null and request.supplierDeliveryNo !='')
                    or (request.supplierDeliveryBoxCode != null and request.supplierDeliveryBoxCode !='')
                    or (request.purchasePlanNo != null and request.purchasePlanNo !='')
                    or (request.isReturnApply != null)">
            inner join stockin_qa_task_item ti
            on t.task_id = ti.task_id
        </if>
        <include refid="page_where"></include>
    </select>

    <sql id="page_where">
        <where>
            <if test="request.internalBoxCode != null and request.internalBoxCode !=''">
                AND t.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.skuList != null and request.skuList.size() > 0 ">
                and t.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                and t.spu in
                <foreach collection="request.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.supplierId != null">
                AND t.supplier_id =#{request.supplierId}
            </if>

            <if test="request.supplierDeliveryNo != null and request.supplierDeliveryNo !=''">
                AND ti.supplier_delivery_no = #{request.supplierDeliveryNo}
            </if>

            <if test="request.supplierDeliveryBoxCode != null and request.supplierDeliveryBoxCode !=''">
                AND ti.supplier_delivery_box_code = #{request.supplierDeliveryBoxCode}
            </if>

            <if test="request.purchasePlanNo != null and request.purchasePlanNo !=''">
                AND ti.purchase_plan_no = #{request.purchasePlanNo}
            </if>

            <if test="request.checkStatus != null and request.checkStatus !=''">
                AND t.check_status = #{request.checkStatus}
            </if>
            <if test="request.checkStatusList != null and request.checkStatusList.size() > 0 ">
                and t.check_status in
                <foreach collection="request.checkStatusList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.skuTypeInfo != null and request.skuTypeInfo.size() > 0 ">
                AND EXISTS (
                select task_id from stockin_qa_order_sku_type_info
                where task_id = t.task_id
                and sku_type in
                <foreach collection="request.skuTypeInfo" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="request.spaceId != null">
                AND t.space_id = #{request.spaceId}
            </if>
            <if test="request.spaceIdList != null and request.spaceIdList.size() > 0 ">
                and t.space_id in
                <foreach collection="request.spaceIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.categoryIdList != null and request.categoryIdList.size() > 0 ">
                and t.category_id in
                <foreach collection="request.categoryIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.operator != null and request.operator !=''">
                AND t.operator = #{request.operator}
            </if>

            <if test="request.isNew != null">
                AND t.is_new = #{request.isNew}
            </if>

            <if test="request.isReturnApply != null and request.isReturnApply == 0">
                AND ti.purchasing_apply_type != 6
            </if>
            <if test="request.isReturnApply != null and request.isReturnApply == 1">
                AND ti.purchasing_apply_type = 6
            </if>
            <if test="request.operator != null and request.operator !=''">
                AND t.operator = #{request.operator}
            </if>
            <if test="request.department != null and request.department !=''">
                AND s.affiliate_dept_name = #{request.department}
            </if>

            <if test="request.stockinStartDate != null">
                and t.stockin_date &gt;= #{request.stockinStartDate}
            </if>
            <if test="request.stockinEndDate != null">
                and t.stockin_date &lt;= #{request.stockinEndDate}
            </if>
        </where>
    </sql>

    <select id="getQaTaskByInfo" resultType="com.nsy.wms.repository.entity.qa.StockinQaTaskEntity">
        select distinct t.* from stockin_qa_task t
        inner join stockin_qa_task_item ti on t.task_id = ti.task_id
        where
        t.check_status in('PENDING_QC','QC_PROCESSING')
        <if test="internalBoxCode!= null and internalBoxCode!=''">
            AND t.internal_box_code = #{internalBoxCode}
        </if>
        <if test="sku!= null and sku!=''">
            AND t.sku = #{sku}
        </if>
        limit 1
    </select>
    <select id="listWaitQaList" resultType="com.nsy.api.wms.response.qa.StockinWaitQaTaskPageResponse">
        select t.task_id,
               t.internal_box_code,
               t.sku,
               t.skc,
               t.spu,
               si.preview_image_url,
               si.image_url,
               si.thumbnail_image_url,
               si.product_id,
               si.size,
               t.arrival_count,
               t.qa_qty,
               t.supplier_name,
               t.need_product_sample,
               t.stockin_date,
               t.is_new,
               t.label_attribute_names,
               ti.first_label,
               ti.purchasing_apply_type,
               oi.workmanship_version,
               GROUP_CONCAT(DISTINCT ti.purchase_plan_no) as purchasePlanNos,
               if(ifnull(si.package_height, 0) > 0, 0, 1) as needHeight,
               if(ifnull(si.actual_weight, 0) > 0, 0, 1)  as needWeight
        from stockin_qa_task t
        inner join stockin_qa_task_item ti on t.task_id = ti.task_id
        inner join stockin_order_item oi on oi.stockin_order_item_id = ti.stockin_order_item_id
        inner join product_spec_info si on si.sku = t.sku
        left join stockin_qa_order sqo on sqo.task_id = t.task_id and sqo.process_status &lt;&gt; 'CANCELED'
        where t.internal_box_code = #{internalBoxCode}
          and t.check_status in ('PENDING_QC', 'QC_PROCESSING')
          and (sqo.stockin_qa_order_id is null or sqo.process_status ='INCOMPLETE')
        group by t.task_id
    </select>
    <select id="getAutoCompleteTask" resultType="com.nsy.wms.repository.entity.qa.StockinQaTaskEntity">
        select task_id, location
        from (
                 select t.task_id,
                        t.location,
                        count(if(bi.`status` in
                                 ('WAIT_QC', 'QC_PROCESSING', 'WAIT_DEAL', 'WAIT_PURCHASE_CONFIRM'),
                                 1, null)) as waitQc
                 from stockin_qa_task t
                          left join stock_internal_box_item bi
                                    on t.internal_box_code = bi.internal_box_code
                                        and t.sku = bi.sku
                                        and bi.qty > 0
                 where t.`check_status` in ('PENDING_QC', 'QC_PROCESSING')
                 GROUP BY t.task_id
                 HAVING waitQc = 0
             ) t

    </select>

    <select id="listWaitQaListNew" resultType="com.nsy.api.wms.response.qa.StockinWaitQaTaskPageResponse">
         select t.task_id,
               t.internal_box_code,
               t.sku,
               t.skc,
               t.spu,
               si.product_id,
               si.size,
               si.preview_image_url,
               si.image_url,
               si.thumbnail_image_url,
               t.arrival_count,
               t.qa_qty,
               t.supplier_name,
               t.need_product_sample,
               t.stockin_date,
               t.is_new,
               t.label_attribute_names,
               ti.first_label,
               ti.purchasing_apply_type,
               oi.workmanship_version,
               GROUP_CONCAT(DISTINCT ti.purchase_plan_no) as purchasePlanNos,
               if(ifnull(si.package_height, 0) > 0, 0, 1) as needHeight,
               if(ifnull(si.actual_weight, 0) > 0, 0, 1)  as needWeight
        from stockin_qa_task t
        inner join stockin_qa_task_item ti on t.task_id = ti.task_id
        inner join stockin_order_item oi on oi.stockin_order_item_id = ti.stockin_order_item_id
        inner join product_spec_info si on si.sku = t.sku
        left join stockin_qa_product_sample_record sqs on sqs.task_id = t.task_id
        where t.internal_box_code = #{internalBoxCode}
          and t.check_status in ('PENDING_QC', 'QC_PROCESSING')
          and (sqs.id is null or sqs.status='QC_PROCESSING')
        group by t.task_id
    </select>

    <select id="statisticsQty" resultType="java.lang.Integer">
        select
        sum(ti.qty)
        from stockin_qa_task t
        left join stock_internal_box b on b.internal_box_code = t.internal_box_code
        inner join stockin_qa_task_item ti on t.task_id = ti.task_id
        <if test="request.department != null and request.department !=''">
            left join supplier s on s.supplier_id = t.supplier_id
        </if>
        <include refid="page_where"></include>
        <trim prefix="AND" prefixOverrides="AND ">
            t.check_status &lt;&gt; 'CANCELLED'
        </trim>
    </select>
    
    <select id="statisticsArriveQty" resultType="java.lang.Integer">
        select sum(t.qty) from (
            select
            distinct t.task_id,ti.supplier_delivery_no,oi.stockin_order_item_id,oi.qty
            from stockin_qa_task t
            left join stock_internal_box b on b.internal_box_code = t.internal_box_code
            inner join stockin_qa_task_item ti on t.task_id = ti.task_id
            inner join stockin_order_task sot on sot.supplier_delivery_no = ti.supplier_delivery_no
            inner join stockin_order o on sot.task_id = o.task_id
            inner join stockin_order_item oi on oi.stockin_order_id = o.stockin_order_id  and t.sku = oi.sku
            <if test="request.department != null and request.department !=''">
                left join supplier s on s.supplier_id = t.supplier_id
            </if>
            <include refid="page_where"></include>
            <trim prefix="AND" prefixOverrides="AND ">
                t.check_status &lt;&gt; 'CANCELLED'
            </trim>
        ) t
    </select>
    
    <select id="statisticsQaQty" resultType="java.lang.Integer">
        select sum(t.qaQty) from (
            select
            t.qa_qty as qaQty
            from stockin_qa_task t
            left join stock_internal_box b on b.internal_box_code = t.internal_box_code
            inner join stockin_qa_task_item ti on t.task_id = ti.task_id
            <if test="request.department != null and request.department !=''">
                left join supplier s on s.supplier_id = t.supplier_id
            </if>
            <include refid="page_where"></include>
            <trim prefix="AND" prefixOverrides="AND ">
                t.check_status &lt;&gt; 'CANCELLED'
            </trim>
            group by t.task_id 
        ) t
    </select>
</mapper>