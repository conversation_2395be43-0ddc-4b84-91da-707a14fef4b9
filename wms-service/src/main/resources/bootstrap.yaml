spring:
  application:
    name: ${appName:api-wms}
  cloud:
    bootstrap:
      enabled: true
    nacos:
      discovery:
        server-addr: ${nacosURL:172.16.8.185:8848}
        enabled: ${nacosFlag:true}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        enabled: ${nacosFlag:true}
        file-extension: yml
        shared-configs[0]:
          data-id: application-common.${spring.cloud.nacos.config.file-extension} # 配置文件名-Data Id
          refresh: false   # 是否动态刷新，默认为false
        shared-configs[1]:
          data-id: ${spring.application.name}-custom.${spring.cloud.nacos.config.file-extension}
          refresh: true
  profiles:
    active: dev