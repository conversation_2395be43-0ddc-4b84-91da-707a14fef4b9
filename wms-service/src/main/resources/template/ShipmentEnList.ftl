<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>打印装箱清单</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        .outBox {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .codeImg{
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .spaceBetween {
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            width: 100%;
        }
        .spaceBetween div{
            line-height: 30px;
        }
        .flexEnd {
            width: 100%;
            text-align:right;
        }

        table {
            border-collapse: collapse;
        }

        /*table, table tr th, table tr td {*/
        /*    border: 1px solid #000000;*/
        /*}*/
    </style>
</head>
<body>
<div class="outBox">
    <div class="codeImg">
        ${data.orderNo!}
    </div>
    <br>
    <div class="spaceBetween">
        <div style="width: 100%;">
            Print Date：${data.printDate}
        </div>
        <div style="width: 50%;">
            To：${data.to!''}
        </div>
        <div style="width: 50%;">
            Tel：${data.receiverMobile!''}
        </div>
        <div style="width: 100%;">
            Delivery Address：${data.receiverAddress!''}
        </div>
        <div style="width: 50%;">
            Zip：${data.receiverZip!''}
        </div>
        <div style="width: 50%;">
            ${data.skuQty} skus，${data.totalQty} PCS
        </div>
        <#if data.platformReferenceNo?? && data.platformReferenceNo?has_content>
            <div style="width: 100%;">
                Platform Reference No：${data.platformReferenceNo}
            </div>
        </#if>
        <div style="width: 100%;">
            Description：${data.description!''}
        </div>
    </div>
    <br>
    <!--    sku列表-->
    <table align="center" width="100%" style="width: 100%;table-layout: fixed; word-break: break-all;">
        <tr>
            <th align="center" valign="middle">Box Index</th>
            <th align="left" valign="middle">Sku</th>
            <th align="left" valign="middle">Color/size</th>
            <th align="center" valign="middle">Quantity</th>
        </tr>
        <#list data.skuInfoList as skuInfo>
            <tr>
                <td align="center" valign="middle">${skuInfo.boxIndex!''}</td>
                <td align="left" valign="middle">${skuInfo.sellerSku!skuInfo.sku}</td>
                <td align="left" valign="middle">${skuInfo.color!}-${skuInfo.size!}</td>
                <td align="center" valign="middle">${skuInfo.qty!}</td>
            </tr>
        </#list>
        <tr>
            <td align="center" valign="middle">Total：</td>
            <td align="left" valign="middle"></td>
            <td align="left" valign="middle"></td>
            <td align="center" valign="middle">${data.totalQty!''}</td>
        </tr>
    </table>
</div>
</body>
</html>
