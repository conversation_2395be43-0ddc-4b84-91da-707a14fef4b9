/*
* =============================================================================
* Designer: Administrator@CAISHAOHUI
* Description: nsy_359279
* Created: 2021/06/22 14:04:38
* =============================================================================
*/

create table if not exists stockin_order
(
   stockin_order_id     int not null primary key auto_increment,
   location             varchar(50) not null comment '地区',
   stockin_order_no     varchar(50) not null comment '入库单号',
   task_id              int not null comment '入库任务id',
   status               varchar(50) not null comment '状态',
   create_date          timestamp NOT NULL default CURRENT_TIMESTAMP comment '创建时间',
   create_by            varchar(50) comment '创建人',
   update_date          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   update_by            varchar(50) comment '更新人',
   version              bigint default 0 not null comment '版本号',
   KEY `idx_location` (`location`) USING BTREE,
   KEY `idx_task_id` (`task_id`) USING BTREE,
   KEY `idx_stockin_order_no` (`stockin_order_no`) USING BTREE
);

alter table stockin_order comment '入库单';

create table if not exists stockin_order_item
(
   stockin_order_item_id     int not null primary key auto_increment,
   location             varchar(50) not null comment '地区',
   stockin_order_id     int not null comment '入库单id',
   task_item_id         int not null comment '入库任务明细id',
   internal_box_code    varchar(50) comment '内部箱号',
   product_id           int not null comment '商品系统的product.id',
   spec_id              int not null comment '商品系统product_spec.id',
   sku                  varchar(50) not null comment '商品sku',
   purchase_plan_no     varchar(50) not null comment '计划采购单号',
   qty                  int not null comment '数量',
   batch_code           varchar(50) not null comment '批次号',
   create_date          timestamp NOT NULL default CURRENT_TIMESTAMP comment '创建时间',
   create_by            varchar(50) comment '创建人',
   update_date          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   update_by            varchar(50) comment '更新人',
   version              bigint default 0 not null comment '版本号',
   KEY `idx_location` (`location`) USING BTREE,
   KEY `idx_spec_id` (`spec_id`) USING BTREE,
   KEY `idx_task_item_id` (`task_item_id`) USING BTREE,
   KEY `idx_stockin_order_id` (`stockin_order_id`) USING BTREE
);

alter table stockin_order_item comment '入库单明细';

create table if not exists stockin_scan_log
(
   stockin_scan_log_id  int not null primary key auto_increment,
   location             varchar(50) not null comment '地区',
   stockin_order_no     varchar(50) comment '入库单号',
   supplier_delivery_box_code varchar(50) comment '供应商出库箱码',
   internal_box_code    varchar(50) comment '内部箱号',
   stockin_scan_log_type  varchar(50) comment '入库扫描日志类型',
   content              varchar(50) comment '内容',
   scan_qty             int comment '数量',
   create_date          timestamp NOT NULL default CURRENT_TIMESTAMP comment '创建时间',
   create_by            varchar(50) comment '创建人',
   update_date          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   update_by            varchar(50) comment '更新人',
   version              bigint default 0 not null comment '版本号',
   KEY `idx_supplier_delivery_box_code` (`supplier_delivery_box_code`) USING BTREE,
   KEY `idx_internal_box_code` (`internal_box_code`) USING BTREE,
   KEY `idx_stockin_order_no` (`stockin_order_no`) USING BTREE
);

alter table stockin_scan_log comment '入库扫描日志';

create index idx_stock_in_order_no on stock_internal_box_item
(
   stock_in_order_no
);

create index idx_internal_box_id on stock
(
   internal_box_id
);

create index idx_supplier_delivery_no on stockin_return_product
(
   supplier_delivery_no
);
