# 合并后的配置文件
server:
  port: 8881

# feign 配置
feign:
  sentinel:
    enabled: false
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        loggerLevel: full
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
    response:
      enabled: true

lock:
  redisson:
    address: ${nsy.redis.host}:${nsy.redis.port}
    password: ${nsy.redis.password}
    type: STANDALONE
    enabled: true
  key:
    prefix: API-WMS:LOCK

spring:
  flyway:
    enabled: false
  jpa:
    show-sql: false
    properties:
      hibernate:
        cache:
          use_second_level_cache: false
          use_query_cache: false
        dialect: org.hibernate.dialect.MySQLDialect
        query:
          plan_cache_max_size: 64
          plan_parameter_metadata_max_size: 32
          plan_cache_max_soft_references: 128
          plan_cache_max_strong_references: 64
  datasource:
    primary:
      url: jdbc:mysql://${nsy.mysql.host}:${nsy.mysql.port}/nsy_wms?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&noAccessToProcedureBodies=true
      username: ${nsy.mysql.username}
      password: ${nsy.mysql.password}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: ${spring.application.name}-DataSourcePool
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 60000
        connection-timeout: 15000
    secondary:
      url: jdbc:mysql://${nsy.mysql.host}:${nsy.mysql.port}/nsy_wms?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&noAccessToProcedureBodies=true
      username: ${nsy.mysql.username}
      password: ${nsy.mysql.password}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: ${spring.application.name}-DataSourcePool
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 60000
        connection-timeout: 15000
  cache:
    type: redis
  redis:
    host: ${nsy.redis.host}
    port: ${nsy.redis.port}
    password: ${nsy.redis.password}
    timeout: 500
    jedis:
      pool:
        max-wait: 10000
        max-idle: 8
        min-idle: 0
  jackson:
    time-zone: GMT+8
  rabbitmq:
    host: rmq.lumaotong.com
    port: 5671
    username: xsydztest
    password: xsydztest@20221216
    virtual-host: 91350503MA32TPH5XB
    virtual-host-lxfs: 91350503MA33311F3U
    virtual-host-syfs: 91350503611608429U
    virtual-host-xjsm: 91350503MA35D98R4D
    connection-timeout: 15000
    ssl:
      enabled: true
    listener:
      declare-queue: 91350503MA32TPH5XB_EntryFile_Send_test
      declare-queue-enable: false
      simple:
        concurrency: 1
        max-concurrency: 1
        acknowledge-mode: none
        prefetch: 1

kafka:
  producer:
    broker: ${nsy.kafka.broker}
  consumer:
    bootstrap-server: ${nsy.kafka.bootstrap-server}
    consumer-group-id: ${spring.application.name}

elastic-job:
  registry-center-server-list: ${nsy.zookeeper.url}
  registry-namespace: ${spring.application.name}-brent
  kafka:
    bootstrap-server: ${nsy.kafka.bootstrap-server}
    consumer-group-id: ${spring.application.name}-job
scheduler:
  elastic:
    job:
      config: elastic-jobs.json

# health management
management:
  endpoints:
    web:
      exposure:
        include: info,health,mappings,threaddump,metrics,httptrace,prometheus
  endpoint:
    health:
      show-details: always
  health:
    diskspace:
      enabled: false
    mail:
      enabled: false
  metrics:
    tags:
      application: ${spring.application.name}
      region: cn-hangzhou

site:
  long:
    request:
      open: Y
      timeout: 3

mybatis-plus:
  mapper-locations: classpath:mapper/**/*Mapper.xml
  global-config:
    banner: true
    db-config:
      table-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true

logging:
  level:
    root: debug
  config: classpath:logback.xml

nsy:
  web:
    syn:
      evaless:
        url: http://51028.cn01.dearlovertech.com
        token: core_51028_08f3be8aa9cca06f360a8c6364441640
      dearlover:
        url: http://43786.cn01.dearlovertech.com
        token: core_43786_87ef016805c88ca09eabfb894a788d92
  service:
    url:
      erp: http://172.16.8.180:8080
      image-tool: http://172.16.8.166:32650/
      product: http://************:8081/
      user: http://************:8088/
      tms: http://************:8880/
      oauth2-server: http://************:7777/
      bi: http://************:8085/
      notify: http://************:8089/
      search: http://************:8084/
      selection: http://************:8082/
      thirdparty: http://************:8087/
      ads: http://************:8896/
      report: http://************:8892/
      supplier: http://************:8086/
      etl: http://************:8902/
      scm: http://************:8882/
      fds: http://************:8908/
      mes: http://************:8904/
      pms: http://************:8884/
      oms: http://************:8905/
      oms-publish: http://************:8883/
      crawl: http://************:8897/
      wms: http://************:8881/
      kms: http://************:8901/
      fms: http://************:8893/
      activiti: http://************:8891/
      amazon: http://************:8083/
      settlement: http://************:8890/
      image: http://************:8895/
      shopxsybi: http://************:8898/
      crm: http://************:8903/
      business-base: http://************:8907/
      api-gc: http://************:8910/
      preview-img: http://dev-scm.dearlovertech.com/previewImg
  mysql:
    host: ************
    port: 3306
    database: nsy_wms
    username: nsy_mysql
    password: t6DT6hKLSTbaI.
  redis:
    host: ************
    port: 6379
    password: jPaZbnLgK3zFYZIV
  kafka:
    broker: ************:9092
    bootstrap-server: ************:9092
  zookeeper:
    url: ************:2181
  baidu:
    clientId: YyaxKL0gZMgLy1SvqKs8zXs3
    clientSecret: krMqXGQGIT6Xy6GzxkvaGALR6bQyjS7n
    fanyi:
      appKey: 20200805000533739
      securityKey: vE5UCGVWPDwqsXQAjjk9

aliyun:
  oss:
    accessKeyId: LTAI5tHmEg4bxfksAoZzMp7p
    accessKeySecret: ******************************
    region: cn-hangzhou
    bucket: stage-nsy-wms

wcsSecretKey: gNYuoBF6eYsbBUwjzBVd
aliyunPdfOcrCommonAppCode: 24e5463c4e854aa2830266a358172e19

declare:
  form:
    fetch:
      url: https://conpltapitest.lumaotong.com/api/v2/receive
      channel-xsydz: D5F7C941B4BF483F95F6E4517A69D9F6
      key-xsydz: 546A719EFFBF492983E4491DFE10379A
      channel-lxfs: FC5AD0502A9F30BBE053DEA6A80ABBD4
      key-lxfs: FC5AD0502AA430BBE053DEA6A80ABBD4
      channel-syfs: FC5AD0502A9A30BBE053DEA6A80ABBD4
      key-syfs: FC5AD0502A9C30BBE053DEA6A80ABBD4
      channel-xjsm: FC5AD0502A9830BBE053DEA6A80ABBD4
      key-xjsm: FC5AD0502A9930BBE053DEA6A80ABBD4

dingding:
  robotAccessToken:
    orderMonitor: 0aadbe19096a4e6f509e57b9b5264cfb3151844f6582fea9d1ff9265bd873ef3
    temuOrderMonitor: 25ba8c91e7899628469c69dfe917cdf1afcc59b0ffb4361e3c448c6b6407f240
    temuGrabOrderMonitor: e8e97f747b06ac4df7a7f608afa276f5cd565b7910f32a5c063edeaeeac89e34
    spaceOverseaMonitor: 813f3d35ac221b0a3974bd0755b6f03e0b8d84a31ee5e002fb9e8e76cdf97580

factory-rabbitmq:
  lxfs:
    password: lxfs@20230516
    username: lxfs
  syfs:
    password: syfs@20230516
    username: syfs
  xjsm:
    password: xjsm@20230516
    username: xjsm

otto:
  token: Llgfl7pK1JPbG43c8cSQ
  key: jvFj51KmrKpAseoZcYGF
  order-warehouse-id: 19
  return-order-warehouse-id: 19
  order-shipping-code: DHL
  return-order-shipping-code: DE-DHLRT-SKD
  stock-warehouse-ids:
    - 97
    - 90

auth:
  app-key: api-wms-test
  secret: L4NC|9*4FF9Bi1

