package com.nsy.wms.mp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.wms.SpringServiceTest;
import com.nsy.wms.repository.entity.demo.DemoEntity;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.wms.repository.jpa.mapper.demo.DemoMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DemoMapperTest extends SpringServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(DemoMapperTest.class);

    @Autowired
    DemoMapper demoMapper;

    private DemoEntity demoEntity;


    // 创建数据
    private void create() {
        demoEntity = new DemoEntity();
        demoEntity.setName("demo test data");
        demoEntity.setIsDeleted(0);
        demoMapper.insert(demoEntity);
    }

    //清除数据
    private void clear() {
        demoMapper.deleteById(demoEntity.getId());
    }

    @Test
    public void testAdd() {
        demoEntity = new DemoEntity();
        demoEntity.setName("li si");
        demoEntity.setLocation(LocationEnum.QUANZHOU.name());
        demoEntity.setIsDeleted(0);
        int updateCount = demoMapper.insert(demoEntity);
        Assertions.assertEquals(updateCount, 1);
        clear();
    }

    @Test
    public void testDelete() {
        create();
        int updateCount = demoMapper.deleteById(demoEntity.getId());
        Assertions.assertEquals(updateCount, 1);
    }

    @Test
    public void testUpdate() {
        create();
        demoEntity.setName("demo update");
        int updateCount = demoMapper.updateById(demoEntity);
        Assertions.assertEquals(updateCount, 1);
        clear();
    }


    @Test
    public void testQuery() {
        create();
        List<DemoEntity> demoEntityList = demoMapper.selectList(new QueryWrapper<>());
        for (DemoEntity entity : demoEntityList) {
            LOGGER.info("{}", entity);
        }
        clear();
        clear();
    }

    @Test
    public void testMybatisSql() {
        createList();
        Page<DemoEntity> page = new Page<>(1, 2);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("name", "test01");
        queryMap.put("location", LocationEnum.QUANZHOU.name());
        List<DemoEntity> demoEntityList = demoMapper.searchDemo(page, queryMap);
        Assertions.assertEquals(demoEntityList.size(), 2);
        IPage<DemoEntity> pageResult = demoMapper.pageSearchDemo(page, queryMap);
        Assertions.assertEquals(pageResult.getTotal(), 3);
        Assertions.assertEquals(pageResult.getRecords().size(), 2);
        clearList();
    }

    private void clearList() {
        demoMapper.delete(new QueryWrapper<>());
    }

    private void createList() {
        DemoEntity demoEntity1 = new DemoEntity();
        demoEntity1.setName("test01");
        demoEntity1.setLocation(LocationEnum.QUANZHOU.name());
        demoEntity1.setIsDeleted(0);
        demoMapper.insert(demoEntity1);

        DemoEntity demoEntity2 = new DemoEntity();
        demoEntity2.setName("test02");
        demoEntity2.setLocation(LocationEnum.GUANGZHOU.name());
        demoEntity2.setIsDeleted(0);
        demoMapper.insert(demoEntity2);

        DemoEntity demoEntity3 = new DemoEntity();
        demoEntity3.setName("test03");
        demoEntity3.setLocation(LocationEnum.GUANGZHOU.name());
        demoEntity3.setIsDeleted(0);
        demoMapper.insert(demoEntity3);

        DemoEntity demoEntity4 = new DemoEntity();
        demoEntity4.setName("test01");
        demoEntity4.setLocation(LocationEnum.QUANZHOU.name());
        demoEntity4.setIsDeleted(0);
        demoMapper.insert(demoEntity4);

        DemoEntity demoEntity5 = new DemoEntity();
        demoEntity5.setName("test01");
        demoEntity5.setLocation(LocationEnum.QUANZHOU.name());
        demoEntity5.setIsDeleted(0);
        demoMapper.insert(demoEntity5);
    }

}
