package com.nsy.wms.service.bd;

import com.nsy.wms.SpringServiceTest;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.wms.repository.entity.bd.BdMaterialEntity;
import com.nsy.api.wms.request.bd.BdMaterialAddRequest;
import com.nsy.api.wms.request.bd.BdMaterialListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdMaterialListResponse;
import com.nsy.wms.business.service.bd.BdMaterialService;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

public class BdMaterialServiceTest extends SpringServiceTest {
    @Autowired
    BdMaterialService materialService;

    BdMaterialEntity materialEntity;

    @MockBean
    EnumConversionChineseUtils enumConversionChineseUtils;

    @BeforeEach
    public void init() {
        Mockito.doReturn("")
                .when(enumConversionChineseUtils).baseConversion(Mockito.anyString(), Mockito.anyString());
        materialEntity = new BdMaterialEntity();
        materialEntity.setMaterialType("内部箱");
        materialEntity.setMaterialName("循环箱");
        materialEntity.setMaterialSize("90*90*60");
        materialEntity.setTotalQty(900);
        materialEntity.setAvailableQty(900);
        materialEntity.setProperty("固资");
        materialEntity.setUnit("个");
        materialService.save(materialEntity);
    }

    @AfterEach
    public void deleteAll() {
        materialService.removeById(materialEntity);
    }

    @Test
    public void addMaterialTest() {
        BdMaterialAddRequest request = new BdMaterialAddRequest();
        request.setMaterialType("纸箱");
        request.setProperty("耗材");
        request.setMaterialSize("90*90*60");
        request.setUnit("个");
        materialService.addMaterial(request);
    }

    @Test
    public void getMaterialListTest() {
        BdMaterialListRequest request = new BdMaterialListRequest();
        PageResponse<BdMaterialListResponse> materialList = materialService.getMaterialList(request);
        Assertions.assertNotNull(materialList);
    }

    @Test
    public void getMaterialSelectTest() {
        List<SelectModel> selectResponses = materialService.getAllMaterialName();
        Assertions.assertNotNull(selectResponses);
    }
}
