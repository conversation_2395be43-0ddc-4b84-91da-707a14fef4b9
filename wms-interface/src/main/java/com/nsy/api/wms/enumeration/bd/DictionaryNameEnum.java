package com.nsy.api.wms.enumeration.bd;

/**
 * 数据字典名称
 */
public enum DictionaryNameEnum {
    WMS("wms_"),
    // 入库类型
    WMS_STOCKIN_TYPE("wms_stockin_type"),
    // 月台入库状态
    WMS_PLATFORM_STOCKIN_STATUS("wms_platform_stockin_status"),
    // 月台预约状态
    WMS_PLATFORM_APPOINTMENT_STATUS("wms_platform_appointment_status"),
    // 采购模式
    WMS_PURCHASE_MODEL("wms_purchase_model"),
    // 入库任务状态
    WMS_STOCKIN_TASK_TYPE("wms_stockin_task_type"),
    // 上架任务状态
    WMS_SHELVE_TASK_STATUS("wms_shelve_task_status"),
    // 上架任务类型
    WMS_SHELVE_TASK_TYPE("wms_shelve_task_type"),
    // 库存交易操作类型
    WMS_STOCK_TRADE_OPT_TYPE("wms_stock_trade_opt_type"),
    // 预约类型
    WMS_APPOINTMENT_TYPE("wms_appointment_type"),
    // 入库优先级
    WMS_STOCKIN_PRIORITY("wms_stockin_priority"),
    // 月台作业区域
    WMS_PLATFORM_OPERATION_AREA("wms_platform_operation_area"),
    // 出库优先级
    WMS_STOCKOUT_PRIORITY("wms_stockout_priority"),
    // 入库月台审核
    WMS_STOCKIN_PLATFORM_AUDIT_RULE("wms_stockin_platform_audit_rule"),
    // 出库月台审核
    WMS_STOCKOUT_PLATFORM_AUDIT_RULE("wms_stockout_platform_audit_rule"),
    // 入库质检优先级
    WMS_STOCKIN_QC_PRIORITY("wms_stockin_qc_priority"),
    // 入库方式
    WMS_STOCKIN_METHOD("wms_stockin_method"),
    // 入库单状态
    WMS_STOCKIN_ORDER_STATUS("wms_stockin_order_status"),
    // 质检后上架方式
    WMS_QC_SHELVE_TYPE("wms_qc_shelve_type"),
    // 称重顺序
    WMS_WEIGHT_ORDER("wms_weight_order"),
    // 规则级别
    WMS_RULE_RANK("wms_rule_rank"),
    // 品质包含
    WMS_QUALITY_INCLUDE("wms_quality_include"),
    // 使用类型
    WMS_USE_TYPE("wms_use_type"),
    // 适用车辆
    WMS_CAR_TYPE("wms_car_type"),
    // 报关方式
    WMS_CUSTOMS_TYPE("wms_customs_type"),
    // 报关单位
    WMS_CUSTOMS_UNIT("wms_customs_unit"),
    // 仓库类型
    WMS_SPACE_TYPE("wms_space_type"),
    // 库区类型
    WMS_SPACE_AREA_TYPE("wms_space_area_type"),
    // 库位类型
    WMS_POSITION_TYPE("wms_position_type"),
    // 物料类型
    WMS_MATERIAL_TYPE("wms_material_type"),
    // 物料规格-内部箱
    WMS_MATERIAL_SIZE_INTERNAL_BOX("wms_material_size_internal_box"),
    // 物料规格-纸箱
    WMS_MATERIAL_SIZE_CARTON("wms_material_size_carton"),
    // 物料规格-热敏纸
    WMS_MATERIAL_SIZE_THERMAL_PAPER("wms_material_size_thermal_paper"),
    // 物料规格-胶带
    WMS_MATERIAL_SIZE_TAPE("wms_material_size_tape"),
    // 物料规格-卡板
    WMS_MATERIAL_SIZE_PALLET("wms_material_size_pallet"),
    // 性质
    WMS_PROPERTY("wms_property"),
    // 计量单位
    WMS_CALCULATE_UNIT("wms_calculate_unit"),
    // 登记类型
    WMS_REGISTER_TYPE("wms_register_type"),
    // 内部箱类型
    WMS_INTERNAL_BOX_TYPE("wms_internal_box_type"),
    // 内部箱状态
    WMS_INTERNAL_BOX_STATUS("wms_internal_box_status"),
    // 拣货箱状态
    WMS_PICKING_BOX_STATUS("wms_picking_box_status"),
    // 撤货箱状态
    WMS_WITHDRAWAL_BOX_STATUS("wms_withdrawal_box_status"),
    // 上架规则明细
    WMS_SHELVE_RULE_DETAIL("wms_shelve_rule_detail"),
    // 出库状态
    WMS_STOCKOUT_DOC_STATUS("wms_stockout_doc_status"),
    // 出库类型
    WMS_STOCKOUT_TYPE("wms_stockout_type"),
    // 拣货模式
    WMS_STOCKOUT_PICKING_TYPE("wms_stockout_picking_type"),
    // 工作区域
    WMS_STOCKOUT_WORK_LOCATION("wms_stockout_work_location"),
    // 发货通知
    WMS_STOCKOUT_DELIVERY_NOTICE("wms_stockout_delivery_notice"),
    // 分配规则item名称
    WMS_STOCKOUT_DISTRIBUTION_TYPE("wms_stockout_distribution_type"),
    //包装
    WMS_PACKAGE_STANDARD("wms_package_standard"),
    // 包装类型
    WMS_PACKAGING_TYPE("wms_packaging_type"),
    // 品牌类型
    TMS_DACLARE_BRAND_TYPE("tms_daclare_brand_type"),

    WMS_HS_CODE_EXPORT_CONDITION("wms_hs_code_export_condition"),
    // 拣货任务规则明细
    WMS_STOCKOUT_PICKING_TASK_RULE("wms_stockout_picking_task_rule"),
    // 拣货任务状态
    WMS_STOCKOUT_PICKING_TASK_STATUS("wms_stockout_picking_task_status"),

    // 拣货任务类型
    WMS_PICKING_TASK_TYPE("wms_picking_task_type"),

    // 扫描台
    WMS_STOCKOUT_SORTING_SCANNING_STATION("wms_stockout_sorting_scanning_station"),
    // 出口享惠情况
    TMS_DECLARE_EXPORT_BENEFIT("tms_declare_export_benefit"),
    // 分拣类型
    WMS_STOCKOUT_SORTING_TYPE("wms_stockout_sorting_type"),

    // 分拣任务状态
    WMS_STOCKOUT_SORTING_TASK_STATUS("wms_stockout_sorting_task_status"),
    // 复核任务状态
    WMS_STOCKOUT_REVIEW_TASK_STATUS("wms_stockout_review_task_status"),

    // 装箱清单状态
    WMS_PACKING_LIST_STATUS("wms_packing_list_status"),

    // pack装箱清单状态
    WMS_SHIPMENT_PACKING_LIST_STATUS("wms_shipment_packing_list_status"),

    // 箱子规格
    WMS_BOX_STANDARDS("wms_box_standards"),

    // fba箱贴状态
    WMS_FBA_LABEL_STATUS("wms_fba_label_status"),

    // 运单来源
    WMS_WAYBILL_SOURCE("wms_waybill_source"),

    // 波次状态
    WMS_STOCKOUT_WAVE_TASK_STATUS("wms_stockout_wave_task_status"),

    // 缺货来源
    WMS_SHORTAGE_SOURCE("wms_shortage_source"),

    // 缺货状态
    WMS_OUT_OF_STOCK_LIST_STATUS("wms_out_of_stock_list_status"),

    // 差异核对单状态
    WMS_VARIANCE_CHECKLIST_STATUS("wms_variance_checklist_status"),

    // 缺货SKU状态
    WMS_OUT_OF_STOCK_SKU_STATUS("wms_out_of_stock_sku_status"),

    // 库位调拨任务类型
    WMS_LOCATION_TRANSFER_TASK_TYPE("wms_location_transfer_task_type"),

    // 库位调拨任务状态
    WMS_LOCATION_TRANSFER_TASK_STATUS("wms_location_transfer_task_status"),

    // 内部箱调拨任务类型
    WMS_INTERNAL_BOX_TRANSFER_TYPE("wms_internal_box_transfer_type"),

    // 内部箱调拨任务状态
    WMS_INTERNAL_BOX_TRANSFER_STATUS("wms_internal_box_transfer_status"),

    // 仓间调拨类型
    WMS_WH_TRANSFER_TYPE("wms_wh_transfer_type"),
    // 仓间调拨状态
    WMS_WH_TRANSFER_STATUS("wms_wh_transfer_status"),
    // 调拨箱状态
    WMS_TRANSFER_BOX_STATUS("wms_transfer_box_status"),
    // 借用归还箱状态
    WMS_BORROW_RETURN_BOX_STATUS("wms_borrow_return_box_status"),
    // 退货箱状态
    WMS_RETURN_BOX_STATUS("wms_return_box_status"),
    // 仓库系统-盘点任务状态
    WMS_INVENTORY_TASK_STATUS("wms_inventory_task_status"),
    // 仓库系统-盘点任务生成方式
    WMS_INVENTORY_TASK_METHOD("wms_inventory_task_method"),
    // 仓库系统-盘点任务类型
    WMS_INVENTORY_TASK_TYPE("wms_inventory_task_type"),
    // 小包发货状态
    WMS_STOCKOUT_BAG_PACK_STATUS("wms_stockout_bag_pack_status"),
    // 内部借用单状态
    WMS_INSIDE_LEND_DOC_STATUS("wms_inside_lend_doc_status"),
    // 业务部门
    WMS_BUSINESS_UNIT("wms_business_unit"),
    // 分拣上架任务状态
    WMS_STOCKIN_SHELVE_SORT_STATUS("wms_stockin_shelve_sort_status"),
    // 外部系统接口类型
    WMS_EXTERNAL_SYSTEM_INTERFACE_TYPE("wms_external_system_interface_type"),
    // 系统模块
    BASE_SYSTEM_MODULE("base_system_module"),
    // 退货类型
    WMS_STOCKIN_RETURN_TYPE("wms_stockin_return_type"),
    // 销售退货单状态
    WMS_STOCKIN_SALE_RETURN_STATUS("wms_stockin_sale_return_status"),
    // 工厂退货任务状态
    WMS_STOCKIN_SUPPLY_RETURN_STATUS("wms_stockin_supply_return_status"),
    // 退货方式
    WMS_STOCK_RETURN_WAY("wms_stockout_return_way"),
    // 面单模板类型
    WMS_PRINT_TEMPLATE_TYPE("wms_print_template_type"),
    // 退货性质
    WMS_STOCKIN_RETURN_NATURE("wms_stockin_return_nature"),
    // 退货任务退货类型
    WMS_STOCKIN_RETURN_TASK_TYPE("wms_stockin_return_task_type"),
    // 报关订单类型
    WMS_CUSTOMER_DECLARE_TYPE("wms_customer_declare_type"),
    // 报关订单状态
    WMS_CUSTOMER_DECLARE_STATUS("wms_customer_declare_status"),
    // 报关单据类型
    WMS_CUSTOMER_DECLARE_DOCUMENT_TYPE("wms_customer_declare_document_type"),
    // 港口
    WMS_PORT("wms_port"),
    //目的地
    WMS_DESTINATION("wms_destination"),
    //出境关别
    WMS_CUSTOMS("wms_exit_clearance"),
    //出境关别
    LOCATION("location"),
    //tag
    WMS_BD_TAG_TYPE("wms_tag_type"),
    //工厂出库单状态
    WMS_SUPPLIER_DELIVERY_ORDER_STATUS("wms_supplier_delivery_order_status"),
    //简易复核状态
    WMS_STOCKOUT_EASY_SCAN_TASK_STATUS("wms_stockout_easy_scan_task_status"),
    // 内部箱sku状态
    WMS_INTERNAL_BOX_SKU_STATUS("wms_internal_box_sku_status"),
    //归还单状态
    WMS_INSIDE_LEND_RETURN_DOC_STATUS("wms_inside_lend_return_doc_status"),
    // 织造方式
    WMS_MAKE_TYPE("wms_make_type"),
    // 缝制任务类型
    WMS_SEW_TASK_TYPE("wms_sew_task_type"),
    // 缝制任务状态
    WMS_SEW_TASK_STATUS("wms_sew_task_status"),
    // 税号
    WMS_VAT_NUM("wms_vat_num"),
    //调拨分拣类型
    WMS_TRANSFER_SPLIT_TYPE("wms_transfer_split_type"),
    //调拨分拣状态
    WMS_TRANSFER_SPLIT_STATUS("wms_transfer_split_status"),
    //报关合同状态
    WMS_STOCKOUT_CUSTOMS_DECLARE_CONTRACT_STATUS("wms_stockout_customs_declare_contract_status"),
    //关单状态
    WMS_STOCKOUT_CUSTOMS_DECLARE_FORM_STATUS("wms_stockout_customs_declare_form_status"),
    //波次类型
    WMS_STOCKOUT_WAVE_PLAN_TYPE("wms_stockout_wave_plan_type"),
    //temu快递公司
    WMS_TEMU_DELIVERY_COMPANY("wms_temu_delivery_company"),
    //faire装箱清单状态
    WMS_STOCKOUT_FAIRE_SHIPMENT_STATUS("wms_stockout_faire_shipment_status"),
    //盘点单盘点状态
    WMS_STOCK_TAKE_ORDER_CHECK_RESULT_STATUS("wms_stock_take_order_check_result_status"),
    //盘点单审核状态
    WMS_STOCK_TAKE_ORDER_AUDIT_STATUS("wms_stock_take_order_audit_status"),
    //平台名称
    WMS_STOCKOUT_ORDER_PLATFORM_NAME("wms_stockout_order_platform_name"),
    //通用分拣状态
    WMS_STOCKOUT_UNIVERSAL_SORTING_TASK_STATUS("wms_stockout_universal_sorting_task_status"),
    //补货任务状态
    WMS_STOCK_REPLENISHMENT_TASK_STATUS("wms_stock_replenishment_task_status"),
    //品牌字典
    WMS_BRAND("wms_brand"),
    //退货物流类型
    WMS_RETURN_LOGISTIC_TYPE("wms_return_logistic_type"),
    //处理失败
    WMS_STOCKOUT_ORDER_HANDLE_FAIL_TYPE("wms_stockout_order_handle_fail_type"),
    //处理失败状态
    WMS_STOCKOUT_ORDER_HANDLE_FAIL_STATUS("wms_stockout_order_handle_fail_status"),
    //运费承担方
    WMS_FREIGHT_CARRIER("wms_freight_carrier"),
    //调拨任务状态
    WMS_STOCK_TRANSFER_TASK_STATUS("wms_stock_transfer_task_status"),
    //调拨任务类型
    WMS_STOCK_TRANSFER_TASK_TYPE("wms_stock_transfer_task_type"),
    //报关比例数据 类型
    WMS_CUSTOMS_DECLARE_RATE_TYPE("wms_customs_declare_rate_type"),

    //入库质检任务状态
    WMS_STOCKIN_QA_TASK_STATUS("wms_stockin_qa_task_status"),
    //入库质检单状态
    WMS_STOCKIN_QA_ORDER_STATUS("wms_stockin_qa_order_status"),
    //入库质检单流程状态
    WMS_STOCKIN_QA_ORDER_PROCESS_STATUS("wms_stockin_qa_order_process_status"),
    //入库sop类型
    WMS_SOP_STOCKIN_TYPE("wms_sop_stockin_type"),
    // 对接部门
    SCM_DOCKING_DEPARTMENT("scm_docking_department"),
    //产前样状态
    WMS_STOCKIN_QA_PRODUCT_SAMPLE_RECORD_STATUS("WMS_STOCKIN_QA_PRODUCT_SAMPLE_RECORD_STATUS"),
    //让步接收 - 责任部门
    WMS_STOCKIN_QA_DEPART_RESPONSIBILITY("wms_stockin_qa_depart_responsibility"),

    //退货处理方式
    WMS_RETURN_HANDEL_METHOD("wms_return_handel_method"),

    //稽查任务状态
    WMS_STOCKIN_QA_INSPECT_STATUS("wms_stockin_qa_inspect_status"),

    //全检任务状态
    WMS_STOCKIN_QA_FULL_INSPECT_STATUS("wms_stockin_qa_full_inspect_status"),

    //稽查结果
    WMS_STOCKIN_QA_INSPECT_RESULT("wms_stockin_qa_inspect_result");

    DictionaryNameEnum(String name) {
        this.name = name;
    }

    private final String name;

    public String getName() {
        return name;
    }
}
