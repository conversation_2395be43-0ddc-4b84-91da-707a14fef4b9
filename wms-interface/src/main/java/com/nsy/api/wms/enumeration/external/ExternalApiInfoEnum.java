package com.nsy.api.wms.enumeration.external;

import org.springframework.web.bind.annotation.RequestMethod;

public enum ExternalApiInfoEnum {
    FEEDBACK_RECEIVED("上架反馈", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    FEEDBACK_EDIT_RECEIVED("上架编辑反馈", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    DELIVERY_OVER_SHIPMENT("工厂多发数据处理", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    DELIVERY_OVER_SHIPMENT_ERP("工厂多发回填商通", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    FEEDBACK_CONCESSION_RECEIVED("让步接收反馈", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    FEEDBACK_PACKAGE_QTY("包装数反馈", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    FEEDBACK_SHELVED("上架完成反馈", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    FEEDBACK_CHECKED("核对反馈", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    COMPLETE_RETURN_PRODUCT("退货登记同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    UPDATE_RETURN_PRODUCT("退货登记更新店铺", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    CHECK_RETURN_PRODUCT("退货登记校验", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    FEEDBACK_SPOT_RETURN_PRODUCT("现货收货产生退货反馈", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    GENERATE_RECEIVING_ORDER("生成接收单", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    RETURN_PRODUCT_SYNC("采购退货同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    STOCK_LEND_CANCEL("借用单取消同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCK_LEND_FEEDBACK, RequestMethod.POST.name()),
    STOCK_LEND_CANT_RETURN("借用单无法归还同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCK_LEND_FEEDBACK, RequestMethod.POST.name()),
    STOCK_LEND_WAIT_RETURN("借用单借出同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCK_LEND_FEEDBACK, RequestMethod.POST.name()),
    STOCK_LEND_RETURN("借用单归还同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCK_LEND_FEEDBACK, RequestMethod.POST.name()),
    GENERATE_INVENTORY("生成盘点单", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.INVENTORY_ADJUST_FEEDBACK, RequestMethod.POST.name()),
    TRANSFER_IN("调拨入库", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.INVENTORY_ADJUST_FEEDBACK, RequestMethod.POST.name()),
    SYNC_TRANSFER_STOCK("同步调拨库存", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.INVENTORY_ADJUST_FEEDBACK, RequestMethod.POST.name()),
    SYNC_SHIP_DIFF_STOCK("同步发货差异库存", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.INVENTORY_ADJUST_FEEDBACK, RequestMethod.POST.name()),
    SYNC_SPACE_TRANSFER_STOCK("同步仓间调拨库存", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.INVENTORY_ADJUST_FEEDBACK, RequestMethod.POST.name()),
    SYNC_STOCKOUT_ORDER_LOG("同步出库单日志", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    TRANSFER_OUT("调拨出库", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.INVENTORY_ADJUST_FEEDBACK, RequestMethod.POST.name()),
    INVENTORY_TRANSFER("盘点调拨", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.INVENTORY_ADJUST_FEEDBACK, RequestMethod.POST.name()),
    FEEDBACK_SPOT_RECEIVE_COMPLETE("现货收货完成", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    GET_LOGISTICS_PLAN_MAPPING("获取物流和采购单的映射", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.GET.name()),
    ADD_PURCHASE_ORDER_LOG("记录采购单日志", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    CHECK_SUPPLIER_EXISTS_SKU("判断工厂是否生产sku", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.GET.name()),
    GENERATE_SPOT_RETURN_TASK("生成现货退货任务", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    SPOT_GENERATE_DOCUMENTS("现货收货生成单据主表", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    SPOT_GENERATE_DOCUMENTS_ITEM("现货收货生成单据明细", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    RETURN_SHELVE("退货上架", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    SYNC_RETURN_DELIVERY_DATE("同步退货单发货时间", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    REWORK_CONFIRM_RECEIPT("返工收货确认", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    GET_NEW_ORDER_NO("获取新采购单号", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),

    // 外部系统
    FETCH_DECLARE_FORM_DATA("获取关单数据", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),

    SYNC_PICKING_BOX_SHIPPED("装箱清单发货同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_PICKING_BOX("装箱清单同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    FINISH_PARTIAL_PICK("拣货单完成同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    UPDATE_PARTIAL_PICK("更新拣货单扫描数", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    BACK_TRADE("订单回退", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    CANCEL_TRADE("订单取消", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    GET_LOG_OPERATION_LIST("订单日志", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    GET_LABEL("获取面单", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.GET_LOGISTICS_LABEL, RequestMethod.POST.name()),
    SYNC_QC_TASK("质检任务同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.QC_TASK_PUSH, RequestMethod.POST.name()),
    GET_QC_TASK("质检查询", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.QC_RESULTS_GET, RequestMethod.POST.name()),
    PROCESS_STOCKOUT_COMPLETE("胚款出库同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.PUT.name()),
    SHELVE_COMPLETE("上架完成同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.PUT.name()),
    GENERATE_SPLIT_BATCH("生成加工波次", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.PUT.name()),
    PROCESS_LACK_PICKING_COMPLETE("加工缺货拣货完成", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.PUT.name()),
    PROCESS_CANCEL("加工取消", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.PUT.name()),
    SYNC_LACK("确定缺货同步", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    REPLACE_LOGISTICS("更换转运单号", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_RECEIVING("同步接收单", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    RETURN_PRODUCT_LIST("获取退货数据", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.GET.name()),
    GET_SPOT_INFO("获取市场单数据", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.GET.name()),
    SYNC_PRINT_EXCEPTION("同步打印异常", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.GET_LOGISTICS_LABEL, RequestMethod.POST.name()),
    SYNC_REDUCE_RECEIVE_RETURN("同步接收单退货数", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    ADD_PROCESS_TRANSFER_OUT("次品调拨出库", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    ADD_PROCESS_TRANSFER_IN("次品调拨入库", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    ADJUST_PROCESS_STOCK("加工库存调整", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    LIGHT_PROCESS_COMPLETE("轻定制加工完成", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    LIGHT_PROCESS_OPERATE("轻定制加工处理", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    UPDATE_LOGISTICS("出库单同步物流", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    GETAMAZONTRADEINFO("获取亚马逊订单信息", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    UPDATE_FBA_LABEL_STATUS("更新Fba箱贴申请状态", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    BACK_FBA_LABEL_STATUS("回退Fba箱贴", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),


    AMAZON_CREATE_REPLENISH_ORDER("创建FBA补货单", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    AMAZON_CANCEL_REPLENISH_ORDER("取消FBA补货单", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),

    SYNC_PLATFORM_SCHEDULE("月台同步erp", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    GET_USER_INFO("获取用户信息", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    GET_SUPPLIER_INFO("获取供应商信息", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    // 订单系统
    STOCKOUT_PUSH("生成月台任务", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_RETURN_APPLY("生成退货申请单", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.RETURN_APPLY_PUSH, RequestMethod.POST.name()),
    SYNC_RETURN_APPLY_CONFIRM("退货申请单确认", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.RETURN_APPLY_PUSH, RequestMethod.POST.name()),
    PARTIAL_PICK_PUSH("生成出库单", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    STOCK_LEND_PUSH("生成借用单", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCK_LEND_FEEDBACK, RequestMethod.POST.name()),
    SET_URGENT("设置急单", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SET_MEMO("设置备注", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_RECEIVER_INFO("同步收件人信息", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_SHIPMENT_NOTICE("同步发货通知", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_CUSTOMER_BARCODE("同步客户商品", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_CANCEL_PICK("同步取消拣货单", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    CHECK_CANCEL_PICK("校验拣货单是否能取消", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.GET.name()),
    CHECK_BACK_TRADE("校验订单是否能回退", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.GET.name()),
    SYNC_INVENTORY("同步盘点结果", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    SYNC_SHIP_DEDUCT_STOCK("同步发货扣减库存", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.STOCKOUT_PUSH, RequestMethod.POST.name()),
    // 质检系统
    QC_RESULTS_FEEDBACK("质检结果反馈", ExternalApiLogApiModuleEnum.API_QMS, ExternalApiLogApiTypeEnum.QC_RESULTS_FEEDBACK, RequestMethod.POST.name()),
    QC_ADD_RETURN_QUANTITY("质检增加退货数", ExternalApiLogApiModuleEnum.API_QMS, ExternalApiLogApiTypeEnum.QC_RESULTS_FEEDBACK, RequestMethod.POST.name()),
    QC_REDUCE_RETURN_QUANTITY("质检减少退货数", ExternalApiLogApiModuleEnum.API_QMS, ExternalApiLogApiTypeEnum.QC_RESULTS_FEEDBACK, RequestMethod.POST.name()),
    // 供应商系统
    SUPPLIER_DELIVERY_MODIFY_SELLER_INFO("修改Seller信息", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.SUPPLIER_DELIVERY_MODIFY_SELLER_INFO, RequestMethod.POST.name()),
    CREATE_SUPPLIER_DELIVERY("创建工厂出库单", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    DELETE_SUPPLIER_DELIVERY("删除工厂出库单", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    SYNC_WAIT_RETURN_QTY("同步待退货数", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    SCM_ADD_LACK_PICK("SCM生成缺货拣货任务", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    SCM_ADD_LACK_PICK_AFTER_QC("SCM质检后，生成缺货拣货任务", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    SCM_PROCESS_COMPLETE("SCM加工完成", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    SCM_PROCESS_BATCH_COMPLETE("SCM加工波次完成", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    SCM_WITHDRAWAL("SCM撤货", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    SCM_CHECK_STOCK("SCM检查库存", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.PROCESS_FEEDBACK, RequestMethod.POST.name()),
    SCM_INVOICE_BATCH("SCM批量匹配供应商", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.DECLARE_FEEDBACK, RequestMethod.POST.name()),
    SCM_INVOICE_ONE("SCM匹配单个供应商", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.DECLARE_FEEDBACK, RequestMethod.POST.name()),
    SCM_REJECT_SUPPLIER("SCM驳回供应商", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    SUPPLIER_SUPPLIER_SYNC_INVOICE("SUPPLIER同步发票数据", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_FEEDBACK, RequestMethod.POST.name()),
    SUPPLIER_SYNC_CONTRACT_DATA("SUPPLIER同步合同数据", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    SCM_GENERATE_CONTRACT_FEEDBACK("SCM生成合同反馈", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    SCM_INVOICE_INFO_FEEDBACK("开票信息同步", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    SUPPLIER_SYNC_CONTRACT_SIGN("SUPPLIER同步合同已签署", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    SUPPLIER_REJECT_FORM("SUPPLIER供应商关单驳回", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_FEEDBACK, RequestMethod.POST.name()),
    SUPPLIER_REJECT_CONTRACT("SUPPLIER供应商合同驳回", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_FEEDBACK, RequestMethod.POST.name()),
    NOTIFY_ESIGN_CALLBACK("NOTIFY E签宝回调", ExternalApiLogApiModuleEnum.API_NOTIFY, ExternalApiLogApiTypeEnum.DECLARE_FEEDBACK, RequestMethod.POST.name()),
    WMS_FORM_ITEM_AUDIT("WMS关单进项明细审核", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    WMS_CONTRACT_AUDIT("WMS关单合同审核", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    WMS_CONTRACT_REJECT("WMS关单合同驳回", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    SCM_REJECT_CONTRACT_FEEDBACK("驳回合同反馈SCM", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    WMS_FORM_CLEAR("WMS清楚关单", ExternalApiLogApiModuleEnum.API_VMS, ExternalApiLogApiTypeEnum.DECLARE_PUSH, RequestMethod.POST.name()),
    SCM_UPDATE_SUPPLIER_BUYER("SCM更新采购员同步", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.UPDATE_SUPPLIER_FEEDBACK, RequestMethod.PUT.name()),
    TEMU_CREATE_SHIPORDER("TEMU创建发货单", ExternalApiLogApiModuleEnum.API_THIRDPARTY, ExternalApiLogApiTypeEnum.TEMU_CREATE_SHIPORDER, RequestMethod.POST.name()),
    TEMU_SEND_SHIPORDER("TEMU操作发货单", ExternalApiLogApiModuleEnum.API_THIRDPARTY, ExternalApiLogApiTypeEnum.TEMU_SEND_SHIPORDER, RequestMethod.POST.name()),
    TEMU_POP_CREATE_SHIPMENT("TEMU半托管创建物流发货", ExternalApiLogApiModuleEnum.API_TRANSFER, ExternalApiLogApiTypeEnum.TEMU_CREATE_SHIPORDER, RequestMethod.POST.name()),
    TEMU_POP_UPDATE_SHIPMENT("TEMU半托管更新发货单", ExternalApiLogApiModuleEnum.API_TRANSFER, ExternalApiLogApiTypeEnum.TEMU_SEND_SHIPORDER, RequestMethod.POST.name()),
    TEMU_POP_CONFIRM_PACKAGE("TEMU半托管包裹发货确认", ExternalApiLogApiModuleEnum.API_TRANSFER, ExternalApiLogApiTypeEnum.TEMU_SEND_SHIPORDER, RequestMethod.POST.name()),
    APPLY_T_CODE("预占T code", ExternalApiLogApiModuleEnum.API_OMS, ExternalApiLogApiTypeEnum.FBA_APPLY_T_CODE, RequestMethod.POST.name()),
    UPDATE_RECEIVER_INFO("修改erp收货地址", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.TEMU_GET_SHIPORDER, RequestMethod.POST.name()),
    SHEIN_CREATE_SHIPORDER("SHEIN创建发货单", ExternalApiLogApiModuleEnum.API_THIRDPARTY, ExternalApiLogApiTypeEnum.TEMU_CREATE_SHIPORDER, RequestMethod.POST.name()),
    TIKTOKSHOP_CREATE_SHIPORDER("TIKTOK全托管创建发货单", ExternalApiLogApiModuleEnum.API_THIRDPARTY, ExternalApiLogApiTypeEnum.TIKTOKSHOP_CREATE_SHIPORDER, RequestMethod.POST.name()),
    UPDATE_STOCK_IS_LOCK("锁定或解锁SKU库存", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.TEMU_GET_SHIPORDER, RequestMethod.POST.name()),
    ERP_CANCEL_STOCK_LEND("ERP取消借用", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.ERP_CANCEL_STOCK_LEND, RequestMethod.POST.name()),
    ERP_UPDATE_OTTO_STOCK("修改otto平台sku的库存", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.UPDATE_OTTO_STOCK, RequestMethod.POST.name()),
    LX_SYNC_RETURN_ORDER("同步调整单", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.LX_RETURN_ORDER_SYNC, RequestMethod.POST.name()),
    TMS_AMAZON_BUY_SHIPPING_CANCEL("tms购买配送取消面单", ExternalApiLogApiModuleEnum.API_TMS, ExternalApiLogApiTypeEnum.TMS_AMAZON_BUY_SHIPPING_CANCEL, RequestMethod.POST.name()),
    BUSINESS_CREATE_TASK("创建工作台待办", ExternalApiLogApiModuleEnum.API_BUSINESS_BASE, ExternalApiLogApiTypeEnum.BUSINESS_TASK, RequestMethod.POST.name()),
    BUSINESS_COMPLETE_TASK("完成工作台待办", ExternalApiLogApiModuleEnum.API_BUSINESS_BASE, ExternalApiLogApiTypeEnum.BUSINESS_TASK, RequestMethod.POST.name()),
    TRANSFER_TASK_FINISH("调拨任务完成", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.TRANSFER_TASK, RequestMethod.POST.name()),
    TRANSFER_TASK_ADD("erp调拨任务新增", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.TRANSFER_TASK, RequestMethod.POST.name()),
    ERP_TRANSFER_TASK_FINISH("erp调拨任务完成", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.TRANSFER_TASK, RequestMethod.POST.name()),
    //新增库位同步商通
    SYNC_ADD_POSITION("新增库位同步商通", ExternalApiLogApiModuleEnum.API_WMS, ExternalApiLogApiTypeEnum.BD_SYNC_ERP, RequestMethod.POST.name()),

    PURCHASE_ORDER_SYNC_SCM("采购接收单完成同步SCM", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name()),
    OTHER_STOCKIN_ORDER_SYNC_SCM("多发入库接收单完成同步SCM", ExternalApiLogApiModuleEnum.API_SCM, ExternalApiLogApiTypeEnum.STOCKIN_FEEDBACK, RequestMethod.POST.name());


    // 接口模块
    ExternalApiLogApiModuleEnum apiModule;
    // 接口类型
    ExternalApiLogApiTypeEnum apiType;
    // 接口名称
    String apiName;

    String requestMethod;

    ExternalApiInfoEnum(String apiName, ExternalApiLogApiModuleEnum apiModule, ExternalApiLogApiTypeEnum apiType, String requestMethod) {
        this.apiModule = apiModule;
        this.apiType = apiType;
        this.apiName = apiName;
        this.requestMethod = requestMethod;
    }

    public String getRequestMethod() {
        return requestMethod;
    }


    public ExternalApiLogApiModuleEnum getApiModule() {
        return apiModule;
    }

    public ExternalApiLogApiTypeEnum getApiType() {
        return apiType;
    }

    public String getApiName() {
        return apiName;
    }
}
