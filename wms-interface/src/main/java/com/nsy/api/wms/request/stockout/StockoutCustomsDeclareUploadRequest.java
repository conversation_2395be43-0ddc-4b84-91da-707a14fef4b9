package com.nsy.api.wms.request.stockout;


import com.nsy.api.wms.domain.stockout.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutCustomsDeclareUploadRequest", description = "报关单据上传request")
public class StockoutCustomsDeclareUploadRequest implements Serializable {

    @ApiModelProperty(value = "主键ID", name = "declareDocumentId")
    @NotNull(message = "id不能为空")
    private Integer declareDocumentId;

    @ApiModelProperty(value = "单据文件地址", name = "fileUrl")
    @NotNull(message = "单据文件地址不能为空")
    private FileInfo fileInfo;

    public Integer getDeclareDocumentId() {
        return declareDocumentId;
    }

    public void setDeclareDocumentId(Integer declareDocumentId) {
        this.declareDocumentId = declareDocumentId;
    }


    public FileInfo getFileInfo() {
        return fileInfo;
    }

    public void setFileInfo(FileInfo fileInfo) {
        this.fileInfo = fileInfo;
    }
}
