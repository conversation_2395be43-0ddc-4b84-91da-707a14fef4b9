package com.nsy.api.wms.request.qa;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 16:17
 */
public class BdQaFullInspectRuleSaveRequest {
    private Integer id;

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("明细信息")
    private BdQaFullInspectRuleItemSaveRequest itemInfo;

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public BdQaFullInspectRuleItemSaveRequest getItemInfo() {
        return itemInfo;
    }

    public void setItemInfo(BdQaFullInspectRuleItemSaveRequest itemInfo) {
        this.itemInfo = itemInfo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}