package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockinOrderBoxListResponse", description = "入库单明细内部箱response")
public class StockinOrderBoxListResponse {

    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "状态Label", name = "statusLabel")
    private String statusLabel;

    @ApiModelProperty(value = "库区", name = "spaceAreaName")
    private String spaceAreaName;

    @ApiModelProperty(value = "sku数量", name = "qty")
    private Integer qty;

    @ApiModelProperty(value = "操作人", name = "updateBy")
    private String updateBy;

    @ApiModelProperty(value = "操作时间", name = "updateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    public String getStatusLabel() {
        return statusLabel;
    }

    public void setStatusLabel(String statusLabel) {
        this.statusLabel = statusLabel;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
