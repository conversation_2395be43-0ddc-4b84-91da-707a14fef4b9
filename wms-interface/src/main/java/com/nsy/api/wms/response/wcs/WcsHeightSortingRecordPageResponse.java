package com.nsy.api.wms.response.wcs;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 测量高度分拣记录分页查询响应DTO
 * 用于返回测量高度分拣记录的分页数据
 * 包含测量数据、对比结果和分拣状态信息
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class WcsHeightSortingRecordPageResponse {

    /**
     * 主键ID
     */
    private Integer id;


    /**
     * 商品SKU编码
     */
    private String sku;

    /**
     * 商品条形码
     */
    private String barcode;

    /**
     * 商品长度，单位：厘米(CM)
     */
    private BigDecimal length;

    /**
     * 商品宽度，单位：厘米(CM)
     */
    private BigDecimal width;

    /**
     * 商品高度，单位：厘米(CM)
     */
    private BigDecimal height;

    /**
     * 商品重量，单位：千克(KG)
     */
    private BigDecimal weight;

    /**
     * 测量类型
     */
    private String measureType;

    /**
     * 体积重，单位：千克(KG)
     */
    private BigDecimal volumeWeight;

    /**
     * FBA配送费，单位：元
     */
    private BigDecimal fbaCost;

    /**
     * 测量高度，单位：厘米(CM)
     */
    private BigDecimal measureHeight;

    /**
     * 测量体积重，单位：千克(KG)
     */
    private BigDecimal measureVolumeWeight;

    /**
     * 测量FBA配送费，单位：元
     */
    private BigDecimal measureFbaCost;

    /**
     * 分拣口状态
     */
    private String sortingPort;

    /**
     * 创建日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public BigDecimal getMeasureHeight() {
        return measureHeight;
    }

    public void setMeasureHeight(BigDecimal measureHeight) {
        this.measureHeight = measureHeight;
    }

    public BigDecimal getMeasureVolumeWeight() {
        return measureVolumeWeight;
    }

    public void setMeasureVolumeWeight(BigDecimal measureVolumeWeight) {
        this.measureVolumeWeight = measureVolumeWeight;
    }

    public BigDecimal getMeasureFbaCost() {
        return measureFbaCost;
    }

    public void setMeasureFbaCost(BigDecimal measureFbaCost) {
        this.measureFbaCost = measureFbaCost;
    }

    public String getSortingPort() {
        return sortingPort;
    }

    public void setSortingPort(String sortingPort) {
        this.sortingPort = sortingPort;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
} 