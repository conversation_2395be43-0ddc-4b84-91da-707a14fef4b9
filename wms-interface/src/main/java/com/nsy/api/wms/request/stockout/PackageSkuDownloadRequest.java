package com.nsy.api.wms.request.stockout;

import java.util.List;

/**
 * HXD
 * 2021/9/1
 **/

public class ShipmentDownloadRequest {

    private List<String> declareDocumentNo;

    private List<Integer> shipmentIds;

    private StockoutShipmentSearchRequest searchRequest = new StockoutShipmentSearchRequest();

    public StockoutShipmentSearchRequest getSearchRequest() {
        return searchRequest;
    }

    public void setSearchRequest(StockoutShipmentSearchRequest searchRequest) {
        this.searchRequest = searchRequest;
    }

    public List<Integer> getShipmentIds() {
        return shipmentIds;
    }

    public void setShipmentIds(List<Integer> shipmentIds) {
        this.shipmentIds = shipmentIds;
    }

    public List<String> getDeclareDocumentNo() {
        return declareDocumentNo;
    }

    public void setDeclareDocumentNo(List<String> declareDocumentNo) {
        this.declareDocumentNo = declareDocumentNo;
    }
}
