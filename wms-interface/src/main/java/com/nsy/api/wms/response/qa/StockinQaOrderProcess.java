
package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/12/9 10:01
 */
public class StockinQaOrderProcess {

    /**
     * 质检流程id
     */
    private Integer stockinQaOrderProcessId;
    
    /**
     * 质检流程名
     */
    @ApiModelProperty("质检流程名")
    private String processName;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 状态
     */
    @ApiModelProperty("状态中文")
    private String statusStr;

    @ApiModelProperty("质检数量")
    private Integer testTotalCount;

    /**
     * 合格件数
     */
    @ApiModelProperty("合格件数")
    private Integer qualifiedCount;
    /**
     * 质检不合格件数
     */
    @ApiModelProperty("质检不合格件数")
    private Integer unqualifiedCount;
    /**
     * 直接退货件数
     */
    @ApiModelProperty("直接退货件数")
    private Integer directReturnCount;
    /**
     * 退货数量
     */
    @ApiModelProperty("退货数量")
    private Integer returnCount;
    /**
     * 让步接收数
     */
    @ApiModelProperty("让步接收数")
    private Integer concessionsCount;

    @ApiModelProperty("全检件数")
    private Integer fullInspectCount;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private String attachmentUrl;
    /**
     * 排序
     */
    @ApiModelProperty("sort")
    private Integer sort;

    @ApiModelProperty("操作人")
    private String operator;
    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;

    @ApiModelProperty("图片信息")
    private List<String> imageList;

    @ApiModelProperty("是否完成")
    private Integer isComplete;


    /**
     * 不合格原因归类
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;

    /**
     * 质检不合格的问题描述
     */
    @ApiModelProperty("不合格的问题描述")
    private String unqualifiedQuestion;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("次要不合格的问题描述")
    private String unqualifiedQuestionSecondary;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("次要不合格原因归类")
    private String unqualifiedCategorySecondary;

    @ApiModelProperty("次要不合格原因")
    private String unqualifiedReasonSecondary;

    @ApiModelProperty("稽查件数")
    private Integer inspectTotalCount;

    public Integer getQualifiedCount() {
        return qualifiedCount;
    }

    public void setQualifiedCount(Integer qualifiedCount) {
        this.qualifiedCount = qualifiedCount;
    }

    public Integer getTestTotalCount() {
        return testTotalCount;
    }

    public void setTestTotalCount(Integer testTotalCount) {
        this.testTotalCount = testTotalCount;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(Integer isComplete) {
        this.isComplete = isComplete;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getDirectReturnCount() {
        return directReturnCount;
    }

    public void setDirectReturnCount(Integer directReturnCount) {
        this.directReturnCount = directReturnCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public List<String> getImageList() {
        return imageList;
    }

    public void setImageList(List<String> imageList) {
        this.imageList = imageList;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedQuestionSecondary() {
        return unqualifiedQuestionSecondary;
    }

    public void setUnqualifiedQuestionSecondary(String unqualifiedQuestionSecondary) {
        this.unqualifiedQuestionSecondary = unqualifiedQuestionSecondary;
    }

    public String getUnqualifiedCategorySecondary() {
        return unqualifiedCategorySecondary;
    }

    public void setUnqualifiedCategorySecondary(String unqualifiedCategorySecondary) {
        this.unqualifiedCategorySecondary = unqualifiedCategorySecondary;
    }

    public String getUnqualifiedReasonSecondary() {
        return unqualifiedReasonSecondary;
    }

    public void setUnqualifiedReasonSecondary(String unqualifiedReasonSecondary) {
        this.unqualifiedReasonSecondary = unqualifiedReasonSecondary;
    }

    public Integer getInspectTotalCount() {
        return inspectTotalCount;
    }

    public void setInspectTotalCount(Integer inspectTotalCount) {
        this.inspectTotalCount = inspectTotalCount;
    }

    public Integer getFullInspectCount() {
        return fullInspectCount;
    }

    public void setFullInspectCount(Integer fullInspectCount) {
        this.fullInspectCount = fullInspectCount;
    }

    public Integer getStockinQaOrderProcessId() {
        return stockinQaOrderProcessId;
    }

    public void setStockinQaOrderProcessId(Integer stockinQaOrderProcessId) {
        this.stockinQaOrderProcessId = stockinQaOrderProcessId;
    }
}
