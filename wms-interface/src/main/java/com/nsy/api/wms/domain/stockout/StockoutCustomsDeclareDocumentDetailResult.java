package com.nsy.api.wms.domain.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * HXD
 * 2021/8/16
 **/
@ApiModel(value = "StockoutCustomsDeclareDocumentDetailResult", description = "报关单据明细")
public class StockoutCustomsDeclareDocumentDetailResult {

    @ApiModelProperty(value = "主键ID", name = "declareDocumentId")
    private Integer declareDocumentId;

    @ApiModelProperty(value = "亚马逊仓库", name = "amazonSpaceName")
    private String amazonSpaceName;

    @ApiModelProperty(value = "报关单号", name = "declareDocumentNo")
    private String declareDocumentNo;


    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    private Integer companyId;

    @ApiModelProperty(value = "公司抬头", name = "companyName")
    private String companyName;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "状态", name = "statusStr")
    private String statusStr;

    @ApiModelProperty(value = "箱数", name = "boxNum")
    private Integer boxNum;

    @ApiModelProperty(value = "订单数", name = "orderNum")
    private Integer orderNum;

    @ApiModelProperty(value = "件数", name = "skuNum")
    private Integer skuNum;

    @ApiModelProperty(value = "重量(千克)", name = "weight")
    private BigDecimal weight;

    @ApiModelProperty(value = "总体积", name = "volume")
    private BigDecimal volume;

    @ApiModelProperty(value = "运费", name = "freight")
    private BigDecimal freight;

    @ApiModelProperty(value = "fob总价", name = "totalFobPrice")
    private BigDecimal totalFobPrice;

    @ApiModelProperty(value = "C&F总价", name = "cAndFPrice")
    private BigDecimal cAndFPrice;

    @ApiModelProperty(value = "净重", name = "netWeight")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "客户", name = "customer")
    private String customer;

    @ApiModelProperty(value = "港口", name = "port")
    private String port;

    @ApiModelProperty(value = "港口（中文）", name = "portCn")
    private String portCn;
    /**
     * 目的地
     */
    @ApiModelProperty(value = "目的", name = "destination")
    private String destination;

    @ApiModelProperty(value = "目的（中文）", name = "destinationCn")
    private String destinationCn;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签约时间", name = "signingDate")
    private Date signingDate;

    @ApiModelProperty(value = "出境关别", name = "customs")
    private String customs;

    @ApiModelProperty(value = "出境关别（中文）", name = "customsCn")
    private String customsCn;

    @ApiModelProperty(value = "装运时间", name = "shippingDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date shippingDate;

    @ApiModelProperty(value = "运输方式", name = "typeOfShipping")
    private String typeOfShipping;

    @ApiModelProperty(value = "目的国家代码", name = "countryCode")
    private String countryCode;

    @ApiModelProperty(value = "监管方式", name = "supervisionMethod")
    private String supervisionMethod;

    @ApiModelProperty(value = "出口日期", name = "exportDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date exportDate;

    @ApiModelProperty(value = "汇率", name = "exchangeRate")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "制单日期", name = "documentationDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date documentationDate;

    @ApiModelProperty(value = "品牌名", name = "brandName")
    private String brandName;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种", name = "currency")
    private String currency;

    @ApiModelProperty(value = "店铺id列表", name = "storeIds")
    private String storeIds;

    //店铺名称列表
    @ApiModelProperty(value = "店铺名称列表", name = "storeNames")
    private String storeNames;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否审核
     */
    private Boolean isAudit;


    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public Integer getDeclareDocumentId() {
        return declareDocumentId;
    }

    public void setDeclareDocumentId(Integer declareDocumentId) {
        this.declareDocumentId = declareDocumentId;
    }

    public String getAmazonSpaceName() {
        return amazonSpaceName;
    }

    public void setAmazonSpaceName(String amazonSpaceName) {
        this.amazonSpaceName = amazonSpaceName;
    }

    public String getDeclareDocumentNo() {
        return declareDocumentNo;
    }

    public void setDeclareDocumentNo(String declareDocumentNo) {
        this.declareDocumentNo = declareDocumentNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(Integer boxNum) {
        this.boxNum = boxNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getSkuNum() {
        return skuNum;
    }

    public void setSkuNum(Integer skuNum) {
        this.skuNum = skuNum;
    }


    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    public BigDecimal getTotalFobPrice() {
        return totalFobPrice;
    }

    public void setTotalFobPrice(BigDecimal totalFobPrice) {
        this.totalFobPrice = totalFobPrice;
    }

    public BigDecimal getcAndFPrice() {
        return cAndFPrice;
    }

    public void setcAndFPrice(BigDecimal cAndFPrice) {
        this.cAndFPrice = cAndFPrice;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public Date getSigningDate() {
        return signingDate;
    }

    public void setSigningDate(Date signingDate) {
        this.signingDate = signingDate;
    }

    public String getCustoms() {
        return customs;
    }

    public void setCustoms(String customs) {
        this.customs = customs;
    }

    public Date getShippingDate() {
        return shippingDate;
    }

    public void setShippingDate(Date shippingDate) {
        this.shippingDate = shippingDate;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getPortCn() {
        return portCn;
    }

    public void setPortCn(String portCn) {
        this.portCn = portCn;
    }

    public String getDestinationCn() {
        return destinationCn;
    }

    public void setDestinationCn(String destinationCn) {
        this.destinationCn = destinationCn;
    }

    public String getCustomsCn() {
        return customsCn;
    }

    public void setCustomsCn(String customsCn) {
        this.customsCn = customsCn;
    }

    public String getTypeOfShipping() {
        return typeOfShipping;
    }

    public void setTypeOfShipping(String typeOfShipping) {
        this.typeOfShipping = typeOfShipping;
    }

    public String getSupervisionMethod() {
        return supervisionMethod;
    }

    public void setSupervisionMethod(String supervisionMethod) {
        this.supervisionMethod = supervisionMethod;
    }

    public Date getExportDate() {
        return exportDate;
    }

    public void setExportDate(Date exportDate) {
        this.exportDate = exportDate;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public Date getDocumentationDate() {
        return documentationDate;
    }

    public void setDocumentationDate(Date documentationDate) {
        this.documentationDate = documentationDate;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(String storeIds) {
        this.storeIds = storeIds;
    }

    public String getStoreNames() {
        return storeNames;
    }

    public void setStoreNames(String storeNames) {
        this.storeNames = storeNames;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Boolean getIsAudit() {
        return isAudit;
    }

    public void setIsAudit(Boolean isAudit) {
        this.isAudit = isAudit;
    }
}
