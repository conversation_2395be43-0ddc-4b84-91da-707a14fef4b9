package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-18 15:16
 */
public class StockinHeightRecordMappingExportResponse {

    @ApiModelProperty("内部箱号")
    private String internalBoxCode;

    @ApiModelProperty("规格编码")
    private String sku;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("工厂出库单号")
    private String supplierDeliveryNo;

    @ApiModelProperty("采购单号")
    private String purchasePlanNo;

    @ApiModelProperty(value = "包装方式", name = "packageName")
    private String packageName;


    @ApiModelProperty(value = "长度，单位：CM", name = "lengthStandard")
    private BigDecimal lengthStandard;
    //宽度，单位：CM
    @ApiModelProperty(value = "宽度，单位：CM", name = "widthStandard")
    private BigDecimal widthStandard;
    //高度，单位：CM
    @ApiModelProperty(value = "高度，单位：CM", name = "height")
    private BigDecimal heightStandard;
    //重量，单位：G
    @ApiModelProperty(value = "重量，单位：G", name = "weight")
    private BigDecimal weightStandard;
    //长度，单位：英寸
    @ApiModelProperty(value = "长度，单位：英寸", name = "lengthInch")
    private BigDecimal lengthInchStandard;
    //宽度，单位：英寸
    @ApiModelProperty(value = "宽度，单位：英寸", name = "widthInch")
    private BigDecimal widthInchStandard;
    //高度，单位：英寸
    @ApiModelProperty(value = "高度，单位：英寸", name = "heightInch")
    private BigDecimal heightInchStandard;
    //重量，单位：磅
    @ApiModelProperty(value = "重量，单位：磅", name = "weightPound")
    private BigDecimal weightPoundStandard;
    //体积重，单位：磅
    @ApiModelProperty(value = "体积重，单位：磅", name = "volumeWeightPound")
    private BigDecimal volumeWeightPoundStandard;

    @ApiModelProperty("计费重量(KG)")
    private BigDecimal chargedWeightKgStandard;

    @ApiModelProperty("体积重(KG)")
    private BigDecimal volumeWeightKgStandard;

    //计费重量
    @ApiModelProperty(value = "计费重量 kg", name = "chargedWeight")
    private BigDecimal chargedWeightStandard;

    //FBA配送费
    @ApiModelProperty(value = "FBA配送费", name = "fbaCost")
    private BigDecimal fbaCostStandard;

    @ApiModelProperty(value = "标准类型", name = "standardTypeStandard")
    private String standardTypeStandard;

    @ApiModelProperty(value = "长度，单位：CM", name = "length")
    private BigDecimal length;
    //宽度，单位：CM
    @ApiModelProperty(value = "宽度，单位：CM", name = "width")
    private BigDecimal width;
    //高度，单位：CM
    @ApiModelProperty(value = "高度，单位：CM", name = "height")
    private BigDecimal height;
    //重量，单位：G
    @ApiModelProperty(value = "重量，单位：G", name = "weight")
    private BigDecimal weight;
    //重量，单位：磅
    @ApiModelProperty(value = "体积重，单位：kg", name = "volumeWeight")
    private BigDecimal volumeWeight;
    @ApiModelProperty(value = "测量体积重，单位：kg", name = "measureVolumeWeight")
    private BigDecimal measureVolumeWeight;
    //FBA配送费
    @ApiModelProperty(value = "FBA配送费", name = "fbaCost")
    private BigDecimal fbaCost;
    @ApiModelProperty(value = "测量FBA配送费", name = "measureFbaCost")
    private BigDecimal measureFbaCost;
    @ApiModelProperty("测量高度,单位cm")
    private BigDecimal measureHeight;

    //测量类型
    @ApiModelProperty(value = "测量类型", name = "heightMeasureType")
    private String heightMeasureType;
    //测量类型
    @ApiModelProperty(value = "测量类型-中文", name = "heightMeasureTypeStr")
    private String heightMeasureTypeStr;
    //计费重量
    @ApiModelProperty(value = "分拣口状态", name = "sortingPort")
    private String sortingPort;

    @ApiModelProperty(value = "分拣口状态中文", name = "sortingPortStr")
    private String sortingPortStr;

    @ApiModelProperty(value = "是否跳档1-是，0-否", name = "isOverStandard")
    private Integer isOverStandard;

    @ApiModelProperty("测量人")
    private String createBy;

    /**
     * 测量完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "测量完成时间", name = "createDate")
    private Date createDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    private Date stockinDate;

    /**
     * 入库数量
     */
    @ApiModelProperty("入库数量")
    private Integer arrivalCount;

    @ApiModelProperty("箱内数")
    private Integer boxQty;

    @ApiModelProperty("测量标准id")
    private Integer standardId;

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getLengthStandard() {
        return lengthStandard;
    }

    public void setLengthStandard(BigDecimal lengthStandard) {
        this.lengthStandard = lengthStandard;
    }

    public BigDecimal getWidthStandard() {
        return widthStandard;
    }

    public void setWidthStandard(BigDecimal widthStandard) {
        this.widthStandard = widthStandard;
    }

    public BigDecimal getHeightStandard() {
        return heightStandard;
    }

    public void setHeightStandard(BigDecimal heightStandard) {
        this.heightStandard = heightStandard;
    }

    public BigDecimal getWeightStandard() {
        return weightStandard;
    }

    public void setWeightStandard(BigDecimal weightStandard) {
        this.weightStandard = weightStandard;
    }

    public BigDecimal getLengthInchStandard() {
        return lengthInchStandard;
    }

    public void setLengthInchStandard(BigDecimal lengthInchStandard) {
        this.lengthInchStandard = lengthInchStandard;
    }

    public BigDecimal getWidthInchStandard() {
        return widthInchStandard;
    }

    public void setWidthInchStandard(BigDecimal widthInchStandard) {
        this.widthInchStandard = widthInchStandard;
    }

    public BigDecimal getHeightInchStandard() {
        return heightInchStandard;
    }

    public void setHeightInchStandard(BigDecimal heightInchStandard) {
        this.heightInchStandard = heightInchStandard;
    }

    public BigDecimal getWeightPoundStandard() {
        return weightPoundStandard;
    }

    public void setWeightPoundStandard(BigDecimal weightPoundStandard) {
        this.weightPoundStandard = weightPoundStandard;
    }

    public BigDecimal getVolumeWeightPoundStandard() {
        return volumeWeightPoundStandard;
    }

    public void setVolumeWeightPoundStandard(BigDecimal volumeWeightPoundStandard) {
        this.volumeWeightPoundStandard = volumeWeightPoundStandard;
    }

    public BigDecimal getChargedWeightKgStandard() {
        return chargedWeightKgStandard;
    }

    public void setChargedWeightKgStandard(BigDecimal chargedWeightKgStandard) {
        this.chargedWeightKgStandard = chargedWeightKgStandard;
    }

    public BigDecimal getVolumeWeightKgStandard() {
        return volumeWeightKgStandard;
    }

    public void setVolumeWeightKgStandard(BigDecimal volumeWeightKgStandard) {
        this.volumeWeightKgStandard = volumeWeightKgStandard;
    }

    public BigDecimal getChargedWeightStandard() {
        return chargedWeightStandard;
    }

    public void setChargedWeightStandard(BigDecimal chargedWeightStandard) {
        this.chargedWeightStandard = chargedWeightStandard;
    }

    public BigDecimal getFbaCostStandard() {
        return fbaCostStandard;
    }

    public void setFbaCostStandard(BigDecimal fbaCostStandard) {
        this.fbaCostStandard = fbaCostStandard;
    }

    public String getStandardTypeStandard() {
        return standardTypeStandard;
    }

    public void setStandardTypeStandard(String standardTypeStandard) {
        this.standardTypeStandard = standardTypeStandard;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public BigDecimal getMeasureVolumeWeight() {
        return measureVolumeWeight;
    }

    public void setMeasureVolumeWeight(BigDecimal measureVolumeWeight) {
        this.measureVolumeWeight = measureVolumeWeight;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public BigDecimal getMeasureFbaCost() {
        return measureFbaCost;
    }

    public void setMeasureFbaCost(BigDecimal measureFbaCost) {
        this.measureFbaCost = measureFbaCost;
    }

    public BigDecimal getMeasureHeight() {
        return measureHeight;
    }

    public void setMeasureHeight(BigDecimal measureHeight) {
        this.measureHeight = measureHeight;
    }

    public String getHeightMeasureType() {
        return heightMeasureType;
    }

    public void setHeightMeasureType(String heightMeasureType) {
        this.heightMeasureType = heightMeasureType;
    }

    public String getHeightMeasureTypeStr() {
        return heightMeasureTypeStr;
    }

    public void setHeightMeasureTypeStr(String heightMeasureTypeStr) {
        this.heightMeasureTypeStr = heightMeasureTypeStr;
    }

    public String getSortingPort() {
        return sortingPort;
    }

    public void setSortingPort(String sortingPort) {
        this.sortingPort = sortingPort;
    }

    public String getSortingPortStr() {
        return sortingPortStr;
    }

    public void setSortingPortStr(String sortingPortStr) {
        this.sortingPortStr = sortingPortStr;
    }

    public Integer getIsOverStandard() {
        return isOverStandard;
    }

    public void setIsOverStandard(Integer isOverStandard) {
        this.isOverStandard = isOverStandard;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }
}
