package com.nsy.api.wms.response.stockin;

import io.swagger.annotations.ApiModelProperty;
import org.codehaus.jackson.annotate.JsonProperty;

import java.util.List;

public class ScanUpShelvesBarcodeResponse {
    @ApiModelProperty(value = "库位", name = "sku")
    private String sku;

    @ApiModelProperty(value = "图片", name = "image")
    private String image;

    @ApiModelProperty(value = "是否走异常上架,1是", name = "isExceptionShelve")
    private Integer isExceptionShelve;

    @ApiModelProperty(value = "该规格可上架数", name = "qty")
    private int qty;

    @ApiModelProperty(value = "sku库位库存", name = "skuPositionViewList")
    public List<SkuPositionView> skuPositionViewList;

    @ApiModelProperty(value = "质检前置是否全部退货", name = "isPreQaReturn")
    private Integer isPreQaReturn;

    @ApiModelProperty(value = "商品标签", name = "productTag")
    private List<String> productTag;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    @ApiModelProperty(value = "上架区域名", name = "areaName")
    private String areaName;

    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    @ApiModelProperty(value = "越库库位", name = "crossPosition")
    private String crossPosition;

    @ApiModelProperty(value = "店铺库位", name = "storePosition")
    private String storePosition;

    @ApiModelProperty(value = "是否快进快出", name = "isFbaQuick")
    private Integer isFbaQuick;

    @ApiModelProperty(value = "是否撞色", name = "isClash")
    private Integer isClash;

    @ApiModelProperty(value = "二创", name = "secondCreationLabel")
    private String secondCreationLabel;

    public String getStorePosition() {
        return storePosition;
    }

    public void setStorePosition(String storePosition) {
        this.storePosition = storePosition;
    }

    public String getCrossPosition() {
        return crossPosition;
    }

    public void setCrossPosition(String crossPosition) {
        this.crossPosition = crossPosition;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public List<String> getProductTag() {
        return productTag;
    }

    public void setProductTag(List<String> productTag) {
        this.productTag = productTag;
    }

    public Integer getIsPreQaReturn() {
        return isPreQaReturn;
    }

    public void setIsPreQaReturn(Integer isPreQaReturn) {
        this.isPreQaReturn = isPreQaReturn;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Integer getIsExceptionShelve() {
        return isExceptionShelve;
    }

    public void setIsExceptionShelve(Integer isExceptionShelve) {
        this.isExceptionShelve = isExceptionShelve;
    }

    public int getQty() {
        return qty;
    }

    public void setQty(int qty) {
        this.qty = qty;
    }

    public List<SkuPositionView> getSkuPositionViewList() {
        return skuPositionViewList;
    }

    public void setSkuPositionViewList(List<SkuPositionView> skuPositionViewList) {
        this.skuPositionViewList = skuPositionViewList;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getIsFbaQuick() {
        return isFbaQuick;
    }

    public void setIsFbaQuick(Integer isFbaQuick) {
        this.isFbaQuick = isFbaQuick;
    }

    public Integer getIsClash() {
        return isClash;
    }

    public void setIsClash(Integer isClash) {
        this.isClash = isClash;
    }

    public String getSecondCreationLabel() {
        return secondCreationLabel;
    }

    public void setSecondCreationLabel(String secondCreationLabel) {
        this.secondCreationLabel = secondCreationLabel;
    }

    public static class SkuPositionView {
        @JsonProperty("Position")
        @ApiModelProperty(value = "推荐库位", name = "Position")
        private String position;

        @JsonProperty("sku")
        @ApiModelProperty(value = "sku", name = "sku")
        private String sku;

        @ApiModelProperty(value = "当前库存", name = "Stock")
        @JsonProperty("Stock")
        private int stock;

        @ApiModelProperty(value = "是否同款同色 1是", name = "IsSameSkc")
        @JsonProperty("IsSameSkc")
        private int isSameSkc;

        @ApiModelProperty(value = "同款同色", name = "remark")
        @JsonProperty("remark")
        private String remark;

        @ApiModelProperty(value = "预配数", name = "PreMatchQty")
        @JsonProperty("PreMatchQty")
        private int preMatchQty;

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public int getStock() {
            return stock;
        }

        public void setStock(int stock) {
            this.stock = stock;
        }

        public int getIsSameSkc() {
            return isSameSkc;
        }

        public void setIsSameSkc(int isSameSkc) {
            this.isSameSkc = isSameSkc;
        }

        public int getPreMatchQty() {
            return preMatchQty;
        }

        public void setPreMatchQty(int preMatchQty) {
            this.preMatchQty = preMatchQty;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
