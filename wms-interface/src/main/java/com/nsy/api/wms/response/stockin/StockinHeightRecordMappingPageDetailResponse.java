package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-18 13:48
 */
public class StockinHeightRecordMappingPageDetailResponse {

    @ApiModelProperty(value = "主键", name = "id")
    private Integer id;
    //sku
    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;

    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;
    //条形码
    @ApiModelProperty(value = "条形码", name = "barcode")
    private String barcode;
    //长度，单位：CM
    @ApiModelProperty(value = "长度，单位：CM", name = "length")
    private BigDecimal length;
    //宽度，单位：CM
    @ApiModelProperty(value = "宽度，单位：CM", name = "width")
    private BigDecimal width;
    //高度，单位：CM
    @ApiModelProperty(value = "高度，单位：CM", name = "height")
    private BigDecimal height;
    //重量，单位：G
    @ApiModelProperty(value = "重量，单位：G", name = "weight")
    private BigDecimal weight;
    //重量，单位：磅
    @ApiModelProperty(value = "体积重，单位：kg", name = "volumeWeight")
    private BigDecimal volumeWeight;
    @ApiModelProperty(value = "测量体积重，单位：kg", name = "measureVolumeWeight")
    private BigDecimal measureVolumeWeight;
    //FBA配送费
    @ApiModelProperty(value = "FBA配送费", name = "fbaCost")
    private BigDecimal fbaCost;
    @ApiModelProperty(value = "测量FBA配送费", name = "measureFbaCost")
    private BigDecimal measureFbaCost;
    @ApiModelProperty("测量高度,单位cm")
    private BigDecimal measureHeight;

    //测量类型
    @ApiModelProperty(value = "测量类型", name = "heightMeasureType")
    private String heightMeasureType;
    //测量类型
    @ApiModelProperty(value = "测量类型-中文", name = "heightMeasureTypeStr")
    private String heightMeasureTypeStr;
    //计费重量
    @ApiModelProperty(value = "分拣口状态", name = "sortingPort")
    private String sortingPort;

    @ApiModelProperty(value = "分拣口状态中文", name = "sortingPortStr")
    private String sortingPortStr;

    @ApiModelProperty(value = "是否不达标 1-是，0-否", name = "isOverStandard")
    private Integer isOverStandard;

    @ApiModelProperty(value = "创建日期", name = "createDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockinDate;

    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "包装方式", name = "packageName")
    private String packageName;

    @ApiModelProperty(value = "采购单号", name = "purchaseOrderNo")
    private String purchaseOrderNo;


    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNos")
    private String supplierDeliveryNo;

    @ApiModelProperty(value = "供应商", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "工艺测量标准id", name = "standardId")
    private Integer standardId;

    @ApiModelProperty("工艺包装测量信息")
    private StockinVolumeWeightRecordMappingResponse measureStandardInfo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public BigDecimal getMeasureVolumeWeight() {
        return measureVolumeWeight;
    }

    public void setMeasureVolumeWeight(BigDecimal measureVolumeWeight) {
        this.measureVolumeWeight = measureVolumeWeight;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public BigDecimal getMeasureFbaCost() {
        return measureFbaCost;
    }

    public void setMeasureFbaCost(BigDecimal measureFbaCost) {
        this.measureFbaCost = measureFbaCost;
    }

    public BigDecimal getMeasureHeight() {
        return measureHeight;
    }

    public void setMeasureHeight(BigDecimal measureHeight) {
        this.measureHeight = measureHeight;
    }

    public String getHeightMeasureType() {
        return heightMeasureType;
    }

    public void setHeightMeasureType(String heightMeasureType) {
        this.heightMeasureType = heightMeasureType;
    }

    public String getHeightMeasureTypeStr() {
        return heightMeasureTypeStr;
    }

    public void setHeightMeasureTypeStr(String heightMeasureTypeStr) {
        this.heightMeasureTypeStr = heightMeasureTypeStr;
    }

    public String getSortingPort() {
        return sortingPort;
    }

    public void setSortingPort(String sortingPort) {
        this.sortingPort = sortingPort;
    }

    public String getSortingPortStr() {
        return sortingPortStr;
    }

    public void setSortingPortStr(String sortingPortStr) {
        this.sortingPortStr = sortingPortStr;
    }

    public Integer getIsOverStandard() {
        return isOverStandard;
    }

    public void setIsOverStandard(Integer isOverStandard) {
        this.isOverStandard = isOverStandard;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    public StockinVolumeWeightRecordMappingResponse getMeasureStandardInfo() {
        return measureStandardInfo;
    }

    public void setMeasureStandardInfo(StockinVolumeWeightRecordMappingResponse measureStandardInfo) {
        this.measureStandardInfo = measureStandardInfo;
    }
}
