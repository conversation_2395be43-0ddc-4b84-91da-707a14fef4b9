package com.nsy.api.wms.enumeration;

public enum PrintTemplateNameEnum {
    STOCKIN_TASK_DETAIL("入库任务明细"),
    STOCKOUT_SMALL_BAG("包裹出库单"),
    SUPPLIER_DELIVERY_NO("工厂出库箱码"),
    PRODUCT_SPEC_BARCODE("商品条码"),
    PRODUCT_CUSTOMER_SPEC_SINGLE_BARCODE("客户条形码-单排"),
    PRODUCT_CUSTOMER_SPEC_DOUBLE_BARCODE("客户条形码-双排"),
    PRODUCT_CUSTOMER_SPEC_BARCODE("客户条形码"),
    TO_NEW_SPACE_BOX("搬仓箱码"),
    STOCKOUT_PICKING("拣货箱码"),
    STOCKOUT_ORDER_A4("打印出库单"),
    STOCKOUT_PICKING_TASK("热敏打印拣货任务"),
    STOCKOUT_PICKING_TASK_A4("打印拣货任务"),
    STOCK_TRANSFER_SPLIT_SKU_LIST_A4("调拨分拣Sku列表"),
    SHIPMENT_60_40_BARCODE("装箱60*40条形码"),
    SHIPMENT_FBA_BARCODE("客户条形码-双排"),
    SHIPMENT_FBA_50_40_BARCODE("FBA条形码50*40"),
    STOCKOUT_SMALL_PACKAGE("小包发货编号"),
    STOCKIN_INTERNAL_BOX("内部箱打印条码"),
    SPOT_LOGISTICS_NO("现货物流单号"),
    STOCKOUT_SHIPMENT_BARCODE("装箱条码打印"),
    STOCKOUT_SHIPMENT_B2B_BARCODE("打印B2B条码"),
    STOCKOUT_DELIVERY_SIGNATURE_FORM("打印发货签收单"),
    STOCKOUT_DELIVERY_SIGNATURE_FORM_SMALL("打印发货签收单(小)"),
    BDPOSITION_CODE("库位编码"),
    RETURN_BARCODE("退货条码"),
    LA_BARCODE("LA条码"),
    STOCKOUT_BATCH_SPLIT_TASK("分拣配货单"),
    STOCKOUT_BATCH_SPLIT_TASK_DOMESTIC("内贸配货单"),
    STOCKOUT_BATCH_SPLIT_TASK_MERGE("窄带分拣打印"),
    STOCK_TRANSFER_SPLIT("调拨分拣"),
    STOCKOUT_BATCH_SPLIT_TASK_THERMAL("分拣热敏配货单"),
    SHIPMENT_LIST("装箱清单"),
    SHIPMENT_ORDER("装箱订单"),
    YUZHEN_BARCODE("钰真条码"),
    LACK_PICKING("缺货拣货单"),
    SCAN_TASK_ORDER("复核配货单"),
    STOCKOUT_PROCESS_BATCH("加工波次单"),
    STOCK_MATERIAL_BOX_CODE("物料管理箱号打印"),
    SUPPLIER_DELIVERY_A4("工厂出库单A4"),
    REPLACE_SKU("出库单换码"),
    SEW_TASK_LIST("缝制任务"),
    SEW_TASK_BARCODE("缝制条码"),
    AMAZON_ORDER_NO("亚马逊外箱订单号"),
    PACK_SELLER_BARCODE("PACK业务条码"),
    SHIPMENT_BOX_CODE("装箱清单箱号"),
    SHIPMENT_KJP_BOX("装箱清单空加派箱贴"),
    PASTE_SKU_NOTICE("复核sku贴码提醒"),
    OVERSEA_BOX_CODE("海外箱号"),
    ORDER_NO_BY_STOCKOUT_ORDER("订单号"),
    FBA_SHIPMENT_LABEL_BASE_64("FBA箱贴Base64"),
    TEMU_TEMP_BOX_TAG("Temu临时箱贴"),
    TEMU_BARCODE("Temu条码"),
    FAIRE_BOX_CODE("faire箱码"),
    FAIRE_MERGE_BOX_CODE("faire合并箱码"),
    SOUTCKOUT_DECLARE_ORDER_DISTRIBUTE("报关订单分货标"),
    AMAZON_SHIPMENT_DISTRIBUTE("亚马逊分货标"),
    CUSTOMER_SPLIT_BARCODE("客户条码分页标"),
    AMAZON_ORDER_INFO("亚马逊补货信息"),
    SUPPLIER_RETURN_ADDRESS("退货地址信息"),
    SUPPLIER_DELIVERY_BOX_CODE("工厂出库箱码"),
    SUPPLIER_DELIVERY_PRODUCT_CODE("店铺商品条形码"),
    FBT_BARCODE("FBT条码"),
    TEMU_EXPRESS_DELIVERY_SN("TEMU快递单号"),
    STOCKOUT_PICKING_TASK_A4_BATCH("按单拣货任务"),
    STOCKOUT_PICKING_TASK_A4_FDBG("以货找单拣货任务"),
    TIKTOK_PRODUCT_CUSTOMER_SPEC_BARCODE("TIKTOK条码"),
    STOCKIN_SUPPLIER_DELIVERY_QC_ORDER("打印到货质检单"),
    DIAN_CANG_BARCODE("智运商品条码"),
    SHIPMENT_80_40_BARCODE("装箱80*40条形码"),
    OVERSEA_SPACE_BARCODE("海外仓商品条码"),
    STOCKOUT_BATCH_SPLIT_TASK_INTERNAL_PURCHASE("内购配货单"),
    STOCKOUT_BATCH_SPLIT_TASK_INTERNAL_BORROW("暂借配货单"),
    STOCK_TRANSFER_TASK("调拨任务"),
    TIKTOK_DELIVERY_LABEL("TK送货单"),
    TIKTOK_LOGISTICS_LABEL("TK物流面单"),
    SHEIN_EU_LABEL_SMALL("SHEIN欧代标签10*4"),
    SHEIN_EU_LABEL_NORMAL("SHEIN欧代标签10*10"),
    TEMU_MERGE_LABEL("TEMU欧代标签"),
    TIKTOK_EU_LABEL("TK欧代10*10");

    String templateName;

    PrintTemplateNameEnum(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateName() {
        return templateName;
    }

}
