package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-09 10:23
 */
public class StockinQaFullInspectTaskPageResponse {

    /**
     * stockinQaOrderId
     */
    @ApiModelProperty("质检单Id")
    private Integer stockinQaOrderId;

    @ApiModelProperty("质检单流程Id")
    private Integer stockinQaOrderProcessId;
    /**
     * 内部箱号
     */
    @ApiModelProperty("内部箱号")
    private String internalBoxCode;

    @ApiModelProperty("仓库名称")
    private String spaceName;

    @ApiModelProperty("商品id")
    private Integer productId;


    @ApiModelProperty("商品标签")
    private List<String> skuTypeList;

    @ApiModelProperty("商品标签")
    private String skuTypeListStr;
    /**
     * skc
     */
    @ApiModelProperty("颜色编码")
    private String skc;
    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String sku;

    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String department;
    /**
     * 申请部门
     */
    @ApiModelProperty("申请部门")
    private String applyDepartment;

    @ApiModelProperty("箱内数量")
    private Integer boxQty;

    @ApiModelProperty("到货数量")
    private Integer arrivalCount;

    /**
     * 质检数量
     */
    @ApiModelProperty("质检数量")
    private Integer testTotalCount;

    /**
     * 质检数量
     */
    @ApiModelProperty("质检数量")
    private Integer qaCount;
    /**
     * 质检不合格件数
     */
    @ApiModelProperty("质检不合格件数")
    private Integer unqualifiedCount;
    /**
     * 合格件数
     */
    @ApiModelProperty("合格件数")
    private Integer qualifiedCount;

    @ApiModelProperty("直接退货数")
    private Integer directReturnCount;

    /**
     * 退货数量
     */
    @ApiModelProperty("退货数量")
    private Integer returnCount;
    /**
     * 让步接收数
     */
    @ApiModelProperty("让步接收数")
    private Integer concessionsCount;

    @ApiModelProperty("全检件数")
    private Integer fullInspectCount;

    @ApiModelProperty("质检流程状态")
    private String processStatusStr;

    /**
     * 不合格原因归类
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("问题描述")
    private String unqualifiedQuestion;
    /**
     * 问题原因
     */
    @ApiModelProperty("问题原因")
    private String unqualifiedReason;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;

    private String inspectStatus;

    @ApiModelProperty("全检状态")
    private String inspectStatusStr;

    @ApiModelProperty("全检人员")
    private String inspectUserName;

    @ApiModelProperty("质检人员")
    private String qcUserName;

    @ApiModelProperty(value = "全检完成时间", name = "inspectCompleteDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inspectCompleteDate;

    /**
     * 稽查时间
     */
    @ApiModelProperty(value = "全检任务创建时间", name = "qaInspectDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date qaInspectDate;


    @ApiModelProperty(value = "质检完成时间", name = "completeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeDate;

    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNo;

    /**
     * 商品图片地址
     */
    @ApiModelProperty(value = "商品图片地址", name = "imageUrl")
    private String imageUrl;
    /**
     * 缩略图
     */
    @ApiModelProperty(value = "缩略图", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    /**
     * 预览图地址
     */
    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    private String result;

    @ApiModelProperty("稽查结果")
    private String resultStr;

    @ApiModelProperty("品类")
    private String categoryName;


    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Date getQaInspectDate() {
        return qaInspectDate;
    }

    public void setQaInspectDate(Date qaInspectDate) {
        this.qaInspectDate = qaInspectDate;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getApplyDepartment() {
        return applyDepartment;
    }

    public void setApplyDepartment(String applyDepartment) {
        this.applyDepartment = applyDepartment;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getTestTotalCount() {
        return testTotalCount;
    }

    public void setTestTotalCount(Integer testTotalCount) {
        this.testTotalCount = testTotalCount;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getQualifiedCount() {
        return qualifiedCount;
    }

    public void setQualifiedCount(Integer qualifiedCount) {
        this.qualifiedCount = qualifiedCount;
    }

    public Integer getDirectReturnCount() {
        return directReturnCount;
    }

    public void setDirectReturnCount(Integer directReturnCount) {
        this.directReturnCount = directReturnCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getInspectStatus() {
        return inspectStatus;
    }

    public void setInspectStatus(String inspectStatus) {
        this.inspectStatus = inspectStatus;
    }

    public String getInspectStatusStr() {
        return inspectStatusStr;
    }

    public void setInspectStatusStr(String inspectStatusStr) {
        this.inspectStatusStr = inspectStatusStr;
    }

    public String getInspectUserName() {
        return inspectUserName;
    }

    public void setInspectUserName(String inspectUserName) {
        this.inspectUserName = inspectUserName;
    }

    public Date getInspectCompleteDate() {
        return inspectCompleteDate;
    }

    public void setInspectCompleteDate(Date inspectCompleteDate) {
        this.inspectCompleteDate = inspectCompleteDate;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public List<String> getSkuTypeList() {
        return skuTypeList;
    }

    public void setSkuTypeList(List<String> skuTypeList) {
        this.skuTypeList = skuTypeList;
    }

    public Integer getFullInspectCount() {
        return fullInspectCount;
    }

    public void setFullInspectCount(Integer fullInspectCount) {
        this.fullInspectCount = fullInspectCount;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getResultStr() {
        return resultStr;
    }

    public void setResultStr(String resultStr) {
        this.resultStr = resultStr;
    }

    public String getProcessStatusStr() {
        return processStatusStr;
    }

    public void setProcessStatusStr(String processStatusStr) {
        this.processStatusStr = processStatusStr;
    }

    public String getSkuTypeListStr() {
        return skuTypeListStr;
    }

    public void setSkuTypeListStr(String skuTypeListStr) {
        this.skuTypeListStr = skuTypeListStr;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public Integer getStockinQaOrderProcessId() {
        return stockinQaOrderProcessId;
    }

    public void setStockinQaOrderProcessId(Integer stockinQaOrderProcessId) {
        this.stockinQaOrderProcessId = stockinQaOrderProcessId;
    }

    public Integer getQaCount() {
        return qaCount;
    }

    public void setQaCount(Integer qaCount) {
        this.qaCount = qaCount;
    }
}

