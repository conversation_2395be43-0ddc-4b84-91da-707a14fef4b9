
package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/11/18 11:42
 */
@ApiModel(value = "StockinQaTaskPageResponse", description = "入库质检任务Response")
public class StockinQaTaskPageResponse {


    @ApiModelProperty("质检单Id")
    private Integer stockinQaOrderId;
    /**
     * TaskId
     */
    @ApiModelProperty("任务Id")
    private Integer taskId;

    @ApiModelProperty("商品id")
    private Integer productId;
    /**
     * 内部箱号
     */
    @ApiModelProperty("内部箱号")
    private String internalBoxCode;

    @ApiModelProperty(value = "内部箱类型", name = "internalBoxType")
    private String internalBoxType;

    @ApiModelProperty(value = "商品分类Id", name = "categoryId")
    private Integer categoryId;


    @ApiModelProperty(value = "商品分类名称", name = "categoryName")
    private String categoryName;
    /**
     * spu
     */
    @ApiModelProperty("商品编码")
    private String spu;
    /**
     * skc
     */
    @ApiModelProperty("颜色编码")
    private String skc;
    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String sku;
    /**
     * 入库数量
     */
    @ApiModelProperty("到货数量")
    private Integer arrivalCount;

    @ApiModelProperty("箱内数")
    private Integer boxQty;

    /**
     * 推荐质检数量
     */
    @ApiModelProperty("要求质检数量")
    private Integer qaQty;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 是否需要做产前样
     */
    @ApiModelProperty("是否需要做产前样 1是0否")
    private Integer needProductSample;
    /**
     * 是否新款
     */
    @ApiModelProperty("是否新款 1是0否")
    private Integer isNew;

    @ApiModelProperty("是否返工退货 1是0否")
    private Integer isReturnOrder;

    private String checkStatus;

    /**
     * 质检状态：待质检、质检中、已质检、漏检上架
     */
    @ApiModelProperty("质检状态：待质检、质检中、已质检、漏检上架")
    private String checkStatusStr;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stockinDate;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式", name = "packageName")
    private String packageName;

    @ApiModelProperty(value = "质检完成时间", name = "completeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeDate;

    /**
     * 工厂出库单号
     */
    @ApiModelProperty(value = "采购单号", name = "purchasePlanNos")
    private String purchasePlanNos;

    /**
     * 出库箱码
     */
    @ApiModelProperty(value = "出库箱码", name = "supplierDeliveryBoxCodes")
    private String supplierDeliveryBoxCodes;

    @ApiModelProperty(value = "首单标签", name = "firstLabel")
    private String firstLabel;

    @ApiModelProperty(value = "产前样id", name = "recordId")
    private Integer recordId;

    @ApiModelProperty(value = "产前样是否完成-默认否", name = "productSampleComplete")
    private Integer productSampleComplete = 0;

    /**
     * 质检员名字
     */
    @ApiModelProperty(value = "质检员名字", name = "qcUserName")
    private String qcUserName;


    @ApiModelProperty(value = "质检员", name = "operator")
    private String operator;

    @ApiModelProperty(value = "仓库名称", name = "spaceName")
    private String spaceName;

    private String processStatus;

    @ApiModelProperty("质检流程状态")
    private String processStatusStr;

    @ApiModelProperty(value = "初审完成时间", name = "completeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstAuditEndDate;

    @ApiModelProperty(value = "部门", name = "department")
    private String department;

    @ApiModelProperty("商品标签")
    private List<String> skuTypeList;

    @ApiModelProperty(value = "入库人员", name = "stockinUserName")
    private String stockinUserName;
    
    public String getProcessStatusStr() {
        return processStatusStr;
    }

    public void setProcessStatusStr(String processStatusStr) {
        this.processStatusStr = processStatusStr;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public Integer getIsReturnOrder() {
        return isReturnOrder;
    }

    public void setIsReturnOrder(Integer isReturnOrder) {
        this.isReturnOrder = isReturnOrder;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getInternalBoxType() {
        return internalBoxType;
    }

    public void setInternalBoxType(String internalBoxType) {
        this.internalBoxType = internalBoxType;
    }

    public String getFirstLabel() {
        return firstLabel;
    }

    public void setFirstLabel(String firstLabel) {
        this.firstLabel = firstLabel;
    }

    public String getPurchasePlanNos() {
        return purchasePlanNos;
    }

    public void setPurchasePlanNos(String purchasePlanNos) {
        this.purchasePlanNos = purchasePlanNos;
    }

    public String getSupplierDeliveryBoxCodes() {
        return supplierDeliveryBoxCodes;
    }

    public void setSupplierDeliveryBoxCodes(String supplierDeliveryBoxCodes) {
        this.supplierDeliveryBoxCodes = supplierDeliveryBoxCodes;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getQaQty() {
        return qaQty;
    }

    public void setQaQty(Integer qaQty) {
        this.qaQty = qaQty;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getNeedProductSample() {
        return needProductSample;
    }

    public void setNeedProductSample(Integer needProductSample) {
        this.needProductSample = needProductSample;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public String getCheckStatusStr() {
        return checkStatusStr;
    }

    public void setCheckStatusStr(String checkStatusStr) {
        this.checkStatusStr = checkStatusStr;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    public Integer getProductSampleComplete() {
        return productSampleComplete;
    }

    public void setProductSampleComplete(Integer productSampleComplete) {
        this.productSampleComplete = productSampleComplete;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Date getFirstAuditEndDate() {
        return firstAuditEndDate;
    }

    public void setFirstAuditEndDate(Date firstAuditEndDate) {
        this.firstAuditEndDate = firstAuditEndDate;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public List<String> getSkuTypeList() {
        return skuTypeList;
    }

    public void setSkuTypeList(List<String> skuTypeList) {
        this.skuTypeList = skuTypeList;
    }

    public String getStockinUserName() {
        return stockinUserName;
    }

    public void setStockinUserName(String stockinUserName) {
        this.stockinUserName = stockinUserName;
    }
}
