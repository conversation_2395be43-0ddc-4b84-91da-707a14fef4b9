package com.nsy.api.wms.request.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/12/5 15:36
 */
@ApiModel(value = "StockinQaOrderPageRequest", description = "入库质检单分页查询参数")
public class StockinQaOrderPageRequest extends PageRequest {


    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "仓库id", name = "spaceId")
    private Integer spaceId;

    @ApiModelProperty(value = "仓库id", name = "spaceIdList")
    private List<Integer> spaceIdList;


    @ApiModelProperty(value = "spu列表", name = "spuList")
    private List<String> spuList;

    @ApiModelProperty(value = "sku列表", name = "skuList")
    private List<String> skuList;

    private List<String> qcUserList;

    @ApiModelProperty(value = "供应商Id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "质检结果", name = "resultList")
    private List<String> resultList;

    @ApiModelProperty(value = "质检结果", name = "processStatusList")
    private List<String> processStatusList;

    @ApiModelProperty(value = "商品标签", name = "skuTypeInfo")
    private List<String> skuTypeInfo;

    @ApiModelProperty(value = "仓库", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "包装方式", name = "packageName")
    private String packageName;

    @ApiModelProperty(value = "质检员", name = "operator")
    private String operator;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("不合格原因描述")
    private String unqualifiedReason;

    @ApiModelProperty("申请部门")
    private String applyDepartment;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("分组Id集合")
    private List<Integer> groupIdList;

    @ApiModelProperty("分组Id")
    private Integer groupId;

    @ApiModelProperty("分组人员id集合")
    private List<Integer> groupUserIdList;

    @ApiModelProperty(value = "是否退货返工 1是  0否", name = "isReturnApply")
    private Integer isReturnApply;

    @ApiModelProperty(value = "质检开始时间", name = "startDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "质检结束日期", name = "endDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "更新时间", name = "updateStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateStartDate;

    @ApiModelProperty(value = "更新时间", name = "updateEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateEndDate;

    @ApiModelProperty(value = "入库开始时间", name = "updateStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockinStartDate;

    @ApiModelProperty(value = "入库结束时间", name = "updateEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockinEndDate;

    @ApiModelProperty(value = "初检完成开始时间", name = "sopCompleteStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sopCompleteStartDate;

    @ApiModelProperty(value = "初检完成结束日期", name = "sopCompleteEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sopCompleteEndDate;


    @ApiModelProperty(value = "质检完成开始时间", name = "completeStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeStartDate;

    @ApiModelProperty(value = "质检完成结束日期", name = "completeEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeEndDate;

    @ApiModelProperty(value = "商品分类ID", name = "categoryIdList")
    private List<Integer> categoryIdList;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<String> getQcUserList() {
        return qcUserList;
    }

    public void setQcUserList(List<String> qcUserList) {
        this.qcUserList = qcUserList;
    }

    public List<String> getSpuList() {
        return spuList;
    }

    public void setSpuList(List<String> spuList) {
        this.spuList = spuList;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public List<String> getResultList() {
        return resultList;
    }

    public void setResultList(List<String> resultList) {
        this.resultList = resultList;
    }

    public List<String> getProcessStatusList() {
        return processStatusList;
    }

    public void setProcessStatusList(List<String> processStatusList) {
        this.processStatusList = processStatusList;
    }

    public List<String> getSkuTypeInfo() {
        return skuTypeInfo;
    }

    public void setSkuTypeInfo(List<String> skuTypeInfo) {
        this.skuTypeInfo = skuTypeInfo;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getApplyDepartment() {
        return applyDepartment;
    }

    public void setApplyDepartment(String applyDepartment) {
        this.applyDepartment = applyDepartment;
    }

    public Integer getIsReturnApply() {
        return isReturnApply;
    }

    public void setIsReturnApply(Integer isReturnApply) {
        this.isReturnApply = isReturnApply;
    }

    public Date getUpdateStartDate() {
        return updateStartDate;
    }

    public void setUpdateStartDate(Date updateStartDate) {
        this.updateStartDate = updateStartDate;
    }

    public Date getUpdateEndDate() {
        return updateEndDate;
    }

    public void setUpdateEndDate(Date updateEndDate) {
        this.updateEndDate = updateEndDate;
    }

    public Date getCompleteStartDate() {
        return completeStartDate;
    }

    public void setCompleteStartDate(Date completeStartDate) {
        this.completeStartDate = completeStartDate;
    }

    public Date getCompleteEndDate() {
        return completeEndDate;
    }

    public void setCompleteEndDate(Date completeEndDate) {
        this.completeEndDate = completeEndDate;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public List<Integer> getGroupIdList() {
        return groupIdList;
    }

    public void setGroupIdList(List<Integer> groupIdList) {
        this.groupIdList = groupIdList;
    }

    public List<Integer> getGroupUserIdList() {
        return groupUserIdList;
    }

    public void setGroupUserIdList(List<Integer> groupUserIdList) {
        this.groupUserIdList = groupUserIdList;
    }

    public Date getStockinStartDate() {
        return stockinStartDate;
    }

    public void setStockinStartDate(Date stockinStartDate) {
        this.stockinStartDate = stockinStartDate;
    }

    public Date getStockinEndDate() {
        return stockinEndDate;
    }

    public void setStockinEndDate(Date stockinEndDate) {
        this.stockinEndDate = stockinEndDate;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }

    public List<Integer> getCategoryIdList() {
        return categoryIdList;
    }

    public void setCategoryIdList(List<Integer> categoryIdList) {
        this.categoryIdList = categoryIdList;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Date getSopCompleteStartDate() {
        return sopCompleteStartDate;
    }

    public void setSopCompleteStartDate(Date sopCompleteStartDate) {
        this.sopCompleteStartDate = sopCompleteStartDate;
    }

    public Date getSopCompleteEndDate() {
        return sopCompleteEndDate;
    }

    public void setSopCompleteEndDate(Date sopCompleteEndDate) {
        this.sopCompleteEndDate = sopCompleteEndDate;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }
}
