package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "ForwarderAccountExportResponse", description = "货代对账")
public class ForwarderAccountExportResponse {
    @ApiModelProperty(value = "发货日期", name = "deliveryDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDate;

    @ApiModelProperty(value = "fbaShipmentId", name = "fbaShipmentId")
    private String fbaShipmentId;

    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "货代渠道", name = "forwarderChannel")
    private String forwarderChannel;

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "总箱数", name = "totalBoxAmount")
    private Integer totalBoxAmount;

    @ApiModelProperty(value = "发货件数", name = "totalAmount")
    private Integer totalAmount;

    @ApiModelProperty(value = "发货重量", name = "totalWeight")
    private BigDecimal totalWeight;

    @ApiModelProperty(value = "店铺", name = "storeName")
    private String storeName;

    @ApiModelProperty(value = "邮编", name = "receiverZip")
    private String receiverZip;

    @ApiModelProperty(value = "出库单", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    private String shipmentIds;

    public String getShipmentIds() {
        return shipmentIds;
    }

    public void setShipmentIds(String shipmentIds) {
        this.shipmentIds = shipmentIds;
    }

    public String getFbaShipmentId() {
        return fbaShipmentId;
    }

    public void setFbaShipmentId(String fbaShipmentId) {
        this.fbaShipmentId = fbaShipmentId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getForwarderChannel() {
        return forwarderChannel;
    }

    public void setForwarderChannel(String forwarderChannel) {
        this.forwarderChannel = forwarderChannel;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getTotalBoxAmount() {
        return totalBoxAmount;
    }

    public void setTotalBoxAmount(Integer totalBoxAmount) {
        this.totalBoxAmount = totalBoxAmount;
    }

    public Integer getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getReceiverZip() {
        return receiverZip;
    }

    public void setReceiverZip(String receiverZip) {
        this.receiverZip = receiverZip;
    }
}
