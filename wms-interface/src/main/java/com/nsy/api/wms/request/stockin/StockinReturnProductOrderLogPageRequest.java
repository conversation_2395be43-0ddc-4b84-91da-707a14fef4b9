package com.nsy.api.wms.request.stockin;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

@ApiModel("日志列表请求实体")
public class StockinReturnProductOrderLogPageRequest extends PageRequest {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @NotNull(message = "ID不能为空")
    private Integer returnOrderId;

    /**
     * 日志类型
     */
    @ApiModelProperty("日志类型")
    private String type;
    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String content;
    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String operator;

    public Integer getReturnOrderId() {
        return returnOrderId;
    }

    public void setReturnOrderId(Integer returnOrderId) {
        this.returnOrderId = returnOrderId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}

