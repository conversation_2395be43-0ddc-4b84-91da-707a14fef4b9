package com.nsy.api.wms.request.qa;

import com.nsy.api.wms.domain.stockin.QcInboundsPriceItem;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: caishao<PERSON>
 * @time: 2024/12/2 14:19
 */
public class StockinQaOperateSecondAuditRequest {

    @ApiModelProperty("质检单ID")
    @NotNull
    private Integer stockinQaOrderId;

    @ApiModelProperty("是否全检， 发起全检1")
    @NotNull
    private Integer isFullInspect;
    /**
     * 退货数量
     */
    @ApiModelProperty("退货数量")
    @NotNull
    private Integer returnCount;

    @ApiModelProperty("质检不合格件数")
    @NotNull
    private Integer unqualifiedCount;
    /**
     * 让步接收数
     */
    @ApiModelProperty("让步接收数")
    @NotNull
    private Integer concessionsCount;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件地址")
    private String attachmentUrl;


    /**
     * 责任方
     */
    @ApiModelProperty("责任方")
    private String responsibility;

    /**
     * 责任备注
     */
    @ApiModelProperty("责任备注")
    private String responsibilityRemark;
    /**
     * 处理方案
     */
    @ApiModelProperty("处理方案")
    private String processingProgram;

    //责任部门
    @ApiModelProperty("责任部门")
    private String departResponsibility;

    @ApiModelProperty("让步接收价")
    private List<QcInboundsPriceItem> priceList;

    /**
     * 当前步骤检测图片
     */
    @ApiModelProperty("当前步骤检测图片")
    public List<String> imageList;

    public List<String> getImageList() {
        return imageList;
    }

    public void setImageList(List<String> imageList) {
        this.imageList = imageList;
    }


    public String getResponsibilityRemark() {
        return responsibilityRemark;
    }

    public void setResponsibilityRemark(String responsibilityRemark) {
        this.responsibilityRemark = responsibilityRemark;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getResponsibility() {
        return responsibility;
    }

    public void setResponsibility(String responsibility) {
        this.responsibility = responsibility;
    }

    public String getProcessingProgram() {
        return processingProgram;
    }

    public void setProcessingProgram(String processingProgram) {
        this.processingProgram = processingProgram;
    }

    public String getDepartResponsibility() {
        return departResponsibility;
    }

    public void setDepartResponsibility(String departResponsibility) {
        this.departResponsibility = departResponsibility;
    }

    public List<QcInboundsPriceItem> getPriceList() {
        return priceList;
    }

    public void setPriceList(List<QcInboundsPriceItem> priceList) {
        this.priceList = priceList;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getIsFullInspect() {
        return isFullInspect;
    }

    public void setIsFullInspect(Integer isFullInspect) {
        this.isFullInspect = isFullInspect;
    }
}

