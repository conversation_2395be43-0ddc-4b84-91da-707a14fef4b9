package com.nsy.api.wms.request.qa;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-06 10:31
 */
public class StockinQaOperateUnqualifiedRequest {

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("不合格的问题描述")
    private String unqualifiedQuestion;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("次要不合格的问题描述")
    private String unqualifiedQuestionSecondary;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("次要不合格原因归类")
    private String unqualifiedCategorySecondary;

    @ApiModelProperty("次要不合格原因")
    private String unqualifiedReasonSecondary;

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getUnqualifiedQuestionSecondary() {
        return unqualifiedQuestionSecondary;
    }

    public void setUnqualifiedQuestionSecondary(String unqualifiedQuestionSecondary) {
        this.unqualifiedQuestionSecondary = unqualifiedQuestionSecondary;
    }

    public String getUnqualifiedCategorySecondary() {
        return unqualifiedCategorySecondary;
    }

    public void setUnqualifiedCategorySecondary(String unqualifiedCategorySecondary) {
        this.unqualifiedCategorySecondary = unqualifiedCategorySecondary;
    }

    public String getUnqualifiedReasonSecondary() {
        return unqualifiedReasonSecondary;
    }

    public void setUnqualifiedReasonSecondary(String unqualifiedReasonSecondary) {
        this.unqualifiedReasonSecondary = unqualifiedReasonSecondary;
    }
}
