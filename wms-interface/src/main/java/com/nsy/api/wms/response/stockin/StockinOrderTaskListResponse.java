package com.nsy.api.wms.response.stockin;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "StockinOrderTaskListResponse", description = "入库任务列表")
public class StockinOrderTaskListResponse {

    @ApiModelProperty(value = "任务id", name = "taskId")
    private Integer taskId;

    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNo;

    @ApiModelProperty(value = "月台", name = "platformName")
    private String platformName;

    @ApiModelProperty(value = "入库类型", name = "stockinType")
    private String stockinType;

    @ApiModelProperty(value = "工厂", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "预到货时间", name = "planArriveDate")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date planArriveDate;

    @ApiModelProperty(value = "出库箱号", name = "boxIndex")
    private Integer boxIndex;

    @ApiModelProperty(value = "出库箱码", name = "supplierDeliveryBoxCode")
    private String supplierDeliveryBoxCode;

    @ApiModelProperty(value = "出库箱条形码", name = "supplierDeliveryBarcode")
    private String supplierDeliveryBarcode;

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "预入库总件数", name = "expectedQty")
    private Integer expectedQty;

    @ApiModelProperty(value = "实际收货数", name = "stockinQty")
    private Integer stockinQty;

    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    @ApiModelProperty(value = "收货员", name = "operator")
    private String operator;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "收货开始时间", name = "operateStartDate")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date operateStartDate;

    @ApiModelProperty(value = "收货完成时间", name = "operateEndDate")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date operateEndDate;

    @ApiModelProperty(value = "入库单号", name = "stockInOrderNo")
    private String stockinOrderNo;

    @ApiModelProperty(value = "是否快进快出", name = "isFbaQuick")
    private Integer isFbaQuick;

    @ApiModelProperty(value = "收货地", name = "receiptPlace")
    private String receiptPlace;

    @ApiModelProperty(value = "直发备注", name = "remarks")
    private String remarks;

    @ApiModelProperty(value = "是否真空包装", name = "vacuumFlag")
    private Integer vacuumFlag;

    @ApiModelProperty(value = "首单标识", name = "firstLabel")
    private String firstLabel;

    private Integer isOEM;

    private Integer isODM;

    @ApiModelProperty(value = "仓库名称", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "月台审核时间", name = "auditDate")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date auditDate;

    public Integer getIsOEM() {
        return isOEM;
    }

    public void setIsOEM(Integer isOEM) {
        this.isOEM = isOEM;
    }

    public Integer getIsODM() {
        return isODM;
    }

    public void setIsODM(Integer isODM) {
        this.isODM = isODM;
    }

    @ApiModelProperty(value = "袋子真空", name = "vacuumCn")
    String vacuumCn;

    public String getFirstLabel() {
        return firstLabel;
    }

    public void setFirstLabel(String firstLabel) {
        this.firstLabel = firstLabel;
    }

    public void setVacuumCn(String vacuumCn) {
        this.vacuumCn = vacuumCn;
    }

    public String getVacuumCn() {
        return vacuumCn;
    }

    public Integer getVacuumFlag() {
        return vacuumFlag;
    }

    public void setVacuumFlag(Integer vacuumFlag) {
        this.vacuumFlag = vacuumFlag;
    }

    public String getReceiptPlace() {
        return receiptPlace;
    }

    public void setReceiptPlace(String receiptPlace) {
        this.receiptPlace = receiptPlace;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getIsFbaQuick() {
        return isFbaQuick;
    }

    public void setIsFbaQuick(Integer isFbaQuick) {
        this.isFbaQuick = isFbaQuick;
    }

    public String getStockinOrderNo() {
        return stockinOrderNo;
    }

    public void setStockinOrderNo(String stockinOrderNo) {
        this.stockinOrderNo = stockinOrderNo;
    }

    public String getSupplierDeliveryBarcode() {
        return supplierDeliveryBarcode;
    }

    public void setSupplierDeliveryBarcode(String supplierDeliveryBarcode) {
        this.supplierDeliveryBarcode = supplierDeliveryBarcode;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getPlatformName() {
        return platformName;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Date getPlanArriveDate() {
        return planArriveDate;
    }

    public void setPlanArriveDate(Date planArriveDate) {
        this.planArriveDate = planArriveDate;
    }

    public Integer getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(Integer boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Integer getStockinQty() {
        return stockinQty;
    }

    public void setStockinQty(Integer stockinQty) {
        this.stockinQty = stockinQty;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getOperateStartDate() {
        return operateStartDate;
    }

    public void setOperateStartDate(Date operateStartDate) {
        this.operateStartDate = operateStartDate;
    }

    public Date getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(Date operateEndDate) {
        this.operateEndDate = operateEndDate;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }
}
