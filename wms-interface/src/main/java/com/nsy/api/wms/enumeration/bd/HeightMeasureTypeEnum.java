package com.nsy.api.wms.enumeration.bd;

import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-18 9:02
 */
public enum HeightMeasureTypeEnum {
    //稽查任务状态
    LOCAL_HEIGHT("本地高度测量"),

    //全检任务状态
    HEIGHT("高度测量"),

    //稽查结果
    FBA_COST("配送费测量");

    HeightMeasureTypeEnum(String name) {
        this.name = name;
    }

    private final String name;

    public String getName() {
        return name;
    }

    public static String of(String name) {
        if (Strings.isBlank(name)) return null;
        HeightMeasureTypeEnum resultEnum = Arrays.stream(values())
                .filter(instance -> name.equals(instance.name()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(resultEnum)) return name;

        return resultEnum.getName();
    }
}
