package com.nsy.api.wms.response.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 工厂发货列表统计响应
 */
@ApiModel(value = "StockPlatformScheduleStatisticsResponse", description = "工厂发货列表统计响应")
public class StockPlatformScheduleStatisticsResponse {

    @ApiModelProperty(value = "发货总箱数", name = "boxQtyTotal")
    private Integer boxQtyTotal;

    @ApiModelProperty(value = "发货总件数", name = "shipmentQtyTotal")
    private Integer shipmentQtyTotal;

    @ApiModelProperty(value = "收货总件数", name = "receiveQtyTotal")
    private Integer receiveQtyTotal;

    @ApiModelProperty(value = "需退货总件数", name = "waitReturnQtyTotal")
    private Integer waitReturnQtyTotal;

    @ApiModelProperty(value = "上架总件数", name = "shelvedQtyTotal")
    private Integer shelvedQtyTotal;

    public Integer getBoxQtyTotal() {
        return boxQtyTotal;
    }

    public void setBoxQtyTotal(Integer boxQtyTotal) {
        this.boxQtyTotal = boxQtyTotal;
    }

    public Integer getShipmentQtyTotal() {
        return shipmentQtyTotal;
    }

    public void setShipmentQtyTotal(Integer shipmentQtyTotal) {
        this.shipmentQtyTotal = shipmentQtyTotal;
    }

    public Integer getReceiveQtyTotal() {
        return receiveQtyTotal;
    }

    public void setReceiveQtyTotal(Integer receiveQtyTotal) {
        this.receiveQtyTotal = receiveQtyTotal;
    }

    public Integer getWaitReturnQtyTotal() {
        return waitReturnQtyTotal;
    }

    public void setWaitReturnQtyTotal(Integer waitReturnQtyTotal) {
        this.waitReturnQtyTotal = waitReturnQtyTotal;
    }

    public Integer getShelvedQtyTotal() {
        return shelvedQtyTotal;
    }

    public void setShelvedQtyTotal(Integer shelvedQtyTotal) {
        this.shelvedQtyTotal = shelvedQtyTotal;
    }
}
