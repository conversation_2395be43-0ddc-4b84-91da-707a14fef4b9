package com.nsy.api.wms.domain.stockout;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;
import java.util.Date;
import java.math.BigDecimal;

public class PackageSkuInfoExport {

    @NsyExcelProperty("包裹号")
    private String packageNo;

    @NsyExcelProperty("订单号")
    private String orderNo;

    @NsyExcelProperty("SKU编码")
    private String skuCode;

    @NsyExcelProperty("颜色/尺码")
    private String colorSize;

    @NsyExcelProperty("商品条形码")
    private String barcode;

    @NsyExcelProperty("客户SKU")
    private String customerSku;

    @NsyExcelProperty("客户条形码")
    private String fnsku;

    @NsyExcelProperty("件数")
    private Integer quantity;

    @NsyExcelProperty("单价")
    private BigDecimal unitPrice;

    @NsyExcelProperty("收货人名字")
    private String receiverName;

    @NsyExcelProperty("箱号")
    private String boxNo;

    @NsyExcelProperty("发货时间")
    private Date shipmentTime;

    @NsyExcelProperty("物流公司")
    private String logisticsCompany;

    @NsyExcelProperty("物流单号")
    private String trackingNo;

    @NsyExcelProperty("重量")
    private BigDecimal weight;

    @NsyExcelProperty("体积重")
    private String volumeWeight;

    @NsyExcelProperty("规格")
    private String specification;

    @NsyExcelProperty("店铺名称")
    private String storeName;

    @NsyExcelProperty("报关中文品名")
    private String customsNameCn;

    @NsyExcelProperty("报关英文品名")
    private String customsNameEn;

    @NsyExcelProperty("报关海关编码")
    private String customsCode;

    public String getPackageNo() {
        return packageNo;
    }

    public void setPackageNo(String packageNo) {
        this.packageNo = packageNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getColorSize() {
        return colorSize;
    }

    public void setColorSize(String colorSize) {
        this.colorSize = colorSize;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getCustomerSku() {
        return customerSku;
    }

    public void setCustomerSku(String customerSku) {
        this.customerSku = customerSku;
    }

    public String getFnsku() {
        return fnsku;
    }

    public void setFnsku(String fnsku) {
        this.fnsku = fnsku;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getBoxNo() {
        return boxNo;
    }

    public void setBoxNo(String boxNo) {
        this.boxNo = boxNo;
    }

    public Date getShipmentTime() {
        return shipmentTime;
    }

    public void setShipmentTime(Date shipmentTime) {
        this.shipmentTime = shipmentTime;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(String volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getCustomsNameCn() {
        return customsNameCn;
    }

    public void setCustomsNameCn(String customsNameCn) {
        this.customsNameCn = customsNameCn;
    }

    public String getCustomsNameEn() {
        return customsNameEn;
    }

    public void setCustomsNameEn(String customsNameEn) {
        this.customsNameEn = customsNameEn;
    }

    public String getCustomsCode() {
        return customsCode;
    }

    public void setCustomsCode(String customsCode) {
        this.customsCode = customsCode;
    }
}
