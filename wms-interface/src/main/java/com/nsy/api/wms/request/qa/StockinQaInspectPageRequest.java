package com.nsy.api.wms.request.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/12/5 15:36
 */
@ApiModel(value = "StockinQaInspectPageRequest", description = "入库稽查任务分页查询参数")
public class StockinQaInspectPageRequest extends PageRequest {


    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "仓库id", name = "spaceIdList")
    private List<Integer> spaceIdList;

    @ApiModelProperty(value = "spu列表", name = "spuList")
    private List<String> spuList;

    @ApiModelProperty(value = "sku列表", name = "skuList")
    private List<String> skuList;

    @ApiModelProperty(value = "供应商Id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "质检结果", name = "resultList")
    private List<String> resultList;

    @ApiModelProperty(value = "稽查状态", name = "inspectStatusList")
    private List<String> inspectStatusList;

    @ApiModelProperty(value = "稽查结果", name = "inspectStatusList")
    private List<String> processStatusList;

    @ApiModelProperty(value = "创建开始时间", name = "createStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createStartDate;

    @ApiModelProperty(value = "创建结束日期", name = "createEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createEndDate;

    @ApiModelProperty(value = "稽查完成开始时间", name = "completeStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeStartDate;

    @ApiModelProperty(value = "稽查完成结束日期", name = "completeEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeEndDate;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    private Boolean inspectResultIsNotNUll = Boolean.FALSE;

    /**
     * 是否批量退货
     */
    private Boolean batchReturnFlag = Boolean.FALSE;

    /**
     * 部分退货
     */
    private Boolean someReturnFlag = Boolean.FALSE;

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }

    public Boolean getInspectResultIsNotNUll() {
        return inspectResultIsNotNUll;
    }

    public void setInspectResultIsNotNUll(Boolean inspectResultIsNotNUll) {
        this.inspectResultIsNotNUll = inspectResultIsNotNUll;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }

    public List<String> getSpuList() {
        return spuList;
    }

    public void setSpuList(List<String> spuList) {
        this.spuList = spuList;
    }

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public List<String> getInspectStatusList() {
        return inspectStatusList;
    }

    public void setInspectStatusList(List<String> inspectStatusList) {
        this.inspectStatusList = inspectStatusList;
    }

    public Date getCompleteStartDate() {
        return completeStartDate;
    }

    public void setCompleteStartDate(Date completeStartDate) {
        this.completeStartDate = completeStartDate;
    }

    public Date getCompleteEndDate() {
        return completeEndDate;
    }

    public void setCompleteEndDate(Date completeEndDate) {
        this.completeEndDate = completeEndDate;
    }

    public List<String> getResultList() {
        return resultList;
    }

    public void setResultList(List<String> resultList) {
        this.resultList = resultList;
    }

    public List<String> getProcessStatusList() {
        return processStatusList;
    }

    public void setProcessStatusList(List<String> processStatusList) {
        this.processStatusList = processStatusList;
    }

    public Boolean getBatchReturnFlag() {
        return batchReturnFlag;
    }

    public void setBatchReturnFlag(Boolean batchReturnFlag) {
        this.batchReturnFlag = batchReturnFlag;
    }

    public Boolean getSomeReturnFlag() {
        return someReturnFlag;
    }

    public void setSomeReturnFlag(Boolean someReturnFlag) {
        this.someReturnFlag = someReturnFlag;
    }
}
