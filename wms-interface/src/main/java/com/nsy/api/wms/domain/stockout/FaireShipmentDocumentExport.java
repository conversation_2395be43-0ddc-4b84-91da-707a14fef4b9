package com.nsy.api.wms.domain.stockout;

import com.nsy.api.wms.enumeration.stockout.StockoutFaireShipmentProvinceEnum;

import java.math.BigDecimal;

public class FaireShipmentDocumentExport {

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 客户单号：订单号
     */
    private String orderNo;

    /**
     * 收货渠道
     */
    private String receivingChannel;

    /**
     * 目的地国家
     */
    private String country;

    /**
     * 件数：订单的箱数
     */
    private Integer boxQty;

    /**
     * 总重量
     */
    private BigDecimal weight;

    /**
     * 长：放空
     */
    private String length;

    /**
     * 宽：放空
     */
    private String width;

    /**
     * 高：放空
     */
    private String height;

    /**
     * 转单号：放空
     */
    private String transferNo;

    /**
     * 承运商子单号：放空
     */
    private String subNo;

    /**
     * 单件入仓号：放空
     */
    private String singleInStorageNo;

    /**
     * FBA入仓单号：放空
     */
    private String fbaInStorageNo;

    /**
     * 收货地址：省
     */
    private String province;

    /**
     * 收货地址：市
     */
    private String city;

    /**
     * 收货地址：邮编
     */
    private String zip;

    /**
     * 收件人姓名
     */
    private String receiveCompany;

    /**
     * 收件人姓名
     */
    private String receiveName;

    /**
     * 收件人电话
     */
    private String receivePhone;

    /**
     * 收件人地址1
     */
    private String receiveAddress1;

    /**
     * 收件人地址2
     */
    private String receiveAddress2;

    /**
     * 收件人地址3
     */
    private String receiveAddress3;

    /**
     * 收件人识别码
     */
    private String receiveCode;

    /**
     * 收件人郊区
     */
    private String receiveSuburb;

    /**
     * 包裹类型
     */
    private String bagType;

    /**
     * 报关方式
     */
    private String declareType;

    /**
     * 付税金
     */
    private String payTaxes;

    private BigDecimal productInvoicePrice;

    public BigDecimal getProductInvoicePrice() {
        return productInvoicePrice;
    }

    public void setProductInvoicePrice(BigDecimal productInvoicePrice) {
        this.productInvoicePrice = productInvoicePrice;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReceivingChannel() {
        return receivingChannel;
    }

    public void setReceivingChannel(String receivingChannel) {
        this.receivingChannel = receivingChannel;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getTransferNo() {
        return transferNo;
    }

    public void setTransferNo(String transferNo) {
        this.transferNo = transferNo;
    }

    public String getSubNo() {
        return subNo;
    }

    public void setSubNo(String subNo) {
        this.subNo = subNo;
    }

    public String getSingleInStorageNo() {
        return singleInStorageNo;
    }

    public void setSingleInStorageNo(String singleInStorageNo) {
        this.singleInStorageNo = singleInStorageNo;
    }

    public String getFbaInStorageNo() {
        return fbaInStorageNo;
    }

    public void setFbaInStorageNo(String fbaInStorageNo) {
        this.fbaInStorageNo = fbaInStorageNo;
    }

    public String getProvince() {
        return StockoutFaireShipmentProvinceEnum.getSimpleName(province);
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getReceiveCompany() {
        return receiveCompany;
    }

    public void setReceiveCompany(String receiveCompany) {
        this.receiveCompany = receiveCompany;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getReceivePhone() {
        return receivePhone;
    }

    public void setReceivePhone(String receivePhone) {
        this.receivePhone = receivePhone;
    }

    public String getReceiveAddress1() {
        return receiveAddress1;
    }

    public void setReceiveAddress1(String receiveAddress1) {
        this.receiveAddress1 = receiveAddress1;
    }

    public String getReceiveAddress2() {
        return receiveAddress2;
    }

    public void setReceiveAddress2(String receiveAddress2) {
        this.receiveAddress2 = receiveAddress2;
    }

    public String getReceiveAddress3() {
        return receiveAddress3;
    }

    public void setReceiveAddress3(String receiveAddress3) {
        this.receiveAddress3 = receiveAddress3;
    }

    public String getReceiveCode() {
        return receiveCode;
    }

    public void setReceiveCode(String receiveCode) {
        this.receiveCode = receiveCode;
    }

    public String getReceiveSuburb() {
        return receiveSuburb;
    }

    public void setReceiveSuburb(String receiveSuburb) {
        this.receiveSuburb = receiveSuburb;
    }

    public String getBagType() {
        return bagType;
    }

    public void setBagType(String bagType) {
        this.bagType = bagType;
    }

    public String getDeclareType() {
        return declareType;
    }

    public void setDeclareType(String declareType) {
        this.declareType = declareType;
    }

    public String getPayTaxes() {
        return payTaxes;
    }

    public void setPayTaxes(String payTaxes) {
        this.payTaxes = payTaxes;
    }
}
