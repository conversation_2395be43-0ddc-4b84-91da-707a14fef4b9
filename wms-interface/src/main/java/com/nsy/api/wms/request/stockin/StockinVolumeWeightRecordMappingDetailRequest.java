package com.nsy.api.wms.request.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-05 17:49
 */
public class StockinVolumeWeightRecordMappingDetailRequest extends PageRequest {

    @ApiModelProperty("规格编码-必传")
    private String sku;

    @ApiModelProperty("包装方式-必传")
    private String packageName;

    @ApiModelProperty("内部箱号")
    private String internalBoxCode;
    
    @ApiModelProperty("测量方式")
    private String measureType;

    @ApiModelProperty("供应商")
    private Integer supplierId;

    @ApiModelProperty("工厂出库单号")
    private String supplierDeliveryNo;

    @ApiModelProperty("测量人")
    private String createBy;

    @ApiModelProperty("配送费是否跳档")
    private Integer isOverStandard;

    @ApiModelProperty("是否合格")
    private Integer isQualified;

    /**
     * 测量完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "测量完成时间", name = "createDate")
    private Date createDateStart;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "测量完成时间", name = "createDate")
    private Date createDateEnd;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    private Date stockinDateStart;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    private Date stockinDateEnd;


    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getIsOverStandard() {
        return isOverStandard;
    }

    public void setIsOverStandard(Integer isOverStandard) {
        this.isOverStandard = isOverStandard;
    }

    public Integer getIsQualified() {
        return isQualified;
    }

    public void setIsQualified(Integer isQualified) {
        this.isQualified = isQualified;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public Date getStockinDateStart() {
        return stockinDateStart;
    }

    public void setStockinDateStart(Date stockinDateStart) {
        this.stockinDateStart = stockinDateStart;
    }

    public Date getStockinDateEnd() {
        return stockinDateEnd;
    }

    public void setStockinDateEnd(Date stockinDateEnd) {
        this.stockinDateEnd = stockinDateEnd;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }
}