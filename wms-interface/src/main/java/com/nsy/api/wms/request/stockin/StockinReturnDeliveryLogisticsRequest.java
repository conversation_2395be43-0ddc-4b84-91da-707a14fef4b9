package com.nsy.api.wms.request.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/22 15:35
 */
public class StockinReturnDeliveryLogisticsRequest {

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "发货日期", name = "deliveryDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty(value = "发货地址", name = "shippingAddress")
    private String shippingAddress;

    @ApiModelProperty(value = "退货任务id", name = "returnProductTaskId")
    private Integer returnProductTaskId;

    @ApiModelProperty(value = "发货方式", name = "deliveryType")
    private String deliveryType;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件", name = "attachmentUrl")
    private String attachmentUrl;

    /**
     * 运费承担方
     */
    @ApiModelProperty(value = "运费承担方", name = "freightCarrier")
    private Integer freightCarrier;

    @ApiModelProperty(value = "处理方式", name = "handleMethod")
    private Integer handleMethod;


    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getShippingAddress() {
        return shippingAddress;
    }

    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }

    public Integer getReturnProductTaskId() {
        return returnProductTaskId;
    }

    public void setReturnProductTaskId(Integer returnProductTaskId) {
        this.returnProductTaskId = returnProductTaskId;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public Integer getFreightCarrier() {
        return freightCarrier;
    }

    public void setFreightCarrier(Integer freightCarrier) {
        this.freightCarrier = freightCarrier;
    }

    public Integer getHandleMethod() {
        return handleMethod;
    }

    public void setHandleMethod(Integer handleMethod) {
        this.handleMethod = handleMethod;
    }
}
