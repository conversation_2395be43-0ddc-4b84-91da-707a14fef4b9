package com.nsy.api.wms.enumeration.stockout;

/**
 * 出库单平台
 */
public enum StockoutOrderPlatformEnum {
    PDD("PinDuoDuo", "拼多多"),
    TAO_BAO("TaoBao", "淘宝"),
    JING_DONG("<PERSON><PERSON><PERSON>", "京东"),
    DANG_DANG("DangDang", "当当"),
    PAI_PAI("PaiPai", "拍拍"),
    EBAY("EBAY", "ebay"),
    QQ("QQ", "QQ网购"),
    AMAZON("AMAZON", "亚马逊"),
    SU_NING("SuNing", "苏宁"),
    WEI_PIN_HUI("WeiPingHui", "唯品会"),
    JU_MEI("JuMei", "聚美"),
    YI_HAO_DIAN("YiHaoDian", "1号店"),
    YOU_LE("YouLe", "邮乐"),
    YOU_GOU("YouGou", "优购"),
    WALMART("沃尔玛", "沃尔玛"),
    ALIBABA("AliBaBa", "阿里巴巴"),
    FAIRE("站外Faire", "站外Faire"),
    SHEIN("She<PERSON>", "Shein"),
    ALLEGRO("allegro", "allegro"),
    OTTO("OTTO", "OTTO"),
    TIKTOK_SHOP("TikTokShop", "Tiktok全托管"),
    TIKTOK_LOCAL_SHIPPING("TikTokLocalShipping", "Tiktok直邮"),
    TIKTOK("Tiktok", "Tiktok"),
    TEMU_POP("TemuPop", "Temu半托管"),
    OTHERS("OTHERS", "其它平台");

    private String name;

    private String desc;


    StockoutOrderPlatformEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getName() {
        return name;
    }

    public static StockoutOrderPlatformEnum of(String name) {
        for (StockoutOrderPlatformEnum value : StockoutOrderPlatformEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
