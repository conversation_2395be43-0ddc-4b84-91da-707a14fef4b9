package com.nsy.api.wms.domain.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 装箱清单
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutShipmentModel", description = "装箱清单对象")
public class StockoutShipmentModel {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", name = "shipmentId")
    private Integer shipmentId;

    /**
     * 地区
     */
    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    /**
     * 箱子编号
     */
    @ApiModelProperty(value = "箱子编号", name = "shipmentBoxCode")
    private String shipmentBoxCode;

    /**
     * 箱子序号(第几箱)
     */
    @ApiModelProperty(value = "箱子序号(第几箱)", name = "boxIndex")
    private Integer boxIndex;

    /**
     * 箱子尺寸
     */
    @ApiModelProperty(value = "箱子尺寸", name = "boxSize")
    private String boxSize;
    @ApiModelProperty(value = "箱子尺寸", name = "boxSizeText")
    private String boxSizeText;

    /**
     * 箱子类型
     */
    private String packType;

    /**
     * 箱子类型
     */
    private String packTypeLabel;

    /**
     * 物流公司
     */
    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    /**
     * 面单尺寸
     */
    @ApiModelProperty(value = "面单尺寸", name = "logisticsLabelSize")
    private String logisticsLabelSize;

    /**
     * 货代渠道
     */
    @ApiModelProperty(value = "货代渠道", name = "forwarderChannel")
    private String forwarderChannel;

    /**
     * 转运物流公司
     */
    @ApiModelProperty(value = "转运物流公司", name = "transferLogisticsCompany")
    private String transferLogisticsCompany;

    /**
     * 转运单号
     */
    @ApiModelProperty(value = "转运单号", name = "transferLogisticsNo")
    private String transferLogisticsNo;

    /**
     * 装箱状态
     */
    @ApiModelProperty(value = "装箱状态", name = "status")
    private String status;

    /**
     * 发货日期
     */
    @ApiModelProperty(value = "发货日期", name = "deliveryDate")
    private Date deliveryDate;

    /**
     * 海关申报类型
     */
    @ApiModelProperty(value = "海关申报类型", name = "customsDeclareType")
    private String customsDeclareType;

    /**
     * 重量(千克)
     */
    @ApiModelProperty(value = "重量(千克)", name = "weight")
    private BigDecimal weight;

    /**
     * 体积重(千克)
     */
    @ApiModelProperty(value = "体积重(千克)", name = "volumeWeight")
    private BigDecimal volumeWeight;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    private Date createDate;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateDate")
    private Date updateDate;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者", name = "updateBy")
    private String updateBy;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", name = "version")
    private Integer version;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "订单号", name = "orderNoStr")
    private String orderNoStr;

    private Integer isDeleted;

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getPackType() {
        return packType;
    }

    public void setPackType(String packType) {
        this.packType = packType;
    }

    public String getPackTypeLabel() {
        return packTypeLabel;
    }

    public void setPackTypeLabel(String packTypeLabel) {
        this.packTypeLabel = packTypeLabel;
    }

    public String getOrderNoStr() {
        return orderNoStr;
    }

    public void setOrderNoStr(String orderNoStr) {
        this.orderNoStr = orderNoStr;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBoxSizeText() {
        return boxSizeText;
    }

    public void setBoxSizeText(String boxSizeText) {
        this.boxSizeText = boxSizeText;
    }

    public Integer getShipmentId() {
        return this.shipmentId;
    }

    public void setShipmentId(Integer shipmentId) {
        this.shipmentId = shipmentId;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getShipmentBoxCode() {
        return this.shipmentBoxCode;
    }

    public void setShipmentBoxCode(String shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }

    public Integer getBoxIndex() {
        return this.boxIndex;
    }

    public void setBoxIndex(Integer boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getBoxSize() {
        return this.boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }

    public String getLogisticsCompany() {
        return this.logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return this.logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getLogisticsLabelSize() {
        return logisticsLabelSize;
    }

    public void setLogisticsLabelSize(String logisticsLabelSize) {
        this.logisticsLabelSize = logisticsLabelSize;
    }

    public String getTransferLogisticsCompany() {
        return transferLogisticsCompany;
    }

    public void setTransferLogisticsCompany(String transferLogisticsCompany) {
        this.transferLogisticsCompany = transferLogisticsCompany;
    }

    public String getTransferLogisticsNo() {
        return transferLogisticsNo;
    }

    public void setTransferLogisticsNo(String transferLogisticsNo) {
        this.transferLogisticsNo = transferLogisticsNo;
    }

    public String getForwarderChannel() {
        return this.forwarderChannel;
    }

    public void setForwarderChannel(String forwarderChannel) {
        this.forwarderChannel = forwarderChannel;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getDeliveryDate() {
        return this.deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getCustomsDeclareType() {
        return this.customsDeclareType;
    }

    public void setCustomsDeclareType(String customsDeclareType) {
        this.customsDeclareType = customsDeclareType;
    }

    public BigDecimal getWeight() {
        return this.weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolumeWeight() {
        return this.volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}
