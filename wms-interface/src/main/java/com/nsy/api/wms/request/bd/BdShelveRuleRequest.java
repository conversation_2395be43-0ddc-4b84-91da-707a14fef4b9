package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "BdShelveRuleRequest", description = "上架规则request")
public class BdShelveRuleRequest {

    @ApiModelProperty(value = "上架规则id", name = "shelveRuleId")
    private Integer shelveRuleId;

    @ApiModelProperty(value = "编码", name = "shelveRuleCode")
    private String shelveRuleCode;

    @NotBlank(message = "规则名称不能为空")
    @ApiModelProperty(value = "上架规则名称", name = "shelveRuleName", required = true)
    private String shelveRuleName;

    @NotNull(message = "仓库不能为空")
    @ApiModelProperty(value = "仓库id", name = "spaceId", required = true)
    private Integer spaceId;

    @ApiModelProperty(value = "上架规则级别(分类category,商品product,规格spec)", name = "shelveRuleType")
    private String shelveRuleType;

    @ApiModelProperty(value = "入库类型", name = "stockinType")
    private String stockinType;

    @ApiModelProperty(value = "是否停用/启用:0-启用/1-停用", name = "isDeleted")
    @NotNull(message = "状态不能为空")
    private Integer isDeleted;

    @ApiModelProperty(value = "备注描述", name = "description")
    private String description;

    @Valid
    private List<BdShelveRuleItemRequest> bdShelveRuleItemRequestList;

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public Integer getShelveRuleId() {
        return shelveRuleId;
    }

    public void setShelveRuleId(Integer shelveRuleId) {
        this.shelveRuleId = shelveRuleId;
    }

    public String getShelveRuleCode() {
        return shelveRuleCode;
    }

    public void setShelveRuleCode(String shelveRuleCode) {
        this.shelveRuleCode = shelveRuleCode;
    }

    public String getShelveRuleName() {
        return shelveRuleName;
    }

    public void setShelveRuleName(String shelveRuleName) {
        this.shelveRuleName = shelveRuleName;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getShelveRuleType() {
        return shelveRuleType;
    }

    public void setShelveRuleType(String shelveRuleType) {
        this.shelveRuleType = shelveRuleType;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<BdShelveRuleItemRequest> getBdShelveRuleItemRequestList() {
        return bdShelveRuleItemRequestList;
    }

    public void setBdShelveRuleItemRequestList(List<BdShelveRuleItemRequest> bdShelveRuleItemRequestList) {
        this.bdShelveRuleItemRequestList = bdShelveRuleItemRequestList;
    }
}
