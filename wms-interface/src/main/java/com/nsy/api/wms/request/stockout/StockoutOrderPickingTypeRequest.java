package com.nsy.api.wms.request.stockout;

import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StockoutOrderPickingTypeRequest", description = "出库单列表设置拣货模式request")
public class StockoutOrderPickingTypeRequest {

    @ApiModelProperty(value = "出库单Id", name = "stockoutOrderIds")
    private List<Integer> stockoutOrderIds;

    @ApiModelProperty(value = "拣货模式", name = "pickingTypeEnum")
    private StockoutPickingTypeEnum pickingTypeEnum;

    public List<Integer> getStockoutOrderIds() {
        return stockoutOrderIds;
    }

    public void setStockoutOrderIds(List<Integer> stockoutOrderIds) {
        this.stockoutOrderIds = stockoutOrderIds;
    }

    public StockoutPickingTypeEnum getPickingTypeEnum() {
        return pickingTypeEnum;
    }

    public void setPickingTypeEnum(StockoutPickingTypeEnum pickingTypeEnum) {
        this.pickingTypeEnum = pickingTypeEnum;
    }
}
