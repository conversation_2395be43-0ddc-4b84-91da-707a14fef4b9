package com.nsy.api.wms.domain.stockout;

import java.math.BigDecimal;
import java.util.Date;


public class StockoutCustomsDeclareFormExport {
    //购买方
    private String customer;
    //国家
    private String destination;
    //出口开票日期
    private String exportInvoiceDate;
    //出口发票号
    private String exportInvoiceNo;
    //出口日期
    private String exportDate;
    //申报日期
    private String dDate;
    //海关报关单号
    private String declareDocumentNo;
    //合同协议号
    private String protocolNo;
    //出口口岸
    private String exportPort;
    //项号
    private String gNo;
    //商品编码 
    private String gCode;
    //商品名称
    private String gName;
    //申报要素
    private String declareElement;
    //单位
    private String gUnit;
    //数量
    private Integer gQty;
    //C&F金额
    private BigDecimal cfPrice;
    //分摊运费
    private BigDecimal apportionedFreight;
    //fob总价(美元)
    private BigDecimal fobPrice;
    //汇率
    private BigDecimal exchangeRate;
    //fob总价(人民币)
    private BigDecimal fobPriceCny;
    //箱数
    private Integer boxQty;
    //毛重
    private BigDecimal roughWeight;
    //净重
    private BigDecimal netWeight;
    //出库日期
    private String stockoutDate;
    //入库日期
    private String stockinDate;
    //进项发票号码
    private String inputInvoiceNo;
    //进项发票代码
    private String inputInvoiceCode;
    //进项发票开票时间
    private String invoiceDate;
    //供应商名称
    private String supplierName;
    //进项金额
    private String inputPrice;
    //进项税额
    private String taxPrice;
    //采购合同号
    private String declareContractNo;
    //合同签订日期
    private String contractSignedDate;
    //工厂交货日期
    private String deliveryDate;
    //申报批次
    private String declareBatch;
    //含税单价
    private BigDecimal taxInclusiveUnitPrice;
    //含税金额
    private BigDecimal taxInclusivePrice;
    //物流商名称
    private String logisticsCompany;
    //物流开票日期
    private String logisticsInvoiceDate;
    //物流发票号
    private String logisticsInvoiceNumber;

    private Integer supplierId;
    //匹配时间
    private Date matchDate;

    //报关单据签约时间
    private String documentSigningDate;


    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getExportInvoiceNo() {
        return exportInvoiceNo;
    }

    public void setExportInvoiceNo(String exportInvoiceNo) {
        this.exportInvoiceNo = exportInvoiceNo;
    }

    public String getDeclareDocumentNo() {
        return declareDocumentNo;
    }

    public void setDeclareDocumentNo(String declareDocumentNo) {
        this.declareDocumentNo = declareDocumentNo;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getExportPort() {
        return exportPort;
    }

    public void setExportPort(String exportPort) {
        this.exportPort = exportPort;
    }

    public String getgNo() {
        return gNo;
    }

    public void setgNo(String gNo) {
        this.gNo = gNo;
    }

    public String getgCode() {
        return gCode;
    }

    public void setgCode(String gCode) {
        this.gCode = gCode;
    }

    public String getgName() {
        return gName;
    }

    public void setgName(String gName) {
        this.gName = gName;
    }

    public String getDeclareElement() {
        return declareElement;
    }

    public void setDeclareElement(String declareElement) {
        this.declareElement = declareElement;
    }

    public String getgUnit() {
        return gUnit;
    }

    public void setgUnit(String gUnit) {
        this.gUnit = gUnit;
    }

    public Integer getgQty() {
        return gQty;
    }

    public void setgQty(Integer gQty) {
        this.gQty = gQty;
    }

    public BigDecimal getCfPrice() {
        return cfPrice;
    }

    public void setCfPrice(BigDecimal cfPrice) {
        this.cfPrice = cfPrice;
    }

    public BigDecimal getApportionedFreight() {
        return apportionedFreight;
    }

    public void setApportionedFreight(BigDecimal apportionedFreight) {
        this.apportionedFreight = apportionedFreight;
    }

    public BigDecimal getFobPrice() {
        return fobPrice;
    }

    public void setFobPrice(BigDecimal fobPrice) {
        this.fobPrice = fobPrice;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getFobPriceCny() {
        return fobPriceCny;
    }

    public void setFobPriceCny(BigDecimal fobPriceCny) {
        this.fobPriceCny = fobPriceCny;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public BigDecimal getRoughWeight() {
        return roughWeight;
    }

    public void setRoughWeight(BigDecimal roughWeight) {
        this.roughWeight = roughWeight;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public String getInputInvoiceNo() {
        return inputInvoiceNo;
    }

    public void setInputInvoiceNo(String inputInvoiceNo) {
        this.inputInvoiceNo = inputInvoiceNo;
    }

    public String getInputInvoiceCode() {
        return inputInvoiceCode;
    }

    public void setInputInvoiceCode(String inputInvoiceCode) {
        this.inputInvoiceCode = inputInvoiceCode;
    }

    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(String inputPrice) {
        this.inputPrice = inputPrice;
    }

    public String getTaxPrice() {
        return taxPrice;
    }

    public void setTaxPrice(String taxPrice) {
        this.taxPrice = taxPrice;
    }

    public String getDeclareContractNo() {
        return declareContractNo;
    }

    public void setDeclareContractNo(String declareContractNo) {
        this.declareContractNo = declareContractNo;
    }

    public BigDecimal getTaxInclusiveUnitPrice() {
        return taxInclusiveUnitPrice;
    }

    public void setTaxInclusiveUnitPrice(BigDecimal taxInclusiveUnitPrice) {
        this.taxInclusiveUnitPrice = taxInclusiveUnitPrice;
    }

    public BigDecimal getTaxInclusivePrice() {
        return taxInclusivePrice;
    }

    public void setTaxInclusivePrice(BigDecimal taxInclusivePrice) {
        this.taxInclusivePrice = taxInclusivePrice;
    }

    public String getDeclareBatch() {
        return declareBatch;
    }

    public void setDeclareBatch(String declareBatch) {
        this.declareBatch = declareBatch;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getExportInvoiceDate() {
        return exportInvoiceDate;
    }

    public void setExportInvoiceDate(String exportInvoiceDate) {
        this.exportInvoiceDate = exportInvoiceDate;
    }

    public String getExportDate() {
        return exportDate;
    }

    public void setExportDate(String exportDate) {
        this.exportDate = exportDate;
    }

    public String getdDate() {
        return dDate;
    }

    public void setdDate(String dDate) {
        this.dDate = dDate;
    }

    public String getStockoutDate() {
        return stockoutDate;
    }

    public void setStockoutDate(String stockoutDate) {
        this.stockoutDate = stockoutDate;
    }

    public String getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(String stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getContractSignedDate() {
        return contractSignedDate;
    }

    public void setContractSignedDate(String contractSignedDate) {
        this.contractSignedDate = contractSignedDate;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }


    public Date getMatchDate() {
        return matchDate;
    }

    public void setMatchDate(Date matchDate) {
        this.matchDate = matchDate;
    }

    public String getLogisticsInvoiceDate() {
        return logisticsInvoiceDate;
    }

    public void setLogisticsInvoiceDate(String logisticsInvoiceDate) {
        this.logisticsInvoiceDate = logisticsInvoiceDate;
    }

    public String getLogisticsInvoiceNumber() {
        return logisticsInvoiceNumber;
    }

    public void setLogisticsInvoiceNumber(String logisticsInvoiceNumber) {
        this.logisticsInvoiceNumber = logisticsInvoiceNumber;
    }

    public String getDocumentSigningDate() {
        return documentSigningDate;
    }

    public void setDocumentSigningDate(String documentSigningDate) {
        this.documentSigningDate = documentSigningDate;
    }
}
