package com.nsy.api.wms.request.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StockinReturnProductTaskScanFinishedFromStockPositionRequest", description = "采购退货退货完成request - 存储库位")
public class StockinReturnProductTaskScanFinishedFromStockPositionRequest {

    @ApiModelProperty(value = "库位编码", name = "positionCode")
    private String positionCode;

    @ApiModelProperty(value = "明细列表", name = "itemList")
    private List<Item> itemList;

    @ApiModelProperty(value = "工厂id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "工厂名", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public List<Item> getItemList() {
        return itemList;
    }

    public void setItemList(List<Item> itemList) {
        this.itemList = itemList;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public static final class Item {
        @ApiModelProperty(value = "stockId", name = "stockId")
        private Integer stockId;

        @ApiModelProperty(value = "sku", name = "sku")
        private String sku;

        @ApiModelProperty(value = "数量", name = "qty")
        private Integer qty;

        public Integer getStockId() {
            return stockId;
        }

        public void setStockId(Integer stockId) {
            this.stockId = stockId;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public Integer getQty() {
            return qty;
        }

        public void setQty(Integer qty) {
            this.qty = qty;
        }
    }
}
