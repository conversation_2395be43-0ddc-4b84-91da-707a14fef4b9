package com.nsy.api.wms.request.wcs;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;

import java.util.Date;
import java.util.List;

/**
 * 测量高度分拣记录分页查询请求DTO
 * 用于前端分页查询测量高度分拣记录数据
 * 支持多条件组合查询
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class WcsHeightSortingRecordPageRequest extends PageRequest {

    /**
     * SKU列表，用于筛选指定规格编码的测量高度分拣记录
     * 支持多个SKU同时查询，数据格式：字符串列表
     * 示例：["SKU001", "SKU002"]
     */
    private List<String> skuList;

    /**
     * SPU列表，用于筛选指定商品编码的测量高度分拣记录
     * 通过关联ProductInfo表的spu字段进行查询
     * 支持多个SPU同时查询，数据格式：字符串列表
     * 示例：["SPU001", "SPU002"]
     */
    private List<String> spuList;

    /**
     * 条形码，用于精确匹配单个商品的测量高度分拣记录
     * 数据格式：字符串
     * 示例："1234567890123"
     */
    private String barcode;

    /**
     * 测量类型，标识测量方式
     * 取值：AUTO(自动测量)、MANUAL(手动测量)、SYSTEM(系统录入)等
     * 用于筛选指定测量方式的记录
     */
    private String measureType;

    /**
     * 分拣口状态，表示商品测量结果
     * 取值：STANDARD(达标)、NO_STANDARD(不达标)、ERROR(异常)
     * 用于筛选特定分拣状态的记录
     */
    private String sortingPort;

    /**
     * 创建日期范围查询 - 开始时间
     * 格式：yyyy-MM-dd HH:mm:ss
     * 时区：GMT+8
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createBeginDate;

    /**
     * 创建日期范围查询 - 结束时间
     * 格式：yyyy-MM-dd HH:mm:ss
     * 时区：GMT+8
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createEndDate;

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public List<String> getSpuList() {
        return spuList;
    }

    public void setSpuList(List<String> spuList) {
        this.spuList = spuList;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }

    public String getSortingPort() {
        return sortingPort;
    }

    public void setSortingPort(String sortingPort) {
        this.sortingPort = sortingPort;
    }

    public Date getCreateBeginDate() {
        return createBeginDate;
    }

    public void setCreateBeginDate(Date createBeginDate) {
        this.createBeginDate = createBeginDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }
} 