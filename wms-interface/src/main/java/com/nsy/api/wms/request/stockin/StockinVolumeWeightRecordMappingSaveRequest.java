package com.nsy.api.wms.request.stockin;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-04 10:17
 */
public class StockinVolumeWeightRecordMappingSaveRequest {

    @ApiModelProperty(value = "标准计费费用", name = "fbaCost")
    private BigDecimal fbaCost;

    @ApiModelProperty(value = "选择的记录信息", name = "idList")
    private List<BdVolumeWeightRecordMappingInfo> recordMappingInfoList;

    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNos;

    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;

    @ApiModelProperty(value = "采购单号", name = "purchasePlanNo")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "是否合格 1-是，0-否", name = "isQualified")
    private Integer isQualified;

    private List<String> supplierDeliveryNoList;

    @ApiModelProperty(value = "测量类型", name = "measureType")
    private String measureType;

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public List<BdVolumeWeightRecordMappingInfo> getRecordMappingInfoList() {
        return recordMappingInfoList;
    }

    public void setRecordMappingInfoList(List<BdVolumeWeightRecordMappingInfo> recordMappingInfoList) {
        this.recordMappingInfoList = recordMappingInfoList;
    }

    public List<String> getSupplierDeliveryNoList() {
        return supplierDeliveryNoList;
    }

    public void setSupplierDeliveryNoList(List<String> supplierDeliveryNoList) {
        this.supplierDeliveryNoList = supplierDeliveryNoList;
    }

    public String getSupplierDeliveryNos() {
        return supplierDeliveryNos;
    }

    public void setSupplierDeliveryNos(String supplierDeliveryNos) {
        this.supplierDeliveryNos = supplierDeliveryNos;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }


    public Integer getIsQualified() {
        return isQualified;
    }

    public void setIsQualified(Integer isQualified) {
        this.isQualified = isQualified;
    }

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }
}
