package com.nsy.api.wms.request.stockout;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StockoutGenerateBatchByDocRequest", description = "以单找货查询request")
public class StockoutGenerateBatchByDocRequest extends PageRequest {

    @ApiModelProperty(value = "波次Id", name = "batchId")
    private Integer batchId;

    @ApiModelProperty(value = "仓库id", name = "spaceId")
    private Integer spaceId;

    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "标签规格", name = "logisticsLabelSize")
    private String logisticsLabelSize;

    @ApiModelProperty(value = "库区名称", name = "spaceAreaName")
    private String spaceAreaName;

    @ApiModelProperty(value = "1已打印 0未打印", name = "isPrint")
    private Integer isPrint;

    @ApiModelProperty(value = "是否加工", name = "isNeedProcess")
    private Integer isNeedProcess;

    @ApiModelProperty(value = "是否跨仓", name = "multipleSpace")
    private Integer multipleSpace;

    @ApiModelProperty(value = "出库类型", name = "stockoutType")
    private String stockoutType;

    @ApiModelProperty(value = "品牌标签", name = "tagName")
    private String tagName;

    private List<String> brandList;

    @ApiModelProperty(value = "区域", name = "areaName")
    private String areaName;

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public List<String> getBrandList() {
        return brandList;
    }

    public void setBrandList(List<String> brandList) {
        this.brandList = brandList;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getStockoutType() {
        return stockoutType;
    }

    public void setStockoutType(String stockoutType) {
        this.stockoutType = stockoutType;
    }

    public Integer getIsNeedProcess() {
        return isNeedProcess;
    }

    public void setIsNeedProcess(Integer isNeedProcess) {
        this.isNeedProcess = isNeedProcess;
    }

    public Integer getMultipleSpace() {
        return multipleSpace;
    }

    public void setMultipleSpace(Integer multipleSpace) {
        this.multipleSpace = multipleSpace;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getLogisticsLabelSize() {
        return logisticsLabelSize;
    }

    public void setLogisticsLabelSize(String logisticsLabelSize) {
        this.logisticsLabelSize = logisticsLabelSize;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public Integer getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(Integer isPrint) {
        this.isPrint = isPrint;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }
}
