package com.nsy.api.wms.response.qa;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 13:57
 */
public class BdQaFullInspectRuleItemInfoResponse {

    @ApiModelProperty(value = "是否退货返工 1是  0否", name = "isReturnApply")
    private String isReturnApply;

    @ApiModelProperty("商品标签")
    private List<String> skuTypeList;
    
    private List<BdQaFullInspectRuleProductItemInfoResponse> productInfoList;

    public String getIsReturnApply() {
        return isReturnApply;
    }

    public void setIsReturnApply(String isReturnApply) {
        this.isReturnApply = isReturnApply;
    }

    public List<String> getSkuTypeList() {
        return skuTypeList;
    }

    public void setSkuTypeList(List<String> skuTypeList) {
        this.skuTypeList = skuTypeList;
    }

    public List<BdQaFullInspectRuleProductItemInfoResponse> getProductInfoList() {
        return productInfoList;
    }

    public void setProductInfoList(List<BdQaFullInspectRuleProductItemInfoResponse> productInfoList) {
        this.productInfoList = productInfoList;
    }
}
