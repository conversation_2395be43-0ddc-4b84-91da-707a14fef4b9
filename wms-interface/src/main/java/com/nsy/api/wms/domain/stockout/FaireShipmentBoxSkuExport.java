package com.nsy.api.wms.domain.stockout;

import java.math.BigDecimal;

public class FaireShipmentBoxSkuExport {

    private String boxCode;

    private String orderNo;

    private String boxIndex;
    private String customsDeclareCn;
    private String customsDeclareEn;
    private String spinType;
    private String fabricType;
    private String filler;
    private String storeSku;
    private String storeBarcode;
    private String sku;
    private String qty;
    private String weight;
    private String volumeWeight;
    private String boxSize;
    private String unitWeight;

    private String elementValue;

    /**
     * 方数
     */
    private BigDecimal cube;

    private BigDecimal productTotalInvoicePrice;

    public BigDecimal getProductTotalInvoicePrice() {
        return productTotalInvoicePrice;
    }

    public void setProductTotalInvoicePrice(BigDecimal productTotalInvoicePrice) {
        this.productTotalInvoicePrice = productTotalInvoicePrice;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(String boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getCustomsDeclareCn() {
        return customsDeclareCn;
    }

    public void setCustomsDeclareCn(String customsDeclareCn) {
        this.customsDeclareCn = customsDeclareCn;
    }

    public String getCustomsDeclareEn() {
        return customsDeclareEn;
    }

    public void setCustomsDeclareEn(String customsDeclareEn) {
        this.customsDeclareEn = customsDeclareEn;
    }

    public String getSpinType() {
        return spinType;
    }

    public void setSpinType(String spinType) {
        this.spinType = spinType;
    }

    public String getFabricType() {
        return fabricType;
    }

    public void setFabricType(String fabricType) {
        this.fabricType = fabricType;
    }

    public String getFiller() {
        return filler;
    }

    public void setFiller(String filler) {
        this.filler = filler;
    }

    public String getStoreSku() {
        return storeSku;
    }

    public void setStoreSku(String storeSku) {
        this.storeSku = storeSku;
    }

    public String getStoreBarcode() {
        return storeBarcode;
    }

    public void setStoreBarcode(String storeBarcode) {
        this.storeBarcode = storeBarcode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getQty() {
        return qty;
    }

    public void setQty(String qty) {
        this.qty = qty;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(String volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public String getBoxSize() {
        return boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }

    public String getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(String unitWeight) {
        this.unitWeight = unitWeight;
    }

    public String getElementValue() {
        return elementValue;
    }

    public void setElementValue(String elementValue) {
        this.elementValue = elementValue;
    }

    public BigDecimal getCube() {
        return cube;
    }

    public void setCube(BigDecimal cube) {
        this.cube = cube;
    }
}
