package com.nsy.api.wms.domain.logistics.documents.request;

import com.nsy.api.wms.domain.logistics.documents.AddressInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintItemInfo;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

public class LogisticsDocumentsBaseRequestInfo {

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    @ApiModelProperty(value = "订单号集合", name = "orderNos")
    private String orderNos;

    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "物流账号", name = "logisticsAccount")
    private String logisticsAccount;

    @ApiModelProperty(value = "币种", name = "currencyCode")
    private String currencyCode;

    @ApiModelProperty(value = "发件人信息", name = "sender")
    private AddressInfo sender;

    @ApiModelProperty(value = "收件人信息", name = "receiver")
    private AddressInfo receiver;

    @ApiModelProperty(value = "运费", name = "freightCharges")
    private BigDecimal freightCharges;

    @ApiModelProperty(value = "手续费", name = "handingFee")
    private BigDecimal handingFee;

    @ApiModelProperty(value = "发票模板", name = "invoiceTemplate")
    private String invoiceTemplate;

    @ApiModelProperty(value = "箱规", name = "boxSize")
    private String boxSize;

    @ApiModelProperty(value = "包裹数", name = "packageCount")
    private int packageCount;

    @ApiModelProperty(value = "包装类型", name = "packagingType")
    private String packagingType;

    @ApiModelProperty(value = "寄件人参考信息", name = "customerReference")
    private String customerReference;

    @ApiModelProperty(value = "包裹重量(kg)", name = "packageWeight")
    private BigDecimal packageWeight;

    @ApiModelProperty(value = "装箱清单主键", name = "shipmentIdList")
    private List<Integer> shipmentIdList;

    @ApiModelProperty(value = "商品信息", name = "commodityList")
    private List<LogisticsDocumentsPrintItemInfo> commodityList;

    @ApiModelProperty(value = "是否重新打印", name = "rePrint")
    private Boolean rePrint;

    @ApiModelProperty(value = "发票标题", name = "invoiceTitle", hidden = true)
    private String invoiceTitle;

    @ApiModelProperty(value = "体积重(千克)", name = "volumeWeightValue")
    private BigDecimal volumeWeightValue;

    @ApiModelProperty("税金支付账号")
    private String dutyPaymentAccount;

    private BigDecimal totalGoodsPrice;

    public BigDecimal getTotalGoodsPrice() {
        return totalGoodsPrice;
    }

    public void setTotalGoodsPrice(BigDecimal totalGoodsPrice) {
        this.totalGoodsPrice = totalGoodsPrice;
    }

    public String getDutyPaymentAccount() {
        return dutyPaymentAccount;
    }

    public void setDutyPaymentAccount(String dutyPaymentAccount) {
        this.dutyPaymentAccount = dutyPaymentAccount;
    }

    public BigDecimal getVolumeWeightValue() {
        return volumeWeightValue;
    }

    public void setVolumeWeightValue(BigDecimal volumeWeightValue) {
        this.volumeWeightValue = volumeWeightValue;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(String orderNos) {
        this.orderNos = orderNos;
    }

    public Boolean getRePrint() {
        return rePrint;
    }

    public void setRePrint(Boolean rePrint) {
        this.rePrint = rePrint;
    }

    public String getLogisticsAccount() {
        return logisticsAccount;
    }

    public void setLogisticsAccount(String logisticsAccount) {
        this.logisticsAccount = logisticsAccount;
    }

    public AddressInfo getSender() {
        return sender;
    }

    public void setSender(AddressInfo sender) {
        this.sender = sender;
    }

    public AddressInfo getReceiver() {
        return receiver;
    }

    public void setReceiver(AddressInfo receiver) {
        this.receiver = receiver;
    }

    public BigDecimal getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(BigDecimal freightCharges) {
        if (Objects.isNull(freightCharges)) {
            this.freightCharges = BigDecimal.ZERO;
        } else {
            this.freightCharges = freightCharges;
        }
    }

    public BigDecimal getHandingFee() {
        return handingFee;
    }

    public void setHandingFee(BigDecimal handingFee) {
        if (Objects.isNull(handingFee)) {
            this.handingFee = BigDecimal.ZERO;
        } else {
            this.handingFee = handingFee;
        }
    }

    public String getInvoiceTemplate() {
        return invoiceTemplate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public void setInvoiceTemplate(String invoiceTemplate) {
        this.invoiceTemplate = invoiceTemplate;
    }

    public String getBoxSize() {
        return boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }

    public int getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(int packageCount) {
        this.packageCount = packageCount;
    }

    public String getPackagingType() {
        return packagingType;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }

    public String getCustomerReference() {
        return customerReference;
    }

    public void setCustomerReference(String customerReference) {
        this.customerReference = customerReference;
    }

    public BigDecimal getPackageWeight() {
        return packageWeight;
    }

    public void setPackageWeight(BigDecimal packageWeight) {
        this.packageWeight = packageWeight;
    }

    public List<Integer> getShipmentIdList() {
        return shipmentIdList;
    }

    public void setShipmentIdList(List<Integer> shipmentIdList) {
        this.shipmentIdList = shipmentIdList;
    }

    public List<LogisticsDocumentsPrintItemInfo> getCommodityList() {
        return commodityList;
    }

    public void setCommodityList(List<LogisticsDocumentsPrintItemInfo> commodityList) {
        this.commodityList = commodityList;
    }

}
