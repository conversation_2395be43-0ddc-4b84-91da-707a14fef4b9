package com.nsy.api.wms.request.stockin;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-04 15:04
 */
public class StockinVolumeWeightRecordMappingRequest {

    @ApiModelProperty("工厂出库单")
    private String supplierDeliveryNo;

    @ApiModelProperty("测量类型")
    private String measureType;
    
    @ApiModelProperty("sku")
    private String sku;
    
    private String internalBoxCode;
    
    private List<String> supplierDeliveryNoList;

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public List<String> getSupplierDeliveryNoList() {
        return supplierDeliveryNoList;
    }

    public void setSupplierDeliveryNoList(List<String> supplierDeliveryNoList) {
        this.supplierDeliveryNoList = supplierDeliveryNoList;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }
}
