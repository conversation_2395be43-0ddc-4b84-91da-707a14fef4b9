package com.nsy.api.wms.enumeration.bd;

import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-18 9:08
 */
public enum HeightMeasureSortingPortStatusEnum {
    //稽查任务状态
    STANDARD("达标"),

    //全检任务状态
    NO_STANDARD("不达标");

    HeightMeasureSortingPortStatusEnum(String name) {
        this.name = name;
    }

    private final String name;

    public String getName() {
        return name;
    }

    public static String of(String name) {
        if (Strings.isBlank(name)) return null;
        HeightMeasureSortingPortStatusEnum resultEnum = Arrays.stream(values())
                .filter(instance -> name.equals(instance.name()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(resultEnum)) return name;

        return resultEnum.getName();
    }
}
