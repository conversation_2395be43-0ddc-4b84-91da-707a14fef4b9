package com.nsy.api.wms.response.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "StockTransferInSpaceTaskListResponse", description = "库位调拨列表")
public class StockTransferInSpaceTaskListResponse {

    @ApiModelProperty(value = "任务id", name = "taskId")
    private Integer taskId;

    @ApiModelProperty(value = "仓库名", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "任务类型", name = "typeStr")
    private String typeStr;

    @ApiModelProperty(value = "调出库位数", name = "outPositionQty")
    private Integer outPositionQty;

    @ApiModelProperty(value = "调入库位数", name = "inPositionQty")
    private Integer inPositionQty;

    @ApiModelProperty(value = "调拨sku件数", name = "skuQty")
    private Integer skuQty;

    @ApiModelProperty(value = "状态", name = "statusStr")
    private String statusStr;

    @ApiModelProperty(value = "操作人", name = "operator")
    private String operator;

    @ApiModelProperty(value = "调拨开始时间", name = "operateStartDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date operateStartDate;

    @ApiModelProperty(value = "调拨结束时间", name = "operateEndDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date operateEndDate;

    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "创建日期", name = "createDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createDate;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getTypeStr() {
        return typeStr;
    }

    public void setTypeStr(String typeStr) {
        this.typeStr = typeStr;
    }

    public Integer getOutPositionQty() {
        return outPositionQty;
    }

    public void setOutPositionQty(Integer outPositionQty) {
        this.outPositionQty = outPositionQty;
    }

    public Integer getInPositionQty() {
        return inPositionQty;
    }

    public void setInPositionQty(Integer inPositionQty) {
        this.inPositionQty = inPositionQty;
    }

    public Integer getSkuQty() {
        return skuQty;
    }

    public void setSkuQty(Integer skuQty) {
        this.skuQty = skuQty;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateStartDate() {
        return operateStartDate;
    }

    public void setOperateStartDate(Date operateStartDate) {
        this.operateStartDate = operateStartDate;
    }

    public Date getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(Date operateEndDate) {
        this.operateEndDate = operateEndDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
