package com.nsy.api.wms.request.qa;

import com.nsy.api.wms.response.qa.BdQaFullInspectRuleProductItemInfoResponse;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 16:15
 */
public class BdQaFullInspectRuleItemSaveRequest {

    @ApiModelProperty("商品标签")
    private List<String> skuTypeList;

    @ApiModelProperty(value = "是否退货返工 1是  0否", name = "isReturnApply")
    private String isReturnApply;

    @ApiModelProperty("商品信息")
    private List<BdQaFullInspectRuleProductItemInfoResponse> productInfoList;

    public List<String> getSkuTypeList() {
        return skuTypeList;
    }

    public void setSkuTypeList(List<String> skuTypeList) {
        this.skuTypeList = skuTypeList;
    }

    public String getIsReturnApply() {
        return isReturnApply;
    }

    public void setIsReturnApply(String isReturnApply) {
        this.isReturnApply = isReturnApply;
    }

    public List<BdQaFullInspectRuleProductItemInfoResponse> getProductInfoList() {
        return productInfoList;
    }

    public void setProductInfoList(List<BdQaFullInspectRuleProductItemInfoResponse> productInfoList) {
        this.productInfoList = productInfoList;
    }
}
