package com.nsy.api.wms.domain.stockin;

import java.util.Date;

public class StockinOrderDetailExport {
    // 入库单Id
    private Integer stockinOrderId;

    // 分公司入库单Id
    private Integer realStockinOrderId;
    // 入库单号
    private String stockinOrderNo;

    // 出库箱码
    private String supplierDeliveryBoxCode;

    // 仓库
    private String spaceName;

    // 入库类型
    private String stockinType;
    private String stockinTypeStr;

    // 工厂
    private String supplierName;

    // 状态
    private String status;
    private String statusStr;

    private String sku;

    // 入库总件数
    private Integer qty;

    // 上架总件数
    private Integer shelveQty;

    // 退货数量
    private Integer returnQty;

    // 出库箱号
    private String boxIndex;

    // 采购计划单号
    private String purchasePlanNo;

    // 工厂出库单号
    private String supplierDeliveryNo;

    // 物流单号
    private String logisticsNo;

    // 备注
    private String description;

    // 收货员
    private String operator;

    // 开始时间
    private String createDate;

    // 收货完成时间
    private String operateEndDate;

    private Date completeShelvedDate;

    public Integer getStockinOrderId() {
        return stockinOrderId;
    }

    public void setStockinOrderId(Integer stockinOrderId) {
        this.stockinOrderId = stockinOrderId;
    }

    public Integer getRealStockinOrderId() {
        return realStockinOrderId;
    }

    public void setRealStockinOrderId(Integer realStockinOrderId) {
        this.realStockinOrderId = realStockinOrderId;
    }

    public String getStockinOrderNo() {
        return stockinOrderNo;
    }

    public void setStockinOrderNo(String stockinOrderNo) {
        this.stockinOrderNo = stockinOrderNo;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getStockinTypeStr() {
        return stockinTypeStr;
    }

    public void setStockinTypeStr(String stockinTypeStr) {
        this.stockinTypeStr = stockinTypeStr;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public Integer getShelveQty() {
        return shelveQty;
    }

    public void setShelveQty(Integer shelveQty) {
        this.shelveQty = shelveQty;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public String getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(String boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(String operateEndDate) {
        this.operateEndDate = operateEndDate;
    }

    public Date getCompleteShelvedDate() {
        return completeShelvedDate;
    }

    public void setCompleteShelvedDate(Date completeShelvedDate) {
        this.completeShelvedDate = completeShelvedDate;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
