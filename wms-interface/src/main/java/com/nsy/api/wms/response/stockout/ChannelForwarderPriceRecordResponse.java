package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-03-22 15:59:32
 */
@ApiModel(value = "ChannelForwarderPriceRecordResponse", description = "返回对象")
public class ChannelForwarderPriceRecordResponse {

    private BigDecimal chargeWeight;

    // 邮编区域
    // 如果邮编 = 区域名称自动映射邮编，则自动映射到区域
    private String areaName;

    // 偏远等级
    private String level;

    // 目标国家运费id
    private Integer targetCountryFreightId;

    // 目标国家运费
    private BigDecimal targetPrice;

    // 总运费
    private BigDecimal totalPrice;

    // 基础运费
    private BigDecimal basicPrice;

    // 燃油费
    private BigDecimal fuelPrice = BigDecimal.ZERO;

    // 附加费
    private BigDecimal surchargePrice = BigDecimal.ZERO;

    // 挂号费
    private BigDecimal registrationPrice = BigDecimal.ZERO;

    // 偏远费
    private BigDecimal estimateRemoteAreaFee = BigDecimal.ZERO;

    // 税费
    private BigDecimal estimateTax = BigDecimal.ZERO;

    // 手续费
    private BigDecimal estimateServiceFee = BigDecimal.ZERO;

    // 错误信息
    private String errorStr;

    public BigDecimal getChargeWeight() {
        return chargeWeight;
    }

    public void setChargeWeight(BigDecimal chargeWeight) {
        this.chargeWeight = chargeWeight;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Integer getTargetCountryFreightId() {
        return targetCountryFreightId;
    }

    public void setTargetCountryFreightId(Integer targetCountryFreightId) {
        this.targetCountryFreightId = targetCountryFreightId;
    }

    public BigDecimal getTargetPrice() {
        return targetPrice;
    }

    public void setTargetPrice(BigDecimal targetPrice) {
        this.targetPrice = targetPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getBasicPrice() {
        return basicPrice;
    }

    public void setBasicPrice(BigDecimal basicPrice) {
        this.basicPrice = basicPrice;
    }

    public BigDecimal getFuelPrice() {
        return fuelPrice;
    }

    public void setFuelPrice(BigDecimal fuelPrice) {
        this.fuelPrice = fuelPrice;
    }

    public BigDecimal getSurchargePrice() {
        return surchargePrice;
    }

    public void setSurchargePrice(BigDecimal surchargePrice) {
        this.surchargePrice = surchargePrice;
    }

    public BigDecimal getRegistrationPrice() {
        return registrationPrice;
    }

    public void setRegistrationPrice(BigDecimal registrationPrice) {
        this.registrationPrice = registrationPrice;
    }

    public BigDecimal getEstimateRemoteAreaFee() {
        return estimateRemoteAreaFee;
    }

    public void setEstimateRemoteAreaFee(BigDecimal estimateRemoteAreaFee) {
        this.estimateRemoteAreaFee = estimateRemoteAreaFee;
    }

    public BigDecimal getEstimateTax() {
        return estimateTax;
    }

    public void setEstimateTax(BigDecimal estimateTax) {
        this.estimateTax = estimateTax;
    }

    public BigDecimal getEstimateServiceFee() {
        return estimateServiceFee;
    }

    public void setEstimateServiceFee(BigDecimal estimateServiceFee) {
        this.estimateServiceFee = estimateServiceFee;
    }

    public String getErrorStr() {
        return errorStr;
    }

    public void setErrorStr(String errorStr) {
        this.errorStr = errorStr;
    }
}

