package com.nsy.api.wms.enumeration.qa;

public enum StockinQaOrderProcessStatusEnum {
    INCOMPLETE("未完成"),
    FIRST_AUDIT("质检初审"),
    SECOND_AUDIT("质检复审"),
    FULL_INSPECT("质检全检"),
    INSPECT_AUDIT("质检稽查"),
    COMPLETED("质检完成"),
    CANCELED("已取消");

    String status;

    StockinQaOrderProcessStatusEnum(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }


}
