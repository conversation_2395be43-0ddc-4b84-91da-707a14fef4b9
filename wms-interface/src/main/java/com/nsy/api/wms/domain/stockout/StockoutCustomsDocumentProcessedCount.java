package com.nsy.api.wms.domain.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @version 1.0
 * <AUTHOR>
 * @date 2022/3/18
 */
@ApiModel(value = "StockoutCustomsDocumentProcessedCount", description = "已处理数")
public class StockoutCustomsDocumentProcessedCount {

    @ApiModelProperty(value = "已处理总数", name = "todayResolveNum")
    private Integer processedNum;

    @ApiModelProperty(value = "今天已处理总数", name = "todayResolveNum")
    private Integer todayProcessedNum;

    public Integer getProcessedNum() {
        return processedNum;
    }

    public void setProcessedNum(Integer processedNum) {
        this.processedNum = processedNum;
    }

    public Integer getTodayProcessedNum() {
        return todayProcessedNum;
    }

    public void setTodayProcessedNum(Integer todayProcessedNum) {
        this.todayProcessedNum = todayProcessedNum;
    }
}
