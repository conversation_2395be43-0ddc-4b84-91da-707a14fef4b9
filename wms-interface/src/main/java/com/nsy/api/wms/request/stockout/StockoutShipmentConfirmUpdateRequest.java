package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


public class StockoutShipmentConfirmUpdateRequest {
    /**
     * 装箱清单id列表
     */
    @ApiModelProperty(value = "装箱清单id列表", name = "shipmentIds")
    @NotEmpty(message = "装箱清单id列表不能为空")
    private List<Integer> shipmentIds;

    /**
     * 出库单号列表
     */
    @ApiModelProperty(value = "出库单号列表", name = "stockoutOrderNos")
    @NotEmpty(message = "出库单号列表不能为空")
    private List<String> stockoutOrderNos;

    /**
     * 物流渠道
     */
    @ApiModelProperty(value = "物流渠道(公司)", name = "logisticsCompany")
    private String logisticsCompany;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "货代渠道", name = "forwarderChannel")
    private String forwarderChannel;

    // 是否重新打印
    @ApiModelProperty(value = "是否重新打印", name = "rePrint")
    private Boolean rePrint = Boolean.FALSE;

    // 发货日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDate;

    private BigDecimal unitPrice;

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Boolean getRePrint() {
        return rePrint;
    }

    public void setRePrint(Boolean rePrint) {
        this.rePrint = rePrint;
    }

    public List<Integer> getShipmentIds() {
        return shipmentIds;
    }

    public void setShipmentIds(List<Integer> shipmentIds) {
        this.shipmentIds = shipmentIds;
    }

    public List<String> getStockoutOrderNos() {
        return stockoutOrderNos;
    }

    public void setStockoutOrderNos(List<String> stockoutOrderNos) {
        this.stockoutOrderNos = stockoutOrderNos;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getForwarderChannel() {
        return forwarderChannel;
    }

    public void setForwarderChannel(String forwarderChannel) {
        this.forwarderChannel = forwarderChannel;
    }
}
